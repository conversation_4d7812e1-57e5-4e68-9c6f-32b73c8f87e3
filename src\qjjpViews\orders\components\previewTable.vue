<!--
 * @Author: 陈小豆
 * @Date: 2024-11-15 18:05:23
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-11-15 18:07:40
-->
<template>
  <div>
    <page ref="previewTable" :request="request" :list="list" table-title="预览导入订单" />
    <div class="footer-btn">
      <el-button @click="$emit('action', 'close')">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </div>
</template>
<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import moment from 'moment'
import { thirdGoodsOrderImportExcel } from '@/qjjpApi/equityCommodity'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'equityOrders',
  components: {
    page
  },
  props: {
    params: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      listQuery: {
      },
      request: {
        getListUrl: async data => {
          const list = await this.getData()
          console.log(list, '*******************')
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          this.$nextTick(() => {
            setTimeout(() => {
              const table = this.$refs.previewTable.$children.find(item => item.$el.className === 'table-base').$children[0].$el.querySelector('tbody').querySelectorAll('.el-table__row')
              records.forEach((item, index) => {
                if (item.errorPrompt !== '') {
                  table[index].setAttribute('class', `${table[index].getAttribute('class')} lineRow`)
                }
              })
            }, 0)
          })
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '订单编号',
          key: 'orderNo'
        },
        {
          title: '供应商',
          key: 'supplier'
        },
        {
          title: '权益商品名称',
          key: 'goodsName'
        },
        {
          title: '第三方订单',
          key: 'thirdOrderNo'
        },
        {
          title: '商品第三方售价',
          key: 'goodsPrice'
        },
        {
          title: '订单金额',
          key: 'amount'
        },
        {
          title: '兑换的号码/充值账号',
          key: 'exchangeNo'
        },
        {
          title: '订单状态',
          key: 'orderStatusString'
        },
        {
          title: '订单信息',
          key: 'errorPrompt'
        }
      ]
    }
  },
  methods: {
    getData() {
      return new Promise(resolve => {
        const paramsData = JSON.parse(JSON.stringify(this.params))
        const data = {
          records: paramsData.row,
          total: paramsData.row.length
        }
        resolve({ code: 200, data, msg: 'success' })
      })
    },
    submit() {
      const paramsData = JSON.parse(JSON.stringify(this.params))
      const key = paramsData.row[0].redisKey
      thirdGoodsOrderImportExcel({ key }).then(res => {
        if (res.code == 200) {
          this.$message.success('导入成功')
        }
        this.$emit('action', 'confirm')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.footer-btn{
  position: fixed;
  width: 100%;
  display: flex;
  left: 0;
  justify-content: center;
  background-color: #ffffff;
  bottom: 0;
  z-index: 999;
  padding: 20px 0;
}
::v-deep .lineRow{
  background-color: rgba(255,0,0,0.02);
  color:red;
}
</style>
