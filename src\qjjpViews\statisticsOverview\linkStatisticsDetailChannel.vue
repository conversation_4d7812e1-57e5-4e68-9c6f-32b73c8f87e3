<template>
  <div>
    <page :request="request" :list="list" table-title="投放链路统计" :fy="[500]" :maxHeight="550">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
      <div slot="othersSearchLine" style="display: inline-block">
        <el-checkbox v-model="listQuery.fixedDate" @change="fixedDateChange" class="checkFixedDate">仅查看当天</el-checkbox>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page/v8'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getmerchantcount, downOrUp, getlinkChannelDetail, linkChannelDetailExport } from '@/qjjpApi/statisticsOverview'
import moment from 'moment'
import linkStatisticsAll from './linkStatisticsAll'
import { checkChannelLook } from '@/qjjpApi/system'
import {
  get_admin_list
} from '@/api/system'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'LinkStatisticsDetailChannel',
  components: {
    page
  },
  mixins: [linkStatisticsAll],
  props: {},
  data() {
    return {
      haveQX: false,
      adminList: [],
      tableStyle: {
        height: '800'
      },
      total: 500,
      curStatusParams: {
        id: 0,
        status: 0
      },

      siteIds: [],
      listQuery: {
        iosServiceChargeType: 1,
        fixedDate: true,
        startDate: this.$route.query.startDate || moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: this.$route.query.endDate || moment().format('YYYY-MM-DD'),
        ...this.$route.query
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data, pageSize: 500 }
          const list = await getlinkChannelDetail(this.listQuery)
          await checkChannelLook().then(async res => {
            if (res.code === 200) {
              this.haveQX = res.data
              if (res.data) {
                await get_admin_list({ pageSize: 1000 }).then(res => {
                  if (res.code === 0) {
                    this.adminList = res.data
                  }
                })
              }

            }
          })
          setTimeout(() => {
            if (Object.keys(this.$route.query).length > 0) {
              Object.keys(this.$route.query).forEach(key => {
                this.$setLocaUrlQuery(key, '')
              })
            }
          }, 0)
          const { records, total } = list.data
          console.info(list, 'list')
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '渠道ID',
          key: 'channelCode',
          fixed: 'left',
          render: (h, params) => {
            const channelCode = params.data.row.channelCode
            return (
              <div>
                {!channelCode || channelCode == '' || channelCode == '汇总' ? <div>汇总 <el-tooltip
                  content='按照筛选条件进行渠道分组去重汇总计算'
                  placement='top'
                  style='color:#409eff'
                >
                  <i class='el-icon-question' style='font-size: 14px' />
                </el-tooltip>
                </div> : h('a', {
                  attrs: {
                    href: location.origin + '#/qjjp/deliveryManage/channelDetail?id=' + params.data.row.channelId + '&type=edit',
                    target: '_blank'
                  },
                  style: {
                    'text-decoration': 'underline',
                    'color': 'blue'
                  }
                }, channelCode)}
              </div>

            )
          }
        },
        ...this.getListArray({ isShowChannelCode: false, isShowLoginType: false, isShowPayScenee: false, from: 'detailChannel' })
      ]
    }
  },
  created() { },
  methods: {
    fixedDateChange() {
      this.$store.dispatch('tableRefresh', this)
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = linkChannelDetailExport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    changeStatus() {
      const params = this.curStatusParams
      downOrUp({ ...params }).then(res => {
        if (res.code == 200) {
          this.$message.success('更改状态成功')
          this.$store.dispatch('tableRefresh', this)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.checkFixedDate {
  margin-left: 20px;
}

.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
