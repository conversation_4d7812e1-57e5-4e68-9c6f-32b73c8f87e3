import { formItemType, tableItemType } from '@/config/sysConfig'
import { mediaAll, undertakeSelector,count_channel_application_list } from '@/qjjpApi/NewChannel'
import { osList } from '@/qjjpViews/appVersion/basicParams'
import moment from 'moment'
export default {
  data() {
    return {
      list1: [
        {
          id: 2,
          name: '一键登录'
        },
        {
          id: 1,
          name: '验证码登录'
        },
        {
          id: 3,
          name: '微信登录'
        }
      ],
      list2: [
        {
          id: 2,
          name: '新'
        },
        {
          id: 3,
          name: '老'
        },
        {
          id: 1,
          name: '总'
        }
      ],
      list3: [
        {
          id: 1,
          name: '站内支付'
        },
        {
          id: 2,
          name: '站内落地页支付'
        },
        {
          id: 3,
          name: '悬浮窗支付'
        }
      ],
      list4: [
        {
          id: 0,
          name: '启用'
        },
        {
          id: 1,
          name: '禁用'
        }
      ],
      list5: [{
        id: 1,
        name: 'apk链路'
      },
      {
        id: 2,
        name: 'h5链路'
      },
      {
        id: 3,
        name: 'H5下载+APK付费'
      }],
      iosServiceChargeTypeList: [
        {
          id: 1,
          name: '核算'
        },
        {
          id: 0,
          name: '不核算'
        }
      ],
      mediaList: [],
      undertakeList: [],
      siteIdsList:[]
    }
  },
  created() {
    count_channel_application_list().then(res => {
      if (res.code === 200) {
        this.siteIdsList = res.data
      }
    })
    mediaAll().then(res => {
      if (res.code === 200) {
        this.mediaList = res.data
        // this.mediaList = res.data
      }
    })
    if (this.undertakeList.length == 0) {
      undertakeSelector({ type: 1 }).then(res => {
        if (res.code === 200) {
          this.undertakeList = res.data
        }
      })
    }
  },
  methods: {
    getListArray({
      pickerDay = 30, isShowChannelCode = true, isShowPayScenee = true, isShowLoginType = true, from = 'list'
    } = {}) {
      const list = [
        /** ********↓筛选↓*************/
        {
          searchKey: 'dateSearch',
          title: '日期',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          pickerDay,
          search: true,
          tableHidden: true,
          options: {
            on: () => {
              return {
                change: e => {
                  console.info(85455353 + '=============================================<')
                  this.$set(this.listQuery, 'fixedDate', false)
                }
              }
            }
          },
          val: [this.$route.query.startDate || (pickerDay == 0 ? moment().subtract(0, 'days').format('YYYY-MM-DD') : moment().subtract(6, 'days').format('YYYY-MM-DD')), this.$route.query.endDate || moment().format('YYYY-MM-DD')]
        },
        {
          title: '登录方式',
          searchKey: 'loginType',
          type: formItemType.select,
          multiple: true,
          tableView: tableItemType.tableView.text,
          list: this.list1,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          val: this.listQuery.loginType && this.listQuery.loginType != '' ? this.listQuery.loginType.split(',') : '',
          tableHidden: true,
          clearable: true
        },
        {
          title: '支付场景',
          searchKey: 'payScene',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          multiple: true,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true,
          tableHidden: true,
          val: this.listQuery.payScene && this.listQuery.payScene != '' ? this.listQuery.payScene.split(',') : '',
          search: true
        },
        {
          title: '设备类型',
          searchKey: 'deviceType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true,
          tableHidden: true,
          val: this.listQuery.deviceType || '',
          search: true
        },
        {
          title: '渠道ID',
          type: formItemType.input,
          searchKey: 'channelCode',
          val: this.listQuery.channelCode || '',
          tableHidden: !isShowChannelCode || !this.listQuery.channelCode,
          search: true,
          clearable: true
        },
        {
          title: '承接页面',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          searchKey: 'ladingPageName',
          val: this.listQuery.ladingPageName || '',
          tableHidden: true,
          search: true,
          clearable: true,
          list: this.undertakeList,
          listFormat: {
            label: 'name',
            value: 'name'
          },
          reg: ['required']
        },
        {
          title: '投放平台',
          searchKey: 'apiCode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.mediaList,
          val: this.listQuery.apiCode || '',
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          reg: ['required'],
          clearable: true,
          search: true,
          tableHidden: true
        },
        {
          title: '应用类型',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: osList,
          reg: ['required'],
          search: true,
          tableHidden: true,
          val: this.listQuery.os || ''
        },
        {
          title: 'ios手续费',
          key: 'iosServiceChargeType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.iosServiceChargeTypeList,
          reg: ['required'],
          listFormat: {
            label: 'name',
            value: 'id'
          },
          search: true,
          tableHidden: true,
          val: this.listQuery.iosServiceChargeType,
          clearable: false
        },
        // {
        //   title: '优化师',
        //   key: 'adminId',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.adminList,
        //   reg: ['required'],
        //   listFormat: {
        //     label: 'nickname',
        //     value: 'id'
        //   },
        //   multiple: false,
        //   search: this.haveQX && from != 'list',
        //   tableHidden: true,
        //   val: this.listQuery.adminId || ''
        // },
        {
          title: '优化师',
          key: 'adminId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.adminList,
          reg: ['required'],
          listFormat: {
            label: 'nickname',
            value: 'id'
          },
          multiple: true,
          search: this.haveQX && from == 'list',
          tableHidden: true,
          val: this.listQuery.adminId || ''
        },
        {
          title: '应用名称',
          searchKey: 'siteIds',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          multiple: true,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        /** ********↑筛选↑*************/
        {
          otherScreen: true,
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          clearable: true,
          tableHidden: !this.listQuery.siteIds
        },
        {
          otherScreen: true,
          title: '登录方式',
          key: 'loginType',
          type: formItemType.select,
          multiple: true,
          tableView: tableItemType.tableView.text,
          list: this.list1,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          tableHidden: !isShowPayScenee || !this.listQuery.loginType,
          clearable: true
        },
        {
          title: '支付场景',
          key: 'payScene',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          multiple: true,
          listFormat: {
            label: 'name',
            value: 'id'
          },

          reg: ['required'],
          clearable: true,
          tableHidden: !isShowLoginType || !this.listQuery.payScene
        },
        // {
        //   isCustom: true,
        //   title: '投放平台',
        //   key: 'apiCode',
        //   tableHidden: from != 'detailChannel',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.mediaList,
        //   listFormat: {
        //     label: 'platformName',
        //     value: 'platformCode'
        //   }
        // },
        // {
        //   isCustom: true,
        //   title: '承接页面',
        //   key: 'ladingPageName',
        //   tableHidden: from != 'detailChannel'
        // },
        {
          isCustom: true,
          title: '激活数',
          key: 'deviceActive',
          renderHeader: (...args) => this.renderHeader(...args, ['激活设备数'])
        },
        {
          title: '优化师',
          key: 'adminName',
          tableHidden: !this.listQuery.adminId || from == 'detailTimer'
        },
        {
          isCustom: true,
          title: '渠道信息',
          key: 'qdxx',
          tableHidden: from != 'detailChannel',
          children: [
            {
              title: '渠道创建人',
              key: 'channelCreateName'
            },
            {
              title: '应用名称',
              key: 'appName'
            },
            {
              title: '投放链路',
              key: 'downloadType',
              render: (h, params) => {
                return h('span', params.data.row.downloadType ? this.list5.find(item => item.id == params.data.row.downloadType).name : '-')
              }
            },
            {
              title: '投放平台',
              key: 'apiCode',
              render: (h, params) => {
                console.info(this.mediaList.find(item => item.platformCode == params.data.row.apiCode).platformName)
                return h('span', params.data.row.apiCode ? this.mediaList.find(item => item.platformCode == params.data.row.apiCode).platformName : '-')
              }
              // tableHidden: from != 'detailChannel',
              // type: formItemType.select,
              // tableView: tableItemType.tableView.text,
              // list: [...mediaLists],
              // listFormat: {
              //   label: 'platformName',
              //   value: 'platformCode'
              // }
            },
            {
              title: '承接页名称',
              key: 'ladingPageName'
            }
          ]
        },
        {
          isCustom: true,
          title: `投放效果侧${this.listQuery.iosServiceChargeType == 1 ? '（净收益已核算ios手续费）' : ''}`,
          key: 'tfxgc',
          children: [
            {
              title: '总消耗',
              key: 'consumption',
              sortable: from == 'detailChannel' ? 'custom' : '',
              renderHeader: (...args) => this.renderHeader(...args, ['投放媒体的总消耗，计算公式：头条媒体总消耗=数据中台返回的现金消耗（cost）+赠款消耗(grantCost)，除头条媒体外其他媒体总消耗=数据中台返回的现金消耗（cost）'])
            },
            {
              title: '会员卡收益',
              key: 'memberCardIncome',
              sortable: 'custom',
              renderHeader: (...args) => this.renderHeader(...args, ['未核算退款的购卡收益'])
            },
            {
              title: '会员卡净收益(新)',
              key: 'memberCardNetGainNew',
              sortable: 'custom',
              renderHeader: (...args) => this.renderHeader(...args, ['核算退款后的实际购卡收益，计算公式：新=当日购卡金额-当日购卡当日退款金额'])
            },
            {
              title: '会员卡净收益(总)',
              key: 'memberCardNetGainTotal',
              sortable: 'custom',
              renderHeader: (...args) => this.renderHeader(...args, ['核算退款后的实际购卡收益，计算公式：总=当日购卡金额-当日退款金额'])
            },
            {
              title: '广告收益',
              key: 'advertisingEstimatedIncome',
              renderHeader: (...args) => this.renderHeader(...args, ['站内广告收益，计算公式：开屏预估收益+激励视频预估收益'])
            },
            from != 'detailTimer' ? {
              title: '留存毛收益',
              key: 'remainAllEarnings',
              tableHidden: from == 'detailTimer',
              renderHeader: (...args) => this.renderHeader(...args, ['对应设备激活后产生的收益之和，计算公式：会员卡收益+广告收益'])
            } : {},
            from != 'detailTimer' ? {
              title: '留存净收益',
              key: 'remainNetEarnings',
              tableHidden: from == 'detailTimer',
              renderHeader: (...args) => this.renderHeader(...args, ['对应设备激活后产生的净收益之和，计算公式：会员卡收益-退款金额+广告收益'])
            } : {},
            {
              title: '激活成本',
              key: 'activationCost',
              renderHeader: (...args) => this.renderHeader(...args, ['激活人均成本，计算公式：总消耗/激活数'])
            },
            {
              title: '登录成本',
              key: 'loginCost',
              renderHeader: (...args) => this.renderHeader(...args, ['登录人均成本，计算公式：总消耗/登录数'])
            },
            {
              title: '购卡成本',
              key: 'cardCost',
              renderHeader: (...args) => this.renderHeader(...args, ['购卡人均成本，计算公式：总消耗/购卡数'])
            },
            {
              title: '活动成本',
              key: 'activityCost',
              renderHeader: (...args) => this.renderHeader(...args, ['活动成本=使用权益成本，影响字段：会员卡净收益（新）、会员卡净收益（总）、留存净收益、购卡roi（核算退款）、当日roi（净收益）、三日roi（净收益），留存roi（净收益）；以上字段均需要减去活动成本'])
            },
            {
              title: '购卡roi（核算退款）',
              key: 'cardNetGainAccountRefundRoi',
              renderHeader: (...args) => this.renderHeader(...args, ['核算退款后的购卡roi，计算公式：会员卡净收益（新/总）/总消耗'])
            },
            {
              title: '购卡roi（未核算退款）',
              key: 'cardNetGainRoi',
              renderHeader: (...args) => this.renderHeader(...args, ['未核算退款的购卡roi，计算公式：会员卡收益/实际消耗'])
            },
            from != 'detailTimer' ? {
              title: '当日roi（毛收益）',
              key: 'remainTodayAllRoi',
              tableHidden: from == 'detailTimer',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：当日留存毛收益/总消耗'])
            } : {},
            from != 'detailTimer' ? {
              title: '三日roi（毛收益）',
              key: 'remainThreeAllRoi',
              tableHidden: from == 'detailTimer',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：三留存毛收益/总消耗'])
            } : {},
            from != 'detailTimer' ? {
              title: '留存roi（毛收益）',
              key: 'remainAllRoi',
              tableHidden: from == 'detailTimer',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：留存毛收益/总消耗'])
            } : {},
            from != 'detailTimer' ? {
              title: '当日roi（净收益）',
              key: 'remainTodayNetRoi',
              tableHidden: from == 'detailTimer',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：当日留存净收益/总消耗'])
            } : {},
            from != 'detailTimer' ? {
              title: '三日roi（净收益）',
              key: 'remainThreeNetRoi',
              tableHidden: from == 'detailTimer',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：三留存净收益/总消耗'])
            } : {},
            from != 'detailTimer' ? {
              title: '留存roi（净收益）',
              key: 'remainNetRoi',
              tableHidden: from == 'detailTimer',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：留存净收益/总消耗'])
            } : {},
            

          ]
        },
        {
          isCustom: true,
          title: '新人引导侧',
          key: 'xryd',
          defaultShowKey: false,
          children: [
            {
              title: '性别选择页曝光',
              key: 'sexSelectPageShow'
            },
            {
              title: '性别选择页点击',
              key: 'sexSelectPageClick'
            },
            {
              title: '性别选择页点击率',
              key: 'sexSelectPageClickRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：性别选择页点击/性别选择页曝光'])
            },
            {
              title: '生日选择页曝光',
              key: 'birthdaySelectPageShow'
            },
            {
              title: '生日选择页点击',
              key: 'birthdaySelectPageClick'
            },
            {
              title: '生日选择页点击率',
              key: 'birthdaySelectPageClickRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：生日选择页点击/生日选择页曝光'])
            }
          ]
        },
        {
          isCustom: true,
          title: '站外链路侧',
          key: 'zw',
          defaultShowKey: false,
          children: [
            {
              title: 'P4曝光人数',
              key: 'p4PageShow',
              renderHeader: (...args) => this.renderHeader(...args, ['人数按照媒体用户ID对应的唯一uuid去重'])
            },
            {
              title: 'P4下载按钮点击人数',
              key: 'p4Download',
              renderHeader: (...args) => this.renderHeader(...args, ['P4落地页下载按钮点击人数'])
            },
            {
              title: 'P4点击率',
              key: 'p4ClickRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式=P4下载按钮点击人数/P4曝光人数'])
            },
            {
              title: '下载激活率',
              key: 'downloadRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式=激活数/P4下载按钮点击人数'])
            }
          ]
        },
        {
          isCustom: true,
          title: '授权侧',
          key: 'sq',
          defaultShowKey: false,
          children: [
            {
              title: '引导授权页曝光',
              key: 'authorizationPageShow'
            },
            {
              title: '引导授权页点击',
              key: 'authorizationPageClick'
            },
            {
              title: '引导授权页点击率',
              key: 'authorizationPageClickRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：引导授权页点击/引导授权页曝光'])
            },
            {
              title: '授权成功数',
              key: 'authorizationPageSuccess',
              renderHeader: (...args) => this.renderHeader(...args, ['悬浮窗授权成功设备数'])
            }
            // {
            //   title: '授权成功返回数',
            //   key: 'authorizationPageSuccess',
            //   renderHeader: (...args) => this.renderHeader(...args, ['悬浮窗授权成功并回到app的设备数'])
            // }
          ]
        },
        {
          isCustom: true,
          title: '登录侧',
          key: 'dl',
          children: [
            {
              title: '登录弹窗曝光',
              key: 'loginPageShow'
            },
            {
              title: '登录按钮点击',
              key: 'loginPageClick'
            },
            {
              title: '登录按钮点击率',
              key: 'loginPageClickRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：登录按钮点击/登录弹窗曝光'])
            },
            {
              title: '登录设备数',
              key: 'loginNum',
              renderHeader: (...args) => this.renderHeader(...args, ['登录设备数，按照设备去重'])
            },
            {
              title: '登录用户数',
              key: 'loginUserNum',
              renderHeader: (...args) => this.renderHeader(...args, ['登录用户数，按照用户去重'])
            }
          ]
        },

        {
          isCustom: true,
          title: '支付侧',
          key: 'zf',
          children: [
            {
              title: '达标率',
              key: 'useUpChatRatio',
              renderHeader: (...args) => this.renderHeader(...args, ['体验次数用完的用户占比，计算个公式：体验次数用完点击人设人数/激活数'])
            },
            {
              title: '付费弹窗曝光',
              key: 'payPageShow'
            },
            {
              title: '付费弹窗点击',
              key: 'payPageClick'
            },
            {
              title: '支付点击率',
              key: 'payClickRatio'
            },
            {
              title: '支付数',
              key: 'orderNum',
              renderHeader: (...args) => this.renderHeader(...args, ['支付成功订单数'])
            },
            {
              title: '支付人数',
              key: 'orderUserNum',
              renderHeader: (...args) => this.renderHeader(...args, ['会员支付成功人数，按照设备去重'])
            },
            {
              title: '支付金额',
              key: 'orderAmount',
              renderHeader: (...args) => this.renderHeader(...args, ['支付成功订单金额'])
            },
            {
              title: '均单价',
              key: 'avgUnitPrice',
              renderHeader: (...args) => this.renderHeader(...args, ['支付订单均价，计算公式：支付金额/支付笔数'])
            },
            {
              title: '激活cvr',
              key: 'activeCvr',
              renderHeader: (...args) => this.renderHeader(...args, ['激活cvr，计算公式：支付数/激活数'])
            },
            {
              title: '登录cvr',
              key: 'loginCvr',
              renderHeader: (...args) => this.renderHeader(...args, ['登录cvr，计算公式：支付数/登录用户数'])
            },
            
          ]
        },
        {
          isCustom: true,
          title: '活动侧',
          key: 'hd',
          children: [
            {
              title: '参与人数',
              key: 'joinUserCount',
              renderHeader: (...args) => this.renderHeader(...args, ['权益会员支付成功人数，按照设备去重'])
            },
            {
              title: '参与支付金额',
              key: 'joinAmount',
              renderHeader: (...args) => this.renderHeader(...args, ['权益会员支付成功订单金额'])
            },
            {
              title: '发放权益人数',
              key: 'sendRightUser',
              renderHeader: (...args) => this.renderHeader(...args, ['权益会员发放权益人数，按照设备去重'])
            },
            {
              title: '领取人数',
              key: 'receiveUser',
              renderHeader: (...args) => this.renderHeader(...args, ['领取权益会员商品的人数，按照设备去重'])
            },
            {
              title: '领取权益成本',
              key: 'receiveCost',
              renderHeader: (...args) => this.renderHeader(...args, ['领取权益会员商品的成本'])
            },
            {
              title: '使用人数',
              key: 'useCount',
              renderHeader: (...args) => this.renderHeader(...args, ['使用权益会员商品的人数，按照设备去重'])
            },
            {
              title: '使用权益成本',
              key: 'useCost',
              renderHeader: (...args) => this.renderHeader(...args, ['使用权益会员商品的成本'])
            },
            {
              title: '参与占比（人数/金额）',
              key: 'joinRateStr',
              renderHeader: (...args) => this.renderHeader(...args, ['参与占比（人数）=参与人数/支付人数，参与占比（金额）=参与支付金额/支付金额'])
            },
            {
              title: '领取占比（人数）',
              key: 'receiveRate',
              renderHeader: (...args) => this.renderHeader(...args, ['领取占比（人数）=领取权益人数/发放权益人数'])
            },
            {
              title: '使用占比（人数/金额）',
              key: 'useRate',
              renderHeader: (...args) => this.renderHeader(...args, ['使用占比（人数）=使用权益人数/领取权益人数，使用占比（金额）=使用权益成本/领取权益成本'])
            }
          ]
        },
        {
          isCustom: true,
          title: '售后侧',
          key: 'shc',
          children: [
            {
              title: '退款数（新/总）',
              key: 'refundNumNewAll',
              renderHeader: (...args) => this.renderHeader(...args, ['退款成功订单数，新总维度区分：购卡日期和退款日期同一天记为新，非同一天记为总'])
            },
            {
              title: '退款金额（新/总）',
              key: 'refundAmountNewAll',
              renderHeader: (...args) => this.renderHeader(...args, ['退款成功订单金额，新总维度区分：购卡日期和退款日期同一天记为新，非同一天记为总'])
            },
            {
              title: '退款笔数占比（新/总）',
              key: 'refundNumRate',
              renderHeader: (...args) => this.renderHeader(...args, ['退款笔数占比，计算公式：退款数（新/总）/支付数'])
            },
            {
              title: '退款金额占比（新/总）',
              key: 'refundAmountRate',
              renderHeader: (...args) => this.renderHeader(...args, ['退款金额占比，计算公式：金额=退款金额（新/总）/支付金额'])
            },
            {
              title: '投诉数',
              key: 'complaint',
              renderHeader: (...args) => this.renderHeader(...args, ['商户号投诉订单数，新老设备区分：按照购卡时间和投诉时间区分，购卡时间和投诉时间同一天记为新，非同一天记为总，老=总-新'])
            },
            {
              title: '投诉率',
              key: 'complaintRate',
              renderHeader: (...args) => this.renderHeader(...args, ['投诉订单占比，计算公式：投诉数/支付数'])
            }
          ]
        },
        {
          isCustom: true,
          title: '回传侧',
          key: 'hcc',
          children: [
            {
              title: '节点1全量回传数',
              key: 'callbackFirstNodeAll',
              renderHeader: (...args) => this.renderHeader(...args, ['触发节点1回传行为的设备数'])
            },
            {
              title: '节点1打折回传数',
              key: 'callbackFirstNode',
              renderHeader: (...args) => this.renderHeader(...args, ['节点1打折后的回传设备数'])
            },
            {
              title: '节点1实际回传数',
              key: 'callbackFirstNodeReal',
              renderHeader: (...args) => this.renderHeader(...args, ['节点1成功回传媒体的设备数，即调用项目中台回传接口返回成功的设备数'])
            },
            {
              title: '节点2全量回传数',
              key: 'callbackTwoNodeAll',
              renderHeader: (...args) => this.renderHeader(...args, ['触发节点2回传行为的设备数'])
            },
            {
              title: '节点2打折回传数',
              key: 'callbackTwoNode',
              renderHeader: (...args) => this.renderHeader(...args, ['节点2打折后的回传设备数'])
            },
            {
              title: '节点2实际回传数',
              key: 'callbackTwoNodeReal',
              renderHeader: (...args) => this.renderHeader(...args, ['节点2成功回传媒体的设备数，即调用项目中台回传接口返回成功的设备数'])
            },
            {
              title: '其余节点全量回传数',
              key: 'callbackOtherNodeAll',
              renderHeader: (...args) => this.renderHeader(...args, ['节点数大于2的节点全量回传数求和'])
            },
            {
              title: '其余节点打折回传数',
              key: 'callbackOtherNode',
              renderHeader: (...args) => this.renderHeader(...args, ['节点数大于2的节点打折回传数求和'])
            },
            {
              title: '其余节点实际回传数',
              key: 'callbackOtherNodeReal',
              renderHeader: (...args) => this.renderHeader(...args, ['节点数大于2的节点实际回传数求和'])
            }
          ]
        },
        {
          isCustom: true,
          title: '转化侧',
          key: 'zh',
          children: [
            {
              title: '站内付费率',
              key: 'inStationRatio',
              renderHeader: (...args) => this.renderHeader(...args, ['站内付费率=非承接页支付设备数/首页曝光设备数*100%'])
            },
            {
              title: '落地页流失率',
              key: 'landingPageLossRatio',
              renderHeader: (...args) => this.renderHeader(...args, ['落地页流失率=1-落地页曝光且首页曝光设备数/落地页曝光设备数*100%'])
            },
            {
              title: '激活付费率',
              key: 'deviceActiveOrderRatio',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：支付数/激活数'])
            },
            {
              title: '激活授权率',
              key: 'authorizationPageSuccessRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：授权成功数/激活数'])
            },
            {
              title: '激活登录率',
              key: 'loginSuccessRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：登录设备数/激活数'])
            },
            // {
            //   title: '授权返回率',
            //   key: '性别选择页点击',
            //   renderHeader: (...args) => this.renderHeader(...args, ['计算公式：授权成功返回数/引导授权页点击'])
            // },
            {
              title: '授权登录率',
              key: 'authorizationPageSuccessLoginRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：登录成功数/授权成功数'])
            },
            {
              title: '登录付费率',
              key: 'loginSuccessPayRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：支付数/登录用户数'])
            },
            {
              title: '退款率',
              key: 'refundRate',
              renderHeader: (...args) => this.renderHeader(...args, ['计算公式：退款数/支付数'])
            }
          ]
        }
      ]
      return list
    },
    renderHeader(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h(
            'div',
            {
              slot: 'content'
            },
            [textArr.map(item => h('div', null, item))]
          ),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    }
  }
}
