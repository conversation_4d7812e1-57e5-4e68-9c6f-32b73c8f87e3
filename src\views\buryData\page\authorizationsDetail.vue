<template>
  <page :request="request" :list="list" table-title="授权页统计">
    <div
      slot="searchContainer"
      style="display: inline-block; margin-bottom: 10px"
    >
      <el-button
        plain
        type="warning"
        size="small"
        icon="el-icon-upload"
        @click="handUpload"
      >导出数据</el-button>
    </div>
  </page>
</template>
<script>
import page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import moment from 'moment'
import { GET_count_authorizations_page, export_count_authorizations_page } from '@/api/buryData'

export default {
  components: {
    page
  },
  data() {
    return {
      listQuery: {
        startTime: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: (data) => {
          this.listQuery = { ...this.listQuery, ...data }
          return Promise.all([GET_count_authorizations_page(this.listQuery)]).then(
            (res) => {
              return Promise.resolve(res[0])
            }
          )
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startTime', 'endTime'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [
            moment().subtract(6, 'days').format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date'
        },
        {
          title: '授权页访问pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.visitPv} / ${data.visitUv}`)
          }
        },

        {
          title: '不同意按钮点击pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.noInstallsPv} / ${data.noInstalls}`)
          }
        },
        {
          title: '同意按钮点击pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.agreeInstallsPv} / ${data.agreeInstalls}`)
          }
        },
        {
          title: '访问-成功转化',
          key: 'agreeRate',
          type: formItemType.input
        },
        {
          title: '物理返回',
          key: 'physicsReturn',
          type: formItemType.input
        }
      ]
    }
  },
  methods: {
    handUpload() {
      window.location.href = export_count_authorizations_page({
        ...this.listQuery,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>
