<template>
  <div class="ab-face">
    <page :request="request" :list="list" table-title="AB测试列表">
      <div slot="searchContainer" style="display: inline-block; margin-bottom: 15px;">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small"
          @click="handleAdd">添加</el-button>
      </div>
    </page>

    <!-- 新增/编辑抽屉 -->
    <Drawer
      :visible.sync="drawerVisible"
      :title="formData.id ? '编辑' : '新增'"
      @confirm="handleSubmit('form')"
      @cancel="drawerVisible = false"
    >
      <el-form ref="form" :model="formData" :rules="formRules" label-position="top" class="demo-ruleForm">
        <!-- 基础信息 -->
        <FormSection title="基础信息">
          <el-form-item label="应用名称" prop="siteId">
            <el-select v-model="formData.siteId" placeholder="请选择应用名称" style="width: 240px" @change="handleAppNameChange" :disabled="!!formData.id">
              <el-option
                v-for="item in appNameOptions"
                :key="item.siteId"
                :label="item.name"
                :value="item.siteId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="应用市场" prop="relationCode">
            <el-select v-model="formData.relationCode" placeholder="请选择应用市场" style="width: 240px" :disabled="!!formData.id">
              <el-option
                v-for="item in channelOptions"
                :key="item.appCode"
                :label="item.title"
                :value="item.appCode"
              />
            </el-select>
          </el-form-item>
        </FormSection>

        <!-- 判断条件 -->
        <FormSection title="判断条件">
          <el-form-item label="渠道类型" prop="channelType">
            <el-radio-group v-model="formData.channelType">
              <el-radio :label="0">无渠道</el-radio>
              <el-radio :label="1">应用市场渠道</el-radio>
              <!-- <el-radio :label="2">应用市场且无渠道</el-radio> -->
            </el-radio-group>
          </el-form-item>

          <el-form-item 
            label="屏蔽地区" 
            class="city-select-form-item"
            required
          >
            <div class="city-input-group">
              <el-select v-model="provinceCode" placeholder="请选择省份" style="width: 240px" @change="handleProvinceChange">
                <el-option
                  v-for="item in provinceOptions"
                  :key="item.provinceCode"
                  :label="item.provinceName"
                  :value="item.provinceCode"
                />
              </el-select>
              <el-button type="primary" @click="showCitySelector">添加地区</el-button>
            </div>
          </el-form-item>

          <div class="added-cities-container">
            <div class="added-cities-header">
              <span>已添加地区</span>
              <div>
                <span class="link-text" @click="selectAllCities">全选</span>
                <span class="link-text" @click="deleteSelectedCities">全部删除</span>
              </div>
            </div>

            <el-table 
              ref="citiesTable"
              :data="selectedCities" 
              border 
              style="width: 100%"
              @selection-change="handleCitySelectionChange">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column label="序号" width="80" type="index"></el-table-column>
              <el-table-column prop="fullName" label="城市名称"></el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" size="small" style="color: #409eff;" @click="deleteCity(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </FormSection>

        <!-- 状态 -->
        <FormSection title="状态">
          <el-form-item label="状态" prop="enable">
            <el-radio-group v-model="formData.enable">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </FormSection>
      </el-form>
    </Drawer>

    <!-- 选择城市弹窗 -->
    <el-dialog title="选择城市" :visible.sync="citySelectorVisible" width="50%" :close-on-click-modal="false">
      <el-table 
        ref="searchCitiesTable"
        :data="currentProvinceData.cityDtoList || []" 
        border 
        style="width: 100%" 
        @selection-change="handleCitySelectionChange">
        <el-table-column 
          type="selection" 
          width="55"
          :selectable="checkSelectable">
        </el-table-column>
        <el-table-column prop="id" label="城市编码"></el-table-column>
        <el-table-column prop="fullName" label="城市名称">
          <template slot-scope="scope">
            <span :class="{ 'disabled-city': isCitySelected(scope.row) }">{{ scope.row.fullName }}</span>
          </template>
        </el-table-column>
      </el-table>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="citySelectorVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelectedCities">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { GET_AB_PAGE, GET_CHANNEL_LIST, ADD_AB, UPDATE_AB } from '@/api/appVersion'
import { getCityList } from '@/api/appVersion'
import { count_channel_application_list } from '@/api/NewChannel'
import moment from 'moment'
import Drawer from '@/components/Drawer'
import FormSection from '@/components/Drawer/FormSection'

export default {
  name: 'AbFace',
  components: {
    page,
    Drawer,
    FormSection
  },
  data() {
    return {
      // 查询参数
      listQuery: {},
      // 抽屉显示状态
      drawerVisible: false,
      // 表单数据
      formData: {
        siteId: '',
        relationCode: '',
        channelType: '',
        city: '',
        enable: 1
      },
      // 表单验证规则
      formRules: {
        siteId: [
          { required: true, message: '请选择应用名称', trigger: 'change' }
        ],
        relationCode: [
          { required: true, message: '请选择应用市场', trigger: 'change' }
        ],
        channelType: [
          { required: true, message: '请选择渠道类型', trigger: 'change' }
        ],
        city: [
          { required: true, message: '请选择屏蔽地区', trigger: 'change' }
        ],
        enable: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      // 选项数据
      appNameOptions: [],
      channelOptions: [],
      provinceOptions: [],
      // API请求配置
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await GET_AB_PAGE(this.listQuery)
          const { records = [], total = 0 } = res.data
          return {
            data: {
              total: total,
              rows: records
            }
          }
        }
      },
      // 省份和城市选择相关
      provinceCode: '',
      currentProvinceData: {},
      selectedCities: [],
      citySelectorVisible: false,
      tempSelectedCities: [],
    }
  },
  computed: {
    list() {
      return [
        {
          title: '应用名称',
          key: 'siteId',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          list: this.appNameOptions,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          tableHidden: true,
          options: {
            placeholder: '请选择应用名称'
          }
        },
        {
          title: '应用市场',
          key: 'relationCode',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          list: this.channelOptions,
          listFormat: {
            label: 'title',
            value: 'appCode'
          },
          tableHidden: true,
          options: {
            placeholder: '请选择应用市场'
          }
        },
        {
          title: '状态',
          key: 'enable',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          list: [
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 }
          ],
          tableHidden: true,
          options: {
            placeholder: '请选择状态'
          }
        },
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '应用名称',
          key: 'siteId',
          render: (h, params) => {
            const appName = this.appNameOptions.find(item => item.siteId === params.data.row.siteId)
            return h('span', appName ? appName.name : '--')
          }
        },
        {
          title: '应用市场',
          key: 'relationCode',
          render: (h, params) => {
            const channel = this.channelOptions.find(item => String(item.appCode) === String(params.data.row.relationCode))
            return h('span', channel ? channel.title : '--')
          }
        },
        {
          title: '渠道类型',
          key: 'channelType',
          render: (h, params) => {
            const typeMap = {
              0: '无渠道',
              1: '应用市场渠道',
              // 2: '应用市场且无渠道'
            }
            return h('span', typeMap[params.data.row.channelType] || '--')
          }
        },
        {
          title: '屏蔽地区',
          key: 'city',
          render: (h, params) => {
            if (!params.data.row.city) return h('span', '--')
            
            const cityIds = params.data.row.city.split(',')
            const cityNames = []
            
            for (const cityId of cityIds) {
              for (const province of this.provinceOptions) {
                const city = province.cityDtoList.find(c => String(c.id) === String(cityId))
                if (city) {
                  cityNames.push(city.fullName)
                  break
                }
              }
            }
            
            return h('span', cityNames.join('、') || '--')
          }
        },
        {
          title: '状态',
          key: 'enable',
          render: (h, params) => {
            return h('span', params.data.row.enable === 1 ? '启用' : '禁用')
          }
        },
        {
          title: '更新人',
          key: 'updateUser'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) return h('span', '--')
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.handleEdit(params)
              }
            }
          ]
        }
      ]
    }
  },
  created() {
    // 获取应用名称列表
    this.fetchAppNames()
    // 获取应用市场列表
    this.fetchChannelList()
    // 获取城市列表
    this.fetchCityList()
  },
  methods: {
    // 获取应用名称列表
    async fetchAppNames() {
      try {
        const res = await count_channel_application_list({ status: 0 })
        if (res.code === 200) {
          this.appNameOptions = res.data || []
        }
      } catch (error) {
        console.error('获取应用名称列表失败:', error)
      }
    },
    // 获取应用市场列表
    async fetchChannelList(siteId) {
      try {
        const res = await GET_CHANNEL_LIST({ status: 0 })
        if (res.code === 200) {
          this.channelOptions = res.data || []
        }
      } catch (error) {
        console.error('获取应用市场列表失败:', error)
      }
    },
    // 获取城市列表
    async fetchCityList() {
      try {
        const res = await getCityList()
        const data = await res.json()
        if (data.code === 200) {
          const list = data.data || []

          // 全国数据
          const allProvince = {
              "provinceCode": 0,
              "provinceName": "全国",
              "provinceShortName": "全国",
              "cityDtoList": [
                  {
                      "id": "0",
                      "fullName": "全国"
                  }
              ]
          }

          this.provinceOptions = [allProvince, ...list]
        }
      } catch (error) {
        console.error('获取城市列表失败:', error)
      }
    },
    // 检查城市选择
    checkCity() {
      if (this.selectedCities.length === 0) {
        this.$message.warning('请至少选择一个屏蔽地区')
        return false
      }
      return true
    },
    // 新增
    handleAdd() {
      this.drawerVisible = true
      this.formData = {
        siteId: '',
        relationCode: '',
        channelType: '',
        city: '',
        enable: 1
      }
      this.selectedCities = []
      this.channelOptions = []
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
      })
    },
    // 编辑
    async handleEdit(params) {
      try {
        this.drawerVisible = true
        
        // 先获取应用市场列表
        await this.fetchChannelList()
        
        // 设置表单数据，确保 relationCode 为数字类型
        this.formData = {
          id: params.id,
          siteId: params.siteId,
          relationCode: params.relationCode,
          channelType: params.channelType,
          city: params.city,
          enable: params.enable
        }
        
        // 处理城市数据
        if (params.city) {
          const cityIds = params.city.split(',')
          this.selectedCities = cityIds
            .map(id => {
              for (const province of this.provinceOptions) {
                const city = province.cityDtoList.find(city => String(city.id) === String(id))
                if (city) return city
              }
              return null
            })
            .filter(Boolean)
        } else {
          this.selectedCities = []
        }

        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.clearValidate()
          }
        })
      } catch (error) {
        console.error('编辑数据加载失败:', error)
        this.$message.error('编辑数据加载失败')
      }
    },
    // 提交表单
    handleSubmit(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          try {
            if (!this.checkCity()) return

            // 准备提交数据
            const submitData = {
              ...this.formData,
              city: this.selectedCities.map(city => city.id).join(',')
            }

            // 根据是否有 id 判断是新增还是编辑
            const res = this.formData.id 
              ? await UPDATE_AB(submitData)
              : await ADD_AB(submitData)

            if (res.code === 200) {
              this.drawerVisible = false
              this.$message.success(this.formData.id ? '编辑成功' : '新增成功')
              this.$store.dispatch('tableRefresh', this)
            } else {
              this.$message.error(res.msg || '操作失败')
            }
          } catch (error) {
            this.$message.error(error.message || '操作失败')
          }
        }
      })
    },
    // 处理应用名称变化
    handleAppNameChange(siteId) {
      // 清空应用市场选择
      this.formData.relationCode = ''
      // 获取新的应用市场列表
      this.fetchChannelList(siteId)
    },
    // 处理省份选择变化
    handleProvinceChange(provinceCode) {
      const province = this.provinceOptions.find(p => p.provinceCode === provinceCode)
      this.currentProvinceData = province || {}
    },
    // 显示城市选择器
    showCitySelector() {
      if (this.provinceCode === undefined || this.provinceCode === '' || this.provinceCode === null) {
        this.$message.warning('请先选择省份')
        return
      }
      this.citySelectorVisible = true
    },
    handleCitySelectionChange(selection) {
      this.tempSelectedCities = selection
    },
    confirmSelectedCities() {
      const existingIds = this.selectedCities.map(item => item.id)
      const newCities = this.tempSelectedCities.filter(item => !existingIds.includes(item.id))
      this.selectedCities = [...this.selectedCities, ...newCities]
      this.citySelectorVisible = false
      this.updateFormCities()
    },
    selectAllCities() {
      this.$refs.citiesTable.toggleAllSelection()
    },
    deleteSelectedCities() {
      const selectedRows = this.$refs.citiesTable.selection
      if (selectedRows.length === 0) {
        this.$message.warning('请先选择要删除的城市')
        return
      }
      
      const selectedIds = selectedRows.map(row => row.id)
      this.selectedCities = this.selectedCities.filter(item => !selectedIds.includes(item.id))
      this.updateFormCities()
    },
    deleteCity(city) {
      this.selectedCities = this.selectedCities.filter(item => item.id !== city.id)
      this.updateFormCities()
    },
    updateFormCities() {
      // 更新表单中的城市数据，使用逗号分隔的城市ID
      this.formData.city = this.selectedCities.map(city => city.id).join(',')
    },
    // 检查城市是否可选
    checkSelectable(row) {
      return !this.isCitySelected(row)
    },

    // 检查城市是否已被选择
    isCitySelected(city) {
      return this.selectedCities.some(item => item.id === city.id)
    },
  }
}
</script>

<style lang="scss" scoped>
.ab-face {
  padding: 20px;
  
  ::v-deep .activeButton {
    .el-button {
      margin-right: 5px;
      padding: 7px 6px;
    }
  }
}

.city-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.added-cities-container {
  margin-top: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  
  .added-cities-header {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #EBEEF5;
    
    .link-text {
      color: #409EFF;
      margin-left: 15px;
      cursor: pointer;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

::v-deep .el-radio-group {
  display: flex;
  gap: 20px;
}

.disabled-city {
  color: #C0C4CC;
}
</style>
