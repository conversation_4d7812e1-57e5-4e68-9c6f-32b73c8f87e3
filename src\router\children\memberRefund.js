/*
 * @Author: 陈小豆
 * @Date: 2022-04-20 17:33:24
 * @LastEditors: 陈小豆
 * @LastEditTime: 2023-12-08 17:08:29
 */
/*
 * 会员订单退款
 * */
const memberRefund = [
  {
    path: '/memberRefund/refundApplyList',
    name: 'refundApplyList',
    meta: {
      title: '退款申请列表'
    },
    component: () => import('@/views/memberRefund/page/refundApplyList')
  },
  {
    path: '/memberRefund/complaintList',
    name: 'refundComplaintList',
    meta: {
      title: '投诉订单列表'
    },
    component: () => import('@/views/memberRefund/page/complaintList')
  },
  {
    path: '/memberRefund/refundOrderList',
    name: 'refundOrderList',
    meta: {
      title: '退款订单列表'
    },
    component: () => import('@/views/memberRefund/page/refundOrderList/index')
  },
  {
    path: '/memberRefund/refundStatistics',
    name: 'refundStatistics',
    meta: {
      title: '退款统计'
    },
    component: () => import('@/views/memberRefund/page/refundStatistics')
  },
  {
    path: '/memberRefund/refundStatisticsDetail',
    name: 'refundStatisticsDetail',
    meta: {
      title: '退款统计详情',
      parentTitle: '退款统计',
      activeMenu: '/memberRefund/refundStatistics'
    },
    component: () => import('@/views/memberRefund/page/refundStatisticsDetail')
  },
  {
    path: '/memberRefund/refundDataList',
    name: 'refundDataList',
    meta: {
      title: '退款数据'
    },
    component: () => import('@/views/memberRefund/page/refundDataList')
  },
  {
    path: '/memberRefund/refundPaypal',
    name: 'refundPaypal',
    meta: {
      title: '支付宝退款导入'
    },
    component: () => import('@/views/memberRefund/page/refundPaypal')
  },
  {
    path: '/memberRefund/payPalDetail',
    name: 'payPalDetail',
    meta: {
      title: '退款明细',
      parentTitle: '支付宝退款导入',
      activeMenu: '/memberRefund/refundPaypal'
    },
    component: () => import('@/views/memberRefund/page/payPalDetail')
  },
  {
    path: '/memberRefund/refundDomainName',
    name: 'refundDomainName',
    meta: {
      title: '退款域名列表'
    },
    component: () => import('@/views/memberRefund/page/refundDomainName')
  },
  {
    path: '/memberRefund/service',
    name: 'refundDomainName',
    meta: {
      title: '在线客服QA'
    }
    // component: () => import('@/views/memberRefund/page/service')
  },
  {
    path: '/memberRefund/addServiceContent',
    name: 'addServiceContent',
    meta: {
      title: '新增客服标题',
      parentTitle: '在线客服QA',
      activeMenu: '/memberRefund/service'
    },
    component: () => import('@/views/memberRefund/page/addServiceContent')
  },
  {
    path: '/memberRefund/QALibrary',
    name: 'QALibrary',
    meta: {
      title: '问答库管理'
    },
    component: () => import('@/views/memberRefund/page/QALibrary')
  },
  {
    path: '/memberRefund/appointmentManagement',
    name: 'appointmentManagement',
    meta: {
      title: '预约记录'
    },
    component: () => import('@/views/memberRefund/page/appointmentManagement')
  },
  {
    path: '/memberRefund/callCenterManagement',
    name: 'callCenterManagement',
    meta: {
      title: '客服中心管理'
    },
    component: () => import('@/views/memberRefund/page/callCenterManagement')
  }
]

export default memberRefund
