<template>
  <div v-if="isShow" class="account">
    <div class="add-container">
      <ElButton plain type="primary" size="small" @click="handAdd"> 添加账号 </ElButton>
      <ElUpload
        ref="upload"
        accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        style="display: inline-block;margin-left:5px"
        action=""
        :before-upload="beforeUpload"
        :multiple="false"
        :limit="1"
      >
        <ElButton id="uploadEle" icon="el-icon-upload" size="small" plain type="warning">导入账号</ElButton>
      </ElUpload>
    </div>
    <Page :request="request" :list="list" table-title="账户管理">
      <div slot="searchContainer" style="display: inline-block;position: relative;">
        <ElButton plain type="warning" size="small" icon="el-icon-download" @click="handUpload">导出数据</ElButton>
      </div>
    </Page>

    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="dialogOps">
      <AcountEdit :current-params="currentParams" :proxy-list="proxyList" @cancle="handCancle" @success="handleSuccess" />
    </SDialog>
  </div>
</template>

<script>
import Page from '@/components/restructure/page'
import SDialog from '@/components/restructure/dialog'
import AcountEdit from './components/accountEdit.vue'
import { tableItemType, formItemType } from '@/config/sysConfig'
import {
  pageAccountList, pageAccountDel, post_account_import, EXPORT_PAGE_ACCOUNT, proxyConfigSelector
} from '@/api/deliveryManage'
import moment from 'moment'
export default {
  name: 'AccountManage',
  desc: '投放账户管理',
  components: {
    Page, SDialog, AcountEdit
  },
  props: {},
  data() {
    return {
      dialogFormVisible: false,
      advertisementTypeList: [
        { label: '头条', value: 1 },
        { label: '快手', value: 2 }
      ],
      isShow: true,
      currentParams: {},
      dialogOps: {
        title: '新增账户',
        width: '500px'
      },
      listQuery: {
        status: 0,
        mediaType: '',
        mediaAccount: ''
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          return Promise.all([pageAccountList(this.listQuery)]).then(res => {
            return Promise.resolve(res[0])
          })
        }
      },
      proxyList: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '媒体',
          key: 'mediaType',
          type: formItemType.select,
          search: true,
          tableView: tableItemType.tableView.text,
          list: this.advertisementTypeList,
          options: {
            placeholder: '请选择媒体类型'
          }
        },
        {
          title: '关联上级',
          key: 'parentMediaAccount'
        },
        {
          title: '账号',
          key: 'mediaAccount',
          type: formItemType.input,
          search: true
        },
        {
          title: '密码',
          key: 'mediaPassword'
        },
        {
          title: '代理名称',
          key: 'proxyName',
          type: formItemType.input,
          search: true,
          clearable: true
        },
        {
          title: '创建人',
          key: 'adminStr'
        },
        {
          title: '创建时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '禁用时间',
          key: 'forbidTime',
          render: (h, params) => {
            if (!params.data.row.forbidTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.forbidTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          search: true,
          clearable: true,
          tableView: tableItemType.tableView.text,
          list: [
            { label: '启用', value: 0 },
            { label: '禁用', value: 1 }
          ],
          val: 0,
          options: {
            placeholder: '请选择状态'
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                params.parentMediaAccount
                this.currentParams = { ...params, accountType: params.parentMediaAccount ? 1 : 0 }
                this.dialogOps.title = '修改账户'
                this.dialogFormVisible = true
              }
            },
            {
              text: '删除',
              key: 'edit',
              theme: 'danger',
              click: ($index, item, params) => {
                this.$confirm('是否删除该账号, 是否继续?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                })
                  .then(() => {
                    pageAccountDel({ id: params.id }).then(val => {
                      if (val.code == 0) {
                        this.isShow = false
                        this.$message({
                          type: 'success',
                          message: '删除成功!'
                        })
                        this.$nextTick(() => {
                          this.isShow = true
                        })
                      }
                    })
                  })
                  .catch(() => {
                    this.$message({
                      type: 'info',
                      message: '已取消删除'
                    })
                  })
              }
            }
          ]
        }
      ]
    }
  },
  created() {
    proxyConfigSelector().then(res => {
      this.proxyList = res.data
    })
  },
  methods: {
    handCancle() {
      this.dialogFormVisible = false
    },
    handAdd() {
      this.currentParams = {}
      this.dialogOps.title = '新增'
      this.dialogFormVisible = true
    },
    handUpload() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_PAGE_ACCOUNT({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    handleSuccess() {
      this.$store.dispatch('tableRefresh', this)
      this.dialogFormVisible = false
    },
    beforeUpload(file) {
      const fileFormData = new FormData()
      fileFormData.append('file', file)
      post_account_import(fileFormData).then(res => {
        if (res.code === 0) {
          this.$message.success('上传成功')
          this.$store.dispatch('tableRefresh', this)
        } else {
          this.$message.error(res.msg)
        }
      })
      return false
    }

  }
}
</script>

<style lang="scss" scoped>
::v-deep .filter-container .title {
  border-bottom: none;
}

.add-container {
  float: right;
  margin: 10px 10px 0 0;
}
</style>
