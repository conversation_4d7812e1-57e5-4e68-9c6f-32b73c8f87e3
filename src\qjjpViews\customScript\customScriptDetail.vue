<template>
  <div id="customScriptDetail">
    <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="100px" class="demo-ruleForm">
      <div style="width:90%">
        <div class="form_view_title">
          <span>基础信息</span>
        </div>
        <el-form-item
          style="width:80%"
          label="名称"
          prop="name"
          :rules="addRules.common"
        >
          <el-input
            v-model="addForm.name"
            maxlength="20"
            show-word-limit
            placeholder="请输入名称"
          />
        </el-form-item>
        <el-form-item label="对应人设" prop="styleIdList" :rules="addRules.common">
          <el-select v-model="addForm.styleIdList" multiple clearable placeholder="请选择对应人设" style="width:29%">
            <el-option
              v-for="item in styleList"
              :key="item.messageStyleId"
              :label="item.styleName+'(id:'+item.messageStyleId+')'"
              :value="item.messageStyleId"
              :disabled="item.status===1"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="d-line" />
      <div v-for="(item,index) in applicationList" :key="'flex'+index" style="display: flex;align-items: center;">
        <div class="form_view" style="width:90%">
          <div class="form_view_title">
            <span>应用对象</span>
          </div>
          <div style="display:flex; width:80%">
            <!-- <el-form-item label="性别预设" style="width:50%">
              <el-select v-model="applicationList[index].sex" multiple clearable placeholder="请选择性别预设" style="width:80%" @change="valideQuestion({index})">
                <el-option
                  v-for="litem in list1"
                  :key="litem.id"
                  :label="litem.name"
                  :value="litem.id"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item label="问题内容" class="valide-form-item" style=" width:100%">
              <el-input
                v-model="applicationList[index].question"
                maxlength="20"
                show-word-limit
                placeholder="请输入"
                @input="applicationList[index].question=applicationList[index].question.replace(/\s/g,'')"
                @change="valideQuestion({index:index})"
              />
              <span v-if="applicationList[index].showTips" class="valide-error">问题文案已存在，请勿重复配置</span>
            </el-form-item>
          </div>
          <div class="form_view_title">
            <span>自定义回答话术：</span>
          </div>
          <div v-for="(el,elIndex) in item.answerList" :key="'answer'+elIndex" style="display: flex;">
            <el-form-item style="width:80%" :label="elIndex+1+'、'">
              <el-input
                v-model="applicationList[index].answerList[elIndex]"
                maxlength="50"
                show-word-limit
                placeholder="请输入"
              />
            </el-form-item>
            <el-button v-if="elIndex==(item.answerList.length-1)&&applicationList[index].answerList.length<100" type="primary" class="c-btn" @click="handlerApplicationAnswer({type:'add',pIndex:index,cIndex:elIndex})">+</el-button>
            <el-button v-if="elIndex>0" type="primary" class="c-btn" @click="handlerApplicationAnswer({type:'remove',pIndex:index,cIndex:elIndex})">-</el-button>
          </div>
        </div>
        <button v-if="index>0" class="re-btn el-icon-delete" style="font-size: 30px;margin-left: 5px;color:#494949;" @click="handlerApplication({type:'remove',index:index})" />
      </div>
      <el-button v-if="(applicationList&&applicationList.length<500||!applicationList)" type="primary" @click="handlerApplication({type:'add'})">添加</el-button>
      <div class="d-line" />
      <el-form-item label="状态" prop="status" :rules="addRules.common">
        <el-radio-group v-model="addForm.status">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <div :style="{'text-align': 'center', width: '100%'}">
        <el-button @click="closeCheckQAShow">取消</el-button>
        <el-button type="primary" @click="handMessageStyleListAdd('addForm')">保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { replyBankEdit, bankMessageStyle, replyBankDetail } from '@/qjjpApi/operate'

export default {
  data() {
    return {
      addForm: {
        id: '',
        styleIdList: [],
        name: '',
        status: 1
      },
      applicationList: [],
      applicationConfig: {
        detailId: '',
        // sex: '',
        question: '',
        answerList: ['']
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      styleList: []// 风格列表
    }
  },
  watch: {
    '$route.query': {
      handler(val) {
        this.initeForm()
      },
      immediate: true
    }
  },
  created() {

  },
  mounted() {
    this.getAllStyleList()
  },
  methods: {
    getAllStyleList() {
      return bankMessageStyle().then(res => {
        if (res.code == 200) {
          this.styleList = res?.data ?? []
        }
        return res
      })
    },
    /**
     * 初始化表单
     */
    initeForm() {
      if (this.$route.query.id || this.$route.query.id === 0) {
        return replyBankDetail({ id: this.$route.query.id }).then(async res => {
          if (res.code == 200 && res.data) {
            this.addForm = res.data
            const detailEditList = (this.addForm?.detailEditList ?? []).map(item => {
              const { detailId, question, answerList } = item
              item = {
                detailId,
                question,
                answerList
              }
              return item
            })
            this.applicationList = detailEditList
            console.log(this.addForm, detailEditList, 'detailEditList')
          }
          return res
        }).catch(() => {
          return {}
        })
      } else {
        this.applicationList.push({ ...this.applicationConfig })
      }
    },
    /**
     * 校验问题重复  有重复问题如果性别一样即为重复
     */
    valideQuestion({ index = 0 }) {
      const copyList = JSON.parse(JSON.stringify(this.applicationList))
      const curItemVal = this.applicationList[index]
      this.applicationList.splice(index, 1, { ...curItemVal, showTips: false })
      for (let i = 0; i < copyList.length; i++) {
        if (i !== index && curItemVal.question && copyList[i].question === curItemVal.question) {
          // const oSex = copyList[i].sex
          // const sexLen = this.list1.length
          // const curSex = curItemVal.sex
          // if (oSex.length === sexLen || curItemVal.sex.length === sexLen || (oSex.length > 0 && JSON.stringify(oSex) === JSON.stringify(curSex))) {
          //   this.applicationList.splice(index, 1, { ...copyList[index], showTips: true })
          //   return
          // }
          this.applicationList.splice(index, 1, { ...copyList[index], showTips: true })
          return
        }
      }
    },
    closeCheckQAShow() {
      this.$router.push('/qjjp/customScript/customScript')
    },
    handlerApplicationAnswer({ type, pIndex, cIndex } = {}) {
      if (type === 'add') {
        this.applicationList[pIndex].answerList.push('')
      } else {
        this.applicationList[pIndex].answerList.splice(cIndex, 1)
      }
    },
    handlerApplication({ type, index }) {
      if (type === 'add') {
        this.applicationList.push({ ...this.applicationConfig, answerList: [''] })
      } else {
        this.applicationList.splice(index, 1)
      }
    },
    handMessageStyleListAdd(formName) {
      const sameQuestionIndex = this.applicationList.findIndex(item => item.showTips)
      if (sameQuestionIndex >= 0) {
        this.$message.error('存在重复问题文案')
        return
      }
      let errorTex = ''
      for (let i = 0; i < this.applicationList.length; i++) {
        const curVal = this.applicationList[i]
        if (!curVal.question) {
          errorTex = `请填写第${i + 1}应用对象的问题内容`
          this.$message.error(errorTex)
          return
        }
        const { answerList = [] } = curVal
        const noWords = answerList.findIndex(item => (item === '' || typeof item === undefined || item === null))
        if (noWords >= 0) {
          this.$message.error(`请填写第${i + 1}应用对象的自定义回答话术`)
          return
        }
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          const detailEditList = this.applicationList.map(item => {
            const { detailId, question, answerList } = item
            item = {
              detailId,
              question,
              answerList
            }
            return item
          })
          this.addForm.detailEditList = detailEditList
          replyBankEdit({ ...this.addForm }).then(res => {
            if (res.code == 200) {
              this.$store.dispatch('tagsView/delView', this.$route)
              this.$message.success('添加成功')
              this.$router.push('/qjjp/customScript/customScript')
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#customScriptDetail{
  button.re-btn{
    background: transparent;
    text-decoration: none;
    cursor: pointer;
  }
  .d-line{
    margin: 20px auto ;
    width: 100%;
    border-top: 1px solid rgba(0,0,0,0.2);
  }
    padding: 30px;
    .form_view{
      margin: 0 0px 20px;
        background-color: rgb(189, 184, 184,0.2);
        border: 1px solid rgba(0,0,0,0.2);
        width: 100%;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .form_view_title{
            margin-bottom: 20px;
            span{
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 16px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
    .c-btn{
      width:30px;
      height: 40px;
      margin-left: 10px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 0px;
    }
    .valide-form-item{
      position: relative;
    }
    .valide-error{
      position: absolute;
     left: 100%;
      top: 50%;
      transform: translateY(-50%);
      color: red;
      white-space: nowrap;
    }
    .valide-input{

    }
}
</style>
