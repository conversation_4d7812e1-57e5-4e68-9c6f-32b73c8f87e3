<template>
  <Page
    :request="request"
    :list="list"
    table-title="统计"
    :table-pagination-state="false"
  >
    <div slot="searchContainer" style="display: inline-block">
      <el-button
        plain
        type="warning"
        size="small"
        icon="el-icon-download"
        @click="handExport"
      >
        导出数据
      </el-button>
    </div>
  </Page>
</template>

<script>
import { countAdvertisingChannelInsertionScreenAdCount, countAdvertisingChannelInsertionScreenAdExport } from '@/api/advertisement'
import { count_channel_application_list } from '@/api/NewChannel'
import Page from '@/components/restructure/page/v4.vue'
import { formItemType, tableItemType } from '@/config/sysConfig'
import moment from 'moment'

export default {
  components: {
    Page
  },
  props: {},
  data() {
    return {
      siteIds: [],
      listQuery: {
        startDate: moment().subtract(5, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        siteId: '',
        scenes: 0
      },
      request: {
        getListUrl: async(data) => {
          this.listQuery = { ...this.listQuery, ...data }
          const { startDate, endDate } = this.listQuery

          if (this.listQuery.scenes == 0) {
            delete this.listQuery.scenes
          }

          const startDateTimestamp = new Date(startDate).getTime()
          const endDateTimestamp = new Date(endDate).getTime()
          if (endDateTimestamp - startDateTimestamp > 1000 * 60 * 60 * 24 * 10) {
            throw this.$message({
              type: 'error',
              message: '查询时间不能大于10天'
            })
          }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = [{ siteName: '全部', siteId: '' }, ...res.data]
              }
            })
          }
          return countAdvertisingChannelInsertionScreenAdCount(this.listQuery)
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [
            moment().subtract(5, 'days').format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          search: true,
          searchKey: 'date',
          tableHidden: true
        },
        {
          title: '场景',
          key: 'scenes',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [
            {
              label: '综合',
              value: 0
            },
            {
              label: '首页',
              value: 301
            },
            {
              label: '底导-赚现金',
              value: 302
            },
            {
              label: '底导-返利商城',
              value: 303
            },
            {
              label: '底导-尊享特权',
              value: 304
            },
            {
              label: '底导-会员中心',
              value: 305
            },
            {
              label: '我的',
              value: 306
            },
            {
              label: '提现',
              value: 307
            },
            {
              label: '一元抢',
              value: 308
            },
            {
              label: '抽抽乐',
              value: 309
            },
            {
              label: '冲话费',
              value: 310
            },
            {
              label: '视频会员',
              value: 311
            },
            {
              label: '登录返回',
              value: 312
            },
            {
              label: '承接页(未登录)',
              value: 314
            }
          ],
          val: this.listQuery.scenes,
          search: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: this.listQuery.siteId,
          search: true,
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date'
        },
        {
          title: '激活渗透（总/新）',
          key: 'showUvDauS'
        },
        {
          title: '登录渗透（总/新）',
          key: 'showUvLoginS'
        },
        {
          title: '活动访问（总/新）',
          key: 'pageS'
        },
        {
          title: 'pv（总/新）',
          key: 'showPvS'
        },
        {
          title: 'uv（总/新）',
          key: 'showUvS'
        },
        {
          title: '渗透率（总/新）',
          key: 'showUvPageS'
        },
        {
          title: '展示率',
          key: 'showRatio'
        },
        {
          title: '请求量',
          key: 'requestCount'
        },
        {
          title: '返回量',
          key: 'returnCount'
        },
        {
          title: '点击量',
          key: 'click'
        },
        {
          title: '点击率（总/新）',
          key: 'clickRatio'
        },
        {
          title: '登录人均次数（总/新）',
          key: 'showPvLoginS'
        },
        {
          title: '广告收益（总/新）',
          key: 'advertisingEarningsS'
        },
        {
          title: 'arpu值（总/新）',
          key: 'advertisingArpS'
        },
        {
          title: 'ecpm（总/新）',
          key: 'ecpmS'
        }
      ]
    }
  },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = countAdvertisingChannelInsertionScreenAdExport({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
