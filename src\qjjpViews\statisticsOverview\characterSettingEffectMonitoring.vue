<template>
  <div>
    <Page :request="request" :list="list" table-title="人设效果监控表">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </Page>
  </div>
</template>

<script>
import { osList } from '@/qjjpViews/appVersion/basicParams'
// @ts-ignore
import Page from '@/components/restructure/page/v8'
// @ts-ignore
import { tableItemType, formItemType } from '@/config/sysConfig'
// @ts-ignore
import { countStyle, countStyleExport, countStyleDetail, countStyleDetailExport } from '@/qjjpApi/statisticsOverview'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import moment from 'moment'
// @ts-ignore
export default {
  name: 'CharacterSettingEffectMonitoring',
  components: {
    Page
  },
  props: {},
  data() {
    return {
      isCustomStyleType: false, // 是否选中自定义人设
      pickerDay: 30,
      siteIdsList: [],
      listQuery: {
        startDate: this.$route.query.startDate || moment().subtract(7, 'days').format('YYYY-MM-DD'),
        endDate: this.$route.query.endDate || moment().format('YYYY-MM-DD'),
        ...this.$route.query
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          await count_channel_application_list().then(res => {
            if (res.code === 200) {
              this.siteIdsList = res.data
            }
          })
          const requestFunc = this.pageType === 'detail' ? countStyleDetail : countStyle

          const { data: listRes } = await requestFunc(this.listQuery)

          const { records = [], total = 0 } = listRes

          return {
            data: {
              total: total,
              rows: records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '日期',
          key: 'date1',
          search: true,
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          pickerDay: this.pickerDay,
          tableHidden: true,
          val: [this.listQuery.startDate, this.listQuery.endDate]
        },
        {
          title: '渠道ID',
          type: formItemType.input,
          key: 'channelCode',
          tableHidden: true,
          search: true,
          val: this.listQuery.channelCode || '',
          clearable: true
        },
        ...this.filterCustomStyleItem({
          title: '人设名称',
          key: 'styleNameSearch',
          searchKey: 'styleName',
          search: true,
          type: formItemType.input,
          tableHidden: true,
          val: this.listQuery.styleName
        }),
        ...this.filterCustomStyleItem({
          title: '人设性别',
          key: 'styleSex',
          search: true,
          type: formItemType.select,
          clearable: true,
          list: [
            { label: '男', value: 1 },
            { label: '女', value: 0 }
          ],
          listFormat: {
            label: 'label',
            value: 'value'
          },
          tableHidden: true,
          val: this.listQuery.styleSex
        }),
        ...this.filterCustomStyleItem({
          title: '人设付费类型',
          key: 'freeStyle',
          search: true,
          type: formItemType.select,
          clearable: true,
          list: [
            { label: '付费人设', value: 0 },
            { label: '免费人设', value: 1 }
          ],
          listFormat: {
            label: 'label',
            value: 'value'
          },
          tableHidden: true,
          val: this.listQuery.freeStyle
        }),
        {
          title: '人设分类',
          key: 'styleType',
          search: true,
          type: formItemType.select,
          clearable: true,
          list: [
            { label: '系统人设', value: 1 },
            { label: '自定义人设', value: 2 },
            { label: '开场白', value: 3 }
          ],
          listFormat: {
            label: 'label',
            value: 'value'
          },
          tableHidden: true,
          options: {
            on: () => {
              return {
                change: (e) => {
                  this.isCustomStyleType = String(e) === '2'
                }
              }
            }
          },
          val: this.listQuery.styleType
        },
        {
          title: '设备维度',
          key: 'deviceType',
          search: true,
          type: formItemType.select,
          clearable: true,
          list: [
            { label: '新', value: 2 },
            { label: '老', value: 3 },
            { label: '总', value: 1 }
          ],
          listFormat: {
            label: 'label',
            value: 'value'
          },
          tableHidden: true,
          val: this.listQuery.deviceType
        },
        {
          title: '用户类型',
          key: 'vipType',
          search: true,
          type: formItemType.select,
          clearable: true,
          list: [
            { label: '普通用户', value: 0 },
            { label: '会员用户', value: 1 }
          ],
          listFormat: {
            label: 'label',
            value: 'value'
          },
          tableHidden: true,
          val: this.listQuery.vipType
        },
        {
          title: '应用类型',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: osList,
          reg: ['required'],
          search: true,
          tableHidden: true
        },
        {
          title: '人设id',
          type: formItemType.input,
          key: 'styleId',
          tableHidden: true,
          search: true,
          val: this.listQuery.styleId || '',
          clearable: true
        },
        {
          title: '应用名称',
          key: 'siteIds',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          multiple: true,
          search: true,
          clearable: true,
          tableHidden: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        this.filterFirstPageItem(),
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          multiple: true,
          clearable: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        {
          title: '激活数',
          key: 'activeNum',
          isCustom: true,
          renderHeader: (h, { column }) => this.renderHeaders(h, column, '激活（启动app）设备数，按照设备去重')
        },
        {
          title: '登录数',
          key: 'loginNum',
          isCustom: true,
          renderHeader: (h, { column }) => this.renderHeaders(h, column, '登录（启动app）用户数，按照用户去重')
        },
        ...this.filterListPageItem({
          title: '基础信息',
          key: 'basicInfo',
          isCustom: true,
          children: [
            {
              title: '人设性别',
              key: 'styleSexString',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人设的展示性别')
            },
            {
              title: '人设付费类型',
              key: 'freeStyleS',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人设的付费类型')
            },
            {
              title: '人设排序值',
              key: 'styleSort',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人设的排序值')
            }
          ]
        }),
        {
          title: '展示数据',
          key: 'showData',
          isCustom: true,
          children: [
            {
              title: '唤起键盘的次数',
              key: 'keyboardPageNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '唤起键盘的次数')
            },
            {
              title: '唤起键盘的人数',
              key: 'keyboardPageUvNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '唤起键盘的人数，设备去重')
            },
            {
              title: '曝光次数',
              key: 'styleShowNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人设的曝光次数')
            },
            {
              title: '曝光人数',
              key: 'styleShowUvNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人设的曝光设备数，设备去重')
            }
          ]
        },
        {
          title: '用户行为数据',
          key: 'behaviorData',
          isCustom: true,
          children: [
            {
              title: '有效点击次数',
              key: 'validClickNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '粘贴点击人设次数，粘贴板无内容点击不计入')
            },
            {
              title: '有效点击人数',
              key: 'validClickUvNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, ' 粘贴点击人设人数，设备去重')
            },
            {
              title: '会话生成次数',
              key: 'chatCreateNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建次数')
            },
            {
              title: '会话生成人数',
              key: 'chatCreateUvNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建人数，设备去重')
            },
            {
              title: '人均会话生成次数',
              key: 'chatCreateAverage',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人均会话生成的次数，计算公式：会话生成次数/会话生成人数')
            },
            {
              title: '重复生成人数',
              key: 'chatCreateUvRepeatNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建次数≥2的人数，设备去重')
            },
            {
              title: '重复生成率',
              key: 'chatCreateUvRepeatRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建次数≥2的用户占比，计算公式：重复点击人数/会话生成人数')
            },
            {
              title: '重复生成用户人均生成人设次数',
              key: 'chatCreateRepeatAverage',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '重复生成用户人均生成人设次数，计算公式：∑会话记录创建次数≥2的用户会话创建次数/重复生成人数')
            },
            ...this.filterDetailPageItem({
              title: '达标人数',
              key: 'trialNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '体验次数=0的用户数，设备去重')
            }),
            ...this.filterDetailPageItem({
              title: '体验次数用完点击人设人数',
              key: 'useUpChatClickNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '体验次数=0的用户粘贴点击人设的人数，设备去重')
            }),
            ...this.filterDetailPageItem({
              title: '激活达标率',
              key: 'activeTrialRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '激活达标率，计算公式：达标人数/激活数')
            }),
            ...this.filterDetailPageItem({
              title: '激活达标点击率',
              key: 'useUpChatRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '激活达标点击率，计算公式：体验次数=0的用户粘贴点击人设的人数/激活数')
            }),
            ...this.filterDetailPageItem({
              title: '使用达标率',
              key: 'useTrialRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '生成达标率，计算公式：达标人数/会话生成人数')
            }),
            {
              title: '会话生成付费曝光次数',
              key: 'chatPayPopupShowNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建后的支付页曝光次数，其他付费场景归因逻辑：按照设备最近一次会话生成人设归因，若未创建会话记录则不计入；键盘付费场景&人设市场付费场景归因逻辑：按照用户点击人设归因；定制人设付费场景归因逻辑：统一归因到定制人设')
            },
            {
              title: '会话生成付费曝光人数',
              key: 'chatPayPopupShowUvNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建后的支付页曝光人数，设备去重')
            },
            {
              title: '会话生成拉起支付人数',
              key: 'chatPayClickNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建后的拉起支付人数，设备去重')
            },
            {
              title: '会话生成付费数',
              key: 'chatPayOrderNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建后的付费成功数')
            },
            {
              title: '会话生成发起支付率',
              key: 'chatPayRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建后的发起支付率，计算公式：会话生成发起支付人数/会话生成付费曝光人数')
            },
            {
              title: '会话生成支付成功率',
              key: 'chatPayOrderRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话记录创建后的支付成功率，计算公式：会话生成付费数/会话生成拉起支付人数')
            },
            ...this.filterDetailPageItem({
              title: '付费数',
              key: 'orderNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '总的付费数')
            }),
            ...this.filterDetailPageItem({
              title: '激活付费平均时长',
              key: 'activePayAvgDuration',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '激活付费平均时长，计算公式：（订单支付成功时间-设备激活时间）/付费数')
            }),
            ...this.filterDetailPageItem({
              title: '达标付费平均时长',
              key: 'trialPayAvgDuration',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '达标付费平均时长，计算公式：（订单支付成功时间-设备达标时间）/付费数')
            }),
            {
              title: '使用人设次数',
              key: 'useStyleNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '会话创建并发送的次数')
            },
            ...this.filterDetailPageItem({
              title: '使用人设数',
              key: 'useStyleUvNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '∑设备使用人设数，按照设备+人设ID去重汇总')
            }),
            {
              title: '使用人设人数',
              key: 'useStyleDeviceNum',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '使用人设的设备数，按照设备去重')
            },
            {
              title: '人均使用人设次数',
              key: 'useStyle1Average',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人均使用人设的次数，计算公式：使用人设次数/使用人设人数')
            },
            ...this.filterDetailPageItem({
              title: '人均使用人设数',
              key: 'useStyle2Average',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人均使用人设数，计算公式：使用人设数/使用人设人数')
            })
          ]
        },
        {
          title: '转化目标',
          key: 'conversionTarget',
          isCustom: true,
          children: [
            {
              title: '激活生成率',
              key: 'chatCreateActiveRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '激活设备的人设生成率，计算公式：会话生成人数/激活数')
            },
            {
              title: '曝光点击率',
              key: 'validClickStyleShowRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人设的曝光点击率，计算公式：有效点击人数/曝光人数')
            },
            {
              title: '生成使用率',
              key: 'useStyleDeviceChatCreateRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人设的生成使用率，计算公式：使用人设人数/会话生成人数')
            },
            {
              title: '激活使用率',
              key: 'activeUseRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '人设的激活使用率，计算公式：使用人设人数/激活数')
            },
            {
              title: '使用人设激活付费率',
              key: 'chatPayOrderActiveRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '使用人设的激活付费率，计算公式：会话生成付费数/激活数')
            },
            ...this.filterDetailPageItem({
              title: '激活付费率',
              key: 'activePayOrderRatio',
              renderHeader: (h, { column }) => this.renderHeaders(h, column, '总的激活付费率，计算公式：付费数/激活数')
            })
          ]
        },
        ...this.filterDetailPageItem({
          title: '操作',
          key: 'operation',
          isCustom: true,
          type: tableItemType.active,
          headerContainer: false,
          fixed: 'right',
          activeType: [
            {
              text: '人设详情',
              key: 'edit1',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: ($index, item, params) => {
                const paramsData = { ...this.listQuery }

                delete paramsData.pageSize
                delete paramsData.pageNumber
                this.$router.push({
                  path: '/qjjp/statisticsOverview/characterSettingEffectMonitoring/detail',
                  query: { ...paramsData, pageType: 'detail' }
                })
              }
            }
          ]
        })
      ]
    },
    pageType() {
      return this.$route.query.pageType || 'list'
    }
  },
  watch: {
    pageType: {
      handler() {
        this.$nextTick(() => {
          this.$store.dispatch('tableRefresh', this)
        })
      }
    }
  },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }

      const exportFunc = this.pageType === 'detail' ? countStyleDetailExport : countStyleExport

      window.location.href = exportFunc({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    filterListPageItem(item) {
      if (this.pageType === 'list') {
        return []
      }
      return [item]
    },
    filterDetailPageItem(item) {
      if (this.pageType === 'detail') {
        return []
      }
      return [item]
    },
    filterCustomStyleItem(item) {
      if (this.isCustomStyleType) {
        return []
      }
      return [item]
    },
    filterFirstPageItem() {
      return this.pageType !== 'detail' ? ({
        title: '日期',
        key: 'eventDate',
        fixed: 'left',
        isCustom: true
      }) : ({
        title: '人设名称',
        key: 'styleName',
        fixed: 'left',
        isCustom: true
      })
    },

    renderHeaders(h, column, text) {
      return (
        <div>
          <span>{column.label}</span>
          <el-tooltip content={text}>
            <i class='el-icon-question' style='color:#409eff;margin-left:5px;font-size: 16px;'></i>
          </el-tooltip>
        </div>
      )
    }
  }
}
</script>
