import { put, get, post, del } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

// 获取投诉反馈列表
export const getFeedbackList = params => {
  return get('channel/feedback', params)
}

// 处理投诉
export const feedbackExport = data => {
  return get(`channel/feedback/export`, data)
}

// 获取投诉反馈详情
export const getFeedbackItem = id => {
  return get(`channel/feedback${id}`)
}

// 新增投诉类型
export const addFeedbackType = data => {
  return post(`channel/feedbackType`, data)
}

// 修改投诉类型
export const editFeedbackType = data => {
  return put(`channel/feedbackType/${data.id}`, data)
}

// 删除投诉类型
export const delFeedbackType = data => {
  return del(`channel/feedbackType/${data.id}`, data)
}

// 投诉类型列表
export const feedbackTypeList = (data = { keyword: '' }) => {
  return get(`channel/feedbackType?keywords=${data.keyword}`, data)
}

// 处理投诉
export const feedbackSolve = data => {
  return put(`channel/feedback/${data.id}`, data)
}

// 批量处理投诉
export const feedbackBatchSolve = data => {
  return post(`channel/feedback/batch/solve`, data)
}

export const EXPORT_DATA_STATIS = data =>
  CONSTANT.publicPath + '/index/statisticExport?' + qs.stringify(data)
