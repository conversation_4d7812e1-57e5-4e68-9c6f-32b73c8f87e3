<template>
  <div>
    <Page :request="request" :list="list" table-title="信息分类列表">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small"
          @click="handleAdd">添加</el-button>
      </div>
    </Page>

    <Drawer
      :visible.sync="showDialog"
      :title="!formData.id ? '添加' : '编辑'"
      @confirm="handleSubmit"
      @cancel="showDialog = false"
    >
      <el-form ref="form" :model="formData" :rules="formRules" label-position="top" class="demo-ruleForm">
        <!-- 基础信息 -->
        <FormSection title="基础信息">
          <el-form-item label="机构" prop="advertiserId" :rules="formRules.advertiserId">
            <el-select v-model="formData.advertiserId" placeholder="请选择" @change="handleAdvertiserChange" :disabled="!!formData.id">
              <el-option
                v-for="item in advertiserList"
                :key="item.id"
                :label="item.advertiserName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </FormSection>

        <!-- 信息分类 -->
        <FormSection title="信息分类">
          <el-form-item label="信息分类" prop="infoCategoryType" :rules="formRules.infoCategoryType">
            <template slot="label">
              <span>信息分类（将需要分发的信息进行分类）</span>
            </template>
            <el-radio-group v-model="formData.infoCategoryType" size="small" :disabled="!!formData.id">
              <el-radio-button label="1">不限</el-radio-button>
              <el-radio-button label="2">自定义</el-radio-button>
            </el-radio-group>
            <el-form-item prop="infoCategory" :rules="formRules.infoCategory" style="display: inline-block; margin-left: 10px; margin-bottom: 0;">
              <el-select
                v-if="formData.infoCategoryType === '2'"
                v-model="formData.infoCategory"
                placeholder="请选择"
                size="small"
                :disabled="!!formData.id"
              >
                <el-option
                  v-for="item in infoCategoryTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form-item>
        </FormSection>

        <!-- 分发信息 -->
        <FormSection title="分发信息">
          <el-form-item prop="distributionInfo" :rules="formRules.distributionInfo" required>
            <template slot="label">
              <span>基础信息（请选择当前机构用户的基础信息）</span>
              <el-tooltip content="该类信息为用户真实信息，选择后将根据用户真实信息进行分发" placement="top">
                <i class="el-icon-question" style="color:#409eff;margin-left:5px;font-size: 16px;cursor:pointer;"></i>
              </el-tooltip>
            </template>
            <card-checkbox-groups>
              <card-checkbox-group 
                title="个人信息"
                v-model="formData.personInfo"
                :options="personalInfoOptions"
                @change="handlePersonalInfoChange"
                :disabled="!!formData.id"
              ></card-checkbox-group>
              
              <card-checkbox-group 
                title="车辆信息"
                v-model="formData.carInfo"
                :options="vehicleInfoOptions"
                @change="handleVehicleInfoChange"
                :disabled="!!formData.id"
              ></card-checkbox-group>
            </card-checkbox-groups>
          </el-form-item>
          
          <el-form-item>
            <template slot="label">
              <span>补充信息（为提升信息完整度，可选择机构需要分发的补充信息，补充信息非必填）</span>
              <el-tooltip content="该类信息优先根据用户实际信息进行分发，若不存在时，将使用默认信息进行分发" placement="top">
                <i class="el-icon-question" style="color:#409eff;margin-left:5px;font-size: 16px;cursor:pointer;"></i>
              </el-tooltip>
            </template>
            <div v-if="!formData.advertiserId" class="empty-tip">请先选择机构</div>
            <card-checkbox-groups v-else>
              <card-checkbox-group
                v-if="supplementInfoOptions.length > 0"
                title="补充信息"
                v-model="formData.baseInfo"
                :options="supplementInfoOptions"
                :disabled="!!formData.id"
              ></card-checkbox-group>
              <div v-else class="empty-tip">该机构暂无补充信息</div>
            </card-checkbox-groups>
          </el-form-item>
        </FormSection>

        <!-- 状态 -->
        <FormSection title="状态">
          <el-form-item label="状态" prop="status" :rules="formRules.status">
            <el-radio v-model="formData.status" :label="1">启用</el-radio>
            <el-radio v-model="formData.status" :label="0">禁用</el-radio>
          </el-form-item>
        </FormSection>
      </el-form>
    </Drawer>
  </div>
</template>

<script>
import Page from '@/components/restructure/page/index'
import {formItemType, tableItemType} from '@/config/sysConfig'
import {
  getInfoCategoryList,
  getAdvertiserList,
  getInfoCategories,
  addInfoCategory,
  updateInfoCategory
} from '@/api/distribution'
import moment from 'moment'
import { CardCheckboxGroup, CardCheckboxGroups } from '@/components/CardCheckboxGroup'
import Drawer from '@/components/Drawer'
import FormSection from '@/components/Drawer/FormSection'

export default {
  components: {
    Page,
    CardCheckboxGroup,
    CardCheckboxGroups,
    Drawer,
    FormSection
  },
  data() {
    // 自定义校验方法
    const validateDistributionInfo = (rule, value, callback) => {
      if (this.formData.personInfo.length === 0 && this.formData.carInfo.length === 0) {
        callback(new Error('请至少选择一项分发信息'))
      } else {
        callback()
      }
    }

    // 自定义校验信息类型
    const validateInfoCategory = (rule, value, callback) => {
      if (this.formData.infoCategoryType === '2' && !value) {
        callback(new Error('请选择信息类型'))
      } else {
        callback()
      }
    }

    return {
      showDialog: false,
      formData: {
        advertiserId: '',
        infoCategoryType: '1',
        infoCategory: '',
        personInfo: [],
        carInfo: [],
        baseInfo: [],
        status: 1,
        distributionInfo: []
      },
      // 定义选项
      personalInfoOptions: [],
      vehicleInfoOptions: [],
      supplementInfoOptions: [],
      formRules: {
        advertiserId: [
          {required: true, message: '请选择机构', trigger: 'change'}
        ],
        infoCategoryType: [
          {required: true, message: '请选择信息分类', trigger: 'change'}
        ],
        infoCategory: [
          { validator: validateInfoCategory, trigger: 'change' }
        ],
        status: [
          {required: true, message: '请选择状态', trigger: 'change'}
        ],
        distributionInfo: [
          { validator: validateDistributionInfo, trigger: 'change' }
        ]
      },

      // 机构列表
      advertiserList: [],
      // 信息分类列表
      // infoCategoryOptions: [
      //   { label: '不限', value: '1' },
      //   { label: '自定义', value: '2' }
      // ],
      // 信息类型选项
      infoCategoryTypeOptions: [
        // { label: '不限', value: '0' },
        { label: '一类', value: '1' },
        { label: '二类', value: '2' },
        { label: '三类', value: '3' }
      ],

      listQuery: {},
      request: {
        getListUrl: async data => {
          // 获取机构列表
          if (this.advertiserList.length === 0) {
            const res = await getAdvertiserList({ status: 1, pageNumber: 1, pageSize: 999 })
            if (res.code === 200) {
              this.advertiserList = res.data.records || []
            }
          }

          this.listQuery = {...this.listQuery, ...data}
          const list = await getInfoCategoryList(this.listQuery)
          const {records = [], total = 0} = list.data

          return {
            data: {
              total: total,
              rows: records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '机构',
          searchKey: 'advertiserId',
          type: formItemType.select,
          list: this.advertiserList,
          listFormat: {
            label: 'advertiserName',
            value: 'id'
          },
          search: true,
          clearable: true,
          tableHidden: true,
          titleHidden: true
        },
        {
          title: '信息分类',
          searchKey: 'infoCategory',
          type: formItemType.select,
          list: this.infoCategoryTypeOptions,
          search: true,
          clearable: true,
          tableHidden: true,
          titleHidden: true
        },
        {
          title: '状态',
          searchKey: 'status',
          type: formItemType.select,
          list: [
            {
              label: '启用',
              value: 1
            },
            {
              label: '禁用',
              value: 0
            }
          ],
          search: true,
          clearable: true,
          tableHidden: true,
          titleHidden: true
        },
        {
          title: '序号',
          key: 'id'
        },
        {
          title: '机构',
          key: 'advertiserName'
        },
        {
          title: '信息分类',
          key: 'infoCategory',
          render: (h, params) => {
            const type = params.data.row.infoCategory
            const option = this.infoCategoryTypeOptions.find(item => item.value === String(type))
            return h('span', option ? option.label : '--')
          }
        },
        {
          title: '分发信息',
          key: 'info',
          render: (h, params) => {
            const row = params.data.row
            const content = []

            // 直接使用后端返回的名称字符串
            if (row.personInfoStr) {
              content.push(h('div', { style: { marginBottom: '4px' } }, [
                h('span', { style: { color: '#666' } }, '个人信息：'),
                h('span', row.personInfoStr)
              ]))
            }

            if (row.carInfoStr) {
              content.push(h('div', { style: { marginBottom: '4px' } }, [
                h('span', { style: { color: '#666' } }, '车辆信息：'),
                h('span', row.carInfoStr)
              ]))
            }

            if (row.baseInfoStr) {
              content.push(h('div', [
                h('span', { style: { color: '#666' } }, '补充信息：'),
                h('span', row.baseInfoStr)
              ]))
            }

            return content.length > 0 ? h('div', content) : h('span', '--')
          }
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return h('span', params.data.row.status === 1 ? '启用' : '禁用')
          }
        },
        {
          title: '更新人',
          key: 'updateName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) return h('span', '--')
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '操作',
          key: 'operation',
          type: tableItemType.active,
          headerContainer: false,
          width: 120,
          activeType: [
            {
              text: '编辑',
              key: 'details',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.handleEdit(params)
              }
            }
          ]
        }
      ]
    }
  },
  watch: {
    'formData.infoCategoryType'(val) {
      // 当信息分类改变时，如果不是自定义，清空信息类型
      if (val !== '2') {
        this.formData.infoCategory = ''
      }
      // 触发信息类型的校验
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.validateField('infoCategory')
      })
    }
  },
  created() {
    // 获取基础的信息分类数据（个人信息和车辆信息）
    this.fetchInfoCategories()
  },
  methods: {
    // 获取信息分类数据
    async fetchInfoCategories(advertiserCode) {
      try {
        // 如果没有传入 advertiserCode，则获取基础的个人信息和车辆信息
        const res = await getInfoCategories(advertiserCode ? { code: advertiserCode } : {})
        if (res.code === 200) {
          const data = res.data || {}

          // 处理个人信息数据
          if (Array.isArray(data.personList)) {
            this.personalInfoOptions = data.personList.map(item => ({
              value: item.code,
              label: item.name
            }))
          }
          
          // 处理车辆信息数据
          if (Array.isArray(data.carList)) {
            this.vehicleInfoOptions = data.carList.map(item => ({
              value: item.code,
              label: item.name
            }))
          }

          // 只有传入 advertiserCode 时才更新补充信息数据
          if (advertiserCode && Array.isArray(data.baseList)) {
            this.supplementInfoOptions = data.baseList.map(item => ({
              value: item.code,
              label: item.name
            }))
          }
        }
      } catch (error) {
        this.$message.error(error.message || '获取信息分类失败')
      }
    },

    // 监听机构选择变化
    async handleAdvertiserChange(value) {
      // 清空补充信息的选择
      this.formData.baseInfo = []
      this.supplementInfoOptions = []
      
      // 找到选中的机构
      const selectedAdvertiser = this.advertiserList.find(item => item.id === value)
      if (selectedAdvertiser) {
        // 使用机构的 advertiserCode 获取补充信息数据
        await this.fetchInfoCategories(selectedAdvertiser.advertiserCode)
      }
    },

    handleAdd() {
      this.showDialog = true
      this.formData = {
        advertiserId: '',
        infoCategoryType: '1',
        infoCategory: '',
        personInfo: [],
        carInfo: [],
        baseInfo: [],
        status: 1,
        distributionInfo: []
      }
      // 清空补充信息选项
      this.supplementInfoOptions = []
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
      })
    },
    // 个人信息处理
    handlePersonalInfoChange(val) {
      this.updateDistributionInfo()
    },
    // 车辆信息处理
    handleVehicleInfoChange(val) {
      this.updateDistributionInfo()
    },
    // 更新distributionInfo字段
    updateDistributionInfo() {
      this.formData.distributionInfo = [...this.formData.personInfo, ...this.formData.carInfo]
      this.$refs.form.validateField('distributionInfo')
    },
    async handleEdit(params) {
      try {
        // 找到对应的机构信息
        const selectedAdvertiser = this.advertiserList.find(item => item.id === params.advertiserId)
        if (selectedAdvertiser) {
          // 先获取该机构的补充信息数据
          await this.fetchInfoCategories(selectedAdvertiser.advertiserCode)
        } else {
          // 如果没有找到机构，清空补充信息选项
          this.supplementInfoOptions = []
        }

        // 处理数据转换 - 保持原始字符串格式以便匹配选项
        const personInfo = params.personInfo ? String(params.personInfo).split(',') : []
        const carInfo = params.carInfo ? String(params.carInfo).split(',') : []
        const baseInfo = params.baseInfo ? String(params.baseInfo).split(',') : []

        // 设置表单数据
        this.formData = {
          ...params,
          personInfo,
          carInfo,
          baseInfo,
          infoCategoryType: String(params.infoCategoryType),
          infoCategory: params.infoCategory ? String(params.infoCategory) : 0,
          distributionInfo: [...personInfo, ...carInfo]
        }
        
        this.showDialog = true

        // 等待 DOM 更新后重置校验状态
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.clearValidate()
          }
        })
      } catch (error) {
        console.error('编辑信息分类出错:', error)
        this.$message.error('编辑信息分类失败')
      }
    },
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            // 构建提交数据，解构出 distributionInfo，这样就不会包含在提交数据中
            const { distributionInfo, ...rest } = this.formData
            const submitData = {
              ...rest,
              // 确保数据类型正确
              infoCategoryType: Number(this.formData.infoCategoryType),
              infoCategory: this.formData.infoCategoryType === '2' ? Number(this.formData.infoCategory) : 0,
              // 提交时保持原始字符串格式
              personInfo: this.formData.personInfo,
              carInfo: this.formData.carInfo,
              baseInfo: this.formData.baseInfo
            }

            // 根据是否有 id 判断是新增还是编辑
            const res = this.formData.id
              ? await updateInfoCategory(submitData)
              : await addInfoCategory(submitData)

            if (res.code === 200) {
              this.showDialog = false
              this.$message.success(this.formData.id ? '编辑成功' : '新增成功')
              this.$store.dispatch('tableRefresh', this)
            }
          } catch (error) {
            this.$message.error(error.message || '操作失败')
          }
        }
      })
    },
    renderHeader(h, params, tips) {
      const {column} = params
      return (
        <div>
          <span>{column.label}</span>
          <el-tooltip content={tips}>
            <i class="el-icon-question" style="color:#409eff;margin-left:5px;font-size: 16px;"></i>
          </el-tooltip>
        </div>
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-tip {
  color: #909399;
  font-size: 14px;
  padding: 20px 0;
  text-align: center;
  background: #f5f7fa;
  border-radius: 4px;
}

.distribution-institution-info {
  padding: 20px;
  
  ::v-deep .activeButton {
    .el-button {
      margin-right: 5px;
      padding: 7px 6px;
    }
  }
}
</style>
