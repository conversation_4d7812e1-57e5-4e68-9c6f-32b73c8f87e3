<template>
  <page :request="request" :list="list">
    <div slot="searchContainer" style="display: inline-block">
      <el-button
        plain
        type="warning"
        size="small"
        icon="el-icon-download"
        @click="handExport"
      >导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import { formItemType } from '@/config/sysConfig'
import statisticsConfig from '@/config/statistics.conf'

export default {
  components: {
    page
  },
  props: {
    detailId: {
      type: [String],
      default: ''
    }
  },
  data() {
    return {
      request: {
        getListUrl: data => {
          return this.getData(data)
        }
      },
      listQuery: statisticsConfig.defaultDate(true)
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startTime', 'endTime'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: statisticsConfig.defaultDate(),
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date'
        },
        {
          title: '渠道ID',
          key: 'channelCode',
          type: formItemType.input
        },
        {
          title: 'API传输用户总/新',
          key: 'apiUser'
        },
        {
          title: '激活用户数总/新',
          key: 'activeUser'
        },
        {
          title: '登录成功用户总/新',
          key: 'loginSuccessUser'
        },
        {
          title: '参与抽奖用户总/新',
          key: 'takePartIn'
        },
        {
          title: '参与组队(总/新)',
          key: 'teamUser'
        },
        {
          title: '开屏次数(总/新)',
          key: 'coopenShow'
        },
        {
          title: '人均开屏展示',
          key: 'oneUserCoopenShow'
        },
        {
          title: '激励展示(总/新)',
          key: 'stimulateVideoShow'
        },
        {
          title: '激励次数(总/新)',
          key: 'stimulateVideoWatch'
        },
        {
          title: '人均激励观看(总/新)',
          key: 'oneUserStimulateVideoWatch'
        },
        {
          title: '综合激活转化',
          key: 'activeToApi'
        },
        {
          title: '新用户激活转化',
          key: 'activeNewToApiNew'
        },

        {
          title: '激活登录率',
          key: 'activeToLogin'
        },
        {
          title: '激活登录率新',
          key: 'activeNewToLoginNew'
        },
        {
          title: '总参与率',
          key: 'takePartInToLogin'
        },
        {
          title: '新参与率',
          key: 'takePartInNewToLoginNew'
        },
        {
          title: '人均参与总',
          key: 'oneUserTakePartInAll'
        },
        {
          title: '人均参与新',
          key: 'oneUserTakePartInNew'
        },
        {
          title: '总参与组队率',
          key: 'takePartInAllToTeamUser'
        },
        {
          title: '新参与组队率',
          key: 'takePartInNewToTeamUser'
        },
        {
          title: '一天后总登录留存率',
          key: 'oneDayLoginAllRate'
        },
        {
          title: '一天后总参与留存率',
          key: 'oneDayTakePartInAllRate'
        },
        {
          title: '请求量',
          key: 'request'
        },
        {
          title: '返回量',
          key: 'response'
        },
        {
          title: '曝光量',
          key: 'showPv'
        },
        {
          title: '点击量',
          key: 'click'
        },
        {
          title: '填充率',
          key: 'responseToRequest'
        },
        {
          title: '展示率',
          key: 'showPvToResponse'
        },
        {
          title: '点击率',
          key: 'clickToShowPv'
        },
        {
          title: 'cpm',
          key: 'cpm'
        },
        {
          title: '收益',
          key: 'earnings'
        }
      ]
    }
  },
  watch: {},
  mounted() {},
  methods: {
    getData(data) {
      this.listQuery = {
        ...this.listQuery,
        ...data,
        channelCode: this.detailId
      }
      return Promise.all([GET_COUNT_CHANNEL_DETAIL(this.listQuery)]).then(
        res => {
          return Promise.resolve(res[0])
        }
      )
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_COUNT_CHANNEL_DETAIL({
        ...data,
        token: this.$store.getters.getAuthorization
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
