import { get, post, put } from '@/libs/axios.package'
// 充值套餐-添加
export const prepaidPhonePlans_add = obj => {
  return post('/prepaidPhonePlans', obj)
}
// 充值套餐-修改
export const prepaidPhonePlans_upadte = obj => {
  return put('/prepaidPhonePlans', obj)
}
// 充值套餐-分页查询
export const prepaidPhonePlans_page = obj => {
  return get('/prepaidPhonePlans/page', obj)
}
// 充值套餐-回显关联券
export const prepaidPhonePlans_getAssociatedCoupon = obj => {
  return get('/prepaidPhonePlans/getAssociatedCoupon', obj)
}
// 充值套餐-关联关联券
export const prepaidPhonePlans_updateAssociatedCoupon = obj => {
  return post('/prepaidPhonePlans/updateAssociatedCoupon', obj)
}
// 满减券管理-分页查询
export const prepaidPhoneCoupon_page = obj => {
  return get('/prepaidPhoneCoupon/page', obj)
}
// 满减券管理-获取所有优惠券
export const prepaidPhoneCoupon_list = obj => {
  return get('/prepaidPhoneCoupon/list', obj)
}
// 满减券管理-修改
export const prepaidPhoneCoupon_update = obj => {
  return put('/prepaidPhoneCoupon', obj)
}
// 满减券管理-新增
export const prepaidPhoneCoupon_add = obj => {
  return post('/prepaidPhoneCoupon', obj)
}
// 满减券管理-删除
export const prepaidPhoneCoupon_delete = obj => {
  return get('/prepaidPhoneCoupon/delete', obj)
}
// 充多多后台订单列表
export const order_chongduoduo_order_list = obj => {
  return get('/order/chongduoduo/order/list', obj)
}
// 充多多订单退款
export const order_chongduoduo_order_refund = obj => {
  return post('/order/chongduoduo/order/refund', obj)
}
// 充多多活动统计
export const count_duoduopay_list = obj => {
  return get('/count/duoduopay/list', obj)
}
// 充多多收益统计
export const count_duoduopay_earnings_list = obj => {
  return get('/count/duoduopay/earnings/list', obj)
}
// 话费激活-链路统计
export const count_phone_activite = obj => {
  return get('/count/phone/activite', obj)
}
// 活动数据-升级数据
export const countUpgradeMembership = obj => {
  return get('count/countUpgradeMembership/page', obj)
}
// 充值订单批量退款
export const orderRefunds = obj => {
  return post('/order/chongduoduo/order/batch/refund', obj)
}
// 话费减价统计
export const reduceStatistics = obj => {
  return get('count/phone/reduce', obj)
}
// 话费减价统计
export const reduceStatisticsV274 = obj => {
  return get('count/phone/reduceV274', obj)
}
// 话费减价统计
export const activationStatistics = obj => {
  return get('count/phone/activate/v2', obj)
}
// 话费-登录统计
export const phoneBillLoing = obj => {
  return get(`/count/phone/popup`, obj)
}
// 话费拦截统计数据
export const GET_INTERCEPT_DATA_COST_LIST = obj => {
  return get(`/count/phoneChargeIntercept/pageList`, obj)
}
