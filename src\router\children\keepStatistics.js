/*
 * 留存子路由
 * */

const keepStatistics = [
  {
    path: '/keepStatistics/keepStatisticsList',
    name: 'keepStatisticsList',
    meta: {
      title: '任务拆红包留存统计'
    },
    component: () => import('@/views/keepStatistics/page/keepStatisticsList')
  },
  {
    path: '/keepStatistics/redEnvelopeStatistics',
    name: 'redEnvelopeStatistics',
    meta: {
      title: '整点红包留存'
    },
    component: () => import('@/views/keepStatistics/page/redEnvelopeStatistics')
  },
  {
    path: '/keepStatistics/loginRetained',
    name: 'loginRetained',
    meta: {
      title: '登录留存'
    },
    component: () => import('@/views/keepStatistics/page/loginRetained')
  },
  {
    path: '/keepStatistics/countSignList',
    name: 'countSignList',
    meta: {
      title: '签到留存'
    },
    component: () => import('@/views/keepStatistics/page/countSignList')
  }
]

export default keepStatistics
