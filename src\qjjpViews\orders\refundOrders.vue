<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 10:44:17
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-12-02 16:16:03
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="退款订单" ref="refundOrders">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
    <section>
      <el-dialog title="
          继续退款确认
        " width="520px" :visible.sync="dialogFormVisible" :show-close="false">
        <div class="dialog_tips">请确认是否发起继续退款申请，发起退款后该笔订单将按照原申请退款金额进行退款</div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" :loading="btn_disabled" :disabled="time != 0"
            @click="userUpdated('1', 'ruleForm')">{{
              time != 0 ? '确 定' + '（' + time + 's）' : '确 定' }}</el-button>
        </div>
      </el-dialog>
    </section>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { user_list } from '@/qjjpApi/user'
import { count_channel_application_list, mediaAll } from '@/qjjpApi/NewChannel'
import { membershipRefundOrder, refundOrderexport, doRefund, remainRefundOrder, recoveryMembership } from '@/qjjpApi/orders'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
import { osList } from '@/qjjpViews/appVersion/basicParams'
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      btn_disabled: false,
      time: 5,
      currentParams: {},
      ruleForm: {},
      rules: [],
      dialogFormVisible: false,
      optionsList: [
        {
          label: '商户号',
          value: 1
        },
        {
          label: '媒体',
          value: 2
        },
        {
          label: '工商',
          value: 3
        }
      ],
      list1: [
        {
          id: 1,
          name: '奇迹键盘'
        }
      ],
      list2: [
        // {
        //   id: 1,
        //   name: '未退款'
        // },
        {
          id: 2,
          name: '全额退款'
        },
        {
          id: 3,
          name: '退款失败'
        },
        {
          id: 4,
          name: '部分退款'
        },
      ],
      list3: [
        {
          id: 1,
          name: '微信支付'
        },
        {
          id: 2,
          name: '支付宝支付'
        },
        {
          id: 3,
          name: '支付宝自动续费'
        }
      ],
      list4: [
        {
          id: 1,
          name: '会员订单'
        },
        {
          id: 2,
          name: '续费订单'
        },
        {
          id: 3,
          name: '打卡订单'
        },
        {
          id: 4,
          name: '体验会员'
        },
        {
          id: 5,
          name: '单卖人设'
        },
        {
          id: 8,
          name: '权益卡类'
        }
      ],
      list5: [
        {
          id: 1,
          name: '第1期'
        },
        {
          id: 2,
          name: '第2期'
        },
        {
          id: 3,
          name: '第3期'
        },
        {
          id: 4,
          name: '第4期'
        },
        {
          id: 5,
          name: '第5期'
        },
        {
          id: 6,
          name: '第6期'
        },
        {
          id: 7,
          name: '第7期'
        },
        {
          id: 8,
          name: '第8期'
        },
        {
          id: 9,
          name: '第9期'
        },
        {
          id: 10,
          name: '第10期'
        },
        {
          id: 11,
          name: '第11期'
        },
        {
          id: 12,
          name: '第12期'
        }
      ],
      list6: [
        {
          id: 1,
          name: '投诉退款'
        },
        {
          id: 2,
          name: '客服退款'
        },
        {
          id: 3,
          name: '打卡自动退款'
        },
        {
          id: 4,
          name: '用户自主退款'
        }
      ],
      list7: [
        {
          id: 1,
          name: '商户号'
        },
        {
          id: 2,
          name: '媒体'
        },
        {
          id: 3,
          name: '工商'
        },
        {
          id: 4,
          name: 'Apple store'
        }
      ],
      siteIds: [],
      mediaList: [],
      listQuery: {
        startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          await mediaAll().then(res => {
            if (res.code === 200) {
              this.mediaList = res.data
            }
          })
          if (!this.siteIds.length) {
            await count_channel_application_list().then(res => {
              if (res.code === 200) {
                if (res.data && res.data.length) {
                  this.siteIds = res.data
                }
              }
            })
          }

          const list = await membershipRefundOrder(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
          // return Promise.all([user_list(this.listQuery)]).then(res => {
          //   return Promise.resolve(res[0])
          // })
        }
      }
    }
  },
  computed: {
    list8() {
      return this.listQuery.payType == 3 ? [
        {
          id: 1,
          name: '第1期'
        },
        {
          id: 2,
          name: '第2期'
        },
        {
          id: 3,
          name: '第3期'
        },
        {
          id: 4,
          name: '第4期'
        },
        {
          id: 5,
          name: '第5期'
        },
        {
          id: 6,
          name: '第6期'
        },
        {
          id: 7,
          name: '第7期'
        },
        {
          id: 8,
          name: '第8期'
        },
        {
          id: 9,
          name: '第9期'
        },
        {
          id: 10,
          name: '第10期'
        },
        {
          id: 11,
          name: '第11期'
        },
        {
          id: 12,
          name: '第12期'
        }
      ] : []
    },
    list() {
      return [
        {
          title: '设备id',
          key: 'deviceCode',
          type: formItemType.input,
          tableHidden: true,
          search: true
        },
        {
          title: '设备来源',
          key: 'os',
          type: formItemType.select,
          list: osList,
          tableHidden: true,
          search: true
        },
        {
          key: 'dateSearch',
          title: '日期',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          search: true,
          val: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
          tableHidden: true,
          pickerDay: 30
        },
        {
          title: '订单编号',
          key: 'membershipOrderNo',
          type: formItemType.input,
          search: true,
          clearable: true,
          searchKey: 'membershipOrderNo',
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          clearable: true
        },
        {
          title: '退款订单状态',
          key: 'refundStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '退款订单号',
          key: 'refundNo'
        },
        {
          title: '用户账号',
          key: 'mobileNo'
        },
        {
          title: '设备id',
          key: 'deviceCode'
        },
        {
          title: '设备来源',
          key: 'os',
          list: osList,
          type: formItemType.select,
          tableView: tableItemType.tableView.text
        },
        {
          title: '用户Id',
          key: 'userCode',
          search: true,
          type: formItemType.input
        },
        {
          title: '投放媒体',
          key: 'apiCode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.mediaList,
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '渠道id',
          key: 'channelCode'
        },
        {
          title: '订单编号',
          key: 'membershipOrderNo'
        },
        {
          title: '订单金额',
          key: 'orderPrice'
        },
        {
          title: '支付方式',
          key: 'payType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          options: {
            on: () => {
              return {
                change: e => {
                  this.$set(this.listQuery, 'payType', e)
                  if (this.listQuery.payType != 3) {
                    this.$set(this.listQuery, 'numberPeriod', '')
                  }
                  console.info(this.listQuery.numberPeriod)
                }
              }
            }
          },
          search: true,
          reg: ['required'],
          clearable: true
        },
        {
          title: '订单类型',
          key: 'orderType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: false
        },
        {
          title: '扣款期数',
          key: 'numberPeriod',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list5,
          listFormat: {
            label: 'name',
            value: 'id'
          },

          reg: ['required'],
          clearable: false
        },
        {
          title: '收款商户号',
          key: 'merchant'
        },
        {
          title: '退款订单状态',
          key: 'refundStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },

          reg: ['required'],
          clearable: false
        },
        {
          title: '退款来源',
          key: 'refundSourceFrom',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list7,
          listFormat: {
            label: 'name',
            value: 'id'
          },

          reg: ['required'],
          clearable: false
        },
        {
          title: '退款原因',
          key: 'refundReason'
        },
        {
          title: '发起退款人员',
          key: 'applyAdminName'
        },
        {
          title: '发起退款时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '退款成功来源',
          key: 'refundSuccessFrom',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list6,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true,
          search: true
        },
        {
          title: '退款成功时间',
          key: 'refundSuccessTime',
          render: (h, params) => {
            if (!params.data.row.refundSuccessTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.refundSuccessTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '退款金额',
          key: 'refundPrice'
        },
        {
          title: '退款来源',
          key: 'refundSourceFrom',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list7,
          search: true,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true,
          tableHidden: true
        },

        {
          title: '订单类型',
          key: 'orderType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          search: true,
          reg: ['required'],
          clearable: true,
          tableHidden: true
        },
        {
          title: '扣款期数',
          key: 'numberPeriod',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list8,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          search: this.list8.length > 0,
          reg: ['required'],
          clearable: true,
          tableHidden: true
        },

        {
          title: '订单状态',
          key: 'refundStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: false,
          tableHidden: true
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '继续退款',
              key: 'edit1',
              hidden(params) {
                return params.refundStatus == '2' || params.refundStatus == '4'
              },
              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                params.parentMediaAccount
                this.currentParams = { ...params }
                this.dialogFormVisible = true
              }
            },
            {
              text: '保留会员',
              key: 'blhy',
              hidden(params) {
                return !params.checkInMember || params.recoveryMembership || params.osStr != 'ios' || !params.validMembership
              },
              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.$confirm(`
                  保留权益后，在订单有效期内均可以使用会员所有权益，到期后收回
                `, '退款会员订单保留会员权益', {
                  confirmButtonText: '保留会员',
                  dangerouslyUseHTMLString: true,
                  cancelButtonText: '不保留会员',
                  center: true
                })
                  .then(() => {
                    recoveryMembership({ id: params.id }).then(res => {
                      console.info(res, 'remainRefundOrder')
                      if (res.code == 200) {
                        this.$message.success('操作成功')
                        this.$refs.refundOrders.getSubSuccess()
                      }
                    })
                  }).catch(() => {

                  })

              }
            },
            {
              text: '退尾款',
              key: 'edit2',
              hidden(params) {
                return params.refundStatus != '4'
              },
              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                // <div>${params.refundRemark}元</div>
                this.$confirm(`

                <div>订单金额：${params.orderPrice}元</div>
                <div>使用权益：${Number((params.orderPrice - params.refundPrice).toFixed(4))}元${Number((params.orderPrice - params.refundPrice).toFixed(4)) < 0 ? '<span>(实际金额 0元)</span>' : ''}</div>
                <div style="color:red">已退款金额:${params.refundPrice}</div>
                <div style="color:red">剩余未退款：${Number((params.orderPrice - params.refundPrice).toFixed(4))}元${Number((params.orderPrice - params.refundPrice).toFixed(4)) < 0 ? '<span>(实际金额 0元)</span>' : ''}</div>`, '提示', {
                  confirmButtonText: '确定',
                  dangerouslyUseHTMLString: true,
                  cancelButtonText: '取消',
                  center: true
                })
                  .then(() => {

                    remainRefundOrder({ refundOrderId: params.id }).then(res => {
                      console.info(res, 'remainRefundOrder')
                      if (res.code == 200) {
                        this.$message.success('操作成功')
                        this.$refs.refundOrders.getSubSuccess()
                      }
                    })
                  }).catch(() => {
                    console.log(456)
                  })
              }
            }
          ]
        }
      ]
    }
  },
  watch: {
    dialogFormVisible: {
      handler(val) {
        if (val) {
          this.time = 5
          this.setTime = setInterval(() => {
            this.time -= 1
            if (this.time == 0) {
              clearInterval(this.setTime)
            }
          }, 1000)
        } else {
          clearInterval(this.setTime)
        }
      }
    }
  },
  created() { },
  methods: {
    userUpdated(type, formName) {
      // this.$refs[formName].validate(valid => {
      //   if (valid) {
      if (!this.btn_disabled) {
        this.btn_disabled = true
        doRefund({ id: this.currentParams.membershipOrderId }).then(res => {
          this.btn_disabled = false
          if (res.code == 200) {
            this.$message.success('退款成功！')
            this.$store.dispatch('tableRefresh', this)
            this.dialogFormVisible = false
          }
          //   })
          // }
        })
      }
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = refundOrderexport({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog_tips {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
  line-height: 30px;
  text-align: left;
  font-style: normal;
  padding-bottom: 20px;
}

.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
