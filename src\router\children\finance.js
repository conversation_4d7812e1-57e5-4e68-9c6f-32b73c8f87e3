/*
 * 财务子路由
 * */

const finance = [
  {
    path: '/finance/examineList',
    name: 'finance_examineList',
    meta: {
      title: '提现打款'
    },
    // component: () => import("@/views/finance/page/examineList")
    component: () => import('@/views/finance/page/examineList')
  },
  {
    path: '/operate/cashAudit',
    name: 'operate_cashAudit',
    meta: {
      title: '提现审核'
    },
    // component: () => import("@/views/operate/page/cashAudit")
    component: () => import('@/views/operate/page/cashAudit')
  },
  {
    path: '/finance/examineOrderList',
    name: 'finance_examineOrderList',
    meta: {
      title: '提现订单'
    },
    // component: () => import("@/views/operate/page/cashAudit")
    component: () => import('@/views/finance/page/examineOrderList')
  },
  {
    path: '/finance/offlineMemberRefundOrderList',
    name: 'offlineMemberRefundOrderList',
    meta: {
      title: '线下会员退款订单'
    },
    component: () => import('@/views/finance/page/offlineMemberRefundOrderList')
  },
  {
    path: '/finance/withdrawDepositRecord',
    name: 'finance_withdrawDepositRecord',
    meta: {
      title: '极速提现记录'
    },
    component: () => import('@/views/finance/page/withdrawDepositRecord')
  },
  {
    path: '/finance/withdrawDepositRecord',
    name: 'finance_withdrawDepositRecord',
    meta: {
      title: '极速提现记录'
    },
    component: () => import('@/views/finance/page/withdrawDepositRecord')
  },
  {
    path: '/finance/offlineMemberRefundApply',
    name: 'offlineMemberRefundApply',
    meta: {
      title: '线下退款申请'
    },
    component: () => import('@/views/finance/page/offlineMemberRefundApply')
  },
  {
    path: '/finance/kfRefundApplyList',
    name: 'finance_kfRefundApplyList',
    meta: {
      title: '客服退款申请'
    },
    component: () => import('@/views/finance/page/kfRefundApplyList')
  }
]

export default finance
