<template>
  <div class="luckyDraw-data">
    <page :request="request" :list="list" table-title="拦截数据" />
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import moment from 'moment'
import { GET_INTERCEPT_DATA_COST_LIST } from '@/api/costManagement'
import { count_channel_application_list } from '@/api/NewChannel'

export default {
  name: 'LuckyDrawData',
  components: {
    page
  },
  props: {},
  data() {
    return {
      listQuery: {
        startDate: moment().subtract(6, 'd').format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD'),
        siteId: ''
      },
      request: {
        getListUrl: async(data) => {
          const { startDate, endDate, siteId } = this.listQuery
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = [{ siteId: '', siteName: '综合' }, ...res.data]
                this.listQuery.siteId = ''
              }
            })
          }
          return Promise.all([GET_INTERCEPT_DATA_COST_LIST({ startDate: Number(startDate), endDate: Number(endDate), siteId, ...data })]).then(
            (res) => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      siteIds: []

    }
  },
  computed: {
    list() {
      return [
        {
          key: 'date1',
          title: '时间',
          type: formItemType.datePickerDaterangeGai,
          options: {
            format: 'YYYYMMDD',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['startDate', 'endDate'],
          tableHidden: true,
          search: true,
          val: [
            moment().subtract(6, 'd').format('YYYYMMDD'),
            moment().format('YYYYMMDD')
          ]
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: this.listQuery.siteId,
          clearable: false,
          reg: ['required'],
          search: true,
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date',
          with: 100,
          type: formItemType.input
        },
        {
          title: '活动访问（pv/uv）',
          with: 100,
          key: 'activityShowPvUv',
          type: formItemType.input
        },
        {
          title: '减价拦截弹窗曝光（pv/uv）',
          key: 'deductPopupShowS',
          type: formItemType.input
        },
        {
          title: '减价拦截弹窗点击（pv/uv）',
          key: 'deductPopupClickS',
          type: formItemType.input
        },
        {
          title: '拦截必中弹窗曝光（pv/uv）',
          key: 'mustLotteryWindowShow',
          type: formItemType.input
        },
        {
          title: '必中弹窗点击（pv/uv）',
          key: 'mustLotteryWindowClick',
          type: formItemType.input
        },
        {
          title: '减价拦截率',
          key: 'deductPopupRatio',
          type: formItemType.input,
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '减价拦截弹窗点击uv/减价拦截弹窗曝光uv')
          }
        },
        {
          title: '必中拦截率',
          key: 'mustLotteryInterceptRate',
          type: formItemType.input,
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '必中弹窗点击uv/拦截必中弹窗曝光uv')
          }
        }
      ]
    }
  },
  methods: {
    renderHeaders(h, column, text) {
      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            props: {
              content: text
            }
          },
          [
            h('i', {
              class: 'el-icon-question',
              style: 'color:#409eff;margin-left:5px;font-size: 16px;line-height:23px'
            })
          ]
        )
      ])
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
