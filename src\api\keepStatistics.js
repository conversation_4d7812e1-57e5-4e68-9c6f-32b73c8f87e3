import { get } from '@/libs/axios.package'

export const GET_RED_ENVELOPE = obj => {
  return get('/statistics/red/envelope/page', obj)
}

export const GET_RED_ENVELOPE_ALL = obj => {
  return get('/count/events/red/envelope/all', obj)
}

export const get_login_retained_selectListAll = obj => {
  return get('/login/retained/selectListAll', obj)
}

export const get_login_retained_selectListOldAndNew = obj => {
  return get('/login/retained/selectListOldAndNew', obj)
}
export const get_countSign_list = obj => {
  return get('/countSign/list', obj)
}
