<template>
  <div>
    <page :request="request" :list="list" table-title="满减券管理" />
  </div>
</template>
<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { ADD_MOBILE_BILL_COUPONS, EDIT_MOBILE_BILL_COUPONS, GET_MOBILE_BILL_COUPONS_PAGE } from '@/api/business'

export default {
  components: {
    page
  },
  data() {
    return {
      request: {
        getListUrl: GET_MOBILE_BILL_COUPONS_PAGE,
        insertHttp: ADD_MOBILE_BILL_COUPONS,
        updateHttp: EDIT_MOBILE_BILL_COUPONS
      },
      isHidden: false
    }
  },
  computed: {
    list() {
      const that = this
      return [
        {
          title: '名称',
          key: 'name',
          type: formItemType.input,
          reg: ['required'],
          search: true
        },
        {
          title: '满减额度',
          key: 'content',
          type: formItemType.inputGai,
          reg: ['required'],
          options: {
            startPlaceholder: '满多少',
            endPlaceholder: '减多少'
          },
          childKey: ['minAmount', 'minusAmount'],
          val: ['', '']
        },
        {
          title: '创建时间',
          key: 'createTime',
          type: formItemType.input,
          reg: ['required'],
          formHidden: true
        },
        {
          title: '生效规则',
          key: 'rule',
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          list: [
            {
              label: '立即生效',
              value: 1
            },
            {
              label: '延期生效',
              value: 2
            }
          ],
          reg: ['required'],
          options: {
            valueType: 'Number',
            on() {
              return {
                change(value) {
                  that.isHidden = value === 1
                }
              }
            }
          }
        },
        {
          title: '延期生效天数',
          key: 'delayDay',
          type: formItemType.input,
          reg: ['required'],
          formHidden: this.isHidden
        },
        {
          title: '有效天数',
          key: 'effectiveDay',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          list: [
            {
              label: '启用',
              value: 0
            },
            {
              label: '禁用',
              value: 1
            }
          ]
        },
        {
          type: tableItemType.active,
          width: 180,
          headerContainer: true,
          activeType: [
            {
              text: '编辑',
              type: tableItemType.activeType.detailsDialog
            }
          ]
        }
      ]
    }
  },
  methods: {}
}
</script>
