import {
  put,
  get,
  post,
  del
} from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/*
 * 0元购分类管理 列表
 *
 */
export const ZERO_TYPE_LIST = obj => {
  return get('/goodsActivityCategory/list', obj)
}

/*
 * 0元购分类管理 编辑 添加
 *
 */
export const POST_TYPE_SAVE = obj => {
  return post('/goodsActivityCategory/save', obj)
}

/*
 * 任务列表 添加
 *
 */
export const POST_ALL_ACTIVITY = obj => {
  return post('/activitytask/add', obj)
}

/*
 * 任务列表
 *
 */
export const GET_ACTIVITY_LIST = obj => {
  return get('/activitytask/list', obj)
}

/*
 * 首单红包统计
 *
 */
export const GET_firstordercount_LIST = obj => {
  return get('/firstordercount/list', obj)
}

/*
 * 专题页任务选项列表
 *
 */
export const GET_LINK_TYPE = obj => {
  return get('/activitytask/linkType', obj)
}

/*
* 红包查询

*
*/
export const SET_REDPACK_TYPE = obj => {
  return get('/set/redpack/byType', obj)
}

/*
* 红包查询

*
*/
export const SET_REDPACK_updateById = obj => {
  return post('/set/redpack/updateById', obj)
}
// 任务详情
//
export const GET_ACTIVITY_DETAIL = id => {
  return get(`/activitytask/byId?id=${id}`)
}

// 任务列表 - 编辑
//
export const POST_UPDATE = obj => {
  return post(`/activitytask/updateById`, obj)
}

// 任务规则管理
//
export const GET_ACTIVITY_SELECTOR = obj => {
  return get(`/activitytaskrule/list`, obj)
}

// 任务规则下拉选
//
export const GET_ACTIVITY_RULS_SELETE = obj => {
  return get(`/activitytaskrule/selector`, obj)
}

// 任务规则编辑
//
export const POST_ACTIVITY_UPDATE = obj => {
  return post(`/activitytaskrule/updateById`, obj)
}

// 0元购商品列表
export const GET_ACTIVITY_GOODS_LIST = obj => {
  return get(`/goodsActivity/list`, obj)
}

// 0元购商品删除
export const DELETE_GOODS_ACTIVITY = id => {
  return del(`/goodsActivity/${id}`)
}

// 0元购商品选择
export const GET_GOODS_LIST_ZERO = obj => {
  return get(`/goodsActivity/goodsList`, obj)
}

// 0元购商品添加
export const POST_GOODS_SAVE = obj => {
  return post(`/goodsActivity/save`, obj)
}

// 0元购商品详情
export const GET_GOODS_DETAIL = id => {
  return get(`/goodsActivity/details/${id}`)
}

// 红包设置添加
export const GET_REDPACK_TYPE = obj => {
  return get(`/set/redpack/byType`, obj)
}

// 红包设置保存
export const POST_REDPACK_UPDATA = obj => {
  return post(`/set/redpack/updateById`, obj)
}
// 活动任务统计列表 - 按活动
export const GET_TODAY_ACTIVITY = obj => {
  return get(`/activitytask/statistics/activity`, obj)
}

// 活动任务统计列表 - 按日
export const GET_DAY_ACTIVITY = obj => {
  return get(`/activitytask/statistics/day`, obj)
}

// 活动任务统计列表 - 按活动 - 详情（分页）
export const GET_STATISTIC_DETAL = obj => {
  return get(`/activitytask/statistics/activity/detail`, obj)
}

/*
 * 按任务 - 导出
 * */
export const EXPORT_RODAY = data => CONSTANT.publicPath + '/activitytask/statistics/activity/export?' + qs.stringify(data)

/*
 * 按日 - 导出
 * */
export const EXPORT_RODAY_STA = data => CONSTANT.publicPath + '/activitytask/statistics/day/export?' + qs.stringify(data)

// 零元购统计 - 列表
export const GET_AERO_ACTIVITY_LIST = obj => {
  // return get(`/countzeroactivity/list`, obj)
  return get(`/countzeroactivity/selectStatis`, obj)
}

/**
 * 秒杀活动
 */

// 秒杀活动

export const GET_Activityseckill_List = params => {
  return get(`/activityseckill/list`, params)
}

export const GET_Activityseckill = id => {
  return get(`/activityseckill/${id}`)
}

export const POST_Activityseckill = obj => {
  return post(`/activityseckill`, obj)
}
export const PUT_Activityseckill = obj => {
  return put(`/activityseckill/${obj.id}`, obj)
}
export const DELETE_Activityseckill = id => {
  return del(`/activityseckill/${id}`)
}
// 活动的场次

export const GET_Activityseckillgoods_Timelist = obj => {
  return get(`/activityseckillgoods/timelist`, obj)
}

export const PUT_Activityseckillgoods_Timelist = obj => {
  return put(`activityseckillgoods/timelist/update`, obj)
}

export const POST_Activityseckillgoods = obj => {
  return post(`/activityseckillgoods`, obj)
}

// 场次商品
export const GET_Activityseckillgoods_List = obj => {
  return get(`/activityseckillgoods/list`, obj)
}

export const GET_Activityseckillgoods_ById = id => {
  return get(`activityseckillgoods/${id}`)
}

export const DELETE_Activityseckillgoods = id => {
  return del(`/activityseckillgoods/${id}`)
}

export const PUT_Activityseckillgoods = obj => {
  return put(`/activityseckillgoods/${obj.id}`, obj)
}

// 同步场次的商品
export const GET_Activityseckillgoods_Syncgoods = obj => {
  return get(`/activityseckillgoods/syncgoods`, obj)
}

// 秒杀时间 -- 模板
export const GET_Seckill_Time_List = params => {
  return get(`/seckill/time/list`, params)
}

export const GET_Seckill_Time_ById = id => {
  return get(`/seckill/time/details/${id}`)
}

export const POST_Seckill_Time = obj => {
  return post(`/seckill/time/add`, obj)
}

export const PUT_Seckill_Time = obj => {
  return put(`/seckill/time/update/${obj.id}`, obj)
}

export const DELETE_Seckill_Time = id => {
  return del(`/seckill/time/delete/${id}`)
}

// 秒杀商品 -- 模板
export const GET_Seckill_Timegoods_List = params => {
  return get(`/seckill/timegoods/list`, params)
}

export const POST_Seckill_Timegoods = obj => {
  return post(`/seckill/timegoods/add`, obj)
}
export const PUT_Seckill_Timegoods = obj => {
  return put(`/seckill/timegoods/update/${obj.id}`, obj)
}
export const GET_Seckill_TimegoodsById = id => {
  return get(`/seckill/timegoods/${id}`)
}
export const DELETE_Seckill_Timegoods = id => {
  return del(`/seckill/timegoods/delete/${id}`)
}
export const EXPORT_Seckill_Timegoods = data => CONSTANT.publicPath + '/seckill/timegoods/export?' + qs.stringify(data)

// 秒杀商品库
export const GET_Seckill_Goodsstorages_List = params => {
  return get(`/seckill/goodsstorages/list`, params)
}

export const GET_Seckill_Goodsstorages_Store = params => {
  return get(`/seckill/goodsstorages/store`, params)
}

export const POST_Seckill_Goodsstorages = obj => {
  return post(`/seckill/goodsstorages/add`, obj)
}
export const DELETE_Seckill_Goodsstorages = id => {
  return del(`/seckill/goodsstorages/delete/${id}`)
}
// 数据统计
export const GET_Seckill_SeckillList = obj => {
  return get(`/count/seckill/seckillList`, obj)
}

export const EXPORT_Seckill_SeckillExport = data => CONSTANT.publicPath + '/count/seckill/seckillExport?' + qs.stringify(data)

// 参与记录查询
export const GET_Seckill_Graborderlist = obj => {
  return get(`/seckill/graborderlist`, obj)
}

export const EXPORT_Seckill_Graborderlist = data => CONSTANT.publicPath + '/seckill/graborderlist/export?' + qs.stringify(data)

// 参与记录查询
export const GET_BURY_COUNT_SELECT_STATIS = obj => {
  return get(`/bury/count/data/selectStatis`, obj)
}

export const EXPORT_BURY_COUNT_DATA = data => CONSTANT.publicPath + '/bury/count/data/export?' + qs.stringify(data)

export const GET_ACTIVITY_SHARE_CONFIG_FIND_LIST = params => {
  return get(`/activityShareConfig/findList`, params)
}

export const GET_ACTIVITY_SHARE_CONFIG_DETAILS_BY_ID = id => {
  return get(`activityShareConfig/details/${id}`)
}

export const EDIT_ACTIVITY_SHARE_CONFIG = obj => {
  return post(`/activityShareConfig/save`, obj)
}

export const EDIT_ACTIVITY_SHARE_CONFIG_CHECKING_LIST = obj => {
  return get(`/activityShareConfig/checkingList`, obj)
}

export const GET_ACTIVITY_HELP_LIST = obj => {
  return get(`/activityhelp/helplist`, obj)
}

export const GET_ACTIVITY_HELP_REWARD_LIST = obj => {
  return get(`/activityhelp/rewardlist`, obj)
}

export const GET_ACTIVITY_HELP_HELP_LIST_BASE = obj => {
  return get(`/activityhelp/helplistbase`, obj)
}

// 参与记录查询
export const GET_BURY_COUNT_INVITED_SELECT_STATIS = obj => {
  return get(`/bury/count/data/invited/new/selectStatis`, obj)
}

export const GET_ACTIVITY_ACCOUNT_PAGE_LIST = obj => {
  return get(`/activity/account/page`, obj)
}

export const GET_ACTIVITY_INVITE_RECORD_PAGE = params => {
  return get(`/activity/invite/record/page`, params)
}

export const GET_INVITED_NEW_SELECT_STATIS_LIST = obj => {
  return get(`/bury/count/data/invited/new/selectStatis`, obj)
}

export const GET_SHARE_PAGE_LIST = obj => {
  return get(`/base/share/page`, obj)
}

export const GET_BASE_SHARE_DETAILS_BY_ID = id => {
  return get(`/base/share/${id}`)
}

export const EDIT_ONLINE_COUNT_LIST = obj => {
  return get(`/base/share/online/count`, obj)
}

export const ADD_BASE_SHARE = obj => {
  return post(`/base/share`, obj)
}

export const EDIT_BASE_SHARE = obj => {
  return put(`/base/share`, obj)
}

export const EXPORT_INVITED_NEW = data => CONSTANT.publicPath + '/bury/count/data/invited/new/export?' + qs.stringify(data)
// 618活动接口
// 审核

export const getDividedcashApplylist = obj => {
  return get(`/dividedcash/applylist`, obj)
}

// 审核统计
export const getDividedcashSelectStatis = obj => {
  return get(`/dividedcash/selectStatis`, obj)
}
// 运营审核

export const postApplyOperateapproval = params => {
  return post(`/dividedcash/apply/operateapproval`, params)
}

export const postApplyOperaterejected = params => {
  return post(`/dividedcash/apply/operaterejected`, params)
}
export const postApplyOperateApprovalBatch = params => {
  return post(`/dividedcash/apply/operateApprovalBatch`, params)
}
export const postApplyOperateApprovalAll = params => {
  return post(`/dividedcash/apply/operateApprovalAll`, params)
}

// 财务审核

export const postApplyDrawApproval = params => {
  return post(`/dividedcash/apply/drawApproval`, params)
}

export const postApplyDrawApprovalBatch = params => {
  return post(`/dividedcash/apply/drawApproval/batch`, params)
}

export const getApplyDrawApprovalBatch = params => {
  return get(`/dividedcash/apply/drawApproval/batch`, params)
}
export const postApplyDrawApprovalAll = params => {
  return post(`/dividedcash/apply/drawApproval/all`, params)
}
export const getApplyDrawApprovalAll = params => {
  return get(`/dividedcash/apply/drawApproval/all`, params)
}
export const EXPORT_dividedcash_export = data => CONSTANT.publicPath + '/dividedcash/export?' + qs.stringify(data)

// /
// 系统参数
export const getSystemParamSelectByCode = obj => {
  return get(`/systemParam/selectByCode/${code}`, obj)
}

export const EDIT_SYSTEM_CONFIG_UPDATE = obj => {
  return post(`/systemConfig/update`, obj)
}

export const INVITE_BUY_CARD_CONFIG_LIST = obj => {
  return get(`/activity/invite/buy/card/config`, obj)
}

export const GET_INVITE_BUY_CARD_USER_LIST = obj => {
  return get(`/activity/invite/buy/card/user`, obj)
}

export const GET_ACCOUNT_INVITE_BY_ID = userId => {
  return get(`/activityaccount/invite/${userId}`, null)
}

export const GET_INVITE_BUY_CARD_INVITE_LIST = obj => {
  return get(`/activity/invite/buy/card/invite`, obj)
}

export const EXPORT_CARD_INVITE = data => CONSTANT.publicPath + '/activity/invite/buy/card/invite/export?' + qs.stringify(data)

export const GET_INVITE_BUY_CARD_REACH_LIST = obj => {
  return get(`/activity/invite/buy/card/reach`, obj)
}

export const EXPORT_CARD_REACH = data => CONSTANT.publicPath + '/activity/invite/buy/card/reach/export?' + qs.stringify(data)

export const GET_INVITE_RED_CONFIG_LIST = obj => {
  return get(`/activity/invite/red/config/10`, obj)
}

export const GET_INVITE_RED_DETAIL_BY_ID = ID => {
  return get(`/activity/invite/red/detail/${ID}`, null)
}

export const ADD_INVITE_BUY_CARD_CONFIG = obj => {
  return post(`/activity/invite/red/config/10`, obj)
}

export const EDIT_INVITE_RED_CONFIG_CONFIG = obj => {
  return put(`/activity/invite/red/config`, obj)
}

export const GET_INVITE_BUY_CARD_SELECT_STATICS = obj => {
  return get(`/bury/count/invited/new/selectStatis`, obj)
}

export const GET_BASIC_SHARE_LIST = obj => {
  return get(`/basic/share/10`, obj)
}

export const GET_BASIC_SHARE_BY_ID = id => {
  return get(`/basic/share/detail/${id}`, null)
}

export const ADD_SHARE = obj => {
  return post(`/basic/share/10`, obj)
}

export const EDIT_SHARE = obj => {
  return put(`/basic/share`, obj)
}
// 整点抢红包
export const GET_COUNT_EVENTS_MANAGERS = obj => {
  return get(`/activity/events/managers`, obj)
}

export const Put_Activity_Event_Managers = obj => {
  return put(`/activity/events/managers`, obj)
}

export const GET_COUNT_EVENTS_USER_RED_ENVELOPE = obj => {
  return get(`/count/events/user/red/envelope/page`, obj)
}

export const GET_COUNT_EVENTS_USER_RED_ENVELOPE_DETAIL = obj => {
  return get(`/count/events/user/red/envelope/detail`, obj)
}

export const EXPORT_COUNT_EVENTS_USER_RED_ENVELOPE = data => CONSTANT.publicPath + '/count/events/user/red/envelope/export?' + qs.stringify(data)

// 拆红包（多任务活动）

export const GET_Activity_Award = obj => {
  return get(`/activity/award`, obj)
}

export const GET_Activity_Award_ById = obj => {
  return get(`/activity/award/${obj.id}`, obj)
}
export const POST_Activity_Award = obj => {
  return post(`/activity/award`, obj)
}

export const GET_Activity_Award_Last = obj => {
  return get(`/activity/award/getLast`, obj)
}

export const GET_Activity_Invite_Share = obj => {
  return get(`/activity/invite/share`, obj)
}

export const POST_Activity_Invite_Share = obj => {
  return post(`/activity/invite/share`, obj)
}
export const PUT_Activity_Invite_Share = obj => {
  return put(`/activity/invite/share`, obj)
}

export const DEL_Activity_Invite_Share = obj => {
  return del(`/activity/invite/share`, obj)
}

export const GET_Activity_Count_Data_Station = obj => {
  return get(`/activity/count/data/station`, obj)
}

export const GET_Activity_Count_Data_LandingPage = obj => {
  return get(`/activity/count/data/landingPage`, obj)
}

export const GET_Activityhelp_HelpRecordList = obj => {
  return get(`/activityhelp/helpRecordList`, obj)
}

export const GET_Activityhelp_HelpRecordDetail = obj => {
  return get(`/activityhelp/helpRecordDetail`, obj)
}

export const EXPORT_Activityhelp_HelpRecordList_Export = data => CONSTANT.publicPath + '/activityhelp/helpRecordList/export?' + qs.stringify(data)

export const EXPORT_Activityhelp_HelpRecordDetail_Export = data => CONSTANT.publicPath + '/activityhelp/helpRecordDetail/export?' + qs.stringify(data)

// 拆红包奖励配置
export const get_activity_open_red = obj => {
  return get('/activity/open/red', obj)
}

export const post_activity_open_red = obj => {
  return post('/activity/open/red', obj)
}

export const put_activity_open_red = obj => {
  return put('/activity/open/red', obj)
}

// 整点红包奖励配置
export const get_activity_hourly_red = obj => {
  return get('/activity/hourly/red', obj)
}

export const post_activity_hourly_red = obj => {
  return post('/activity/hourly/red', obj)
}

export const put_activity_hourly_red = obj => {
  return put('/activity/hourly/red', obj)
}

export const GET_Activityhelp_Count_Data_ParticipationOpen = obj => {
  return get(`/activity/count/data/participationOpen`, obj)
}

export const EXPORT_Activityhelp_Count_Data_ParticipationOpen = data => CONSTANT.publicPath + '/activity/count/data/participationOpen/export?' + qs.stringify(data)

export const GET_POPUP_SECKILL_LAST = obj => {
  return get(`/seckill/popup/findList`, obj)
}
export const GET_POPUP_SECKILL_BY_ID = id => {
  return get(`/seckill/popup/${id}`, null)
}

export const SAVE_POPUP_SECKILL = obj => {
  return post(`/seckill/popup`, obj)
}
export const get_seckill_goodsstorages_storeCategory = obj => {
  return get(`/seckill/goodsstorages/storeCategory`, obj)
}
export const get_activity_seckill_category_findList = obj => {
  return get(`/activity/seckill/category/findList`, obj)
}
export const get_activity_seckill_categoryById = id => {
  return get(`/activity/seckill/category/${id}`)
}
export const post_activity_seckill_category = obj => {
  return post(`/activity/seckill/category`, obj)
}
export const EXPORT_COUNT_ZERO_ACTIVITY = data => CONSTANT.publicPath + '/countzeroactivity/export?' + qs.stringify(data)

// 打电话 -- 随便打
export const get_phone_meal_page = obj => {
  return get(`/phone/set/meal/page`, obj)
}
export const post_phone_meal_insertOrUpdate = obj => {
  return post(`/phone/set/meal/insertOrUpdate`, obj)
}
export const del_phone_meal = obj => {
  return del(`/phone/set/meal/${obj}`)
}
export const get_order_phone_page = obj => {
  return get(`/order/phone/page`, obj)
}
export const get_order_phone_fee = obj => {
  return get(`/order/phone/fee`, obj)
}
export const get_phone_account_detail_page = obj => {
  return get(`/phone/account/detail/page`, obj)
}
export const couponMiniprogram_config_create = obj => {
  return post(`/channel/tbk/create`, obj)
}
export const couponMiniprogram_config_edit = obj => {
  return post(`/channel/tbk/edit`, obj)
}
export const couponMiniprogram_config_list = obj => {
  return get(`/channel/tbk/list`, obj)
}
export const couponMiniprogram_statistics = obj => {
  return get(`/channel/tbk/count`, obj)
}
export const couponMiniprogram_statistics_detail = obj => {
  return get(`/channel/tbk/channel/count`, obj)
}
export const GET_USER_CASH_OUT_AWARD = obj => {
  return get(`/activity/user/cash/out/award/page`, obj)
}

export const EDIT_USER_CASH_OUT_AWARD_BY_ID = obj => {
  return put(`/activity/user/cash/out/award`, obj)
}

export const GET_USER_CASH_OUT_PAGE = obj => {
  return get(`/count/user/cash/out/page`, obj)
}

export const EXPORT_CASH_OUT = data => CONSTANT.publicPath + '/count/user/cash/out/export?' + qs.stringify(data)

export const Get_Bottom_Navigation_Manager = obj => {
  return get(`/bottom/navigation/manager/page`, obj)
}
export const Get_Bottom_Navigation_Manager_ById = obj => {
  return get(`/bottom/navigation/manager/id`, obj)
}

export const Post_Bottom_Navigation_Manager = obj => {
  return post(`/bottom/navigation/manager/save`, obj)
}

export const Post_Bottom_Navigation_Manager_Update = obj => {
  return post(`/bottom/navigation/manager/updateById`, obj)
}

export const Get_Seckill_video_config = obj => {
  return get(`seckill/video/config/page`, obj)
}

export const Post_Seckill_video_config = obj => {
  return post(`/seckill/video/config`, obj)
}

export const GET_ISOPEN_COUNTDOWN = data => {
  return get('/channel/tbk/time/switch', data)
}

export const POST_ISOPEN_COUNTDOWN = data => {
  return post('/channel/tbk/time/switch', data)
}

export const ADD_PRODUCT_CONFID = data => {
  return post('/channel/tbk/own/goods/conf', data)
}

export const GET_PRODUCT_LIST = data => {
  return get('/channel/tbk/own/list', data)
}

export const GET_PRODUCT_CONFIG = data => {
  return get('/channel/tbk/own/goods/conf', data)
}

/*
微信大额券小程序
*/

export const couponMiniprogram_wx_config_ppdid = obj => {
  return get('/channel/pdd/account/list',obj)
}
export const couponMiniprogram_wx_config_create = obj => {
  return post(`/channel/pdd/create`, obj)
}
export const couponMiniprogram_wx_config_edit = obj => {
  return post(`/channel/pdd/edit`, obj)
}
export const couponMiniprogram_wx_config_list = obj => {
  return get(`/channel/pdd/list`, obj)
}
export const couponMiniprogram_wx_statistics = obj => {
  return get(`/channel/pdd/count`, obj)
}
export const couponMiniprogram_wx_statistics_detail = obj => {
  return get(`/channel/pdd/channel/count`, obj)
}
export const GET_ISOPEN_COUNTDOWN_WX = data => {
  return get('/channel/pdd/time/switch', data)
}
export const POST_ISOPEN_COUNTDOWN_WX = data => {
  return post('/channel/pdd/time/switch', data)
}
export const GET_PRODUCT_LIST_WX = data => {
  return get('/channel/pdd/own/list', data)
}
export const GET_PRODUCT_CONFIG_WX = data => {
  return get('/channel/pdd/own/goods/conf', data)
}
export const GET_TURNTABLE_GET_ONE = obj => {
  return get(`/activity/turntable/award/config/getone`, obj)
}

export const EDIT_TURNTABLE_GET_ONE = obj => {
  return post(`/activity/turntable/award/config/update/one`, obj)
}

export const GET_PHONE_AD_AWARD = obj => {
  return get(`/phoneadaward/find`, obj)
}

export const EDIT_PHONE_AD_AWARD = obj => {
  return post(`/phoneadaward/update`, obj)
}

export const GET_PHONE_SET_MEAL_COUNT = obj => {
  return get(`/phone/set/meal/count`, obj)
}

export const GET_SECKILL_APPOINT_RATE = obj => {
  return get(`/seckill/appoint/rate`, obj)
}

export const GET_SECKILL_APPOINT_RATE_UPDATE = obj => {
  return post(`/seckill/appoint/rate/update`, obj)
}

export const GET_SECKILL_RETENTION_COUNT = obj => {
  return get(`/count/seckill/retention`, obj)
}

export const GET_SECKILL_VIDEO_COUNT = obj => {
  return get(`/count/seckill/video`, obj)
}

export const GET_PRESETPUB_LIST = obj => {
  return post(`/drying/sheet/pageList`, obj)
}

export const POST_PRESETPUB = obj => {
  return post(`/drying/sheet/save`, obj)
}

export const DEL_PRESETPUB = obj => {
  return del(`/drying/sheet/del/${obj.id}`, obj)
}

// 商品列表
export const GET_PDD_GOODS_PAGE_HOME = obj => {
  return get(`/activity/zero/goods/choose/page`, obj)
}
// 商城基础信息保存和更新
export const mallAddOrUpdate = obj => {
  return post(`/mall/addOrUpdate`, obj)
}

// 获取所有商城信息
export const mallPage = obj => {
  return get(`/mall/page`, obj)
}

// 删除商城基础信息
export const mallPageDel = obj => {
  return get(`/mall/page/${obj.id}`, obj)
}

// 获取公司列表
export const mallCompanyList = obj => {
  return get(`/mall/companyList`, obj)
}

// 商品列表
export const GET_PDD_GOODS_PAGE = obj => {
  return get(`/activity/zero/goods/page`, obj)
}
// 查询本地对拼多多商品的分类
export const GET_ZERO_PDD_TYPE_LIST = obj => {
  return get('/goodsActivityCategory/list', obj)
}
// 查询拼多多商品分类
export const GET_PDD_CATERGORY = obj => {
  return get(`/activity/zero/goods/choose/pdd/cat`, obj)
}
// 查询拼多多商品
export const GET_PDD_GOODS = obj => {
  return get(`/activity/zero/goods/choose/pdd/goods`, obj)
}
// 0元购拼多多商品详情
export const GET_PDD_GOODS_DETAIL = id => {
  return get(`/activity/zero/goods?id=${id}`)
}
// 商品详情
export const GET_PDD_GOODS_DETAIL_HOME = id => {
  return get(`/activity/zero/goods/choose/detail/${id}`)
}
// 编辑拼多多商品
export const PUT_CHOOSE_PDD_LOCAL_HOME = obj => {
  return put(`/activity/zero/goods/choose`, obj)
}
// 编辑拼多多商品
export const PUT_CHOOSE_PDD_LOCAL = obj => {
  return put(`/activity/zero/goods`, obj)
}
// 添加拼多多商品到本地
export const POST_CHOOSE_PDD_LOCAL_HOME = obj => {
  return post(`/activity/zero/goods/choose`, obj)
}
// 添加拼多多商品到本地
export const POST_CHOOSE_PDD_LOCAL = obj => {
  return post(`/activity/zero/goods`, obj)
}
