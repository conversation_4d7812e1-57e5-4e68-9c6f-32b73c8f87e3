<template>
  <div v-if="isShow" class="account">
    <div class="add-container">
      <!-- <ElUpload
        ref="upload"
        accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        style="display: inline-block;margin-left:5px"
        action=""
        :before-upload="beforeUpload"
        :multiple="false"
        :limit="1"
      >
        <ElButton id="uploadEle" icon="el-icon-upload" size="small" plain type="warning">导入账号</ElButton>
      </ElUpload> -->
    </div>
    <Page :request="request" :list="list" table-title="账户管理">
      <div slot="searchContainer" style="display: inline-block;position: relative;">
        <ElButton plain type="primary" size="small" @click="handAdd"> 添加账号 </ElButton>
        <ElButton plain type="warning" size="small" icon="el-icon-download" @click="handUpload">导出数据</ElButton>
      </div>
    </Page>

    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="dialogOps">
      <accountEditNew :current-params="currentParams" :proxy-list="proxyList" @cancle="handCancle" @success="handleSuccess" />
    </SDialog>
  </div>
</template>

<script>
import Page from '@/components/restructure/page'
import SDialog from '@/components/restructure/dialog'
import accountEditNew from './components/accountEditNew.vue'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { count_channel_application_list } from '@/api/NewChannel'
import {
  post_account_import, mediaDeliveryAccount_export, proxyConfigSelector, mediaDeliveryAccountPage
} from '@/api/deliveryManage'
import moment from 'moment'
export default {
  name: 'AccountManage',
  desc: '投放账户管理',
  components: {
    Page, SDialog, accountEditNew
  },
  props: {},
  data() {
    return {
      dialogFormVisible: false,
      advertisementTypeList: [
        { label: '字节', value: 1 },
        { label: '快手', value: 2 },
        { label: '百度', value: 3 },
        { label: '腾讯', value: 4 }
      ],
      isShow: true,
      currentParams: {},
      dialogOps: {
        title: '新增账户',
        width: '500px'
      },
      listQuery: {
        // status: '1',
        mediaPlatform: ''
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = res.data
                // this.listQuery.siteId = res.data[0].siteId
              }
            })
          }
          return mediaDeliveryAccountPage(this.listQuery)
        }
      },
      proxyList: [],
      siteIds: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'id'
          // render: (h, params) => {
          //   return h('span', params.data.$index + 1)
          // }
        },
        {
          title: '媒体',
          key: 'mediaPlatform',
          type: formItemType.select,
          search: true,
          tableView: tableItemType.tableView.text,
          list: this.advertisementTypeList,
          options: {
            placeholder: '请选择媒体类型'
          }
        },
        {
          title: '投放应用',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          // val:this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '投放应用',
          key: 'siteName'
        },
        {
          title: '账号id',
          key: 'accountId',
          type: formItemType.input,
          search: true,
          clearable: true,
          options: {
            placeholder: '请输入投放账号Id',
            title: '投放账号'
          }
        },
        {
          title: '账户名称',
          key: 'accountName',
          type: formItemType.input,
          search: true,
          // clearable: true,
          options: {
            placeholder: '请输入投放账户名称',
            title: '账户名称'
          }
        },
        {
          title: 'appId',
          key: 'appId'
        },
        {
          title: 'secretId',
          key: 'secretId'
        },
        {
          title: '授权地址',
          key: 'authorizationUrl',
          render: (h, params) => {
            const data = params.data.row
            if (!data.authorizationUrl) {
              return h('span', '-')
            }
            return h('a', {
              attrs: {
                href: data.authorizationUrl,
                target: '_blank'
              },
              style: {
                'text-decoration': 'underline',
                'color': 'blue'
              }
            }, data.authorizationUrl)
          }
        },
        {
          title: '授权状态',
          key: 'authorizationStatus',
          type: formItemType.select,
          search: true,
          clearable: true,
          tableView: tableItemType.tableView.text,
          list: [
            { label: '已授权', value: '1' },
            { label: '未授权', value: '0' }
          ]
        },
        {
          title: '代理名称',
          key: 'proxyConfigName',
          type: formItemType.input,
          search: true,
          clearable: true
        },
        {
          title: '创建人',
          key: 'creatorName'
        },
        {
          title: '创建时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          search: true,
          clearable: true,
          tableView: tableItemType.tableView.text,
          list: [
            { label: '启用', value: '1' },
            { label: '禁用', value: '0' }
          ],
          // val: '1',
          options: {
            placeholder: '请选择状态'
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          width: '210px',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                params.parentMediaAccount
                this.currentParams = { ...params }
                this.dialogOps.title = '修改账户'
                this.dialogFormVisible = true
              }
            },
            {
              text: '详情',
              key: 'detail',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                console.log(123)
                this.$router.push({
                  path: '/deliveryManage/accountDetail',
                  query: {
                    id: params.id
                  }
                })
              }
            }
          ]
        }
      ]
    }
  },
  watch: {
    dialogFormVisible(value) {
      if (value) {
        this.initProxyConfigSelector()
      }
    }
  },
  created() {
    this.initProxyConfigSelector()
  },
  methods: {
    initProxyConfigSelector() {
      proxyConfigSelector().then(res => {
        this.proxyList = res.data
      })
    },
    handCancle() {
      this.dialogFormVisible = false
    },
    handAdd() {
      this.currentParams = {}
      this.dialogOps.title = '新增'
      this.dialogFormVisible = true
    },
    handUpload() {
      const data = {
        ...this.listQuery
      }
      window.location.href = mediaDeliveryAccount_export({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    handleSuccess() {
      this.$store.dispatch('tableRefresh', this)
      this.dialogFormVisible = false
    },
    beforeUpload(file) {
      const fileFormData = new FormData()
      fileFormData.append('file', file)
      post_account_import(fileFormData).then(res => {
        if (res.code === 0) {
          this.$message.success('上传成功')
          this.$store.dispatch('tableRefresh', this)
        } else {
          this.$message.error(res.msg)
        }
      })
      return false
    }

  }
}
</script>

<style lang="scss" scoped>
::v-deep .filter-container .title {
  border-bottom: none;
}

.add-container {
  float: right;
  margin: 10px 10px 0 0;
}
</style>
