<template>
  <div class="content">
    <div class="main-box">
      <div>
        <div v-for="(todo, row) in accountSelectedList" :key="row">
          <div class="intercept-item">
            <span v-if="todo.mediaAccount">{{ todo.mediaAccount }}：</span>
            <span v-for="(item, index) in todo.channelCodes" :key="index">
              <el-tag v-if="!item.deleted" closable class="channel-item" @close="deleteChannel(index, row)">
                <span>
                  <span>{{ item }}</span>
                </span>
              </el-tag>
            </span>

            <i
              class="el-icon-close"
              style="color: red; padding: 0 10px; cursor: pointer; font-weight: 700;"
              @click="deleteIntercept(row)"
            />
          </div>
        </div>
      </div>
      <div style="margin: 10px">
        <el-button size="mini" @click="closeDialog">取 消</el-button>
        <el-button size="mini" type="primary" @click="submit">确 定</el-button>
      </div>
    </div>
    <div style="display: flex;">
      <div style="width: 700px; padding-top: 42px; margin-right: 20px;">
        <Page
          ref="accountPage"
          :request="pageAccountList"
          :list="accountList"
          table-type="selection"
          @selectionChange="interceptSelectionChange"
          @tableLoad="interceptTableLoad"
        />
      </div>
      <div style="flex: 1;padding-top: 42px;">
        <Page
          v-if="rerenderChannelCodeListTable"
          ref="channelPage"
          :request="request"
          :list="channelCodeList"
          table-type="selection"
          @selectionChange="selectionChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { count_channel_application_list, GET_CHANNEL_LIST_ALL } from '@/api/NewChannel.js'
import Page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import {
  pageAccountList, channelMatchInquire, addChannelMatchBatch
} from '@/api/deliveryManage'
export default {
  components: { Page },
  data() {
    return {
      searchType: '渠道',
      queryPra: {
        siteId: '',
        channelCode: '',
        channelMediaId: '',
        channelMediaAdId: '',
        searchType: 0,
        pageNumber: 1,
        pageSize: 10,
        total: 0
      },
      siteIds: [],
      request: {
        getListUrl: async data => {
          this.queryPra = { ...this.queryPra, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = [{ siteName: '全部', siteId: '' }, ...res.data]
              }
            })
          }
          const params = { ...this.queryPra }
          if (this.queryPra.searchType === 0) {
            params.keyword = params.channelCode
            delete params.channelCode
          }
          return GET_CHANNEL_LIST_ALL(params)
        }
      },
      rerenderChannelCodeListTable: true,

      pageAccountList: {
        getListUrl: data => {
          return pageAccountList({ status: 0, ...data }).then(res => {
            this.accountOriginalList = res.data || []
            return res
          })
        }
      },
      accountOriginalList: [],
      accountSelectedList: [],
      channelCodeSelectedList: [],
      mediaAccountId: this.$route.query.mediaAccountId || ''
    }
  },
  computed: {
    accountList() {
      return [
        {
          title: '账号',
          key: 'mediaAccount',
          search: true,
          type: formItemType.input,
          tableHidden: true
        },
        {
          title: '账号',
          key: 'mediaAccount'
        }
      ]
    },
    channelCodeList() {
      return [
        {
          title: '渠道code',
          key: 'channelCode',
          search: true,
          type: formItemType.input
        },
        {
          title: '渠道名称',
          key: 'channelName'
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: this.queryPra.siteId,
          search: true
        }
      ]
    }
  },
  watch: {
    mediaAccountId: {
      handler(newVal) {
        if (newVal) {
          channelMatchInquire({ mediaAccountId: newVal }).then((res) => {
            if (res.code === 0) {
              let data = res.data
              if (!data.length) {
                data =
                {
                  mediaAccountId: newVal,
                  mediaAccount: res.mediaAccount,
                  channelCodes: []
                }
              } else {
                data = { mediaAccountId: newVal, ...data[0] }
              }
              this.accountSelectedList.push({ ...data })
            }
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 选右边表格
     * @param {*} event
     */
    selectionChange(event) {
      event.forEach((item) => {
        if (!this.channelCodeSelectedList.find(it => it.id === item.id)) {
          this.channelCodeSelectedList.push({ ...item })
        }
      })
      this.addChannelCodeToIntercept()
    },
    /**
     * 清除右边表格选中内容
     */
    clearChannelSelected() {
      this.channelCodeSelectedList = []
      const table = this.$refs.channelPage?.$children.find(item => item.$el.className === 'table-base').$children[0]
      this.toggleSelection(table, this.channelCodeSelectedList)
    },
    /**
     * 添加右边表格选中内容到左边选中的列表内
     */
    addChannelCodeToIntercept() {
      this.accountSelectedList.forEach(item => {
        this.channelCodeSelectedList.forEach(it => {
          if (!item.channelCodes.includes(it.channelCode)) {
            item.channelCodes.push(it.channelCode)
          }
        })
      })
    },
    /**
     * 选中左边表格
     * @param {*} event
     */
    interceptSelectionChange(event) {
      if (event.length == 0 || (event.length == 1 && this.accountSelectedList.length && this.accountSelectedList.find(it => it.mediaAccount == event[0].mediaAccount))) {
        return
      }
      if (event.length > 1 || (event.length == 1 && this.accountSelectedList.length && this.mediaAccount != event[0].mediaAccount)) {
        const table = this.$refs.accountPage?.$children.find(item => item.$el.className === 'table-base').$children[0]
        table.clearSelection()
        return this.$message.warning('渠道只能同时绑定一个账户，仅支持同时操作一个账户')
      }
      event.forEach((item, index) => {
        if (!this.accountSelectedList.find(it => it.mediaAccountId == item.mediaAccountId)) {
          channelMatchInquire({ mediaAccountId: item.id }).then((res) => {
            if (res.code === 0) {
              let data = res.data
              if (!data.length) {
                data =
                {
                  mediaAccountId: item.id,
                  mediaAccount: item.mediaAccount,
                  channelCodes: []
                }
              } else {
                data = { mediaAccountId: item.id, ...data[0] }
              }
              this.accountSelectedList.push({ ...data })
              // 清除右边表格选中内容
              this.clearChannelSelected()
            }
          })
        }
      })
    },
    /**
     * 删除单个渠道
     * @param {*} index
     * @param {*} row
     */
    deleteChannel(index, row) {
      const channelCodes = this.accountSelectedList[row].channelCodes
      channelCodes.splice(index, 1)
    },
    /**
     * 删除拦截整行
     * @param {*} row
     */
    deleteIntercept(row) {
      this.accountSelectedList.splice(row, 1)
      const table = this.$refs.accountPage?.$children.find(item => item.$el.className === 'table-base').$children[0]
      this.toggleSelection(table, this.accountSelectedList)

      // 清除右边表格选中内容
      this.clearChannelSelected()
    },
    /**
     * 切换表格选中状态
     * @param {*} el
     * @param {*} rows
     */
    toggleSelection(el, rows) {
      el.clearSelection()
      this.accountOriginalList.forEach(item => {
        if (rows.find((it) => it.mediaAccount === item.mediaAccount)) {
          el.toggleRowSelection(item, true)
        }
      })
    },
    interceptTableLoad(data) {
      if (!data) return
      const table = this.$refs.accountPage?.$children.find(item => item.$el.className === 'table-base').$children[0]
      setTimeout(() => {
        table.clearSelection()
        data.data.forEach(item => {
          if (this.accountSelectedList.find((it) => it.mediaAccount === item.mediaAccount)) {
            table.toggleRowSelection(item, true)
          }
        })
      }, 0)
    },
    closeDialog() {
      this.queryPra = {
        channelCode: '',
        channelMediaId: '',
        channelMediaAdId: '',
        searchType: 0,
        pageNumber: 1,
        pageSize: 10,
        total: 0
      }
      this.searchType = '渠道'
      this.accountOriginalList = []
      this.accountSelectedList = []
      this.channelCodeSelectedList = []
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push('/deliveryManage/channelMatch')
    },
    submit() {
      const params = JSON.parse(JSON.stringify(this.accountSelectedList))
      addChannelMatchBatch(params).then(res => {
        if (res.code === 0) {
          this.$message.success('关联成功!')
          this.closeDialog()
        } else {
          return this.$alert(res.message, '提示', {
            showCancelButton: true,
            dangerouslyUseHTMLString: true
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep.el-dialog__body {
  padding: 10px 20px 30px 20px !important;
}

.demo-form-inline ::v-deep .el-input {
  width: 145px !important;
}

.intercept-item {
  margin: 10px;
  display: inline-block;
  margin: 4px;
  padding: 4px;
  border: 1px solid #d9ecff;
  border-radius: 4px;
  line-height: 1;

  .channel-item {
    margin: 4px 4px 4px 0;
  }
}

.main-box {
  margin: 0px 0px 10px;
  padding: 4px;
  border: 1px solid #d9ecff;
  border-radius: 4px;
}
</style>
