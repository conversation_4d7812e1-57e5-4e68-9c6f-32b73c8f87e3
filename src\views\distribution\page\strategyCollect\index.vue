<template>
  <div class="distribution-strategy-collect">
    <page :request="request" :list="list" table-title="策略集列表">
      <div slot="searchContainer" style="display: inline-block; margin-bottom: 15px;">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small"
          @click="handleAdd">添加</el-button>
      </div>
    </page>

    <!-- 新增/编辑策略集抽屉 -->
    <Drawer
      :visible.sync="drawerVisible"
      :title="formData.id ? '编辑策略集' : '新增策略集'"
      @confirm="handleSubmit('form')"
      @cancel="drawerVisible = false"
    >
      <el-form ref="form" :model="formData" :rules="formRules" label-width="120px" label-position="top" class="demo-ruleForm">
        <!-- 基础信息 -->
        <FormSection title="基础信息">
          <el-form-item label="策略集名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入策略集名称" maxlength="30" show-word-limit></el-input>
          </el-form-item>
        </FormSection>

        <!-- 策略设置 -->
        <FormSection title="策略设置">
          <el-form-item 
            label="请设置每个路由需要执行的策略" 
            prop="strategyOperations"
            :rules="formRules.strategyOperations"
            required>
            <el-table :data="formData.strategyOperations" border style="width: 100%">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="downloadType" label="投放链路" width="120">
                <template slot-scope="scope">
                  {{ scope.row.downloadType === 1 ? 'APK链路' : 'H5链路' }}
                </template>
              </el-table-column>
              <el-table-column prop="pageType" label="使用场景" min-width="120"></el-table-column>
              <el-table-column prop="landingPageStr" label="投放路由" min-width="120"></el-table-column>
              <el-table-column label="策略名称" min-width="120">
                <template slot-scope="scope">
                  <el-popover
                    placement="top"
                    width="200"
                    trigger="click"
                    v-model="popoverVisibleMap[scope.$index]"
                    @show="handleStrategyPopoverShow(scope.row, scope.$index)">
                    <el-select 
                      v-model="scope.row.triageStrategyId"
                      placeholder="请选择策略"
                      @change="(val) => handleStrategySelect(val, scope.$index)">
                      <el-option
                        v-for="item in strategyOptions"
                        :key="item.id"
                        :label="`${item.id} + ${item.name}`"
                        :value="item.id">
                      </el-option>
                    </el-select>
                    <div slot="reference">
                      <el-button 
                        v-if="!scope.row.name" 
                        type="text">设置策略</el-button>
                      <span 
                        v-else 
                        class="strategy-name">{{ scope.row.name }}</span>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>

          <el-form-item label="是否默认策略" prop="defaultStrategy" style="margin-top: 20px;">
            <el-radio-group v-model="formData.defaultStrategy">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </FormSection>

        <!-- 状态 -->
        <FormSection title="状态">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </FormSection>
      </el-form>
    </Drawer>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import Drawer from '@/components/Drawer'
import FormSection from '@/components/Drawer/FormSection'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getStrategyCollectList, addOrUpdateStrategyCollect, getStrategyCollectDetail, checkDefaultStrategyCollect, getTrialStrategyByParam } from '@/api/distribution'
import moment from 'moment'

export default {
  name: 'DistributionStrategyCollect',
  components: {
    page,
    Drawer,
    FormSection
  },
  data() {
    const validateStrategyOperations = (rule, value, callback) => {
      if (!this.formData.strategyOperations || this.formData.strategyOperations.length === 0) {
        callback(new Error('请设置策略'))
        return
      }
      const hasUnsetStrategy = this.formData.strategyOperations.some(
        operation => !operation.triageStrategyId
      )
      if (hasUnsetStrategy) {
        callback(new Error('请为所有投放链路设置策略'))
      } else {
        callback()
      }
    }

    return {
      // 查询参数
      listQuery: {},
      // 抽屉显示状态
      drawerVisible: false,
      // 表单数据
      formData: {
        name: '',
        defaultStrategy: 0,
        status: 1,
        strategyOperations: []
      },
      // 投放路由数据
      landingPageList: [],
      // 策略选项
      strategyOptions: [],
      // popover显示状态
      popoverVisibleMap: {},
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入策略集名称', trigger: 'blur' },
          { max: 30, message: '策略集名称不能超过30个字符', trigger: 'blur' }
        ],
        defaultStrategy: [
          { required: true, message: '请选择是否默认策略', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        strategyOperations: [
          { required: true, validator: validateStrategyOperations, trigger: ['blur', 'change'] }
        ]
      },
      // API请求配置
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const res = await getStrategyCollectList(this.listQuery)
          const { records = [], total = 0 } = res.data
          return {
            data: {
              total: total,
              rows: records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '策略集名称',
          key: 'name',
          search: true,
          titleHidden: true,
          type: formItemType.input,
          tableHidden: true,
          options: {
            placeholder: '请输入策略集名称'
          }
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableHidden: true,
          list: [
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 }
          ]
        },
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '策略集名称',
          key: 'name'
        },
        {
          title: '是否默认策略',
          key: 'defaultStrategy',
          render: (h, params) => {
            return h('span', params.data.row.defaultStrategy === 1 ? '是' : '否')
          }
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return h('span', params.data.row.status === 1 ? '启用' : '禁用')
          }
        },
        {
          title: '更新人',
          key: 'updateByStr'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) return h('span', '--')
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.handleEdit(params)
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    async handleAdd() {
      try {
        // 获取投放路由数据
        const res = await getStrategyCollectDetail(0)
        if (res.code === 200 && res.data && res.data.strategyOperations) {
          this.landingPageList = res.data.strategyOperations
          // 使用获取到的数据初始化 strategyOperations
          this.formData = {
            name: '',
            defaultStrategy: 0,
            status: 1,
            strategyOperations: this.landingPageList.map(item => ({
              downloadType: item.downloadType,
              landingPageId: item.landingPageId,
              landingPageStr: item.landingPageStr,
              triageStrategyId: '',
              triageStrategyStr: '',
              name: ''
            }))
          }
          // 重置 popover 显示状态
          this.popoverVisibleMap = {}

          // 数据准备好后再显示抽屉
          this.drawerVisible = true
          this.$nextTick(() => {
            if (this.$refs.form) {
              this.$refs.form.resetFields()
            }
          })
        } else {
          this.$message.error('获取投放路由失败')
        }
      } catch (error) {
        console.error('新增策略集失败:', error)
        this.$message.error('新增策略集失败')
      }
    },

    async handleEdit(params) {
      try {
        const res = await getStrategyCollectDetail(params.id)
        if (res.code === 200) {
          // 直接使用接口返回的策略操作数据
          const strategyOperations = res.data.strategyOperations.map(operation => ({
            ...operation,
            name: operation.triageStrategyStr,
            popoverVisible: false
          }))

          this.formData = {
            id: params.id,
            name: res.data.triageStrategyCollectName,
            defaultStrategy: res.data.defaultStrategy,
            status: res.data.status,
            strategyOperations
          }
          
          this.drawerVisible = true
          this.$nextTick(() => {
            if (this.$refs.form) {
              this.$refs.form.clearValidate()
            }
          })
        }
      } catch (error) {
        this.$message.error(error.message || '获取详情失败')
      }
    },
    handleStrategySelect(value, index) {
      const selectedStrategy = this.strategyOptions.find(item => item.id === value)
      if (selectedStrategy) {
        this.formData.strategyOperations[index] = {
          ...this.formData.strategyOperations[index],
          triageStrategyId: value,
          name: selectedStrategy.name
        }
        // 关闭对应的 popover
        this.$set(this.popoverVisibleMap, index, false)
      }
    },
    // 获取策略选项
    async fetchStrategyOptions(downloadType, landingPageId) {
      try {
        const res = await getTrialStrategyByParam({
          downloadType,
          landingPageId
        })
        if (res.code === 200) {
          this.strategyOptions = res.data || []
        }
      } catch (error) {
        console.error('获取策略选项失败:', error)
        this.$message.error('获取策略选项失败')
      }
    },

    // 打开策略选择弹窗前获取策略选项
    async handleStrategyPopoverShow(row, index) {
      await this.fetchStrategyOptions(row.downloadType, row.landingPageId)
      this.$set(this.popoverVisibleMap, index, true)
    },

    showConfirmDialog({ id, name }) {
      const h = this.$createElement
      const confirmMessage = h('div', null, [
        h('div', { style: 'font-size: 14px; margin-bottom: 8px;' }, 
          '当前已存在默认策略集，'
        ),
        h('div', { style: 'font-size: 14px; margin-bottom: 12px;' }, 
          '请确认是否添加该策略集作为默认策略'
        ),
        h('div', { 
          style: 'background: #F5F7FA; padding: 12px; border-radius: 4px;'
        }, [
          h('div', { style: 'margin-bottom: 8px;' }, [
            h('span', '策略集ID：'),
            h('span', { style: 'color: #F56C6C;' }, id)
          ]),
          h('div', null, [
            h('span', '策略集名称：'),
            h('span', { style: 'color: #F56C6C;' }, name)
          ])
        ])
      ])

      return this.$msgbox({
        title: '操作提示',
        message: confirmMessage,
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'custom-dialog'
      })
    },

    // 检查默认策略
    async checkDefaultStrategy() {
      if (this.formData.defaultStrategy !== 1) {
        return true
      }
      
      try {
        // 只有编辑状态才传入id
        const checkRes = await checkDefaultStrategyCollect(this.formData.id || undefined)
        if (checkRes.code === 200 && checkRes.data) {
          // 存在默认策略，显示确认对话框
          try {
            await this.showConfirmDialog({
              id: checkRes.data.id,
              name: checkRes.data.name
            })
            return true
          } catch (error) {
            // 用户取消操作
            if (error === 'cancel') {
              return false
            }
            throw error
          }
        }
        return true
      } catch (error) {
        console.error('检查默认策略失败:', error)
        this.$message.error(error.message || '检查默认策略失败')
        return false
      }
    },

    handleSubmit(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          try {
            // 检查是否所有策略都已设置
            const hasUnsetStrategy = this.formData.strategyOperations.some(
              operation => !operation.triageStrategyId
            )
            if (hasUnsetStrategy) {
              this.$message.error('请为所有投放链路设置策略')
              return
            }

            // 检查默认策略
            const canProceed = await this.checkDefaultStrategy()
            if (!canProceed) {
              return
            }

            // 准备提交数据
            const submitData = {
              id: this.formData.id,
              name: this.formData.name,
              defaultStrategy: this.formData.defaultStrategy,
              status: this.formData.status,
              strategyOperations: this.formData.strategyOperations.map(item => ({
                downloadType: item.downloadType,
                landingPageId: item.landingPageId,
                landingPageStr: item.landingPageStr,
                triageStrategyId: item.triageStrategyId,
                triageStrategyStr: item.name
              }))
            }

            // 提交数据
            const res = await addOrUpdateStrategyCollect(submitData)
            if (res.code === 200) {
              this.$message.success(this.formData.id ? '编辑成功' : '新增成功')
              this.drawerVisible = false
              this.$store.dispatch('tableRefresh', this)
            }
          } catch (error) {
            console.error('提交错误:', error)
            this.$message.error(error.message || '操作失败')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.distribution-strategy-collect {
  padding: 20px;
  
  ::v-deep .activeButton {
    .el-button {
      margin-right: 5px;
      padding: 7px 6px;
    }
  }
}

.strategy-name {
  color: #409EFF;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}

::v-deep .custom-dialog {
  .el-message-box__content {
    padding: 20px;
  }
  .el-message-box__message p {
    margin: 0;
    line-height: 1.5;
  }
}
</style> 