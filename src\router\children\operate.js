/*
 * 运营管理子路由
 * */

const operate = [
  {
    path: '/operate/riskManagement',
    name: 'operate_riskManagement',
    meta: {
      title: '风控管理'
    },
    component: () => import('@/views/operate/page/riskManagement')
  },
  {
    path: '/operate/clueAllocationManagement',
    name: 'operate_clueAllocationManagement',
    meta: {
      title: '线索分配管理'
    },
    component: () => import('@/views/operate/page/clueAllocationManagement')
  },
  {
    path: '/operate/oldUserConversionManagement',
    name: 'operate_oldUserConversionManagement',
    meta: {
      title: '老用户转化管理'
    },
    component: () => import('@/views/operate/page/oldUserConversionManagement')
  },
  {
    path: '/operate/banner',
    name: 'operate_banner',
    meta: {
      title: '广告列表'
    },
    // component: () => import("@/views/operate/page/banner")
    component: () => import('@/views/operate/page/banner')
  },
  {
    path: '/operate/bannerDetail',
    name: 'banner_details',
    meta: {
      title: 'banner详情',
      activeMenu: '/operate/banner',
      parentTitle: '广告列表'
    },
    // component: () => import("@/views/operate/page/banner")
    component: () => import('@/views/operate/page/bannerDetail')
  },
  {
    path: '/operate/banner_detail',
    name: 'banner_detail',
    meta: {
      title: '编辑广告',
      parentTitle: '广告列表',
      activeMenu: '/operate/banner'
    },
    // component: () => import("@/views/operate/page/banner_detail")
    component: () => import('@/views/operate/page/banner_detail')
  },
  {
    path: '/operate/column',
    name: 'operate_column',
    meta: {
      title: '栏目列表'
    },
    // component: () => import("@/views/operate/page/column")
    component: () => import('@/views/operate/page/column')
  },
  {
    path: '/operate/channel',
    name: 'operate_channel',
    meta: {
      title: '渠道列表'
    },
    // component: () => import("@/views/operate/page/channel")
    component: () => import('@/views/operate/page/channel')
  },
  {
    path: '/operate/channelType',
    name: 'operate_channelType',
    meta: {
      title: '渠道类型'
    },
    // component: () => import("@/views/operate/page/channelType")
    component: () => import('@/views/operate/page/channelType')
  },
  {
    path: '/operate/channelStatistics',
    name: 'operate_channelStatistics',
    meta: {
      title: '渠道统计'
    },
    // component: () => import("@/views/operate/page/channelStatistics")
    component: () => import('@/views/operate/page/channelStatistics')
  },
  {
    path: '/operate/loanProducts',
    name: 'operate_loanProducts',
    meta: {
      title: '贷款产品'
    },
    // component: () => import("@/views/operate/page/loanProducts")
    component: () => import('@/views/operate/page/loanProducts')
  },
  {
    path: '/operate/loanProducts_detail',
    name: 'operate_loanProducts_detail',
    meta: {
      title: '编辑贷款产品',
      parentTitle: '贷款产品',
      activeMenu: '/operate/loanProducts'
    },
    // component: () => import("@/views/operate/page/loanProducts_detail")
    component: () => import('@/views/operate/page/loanProducts_detail')
  },
  {
    path: '/operate/zeroPurchase',
    name: 'operate_zeroPurchase',
    meta: {
      title: '0元购列表'
    },
    // component: () => import("@/views/operate/page/zeroPurchase")
    component: () => import('@/views/operate/page/zeroPurchase')
  },
  {
    path: '/operate/zeroPurchaseDetail',
    name: 'operate_zeroPurchaseDetail',
    meta: {
      title: '添加活动商品',
      parentTitle: '0元购列表',
      activeMenu: '/operate/zeroPurchase'
    },
    // component: () => import("@/views/operate/page/zeroPurchaseDetail")
    component: () => import('@/views/operate/page/zeroPurchaseDetail')
  },
  {
    path: '/operate/showWindowList',
    name: 'operate_showWindowList',
    meta: {
      title: '橱窗管理'
    },
    // component: () => import("@/views/operate/page/showWindowList")
    component: () => import('@/views/operate/page/showWindowList')
  },
  {
    path: '/operate/showWindowDetail',
    name: 'operate_showWindowDetail',
    meta: {
      level3: true,
      title: '橱窗详情',
      parentTitle: '橱窗',
      activeMenu: '/operate/showWindowList'
    },
    // component: () => import("@/views/operate/page/showWindowList")
    component: () => import('@/views/operate/page/showWindowDetail')
  },
  {
    path: '/operate/commonProblem',
    name: 'operate_commonProblem',
    meta: {
      title: '常见问题'
    },
    // component: () => import("@/views/operate/page/showWindowList")
    component: () => import('@/views/operate/page/commonProblem')
  },
  {
    path: '/operate/firstDialog',
    name: 'operate_firstDialog',
    meta: {
      title: '首次弹窗'
    },
    // component: () => import("@/views/operate/page/showWindowList")
    component: () => import('@/views/operate/page/firstDialog')
  },
  {
    path: '/operate/specialTopicList',
    name: 'operate_specialTopicList',
    meta: {
      title: '专题页管理'
    },
    component: () => import('@/views/operate/page/specialTopic/specialTopicList')
  },
  {
    path: '/operate/specialTopicDetail',
    name: 'operate_specialTopicDetail',
    meta: {
      title: '专题页详情',
      parentTitle: '专题页列表',
      activeMenu: '/operate/specialTopicList'
    },
    component: () => import('@/views/operate/page/specialTopic/specialTopicDetail')
  },
  {
    path: '/operate/configurationProduct',
    name: 'operate_configurationProduct',
    meta: {
      title: '商品配置',
      parentTitle: '专题页详情',
      activeMenu: '/operate/specialTopicList'
    },
    component: () => import('@/views/operate/page/specialTopic/configurationProduct')
  },
  {
    path: '/operate/specialTopicStatistics',
    name: 'operate_specialTopicStatistics',
    meta: {
      title: '专题页统计'
    },
    component: () => import('@/views/operate/page/specialTopic/specialTopicStatistics')
  },
  {
    path: '/popups/list',
    name: 'popups_list',
    meta: {
      title: '弹窗列表'
    },
    component: () => import('@/views/operate/page/popups/list')
  },
  {
    path: '/popups/popupDetail',
    name: 'popup_detail',
    meta: {
      title: '弹窗详情',
      parentTitle: '弹窗列表',
      activeMenu: '/popups/list'
    },
    component: () => import('@/views/operate/page/popups/popupDetail')
  },
  {
    path: '/popups/mallPopupDetail',
    name: 'popup_mallPopupDetail',
    meta: {
      title: '弹窗详情',
      parentTitle: '弹窗列表',
      activeMenu: '/mallManage/mallPopupList'
    },
    component: () => import('@/views/operate/page/popups/popupDetail')
  },
  {
    path: '/popups/rules',
    name: 'popups_rules',
    meta: {
      title: '弹窗规则管理'
    },
    component: () => import('@/views/operate/page/popups/rules')
  },
  {
    path: '/popups/userSourceList',
    name: 'popups_user_source_list',
    meta: {
      title: '用户来源设置',
      parentTitle: '弹窗规则管理',
      activeMenu: '/popups/rules'
    },
    component: () => import('@/views/operate/page/popups/userSourceList')
  },
  {
    path: '/popups/statistics',
    name: 'popups_statistics',
    meta: {
      title: '弹窗统计'
    },
    component: () => import('@/views/operate/page/popups/statistics')
  },
  {
    path: '/operate/splashScreenList',
    name: 'operate_splash_screen_list',
    meta: {
      title: '启屏页列表'
    },
    component: () => import('@/views/operate/page/splashScreenList')
  },
  {
    path: '/operate/splashScreenDetail',
    name: 'operate_splashScreen_detail',
    meta: {
      title: '启屏页详情',
      parentTitle: '启屏页列表',
      activeMenu: '/operate/splashScreenList'
    },
    component: () => import('@/views/operate/page/splashScreenDetail')
  },
  {
    path: '/operate/thirdPart',
    name: 'thirdPart',
    meta: {
      title: '第三方组件'
    },
    component: () => import('@/views/operate/page/thirdPart')
  },
  {
    path: '/operate/addThirdPart',
    name: 'operate_addThirdPart',
    meta: {
      level3: true,
      title: '添加组件',
      parentTitle: '第三方组件',
      activeMenu: '/operate/thirdPart'
    },
    component: () => import('@/views/operate/module/addThirdPart')
  },
  {
    path: '/thirdPartyConfiguration/surperBrand',
    name: 'thirdPartyConfiguration_surperBrand',
    meta: {
      title: '超级大牌'
    },
    component: () => import('@/views/operate/page/thirdPartyConfiguration/surperBrand')
  },
  {
    path: '/thirdPartyConfiguration/douyinHotMoney',
    name: 'thirdPartyConfiguration_douyinHotMoney',
    meta: {
      title: '抖音爆款'
    },
    component: () => import('@/views/operate/page/thirdPartyConfiguration/douyinHotMoney')
  },
  {
    path: '/operate/homeGoodsRecommend',
    name: 'homeGoodsRecommend',
    meta: {
      title: '首页商品列表'
    },
    component: () => import('@/views/operate/page/homeGoodsRecommend')
  },
  {
    path: '/operate/homeGoodsRecommendDetail',
    name: 'homeGoodsRecommendDetail',
    meta: {
      title: '首页商品详情',
      parentTitle: '首页商品列表',
      activeMenu: '/operate/homeGoodsRecommend'
    },
    component: () => import('@/views/operate/page/homeGoodsRecommendDetail')
  },
  {
    path: '/operate/homeWindowType',
    name: 'homeWindowType',
    meta: {
      title: '首页橱窗分类'
    },
    component: () => import('@/views/operate/page/homeWindowType')
  },
  {
    path: '/operate/homeWindowTypeBannerList',
    name: 'homeWindowTypeBannerList',
    meta: {
      title: '首页橱窗分类详情',
      parentTitle: '首页橱窗分类',
      activeMenu: '/operate/homeWindowType'
    },
    component: () => import('@/views/operate/page/homeWindowTypeBannerList')
  },
  {
    path: '/operate/homeWindowTypeBannerDetail',
    name: 'homeWindowTypeBannerDetail',
    meta: {
      title: '首页橱窗分类详情',
      parentTitle: '首页橱窗分类',
      activeMenu: '/operate/homeWindowType'
    },
    component: () => import('@/views/operate/page/homeWindowTypeBannerDetail')
  },
  {
    path: '/operate/trendSearchList',
    name: 'trendSearchList',
    meta: {
      title: '搜索热词管理'
    },
    component: () => import('@/views/operate/page/trendSearchList')
  },
  {
    path: '/operate/trendSearchNew',
    name: 'trendSearchNew',
    meta: {
      title: '搜索配置（新）'
    },
    component: () => import('@/views/operate/page/trendSearchNew')
  },
  {
    path: '/operate/trendSearchDetail',
    name: 'trendSearchDetail',
    meta: {
      title: '搜索热词详情',
      parentTitle: '搜索热词管理',
      activeMenu: '/operate/trendSearchList'
    },
    component: () => import('@/views/operate/page/trendSearchDetail')
  },
  {
    path: '/operate/h5PositivelinkStatistics',
    name: 'h5PositivelinkStatistics',
    meta: {
      title: 'H5正向链路统计'
    },
    component: () => import('@/views/operate/page/h5PurchaseCardManagement/h5PositivelinkStatisticsNext')
  },
  {
    path: '/operate/h5PositivelinkStatisticsNew',
    name: 'h5PositivelinkStatisticsNew',
    meta: {
      title: 'H5正向链路统计'
    },
    component: () => import('@/views/operate/page/h5PurchaseCardManagement/h5PositivelinkStatisticsNextNew')
  },

  {
    path: '/operate/h5PositivelinkStatisticsDetail',
    name: 'h5PositivelinkStatisticsDetail',
    meta: {
      title: 'H5正向链路统计详情',
      parentTitle: 'H5正向链路统计',
      activeMenu: '/operate/h5PositivelinkStatistics'
    },
    component: () => import('@/views/operate/page/h5PurchaseCardManagement/h5PositivelinkStatisticsDetail')
  },
  {
    path: '/operate/h5PositivelinkStatisticsDetailNew',
    name: 'h5PositivelinkStatisticsDetailNew',
    meta: {
      title: 'H5正向链路统计详情新',
      parentTitle: 'H5正向链路统计',
      activeMenu: '/operate/h5PositivelinkStatisticsNew'
    },
    component: () => import('@/views/operate/page/h5PurchaseCardManagement/h5PositivelinkStatisticsDetailNew')
  },
  {
    path: '/operate/h5RefundStatistics',
    name: 'h5RefundStatistics',
    meta: {
      title: '站内购卡退款统计'
    },
    component: () => import('@/views/operate/page/h5PurchaseCardManagement/h5RefundStatistics')
  },
  {
    path: '/operate/stationComplaintData',
    name: 'stationComplaintData',
    meta: {
      title: '站内投诉数据'
    },
    component: () => import('@/views/operate/page/h5PurchaseCardManagement/stationComplaintData')
  },
  {
    path: '/operate/refundSecondaryEntry',
    name: 'refundSecondaryEntry',
    meta: {
      title: '退款申请后二次进入',
      parentTitle: '退款申请后二次进入',
      activeMenu: '/operate/refundSecondaryEntry'
    },
    component: () => import('@/views/operate/page/h5PurchaseCardManagement/refundSecondaryEntry')
  },
  {
    path: '/operate/operationsList',
    name: 'operationsList',
    meta: {
      title: '运营位置管理'
    },
    component: () => import('@/views/operate/page/operationsManagement/list.vue')
  },
  {
    path: '/operate/operationsListDetail',
    name: 'operationsListDetail',
    meta: {
      title: '运营位置管理详情',
      activeMenu: '/operate/operationsList'
    },
    component: () => import('@/views/operate/page/operationsManagement/detail.vue')
  },
  {
    path: '/operate/operationsStatistics',
    name: 'operationsStatistics',
    meta: {
      title: '运营位统计'
    },
    component: () => import('@/views/operate/page/operationsManagement/operationsStatistics.vue')
  },
  {
    path: '/operate/operationsStatisticsDetail',
    name: 'operationsStatisticsDetail',
    meta: {
      title: '运营位时段统计',
      activeMenu: '/operate/operationsStatistics'
    },
    component: () => import('@/views/operate/page/operationsManagement/operationsStatisticsDetail.vue')
  },
  {
    path: '/operate/operationsCustomLabel',
    name: 'operationsCustomLabel',
    meta: {
      title: '用户标签'
    },
    component: () => import('@/views/operate/page/operationsManagement/customLabel.vue')
  }
]

export default operate
