<template>
  <page :request="request" :list="list" table-title="加油订单统计" :table-pagination-state="false">
    <div slot="titleContainer" class="title-container-box">
      <el-button plain type="warning" size="small" icon="el-icon-upload" @click="handUpload">导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import {
  get_bury_count_oil_selectStatis,
  export_bury_count_oil_selectStatis
} from '@/api/buryData'
import { formItemType } from '@/config/sysConfig'
import moment from 'moment'

const date = {
  startDate: moment()
    .subtract(6, 'days')
    .format('YYYY-MM-DD'),
  endDate: moment().format('YYYY-MM-DD')
}

export default {
  components: {
    page
  },
  props: {},
  data() {
    return {
      listQuery: {},
      request: {
        getListUrl: data => {
          this.listQuery = { ...date, ...data }
          return Promise.all([
            get_bury_count_oil_selectStatis(this.listQuery)
          ]).then(res => {
            return Promise.resolve(res[0])
          })
        }
      },
      list: [
        {
          key: 'time',
          title: '日期',
          search: true,
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startDate', 'endDate'],
          val: [
            moment()
              .subtract(6, 'd')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date'
        },

        {
          title: '活动页访问pv/uv',
          key: 'activityPageVisit'
        },
        {
          title: '油站列表点击pv/uv',
          key: 'oilStationListClick'
        },
        {
          title: '完成升级用户',
          key: 'finishUpGrade'
        },
        {
          title: '完成加油用户',
          key: 'finishRefuel'
        },
        {
          title: '访问-升级转化',
          key: 'visitToUpGrade'
        },
        {
          title: '点击-完成加油转化',
          key: 'clickToRefuel'
        }
      ]
    }
  },
  watch: {},
  mounted() {},
  methods: {
    handUpload() {
      window.location.href = export_bury_count_oil_selectStatis({
        ...this.listQuery,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="" scoped>
</style>
