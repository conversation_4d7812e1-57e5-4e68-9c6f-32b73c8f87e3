/*
 * @Author: 陈小豆
 * @Date: 2025-03-06 16:25:10
 * @LastEditors: 陈小豆
 * @LastEditTime: 2025-03-07 16:24:02
 */
// 应用名称列表
import { getJjjp, getZt } from '@/libs/axios.package'

export const count_channel_application_list = params => {
  return getJjjp(`/application/info`, params, null)
}

export const undertakeSelector = params => {
  return getJjjp(`/channel/undertake/selector`, params, null)
}

export const mediaAll = params => {
  return getJjjp(`/mediaDelivery/advflow/media/all`, params, null)
}

// 根据渠道类型获取投放平台
export const mediaAllByType = params => {
  return getJjjp(`/mediaDelivery/advflow/media/all/byType`, params, null)
}

export const getAppBindEvents = params => {
  return getJjjp(`/mediaDelivery/advflow/trackEvent/getAppBindEvents`, params, null)
}

export const getPayScene = params => {
  return getJjjp(`/countPay/payScene`, params, null)
}

export const channeldetail = params => {
  return getJjjp(`/channel/detail`, params, null)
}