<template>
  <div>
    <el-row class="search-row" type="flex" style="padding-bottom: 30px;">
      <el-col class="search-col" style="display: flex;flex-wrap: wrap">
        <span class="col">
          <label class="name">商品名称：</label>
          <el-input v-model="parameterObj.name" class="search-maxInput" clearable placeholder="请输入商品名称" />
        </span>
        <span>
          <label class="name">分类：</label>
          <el-select
            v-model="parameterObj.level1Id"
            clearable
            placeholder="全部"
            style="margin-right: 10px;"
          >
            <el-option v-for="(type,index) in typeList" :key="index" :label="type.name" :value="type.id" />
          </el-select>
        </span>
        <span>
          <label class="name">是否上架：</label>
          <el-select
            v-model="parameterObj.status"
            clearable
            placeholder="全部"
            style="margin-right: 10px;width: 150px;"
          >
            <el-option label="上架" value="up" />
            <el-option label="下架" value="down" />
          </el-select>
          <el-button type="primary" class="search-btn" icon="el-icon-search" @click="getData()">查询</el-button>
        </span>
      </el-col>
    </el-row>
    <section>
      <div class="tab-head">
        <span class="title">数据列表</span>
        <!-- <el-button type="warning" size="small" icon="el-icon-download" plain>导出数据</el-button> -->
      </div>
      <el-table
        v-loading="DataLoading"
        :data="tableData"
        border
        style="width: 100%;margin-bottom: 30px;"
      >
        <el-table-column label="序列" type="index" />
        <el-table-column prop="id" label="产品编号" />
        <el-table-column prop="goodsType" label="商品分类" />
        <el-table-column prop="name" label="商品名称" />
        <el-table-column prop="price" label="会员价" />
        <el-table-column prop="thirdPrice" label="非会员价" />
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.status=='up'" style="color:#1ABC9C">正常</span>
            <span v-else style="color:#FF7F50">已下架</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button type="warning" size="mini" plain @click="linkDetails('edit',scope.row.id)">编辑</el-button>
            <el-button type="danger" size="mini" plain @click="del(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 70, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </section>
  </div>
</template>

<script>
import {
  rechargeList,
  delRecharge,
  selectGoodsTypeName
} from '@/api/goods'
export default {
  props: {
    status: {
      type: String,
      default: ''
    },
    // 分类数据
    typeList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      parameterObj: {
        name: '',
        status: '',
        level1Id: ''
      },
      DataLoading: false,
      tableData: [1],
      total: 0,
      pageSize: 10,
      currentPage: 1
    }
  },
  mounted() {
  },
  methods: {
    /**
     * 删除数据
     */
    del(id) {
      this.$confirm('是否确认删除？','操作提示')
        .then(() => {
          delRecharge(id).then(res => {
            if (res.code == 200) {
              this.$message({
                message: '删除商品成功',
                type: 'success'
              })
              this.getData()
            }
          })
        })
        .catch(() => {
          // 取消
        })
    },
    /**
     * 获取列表
     */
    getData() {
      this.DataLoading = true
      rechargeList({
        name: this.parameterObj.name,
        status: this.parameterObj.status,
        level1Id: this.parameterObj.level1Id,
        pageNumber: this.currentPage,
        pageSize: this.pageSize
      }).then(res => {
        this.DataLoading = false
        if (res.code == 200) {
          this.tableData = res.data
          this.total = res.totalCount
        } else {
          this.$message.error(res.message)
        }
      })
    },

    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },
    linkDetails(type, id) {
      this.$router.push({
        path: '/commodity/fling_detail',
        query: {
          type: type,
          id: id
        }
      })
    },
    refresh() {
      this.getData()
    }
  }
}
</script>

<style scoped>
.table-img {
  width: 50px;
  height: 50px;
}
</style>
