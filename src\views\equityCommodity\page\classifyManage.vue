<template>
  <!-- 权益商品分类管理 -->
  <div>
    <el-row class="search-row" type="flex" style="margin: 10px 0 30px;">
      <el-col class="search-col">
        <label class="name">分类名称：</label>
        <el-input v-model="parameterObj.title" class="search-maxInput" clearable placeholder="请输入分类名称" style="width: 200px;" />
        <el-button type="primary" class="search-btn" icon="el-icon-search" @click="getData()">查询</el-button>
        <el-button type="primary" icon="el-icon-circle-plus-outline" @click="linkDetails('add')">新增</el-button>
      </el-col>
    </el-row>
    <section>
      <div class="tab-head">
        <span class="title"> 数据列表</span>
      </div>
      <el-table
        v-loading="DataLoading"
        :data="tableData"
        border
        style="

        width: 100%;margin-bottom: 30px;"
      >
        <el-table-column label="序列" type="index" />
        <el-table-column prop="title" label="分类名称" />
        <el-table-column prop="sort" label="排序" />
        <el-table-column prop="createTime" label="创建时间">
          <template slot-scope="scope">
            {{ scope.row.createTime | formatDate("yyyy-MM-dd hh:mm") }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            {{ scope.row.status == 0 ? '上架' : '下架' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button size="small" type="primary" plain @click="linkDetails('edit',scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 70, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </section>

    <!-- 新增菜单/菜单详情 -->
    <section>
      <el-dialog
        :title="isType === 'add' ? '新增' :'编辑'"
        width="500px"
        :visible.sync="dialogFormVisible"
        :show-close="false"
        :close-on-click-modal="false"
        :before-close="onHide"
      >
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm">
          <el-form-item label="分类名称：" prop="title">
            <el-input v-model="ruleForm.title" style="width:300px;" placeholder="请输入分类名称" />
          </el-form-item>
          <el-form-item label="排序：" prop="sort">
            <el-input v-model="ruleForm.sort" style="width:300px;" placeholder="请输入排序值" />
          </el-form-item>
          <el-form-item label="状态：" prop="status">
            <el-radio-group v-model="ruleForm.status">
              <el-radio :label="0">启用</el-radio>
              <el-radio :label="1">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="roleUpdated('0','ruleForm')">取 消</el-button>
          <el-button type="primary" :loading="btn_disabled" :disabled="btn_disabled" @click="roleUpdated('1','ruleForm')">确 定</el-button>
        </div>
      </el-dialog>
    </section>
  </div>
</template>

<script>
import {
  goods_virtual_category_list,
  goods_virtual_category_add,
  goods_virtual_category_update
} from '@/api/equityCommodity'
export default {
  data() {
    return {
      total: 0,
      pageSize: 10,
      currentPage: 1,
      parameterObj: {
        title: ''
      },
      DataLoading: false,
      tableData: [],
      isType: '',
      dialogFormVisible: false,
      btn_disabled: false,
      ruleForm: {
        title: '',
        status: '',
        sort: ''
      },
      rules: {
        title: [
          { required: true, message: '分类名称不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态不能为空', trigger: 'change' }
        ],
        sort: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      },
      itemId: ''
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      this.DataLoading = true
      goods_virtual_category_list({
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
        title: this.parameterObj.title
      }).then(res => {
        this.DataLoading = false
        if (res.code === 0) {
          this.tableData = res.data
          this.total = res.totalCount
        }
      })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },
    // 【提交】 新增 | 修改
    roleUpdated(isShow, formName) {
      const thar = this
      if (isShow === '0') { // 取消
        thar.dialogFormVisible = false
        thar.delForm()
      } else {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            const obj = thar.ruleForm
            this.btn_disabled = true
            if (thar.isType === 'add') { // 新增
              goods_virtual_category_add(obj).then(res => {
                this.btn_disabled = false
                if (res.code === 0) {
                  thar.delForm()
                  thar.getData()
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  })
                  thar.dialogFormVisible = false
                } else {
                  this.$message.error(res.message)
                }
              })
            } else {
              obj.id = thar.itemId
              goods_virtual_category_update(obj).then(res => {
                this.btn_disabled = false
                if (res.code === 0) {
                  thar.delForm()
                  thar.getData()
                  this.$message({
                    message: '修改成功',
                    type: 'success'
                  })
                  thar.dialogFormVisible = false
                } else {
                  this.$message.error(res.message)
                }
              })
            }
          }
        })
      }
    },
    // 【打开】 新增 | 修改
    linkDetails(type, item) {
      const thar = this
      thar.isType = type
      thar.dialogFormVisible = true
      if (thar.isType === 'edit') {
        // 获取详情
        thar.itemId = item.id
        thar.ruleForm.title = item.title
        thar.ruleForm.status = item.status
        thar.ruleForm.sort = item.sort
      }
    },
    delForm() {
      // 清空新增时所填内容
      this.$refs.ruleForm.clearValidate() // 移除校验结果
      this.ruleForm = this.$utils.removeObj(this.ruleForm)
      this.itemId = ''
    },
    delParameter() {
      // 清空查询参数内容
      this.parameterObj = this.$utils.removeObj(this.parameterObj)
    },
    onHide() {
      this.dialogFormVisible = false
      this.delForm()
    }

  }
}
</script>

<style scoped>

</style>
