<template>
  <!-- 0元购数据统计 -->
  <div>
    <el-tabs v-model="activeTabName" type="card" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.name"
      >
        <page
          v-if="activeTabName == item.name"
          :request="request"
          :list="list"
          table-title="0元购数据统计"
        >
          <div slot="searchContainer" style="display: inline-block">
            <el-button
              plain
              type="warning"
              size="small"
              icon="el-icon-download"
              @click="handUpload"
            >
              导出数据
            </el-button>
          </div>
        </page>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  EXPORT_COUNT_ZERO_ACTIVITY,
  GET_AERO_ACTIVITY_LIST
} from '@/api/activity'
import page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import moment from 'moment'
import { count_channel_application_list } from '@/api/NewChannel'

export default {
  components: {
    page
  },
  data() {
    return {
      tabs: [
        { label: '集合页', name: '1' },
        { label: '固定入口', name: '2' },
        { label: '商品列表', name: '3' },
        { label: '外卖数据', name: '4' }
      ],
      activeTabName: '1',
      listQuery: {
        startDate: moment()
          .subtract(6, 'd')
          .format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        siteId: '',
      },
      request: {
        getListUrl:async data => {
          this.listQuery = {
            ...this.listQuery,
            ...data,
            location: this.activeTabName
          }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = res.data
                this.listQuery.siteId = res.data[0].siteId
              }
            })
          }
          return GET_AERO_ACTIVITY_LIST(this.listQuery)
        }
      },
      siteIds: [],
    }
  },
  computed: {
    list() {
      return [
        {
          key: 'date1',
          title: '时间',
          type: formItemType.datePickerDaterangeGai,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          tableHidden: true,
          search: true,
          val: [
            moment()
              .subtract(6, 'd')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ]
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId',
          },
          val:this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable:false,
          tableHidden: true,
        },
        {
          title: '日期',
          key: 'date',
          type: formItemType.datePickerDaterangeGai,
          options: {
            format: 'YYYY-MM-DD'
          },
          tableView: tableItemType.tableView.date
        },
        {
          title: '有资格登录app用户数',
          key: 'joinRuleCount'
        },
        {
          title: '页面访问PV/UV',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.pagePv}/${data.pageUv} `)
          },
          tableHidden: this.activeTabName == 4
        },
        {
          title: '外卖页面访问PV/UV',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.takeOut}/${data.takeOutUv} `)
          },
          tableHidden: this.activeTabName != 4
        },
        {
          title: '商品详情访问PV/UV',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.productDetail}/${data.productDetailUv} `)
          },
          tableHidden: this.activeTabName == 4 // 不包括外卖
        },
        {
          title: '购买点击PV/UV',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.buy}/${data.buyUv} `)
          },
          tableHidden: this.activeTabName == 4 // 不包括外卖
        },
        {
          title: '点击购买PV/UV',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.takeOutBottom}/${data.takeOutBottomUv} `)
          },
          tableHidden: this.activeTabName != 4
        },
        {
          title: '完成用户',
          key: 'finishCount'
        },
        {
          title: '访问-详情',
          key: 'visitDetailRate',
          tableHidden: this.activeTabName == 4 // 不包括外卖
        },
        {
          title: '访问-点击',
          key: 'visitClickRate',
          tableHidden: this.activeTabName != 4 // 不包括外卖
        },
        {
          title: '购买-完成转化',
          key: 'clickFinishRate',
          tableHidden: this.activeTabName == 4 // 不包括外卖
        },
        {
          title: '购买-完成转化', // 外卖
          key: 'takeOutVisitFinishRate',
          tableHidden: this.activeTabName != 4 // 不包括外卖
        },
        {
          title: '访问-完成转化',
          key: 'visitFinishRate'
        },
        {
          title: '升级会员弹窗曝光pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.popupPv}/${data.popupUv} `)
          },
          tableHidden: this.activeTabName != 3
        },

        {
          title: '立即升级按钮点击pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.clickPv}/${data.clickUv} `)
          },
          tableHidden: this.activeTabName != 3
        },
        {
          title: '曝光-升级点击转化',
          key: 'clickRate',
          tableHidden: this.activeTabName != 3
        },
        {
          title: '开通会员人数',
          key: 'userNumber',
          tableHidden: this.activeTabName != 3
        },
        {
          title: '外卖订单金额',
          key: 'takeOutAmount',
          tableHidden: this.activeTabName != 4
        },
        {
          title: '补贴金额',
          key: 'subsidyMoney'
        }
      ]
    }
  },
  methods: {
    handUpload() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_COUNT_ZERO_ACTIVITY({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    handleClick() {
      sessionStorage.setItem(
        'mallZeroDataStatisticsTabActive',
        this.activeTabName
      )
      this.listQuery.location = ''
      this.reFresh = false
      this.$nextTick(() => {
        this.reFresh = true
      })
    }
  }
}
</script>

<style scoped></style>
