const lotteryManage = [{
  path: '/lotteryManage/adverConfig',
  name: 'adverConfig',
  meta: {
    title: '广告配置'
  },
  component: resolve => require(['@/views/lotteryManage/page/adverConfig'], resolve)
}, {
  path: '/lotteryManage/lotteryConfig',
  name: 'lotteryConfig',
  meta: {
    title: '抽奖配置'
  },
  component: resolve => require(['@/views/lotteryManage/page/lotteryConfig'], resolve)
}, {
  path: '/lotteryManage/lotteryStatics',
  name: 'lotteryStatics',
  meta: {
    title: '抽奖统计'
  },
  component: resolve => require(['@/views/lotteryManage/page/lotteryStatics'], resolve)
}, {
  path: '/lotteryManage/lotteryConfigUpdate',
  name: 'lotteryConfigUpdate',
  meta: {
    title: '抽奖配置详情',
    parentTitle: '抽奖配置',
    activeMenu: '/lotteryManage/lotteryConfig'
  },
  component: resolve => require(['@/views/lotteryManage/page/lotteryConfigUpdate'], resolve)
}, {
  path: '/lotteryManage/lotteryStaticsByDate',
  name: 'lotteryStaticsByDate',
  meta: {
    title: '大盘详情',
    parentTitle: '抽奖统计',
    activeMenu: '/lotteryManage/lotteryStatics'
  },
  component: resolve => require(['@/views/lotteryManage/page/lotteryStaticsByDate'], resolve)
}, {
  path: '/lotteryManage/lotteryStaticsByPage',
  name: 'lotteryStaticsByPage',
  meta: {
    title: '抽奖页详情',
    parentTitle: '抽奖统计',
    activeMenu: '/lotteryManage/lotteryStatics'
  },
  component: resolve => require(['@/views/lotteryManage/page/lotteryStaticsByPage'], resolve)
}, {
  path: '/lotteryManage/obtainRewardList',
  name: 'obtainRewardList',
  meta: {
    title: '参与记录'
  },
  component: resolve => require(['@/views/lotteryManage/page/obtainRewardList'], resolve)
}, {
  path: '/lotteryManage/exchangeList',
  name: 'obtainRewardList',
  meta: {
    title: '抽奖开奖列表'
  },
  component: resolve => require(['@/views/lotteryManage/page/exchangeList'], resolve)
}
]
export default lotteryManage
