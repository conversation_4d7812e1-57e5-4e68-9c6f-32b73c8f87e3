import { put, get, post, del } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/*
 * 用户属性列表 分页列表
 *
 */
export const USER_ATTR_LIST = obj => {
  return get('/userAtt/page', obj)
}

/*
 * 用户属性列表 更新
 *
 */
export const USER_ATTR_UPDATE = obj => {
  return post('/userAtt/update', obj)
}

/*
 * 用户属性列表 详情
 *
 */
export const USER_ATTR_DETAIL = obj => {
  return get('/userAtt/' + obj.id)
}

/*
 * 用户属性标记 分页列表
 *
 */
export const USER_ATTR_FLAG_LIST = obj => {
  return get('/userAttFlag/page', obj)
}

/*
 * 用户属性标记 更新
 *
 */
export const USER_ATTR_FLAG_UPDATE = obj => {
  return post('/userAttFlag/update', obj)
}

/*
 * 用户属性标记 详情
 *
 */
export const USER_ATTR_FLAG_DETAIL = obj => {
  return get('/userAttFlag/' + obj.id)
}

/*
 * 用户策略干扰 分页列表
 *
 */
export const USER_ATTR_STRATEGY_LIST = obj => {
  return get('/userAttStrategy/page', obj)
}

/*
 * 用户策略干扰 更新
 *
 */
export const USER_ATTR_STRATEGY_UPDATE = obj => {
  return post('/userAttStrategy/update', obj)
}

/*
 * 用户策略干扰 详情
 *
 */
export const USER_ATTR_STRATEGY_DETAIL = obj => {
  return get('/userAttStrategy/' + obj.id)
}

/*
 * 用户行为占比表
 *
 */

export const USER_BEHAVIOR_DATA = obj => {
  return get('/count/user/behavior/page', obj)
}

/*
 * 用户行为占比渠道表
 *
 */

export const USER_BEHAVIOR_CHANNEL_DATA = obj => {
  return get('/count/user/behavior/page/byChannelCode', obj)
}

/*
 * 用户属性占比表
 *
 */

export const USER_PROPERTY_DATA = obj => {
  return get('/count/userProperty/pageList', obj)
}

/*
 * 用户属性占比渠道表
 *
 */

export const USER_PROPERTY_CHANNEL_DATA = obj => {
  return get('/count/userProperty/channel/pageList', obj)
}

/*
 * 用户属性占比渠道下沉表
 *
 */

export const USER_PROPERTY_MEDIA_DATA = obj => {
  return get('/count/userProperty/media/pageList', obj)
}

/*
 * 用户属性占比媒体下沉表
 *
 */

export const USER_PROPERTY_MEDIA_DOWN_DATA = obj => {
  return get('/count/userProperty/media/adv/pageList', obj)
}

/*
 * 风险用户占比表
 *
 */

export const USER_RISK_DATA = obj => {
  return get('/count/userRisk/pageList', obj)
}

/*
 * 风险用户占比渠道表
 *
 */

export const USER_RISK_CHANNEL_DATA = obj => {
  return get('/count/userRisk/channel/pageList', obj)
}

/*
 * 风险用户占比渠道下沉表
 *
 */

export const USER_RISK_MEDIA_DATA = obj => {
  return get('/count/userRisk/media/pageList', obj)
}

/*
 * 风险用户占比媒体下沉表
 *
 */
export const USER_RISK_MEDIA_DOWN_DATA = obj => {
  return get('/count/userRisk/media/adv/pageList', obj)
}

// 规则添加/修改
export const riskRuleAdd = obj => {
  return post('/riskRule/add', obj)
}
// 规则添加/修改
export const riskRuleEdit = obj => {
  return post('/riskRule/update', obj)
}
// 规则详情
export const riskRuleDetail = id => {
  return get(`riskRule/detail/${id}`)
}

// 风控规则分页查询
export const riskRuleList = obj => {
  return get('/riskRule/page', obj)
}
// 风控规则All
export const riskRuleListAll = obj => {
  return get('/riskRule/all  ', obj)
}

// 风控命中记录查询
export const riskHitRecordList = obj => {
  return get('/riskHitRecord/page', obj)
}

// 风控命中记录查询导出
export const EXPORT_PAGE_RECORD_EXPORT = data =>
  CONSTANT.publicPath + '/riskHitRecord/page/export?' + qs.stringify(data)