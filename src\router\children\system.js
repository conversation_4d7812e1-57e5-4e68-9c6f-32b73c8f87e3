/*
 * 系统管理子路由
 * */

const system = [
  {
    path: '/system/account',
    name: 'account',
    meta: {
      title: '系统账户'
    },
    // component: () => import("@/views/system/page/account")
    component: () => import('@/views/system/page/account')
  },
  {
    path: '/system/role',
    name: 'role',
    meta: {
      title: '系统角色'
    },
    // component: () => import("@/views/system/page/role")
    component: () => import('@/views/system/page/role')
  },
  {
    path: '/system/permission',
    name: 'permission',
    meta: {
      title: '系统权限'
    },
    // component: () => import("@/views/system/page/permission")
    component: () => import('@/views/system/page/permission')
  },
  {
    path: '/system/menu',
    name: 'menu',
    meta: {
      title: '系统菜单'
    },
    // component: () => import("@/views/system/page/menu")
    component: () => import('@/views/system/page/menu')
  },
  {
    path: '/system/ip',
    name: 'ip',
    meta: {
      title: 'IP白名单'
    },
    // component: () => import("@/views/system/page/menu")
    component: () => import('@/views/system/page/ip')
  },
  {
    path: '/system/argument',
    name: 'argument',
    meta: {
      title: '参数设置'
    },
    component: () => import('@/views/system/page/argument')
  },
  {
    path: '/system/argumentRetrun',
    name: 'argumentRetrun',
    meta: {
      title: '多次退款策略管理'
    },
    component: () => import('@/views/system/page/argumentRetrun')
  },
  {
    path: '/system/merchantsManage',
    name: 'merchantsManage',
    meta: {
      title: '商户号管理'
    },
    component: () => import('@/views/system/page/merchantsManage')
  },
  {
    path: '/system/jsapiManage',
    name: 'jsapiManage',
    meta: {
      title: 'jsapi支付域名管理'
    },
    component: () => import('@/views/system/page/jsapiManage')
  },
  {
    path: '/system/alipayManage',
    name: 'alipayManage',
    meta: {
      title: '支付宝H5域名管理'
    },
    component: () => import('@/views/system/page/alipayManage')
  },
  {
    path: '/system/marketDataPermission',
    name: 'marketDataPermission',
    meta: {
      title: '大盘数据权限'
    },
    component: () => import('@/views/system/page/marketDataPermission')
  },
  {
    path: '/system/auditManagement',
    name: 'auditManagement',
    meta: {
      title: '审核管理'
    },
    component: () => import('@/views/system/page/auditManagement')
  },
  {
    path: '/system/lifecycleStatistics',
    name: 'LifecycleStatistics',
    meta: {
      title: '生命周期统计'
    },
    component: () => import('@/views/system/page/lifecycle/lifecycleStatistics.vue')
  },
  {
    path: '/system/lifecycleStatisticsDetail',
    name: 'LifecycleStatisticsDetail',
    meta: {
      title: '渠道生命周期统计',
      parentTitle: '生命周期统计',
      activeMenu: '/system/lifecycleStatistics'
    },
    component: () => import('@/views/system/page/lifecycle/lifecycleStatisticsDetail.vue')
  },
  {
    path: '/system/bottomGuideSetting',
    name: 'bottomGuideSetting',
    meta: {
      title: '底导屏蔽配置'
    },
    component: () => import('@/views/system/page/bottomGuideSetting.vue')
  }
]

export default system
