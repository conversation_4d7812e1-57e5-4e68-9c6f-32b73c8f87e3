import { get, post, put } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import { stringify } from 'qs'
/**
 * 渠道广告
 */

export const post_ad_channel = obj => {
  return post('/ad/channel', obj)
}

export const put_ad_channel = obj => {
  return put('/ad/channel', obj)
}

export const get_channel_page = obj => {
  return get('/ad/channel/page', obj)
}

export const get_adChannel_ById = id => {
  return get(`/ad/channel/${id}`)
}

export const post_advertising_import = obj => {
  return post(`/count/advertising/import`, obj)
}

export const get_count_advertising_channel = obj => {
  return get(`/count/advertising/channel/count`, obj)
}

export const export_count_advertising_channel = data =>
  CONSTANT.publicPath + '/count/advertising/channel/export?' + stringify(data)

export const get_count_advertising_channel_adcount = obj => {
  return get(`/count/advertising/channel/adCount`, obj)
}

export const export_count_advertising_channel_adcount = data =>
  CONSTANT.publicPath + '/count/advertising/channel/adExport?' + stringify(data)
