import { formItemType, tableItemType } from '@/config/sysConfig'
import { osList } from '@/qjjpViews/appVersion/basicParams'

const renderHeaders = (h, column, text) => {
  if (text && text != '') {
    return (
      <div>
        <span>{column.label}</span>
        <el-tooltip content={text}>
          <i class='el-icon-question' style='color:#409eff;margin-left:5px;font-size: 16px;'></i>
        </el-tooltip>
      </div>
    )
  } else {
    return (
      <div>
        <span>{column.label}</span>
      </div>
    )
  }
}
export default function(objectData) {
  return [
    {
      title: '日期',
      searchKey: 'date',
      pickerDay: 31,
      type: formItemType.rangeDatePicker,
      childKey: ['startDate', 'endDate'],
      options: {
        format: 'YYYY-MM-DD',
        valueFormat: 'yyyy-MM-dd'
      },
      options: {
        on: () => {
          return {
            change: e => {
              this.$set(this.listQuery, 'fixedDate', false)
            }
          }
        }
      },
      val: [this.listQuery.startDate, this.listQuery.endDate],
      search: true,
      tableHidden: true
    },
    {
      title: '渠道ID',
      type: formItemType.input,
      searchKey: 'channelCode',
      val: this.listQuery.channelCode || '',
      search: true,
      clearable: true,
      tableHidden: true
    },
    {
      title: '设备维度',
      search: true,
      key: 'deviceType',
      type: formItemType.select,
      tableView: tableItemType.tableView.text,
      val: this.listQuery.deviceType || '-1',
      tableHidden: true,
      list: [
        {
          label: '总',
          value: '-1'
        },
        {
          label: '老用户',
          value: '0'
        },
        {
          label: '新用户',
          value: '1'
        }
      ]
    },
    {
      title: '支付场景',
      key: 'paySceneCode',
      search: true,
      type: formItemType.select,
      tableView: tableItemType.tableView.text,
      val: this.listQuery.paySceneCode || '',
      tableHidden: true,
      list: objectData.paySceneList,
      listFormat: {
        label: 'name',
        value: 'code'
      }
    },
    {
      title: '承接页面',
      key: 'landingPageId',
      type: formItemType.select,
      tableView: tableItemType.tableView.text,
      list: objectData.undertakeList,
      tableHidden: true,
      listFormat: {
        label: 'name',
        value: 'id'
      },
      reg: ['required'],
      search: true,
      val: this.listQuery.landingPageId || ''
    },
    {
      title: '达标类型',
      key: 'noTrial',
      type: formItemType.select,
      tableView: tableItemType.tableView.text,
      search: true,
      tableHidden: true,
      val: this.listQuery.noTrial || '',
      list: [
        {
          label: '否',
          value: '0'
        },
        {
          label: '是',
          value: '1'
        }
      ]
    },
    {
      title: '优化师',
      key: 'adminId',
      type: formItemType.select,
      tableView: tableItemType.tableView.text,
      list: this.adminList,
      reg: ['required'],
      listFormat: {
        label: 'nickname',
        value: 'id'
      },
      multiple: true,
      search: this.haveQX&&!this.listQuery.edit,
      tableHidden: true,
      val: this.listQuery.adminId || ''
    },

    {
      title: '应用类型',
      key: 'os',
      type: formItemType.select,
      tableView: tableItemType.tableView.text,
      list: osList,
      reg: ['required'],
      search: true,
      tableHidden: true,
      val: this.listQuery.os || ''
    },


    {
      title: '优化师',
      key: 'adminId',
      type: formItemType.select,
      tableView: tableItemType.tableView.text,
      list: this.adminList,
      reg: ['required'],
      listFormat: {
        label: 'nickname',
        value: 'id'
      },
      multiple: false,
      search: this.haveQX&&this.listQuery.edit,
      tableHidden: true,
      val: this.listQuery.adminId || ''
    },
    {
      title: '应用名称',
      key: 'siteIds',
      type: formItemType.select,
      tableView: tableItemType.tableView.text,
      list: this.siteIdsList,
      listFormat: {
        label: 'name',
        value: 'siteId'
      },
      multiple: true,
      reg: ['required'],
      search: true,
      clearable: true,
      tableHidden: true,
      options: {
        on: () => {
          return {
            change: e => {
              const a = this.siteIdsList.filter(item => item.siteId == e)
              this.packageName = a[0].packageName
            }
          }
        }
      }
    },
    {
      title: '活动属性',
      key: 'sceneActivityAttributes',
      type: formItemType.select,
      tableView: tableItemType.tableView.text,
      list: this.hdsxList,
      reg: ['required'],
      listFormat: {
        label: 'name',
        value: 'id'
      },
      multiple: false,
      search: true,
      tableHidden: true
    },
  ]
}
