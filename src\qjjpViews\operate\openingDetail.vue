<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 14:53:10
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-06-03 17:22:46
-->
<template>
  <div id="styleDetail">
    <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="100px" class="demo-ruleForm">
      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>基础信息</span>
        </div>
        <div style="display: flex;">
          <el-form-item label="开场白名称" prop="styleName" :rules="addRules.common">
            <div :style="{width: '200px'}">
              <el-input v-model="addForm.styleName" placeholder="请输入开场白名称" />
            </div>
          </el-form-item>
        </div>
      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>开场白预设</span>
        </div>
        <div style="display:flex">
          <el-form-item label="性别预设" prop="sex" :rules="addRules.common">
            <el-select v-model="addForm.sex" placeholder="请选择性别预设" @change="sexChange">
              <el-option
                v-for="item in list1"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="开场白类型" prop="styleType" :rules="addRules.common">
            <el-select v-model="addForm.styleType" placeholder="请选择开场白类型">
              <el-option
                label="普通开场白"
                :value="1"
              />
              <el-option
                label="付费开场白"
                :value="2"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="分类名称" prop="styleClassify" :rules="addRules.common">
            <el-select v-model="addForm.styleClassify" placeholder="请选择分类名称">
              <el-option
                v-for="item in styleClassifyList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>

      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>开场白内容</span>
        </div>
        <el-form-item label="问题" prop="question" :rules="addRules.common">
          <el-input
            v-model="addForm.question"
            style="width:calc(100% - 80px)"
            placeholder="请输入问题"
            maxlength="200"
          />
          <span style="margin-left: 10px;">{{ addForm.question.length }}/200</span>
        </el-form-item>
        <el-form-item label="引导词" prop="guideContent" :rules="addRules.common">
          <el-input
            v-model="addForm.guideContent"
            type="textarea"
            :rows="3"
            placeholder="请输入引导词"
            maxlength="10000"
          />
          <span>{{ addForm.guideContent.length }}/10000</span>
        </el-form-item>
      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>模型信息</span>
        </div>
        <div style="display:flex">
          <el-form-item label="模型平台" prop="platform" :rules="addRules.common">
            <el-select v-model="addForm.platform" placeholder="请选择模型平台" @change="changePla">
              <el-option
                v-for="item in siteIds"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="addForm.platform&&addForm.platform!=''" label="模型名称" prop="modelId" :rules="addRules.common">
            <el-select v-model="addForm.modelId" placeholder="请选择模型名称" filterable>
              <el-option
                v-for="item in pla"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form_view2">
          <div class="form_view_title">
            <div class="title_line" /><span class="form_view_title_view2">模型自定义参数<el-tooltip
              content="模型支持的自定义参数，可辅助模型更好的进行回复"
              placement="top-start"
              :style="{color:'#409eff'}"
            >
              <i class="el-icon-question" style="font-size: 14px" />
            </el-tooltip></span>
          </div>
          <div style="display:flex">
            <el-form-item label="temperature" prop="temperature" :rules="addRules.common" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                temperature
                <el-tooltip
                  content="用于控制输出tokens的多样性，TopP值越大输出的tokens类型越丰富，取值范围0~1，未填写时默认值为1"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.temperature"
                  placeholder="请输入0-1的数值"
                  onkeyup="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1)value=1;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1)value=1;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.temperature=$event.target.value"
                />
              </div>
            </el-form-item>
            <el-form-item label="top_p" prop="topP" :rules="addRules.common" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                top_p
                <el-tooltip
                  content="用于控制输出tokens的多样性，TopP值越大输出的tokens类型越丰富，取值范围0~1，未填写时默认值为0.9"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.topP"
                  placeholder="请输入0-1的数值"
                  onkeyup="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1)value=1;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1)value=1;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.topP=$event.target.value"
                />
              </div>
            </el-form-item>
            <el-form-item label="top_k" prop="topK" :rules="addRules.common" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                top_k
                <el-tooltip
                  content="选择预测值最大的k个token进行采样，取值范围0-1000，0表示不生效，未填写时默认值为0"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.topK"
                  placeholder="请输入0-1000的数值"
                  onkeyup="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1000)value=1000;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1000)value=1000;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.topK=$event.target.value"
                />
              </div>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="temperature" prop="doSample" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                do_sample
                <el-tooltip
                  content="模型是否采样，未填写时将使用模型平台默认值"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-select v-model="addForm.doSample" placeholder="请选择是否采样">
                  <el-option
                    label="是"
                    value="true"
                  />
                  <el-option
                    label="否"
                    value="false"
                  />
                </el-select>
              </div>
            </el-form-item>
            <el-form-item label="top_p" prop="presencePenalty" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                presence_penalty
                <el-tooltip
                  content="存在惩罚，如果为正，值越大，模型谈论到新话题的概率越大，取值范围为 [-2.0, 2.0] ，未填写时将使用模型平台默认值  "
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.presencePenalty"
                  placeholder="请输入-2-2的数值"
                  onkeyup="this.value=this.value.replace(/[^0-9-.]|(?<=\..*)\.|(?<!\d)\.|(?!^)-/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>2)value=2;if(value<-2)value=-2;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^0-9-]|(?<=\..*)\.|(?<!\d)\.|(?!^)-/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>2)value=2;if(value<-2)value=-2;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.presencePenalty=$event.target.value"
                />
              </div>
            </el-form-item>
            <el-form-item label="top_k" prop="frequencyPenalty" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                frequency_penalty
                <el-tooltip
                  content="频率惩罚，如果为正，值越大，模型逐字重复同一行的概率越小，取值范围为 [-2.0, 2.0] ，未填写时将使用模型平台默认值"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.frequencyPenalty"
                  placeholder="请输入-2-2的数值"
                  onkeyup="this.value=this.value.replace(/[^0-9-.]|(?<=\..*)\.|(?<!\d)\.|(?!^)-/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>2)value=2;if(value<-2)value=-2;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^0-9-]|(?<=\..*)\.|(?<!\d)\.|(?!^)-/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>2)value=2;if(value<-2)value=-2;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.frequencyPenalty=$event.target.value"
                />
              </div>
            </el-form-item>
          </div>
        </div>
      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>开场白状态</span>
        </div>
        <el-form-item label="状态" prop="status" :rules="addRules.common">
          <el-switch
            v-model="addForm.status"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort" :rules="addRules.common">
          <div :style="{width: '200px'}">
            <el-input
              v-model="addForm.sort"
              class="noArrowInput"
              type="number"
              placeholder="请输入排序"
            />
          </div>
          <!-- <span>{{ addForm.guideContent.length }}/500</span> -->
        </el-form-item>
      </div>
      <div :style="{'text-align': 'right', width: '100%'}" class="button_view">
        <el-button @click="closeCheckQAShow">取消</el-button>
        <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { messageStyleListAdd, modelRepositoryplatform, modelRepositoryselect, categoryList } from '@/qjjpApi/operate'
export default {
  components: {
  },
  data() {
    return {
      pla: [],
      value1: '1',
      styleClassifyList: [],
      addForm: {
        styleClassify: '', // 风格分类

        id: '',
        styleName: '',
        sex: 1,
        question: '',
        guideContent: '',
        status: 1,
        styleType: 1,
        sort: '',
        modelId: '',
        platform: '',
        temperature: 1,
        topP: 0.9,
        topK: 0,
        doSample: '',
        presencePenalty: '',
        frequencyPenalty: '',
        category: 2
      },
      siteIds: [],
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      form: {

      },
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      isShow: true
    }
  },
  async mounted() {
    await this.getCategoryList()
    if (this.$route.query.id) {
      const paramsData = localStorage.getItem(`openingListDetail`) || null
      if (paramsData) {
        Object.assign(this.addForm, JSON.parse(paramsData))
        this.addForm.platform && this.changePla(this.addForm.platform)
        this.addForm.styleClassify = this.addForm.styleCategories ? this.addForm.styleCategories.map(n => n.categoryId)[0] : ''
        await this.getCategoryList()
        if (this.styleClassifyList.findIndex(n => n.value == this.addForm.styleClassify) == -1) {
          this.addForm.styleClassify = ''
        }
      }
    }

    await modelRepositoryplatform().then(res => {
      if (res.code === 200) {
        if (res.data && res.data.length) {
          this.siteIds = res.data
        }
      }
    })
  },
  methods: {
    async sexChange() {
      this.addForm.styleClassify = ''
      await this.getCategoryList()
    },
    analysis(str, data, matchArray = ['${', '}']) {
      const regX = RegExp(`\\${matchArray[0]}+.*?([\\s\\S]*?)${matchArray[1]}.*?`, 'g')
      return str.replace(regX, function(word, key) {
        const returnData = data[key] || null
        if (returnData) {
          return returnData
        }
        return word
      })
    },
    changePla(e) {
      modelRepositoryselect(e).then(res => {
        if (res.code == 200) {
          this.pla = res.data
        }
      })
    },
    closeCheckQAShow() {
      // this.isShow = false
      this.$router.push('/qjjp/operate/styleManage')
    },
    // 获取分类
    getCategoryList() {
      return new Promise(async(r) => {
        const { data } = await categoryList({ type: 2, sex: this.addForm.sex })
        this.styleClassifyList = data.map(item => {
          return {
            value: item.id,
            label: item.name
          }
        })
        this.$forceUpdate()
        r(true)
      })
    },
    handMessageStyleListAdd(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const params = { ...this.addForm }
          params.styleCategories = String(params.styleClassify).split(',').map(n => {
            return {
              categoryId: n
            }
          })
          messageStyleListAdd(params).then(res => {
            if (res.code == 200) {
              localStorage.removeItem(`openingListDetail`)
              this.$store.dispatch('tagsView/delView', this.$route)
              this.$message.success('添加成功')
              this.$router.push('/qjjp/operate/styleManage')
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#styleDetail{
    padding: 15px;
    background-color: rgb(189, 184, 184,0.2);
    .button_view{
      padding: 20px 20px;
      background-color: #fff;
      border-top:1px dashed #000000;
      ::v-deep .el-button{
        margin: 0 10px;
      }
    }
    .form_view,.form_view2{
      margin: 0 0px 0px;
        background-color: #fff;
        // border: 1px solid rgba(0,0,0,0.2);
        width: 100%;
        padding: 15px;
        border-radius: 5px;
        // margin-bottom: 20px;
        .form_view_title{
            margin-bottom: 20px;
            .title_line{
              width: 2px;
              height: 10px;
              background-color:#66b1ff ;
              display: inline-block;
              vertical-align: middle;
            }
            span{
              padding-left: 5px;
              vertical-align: middle;
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
    }
    .form_view2{
      background-color: rgb(189, 184, 184,0.1);
      .form_view_title_view2{
        font-size: 16px !important;
      }
    }
}
.dialogue{
  width: 100%;
  height: 150px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  resize: vertical;
  overflow: auto;
  ::v-deep .el-select{
    margin: 10px;
    width: 100px;
    height: 30px;
    display: inline-block;
  }
  ::v-deep .dialogue_input{
    width: calc(100% - 130px);
    height: 30px;
    display: inline-block;
    input{
      border-radius: 4px 4px 0 0;
      border: none;
      border-bottom: 1px solid #DCDFE6;
      &.el-input__inner:focus {
        border-color: #409EFF;
      }
    }
  }
}
</style>
