<!--
 * @Author: 陈小豆
 * @Date: 2024-05-30 14:21:05
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-05-31 10:27:08
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="自定义话术">
      <div slot="searchContainer" style="display: inline-block">
        <el-button
          plain
          icon="el-icon-circle-plus-outline"
          type="primary"
          size="small"
          @click="handleAdd"
        >新增</el-button>
        <el-button
          plain
          icon="el-icon-circle-plus-outline"
          type="primary"
          size="small"
          @click="dialogFormVisible=true"
        >默认文案配置</el-button>
      </div>
    </page>
    <SDialog
      :dialog-form-visible.sync="dialogFormVisible"
      :data="{title: '默认文案配置',width:'600px'}"
      :close-on-click-modal="false"
    >
      <customQuestion v-model="dialogFormVisible" />
    </SDialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { replyBankList/* ,messageStyleList*/, bankMessageStyle } from '@/qjjpApi/operate'
import SDialog from '@/components/restructure/dialog'
import customQuestion from './customQuestion'
export default {
  name: 'qjjpCustomScript',
  components: {
    page,
    SDialog,
    customQuestion
  },
  props: {},
  data() {
    return {
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      list2: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ],
      listQuery: {
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await replyBankList(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      },
      dialogFormVisible: false,
      styleList: []
    }
  },
  computed: {
    list() {
      return [
        // {
        //   title: '性别预设',
        //   key: 'sex',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.list1,
        //   listFormat: {
        //     label: 'name',
        //     value: 'id'
        //   },
        //   val: this.listQuery.siteId,
        //   reg: ['required'],
        //   search: true,
        //   clearable: true,
        //   tableHidden: true
        // },
        {
          title: '人设',
          key: 'styleIds',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.styleList,
          listFormat: {
            // label: 'styleName',
            // value: 'id'
            label: 'styleName',
            value: 'messageStyleId'
          },
          multiple: true,
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '搜索关键词',
          key: 'name',
          type: formItemType.input,
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '名称',
          key: 'name'
        },
        {
          title: '风格',
          key: 'styleName'
        },
        {
          title: '自定义文案数量',
          key: 'num'
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.$router.push({
                  path: '/qjjp/customScript/customScriptDetail',
                  query: {
                    id: params.id
                  }
                })
              }
            }
          ]
        }
      ]
    }
  },
  created() { this.getAllStyleList() },
  methods: {
    getAllStyleList() {
      return bankMessageStyle({ pageSize: 1000, pageNumber: 1 }).then(res => {
        // const { data } = res
        // if (data && data.records && data.records.length) {
        //   this.styleList = data.records
        // }
        this.styleList = res?.data ?? []
        return res
      })
    },
    handleAdd() {
      this.$router.push({
        path: '/qjjp/customScript/customScriptDetail'
      })
    },
    handleAddQuestion() {

    }
  }
}
</script>

<style lang="scss" scoped>
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
