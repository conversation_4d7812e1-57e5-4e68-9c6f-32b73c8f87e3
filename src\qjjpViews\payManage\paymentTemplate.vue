<!--
 * @Author: 陈小豆
 * @Date: 2024-08-08 11:43:49
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-11-20 18:10:44
-->
<template>
  <div id="paymentTemplates" class="position_sticky">
    <page v-loading="DataLoading" :request="request" :list="list" table-title="会员支付模板" :table-pagination-state="true">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain icon="el-icon-circle-plus-outline" type="warning" size="small"
          @click="handleAdd">新增</el-button>
      </div>
    </page>

    <el-dialog :visible.sync="dialogVisible" width="1000px" :before-close="cancel" :title="dialogParams.title">
      <editTemplate />
      <div style="text-align: center; margin-top: 20px">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>

    <el-drawer v-if="drawer" :visible.sync="drawer" direction="rtl" size="50%" :modal-append-to-body="false"
      :with-header="false" :wrapper-closable="false">
      <div class="close_button">
        <i class="el-icon-close" @click="drawer = false" />
      </div>
      <div class="drawer_package">
        <div class="drawer_title">
          <span>模型编辑/新增</span>
        </div>
        <div class="addForm_package">
          <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="130px" class="demo-ruleForm">
            <div class="form_view">
              <div class="form_view_title">
                <span>基础信息</span>
              </div>
              <div>
                <el-form-item label="应用名称" prop="siteId" :rules="addRules.common">
                  <el-select v-model="addForm.siteId" placeholder="请选择应用名称" @change="siteIdChange">
                    <el-option v-for="item in siteIdsList" :key="item.siteId" :label="item.name" :value="item.siteId" />
                  </el-select>
                </el-form-item>
                <el-form-item label="是否默认模板" prop="defaultTemplate" :rules="addRules.common">
                  <el-radio-group v-model="addForm.defaultTemplate" disabled>
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="模板名称" prop="name" :rules="addRules.common"
                  :style="{ 'display': 'inline-block', 'margin-right': '30px' }">
                  <div :style="{ width: '200px' }">
                    <el-input v-model="addForm.name" placeholder="请输入模板名称" maxlength="30" />
                  </div>
                </el-form-item>
                <span>{{ addForm.name ? addForm.name.length : 0 }}/30</span>
              </div>
              <el-form-item label="应用类型" prop="os" :rules="addRules.common"
                :style="{ 'display': 'inline-block', 'margin-right': '30px' }">
                <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                  应用类型
                  <el-tooltip content="配置对应应用类型后该面板下的sku将在客户端进行展示" placement="top-start" :style="{ color: '#409eff' }">
                    <i class="el-icon-question" style="font-size: 14px" />
                  </el-tooltip>
                </span>
                <div :style="{ width: '200px' }">
                  <el-select v-model="addForm.os" placeholder="请选择应用类型" :disabled="!!addForm.id"
                    @change="resetMembershipPackages">
                    <el-option v-for="item in osListForm" :key="item.value" :label="item.label" :value="item.value">{{
                      item.label }}</el-option>
                  </el-select>
                </div>
              </el-form-item>
            </div>
            <div class="form_view">
              <div class="form_view_title">
                <span>支付SKU信息</span>
              </div>
              <div v-loading="!sort">
                <div v-if="sort" id="categoryList">
                  <div v-for="(item, index) in addForm.membershipPackages" :key="'form_package' + index"
                    class="form_package_package">
                    <div class="form_package">
                      <div class="form_package_title_package">
                        <i class="el-icon-rank"
                          :style="{ cursor: 'pointer', 'font-size': '25px', 'padding-right': '30px', color: '#333333' }" />
                        <el-dropdown trigger="click" @command="(command) => { handleCommand(command, item) }">
                          <div class="form_package_title">
                            SKU{{ index + 1 }}({{ item.packageType == 0 ? '普通会员' : item.packageType == 1 ? '打卡会员' :
                              '续费会员' }})<i class="el-icon-caret-bottom"
                              :style="{ cursor: 'pointer', 'font-size': '20px' }" />
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-for="(el, elIndex) in vipTypeList" :key="'command' + elIndex"
                                :command="el.value" :class="[item.packageType === el.value && 'active-command-item']">{{
                                  el.label }}</el-dropdown-item>
                            </el-dropdown-menu>
                          </div>
                        </el-dropdown>
                      </div>
                      <!--ios相关内容-->
                      <template v-if="addForm.os === 0 || addForm.os === 1">
                        <template>
                          <div :style="{ display: 'inline-block', width: '390px' }">
                            <el-form-item label="iOS商品" :prop="`membershipPackages[${index}].appleProductId`"
                              :rules="addRules.common" :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                              <div :style="{ width: '200px' }">
                                <el-select v-model="item.appleProductId" placeholder="请选择iOS商品"
                                  @change="itemAppleProChange({ appleProductId: item.appleProductId, index, key: 'iosNormalParams' })">
                                  <template v-if="item.packageType == 2">
                                    <el-option v-for="el in productsIos.cycleProduct" :key="el.id"
                                      :label="el.productName" :value="el.id">{{ el.productName }}</el-option>
                                  </template>
                                  <template v-else>
                                    <el-option v-for="el in productsIos.normalProduct" :key="el.id"
                                      :label="el.productName" :value="el.id">{{ el.productName }}</el-option>
                                  </template>
                                </el-select>
                                
                                <el-button v-if="item.appleProductId" @click="toFn(item.appleProductId,item.packageType == 2?productsIos.cycleProduct:productsIos.normalProduct)" type="primary" size="mini" style="position: absolute;right: -5px;top: 50%;transform: translate(100%,-50%);">查看商品</el-button>
                              </div>
                            </el-form-item>
                          </div>
                          <div v-if="item.appleProductId && item.iosNormalParams"
                            :style="{ display: 'inline-block', width: '390px' }">
                            <el-form-item v-if="item.packageType != 2" label="iOS支付价"
                              :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                              <div :style="{ width: '200px' }">
                                <span class="disabed-input">{{ item.iosNormalParams.paymentPrice }}</span>
                              </div>
                            </el-form-item>
                          </div>
                        </template>
                        <!--订阅商品-->
                        <template v-if="item.packageType == 2 && item.appleProductId && item.iosNormalParams">
                          <div>
                            <div :style="{ display: 'inline-block', width: '390px' }">
                              <el-form-item label="iOS订阅金额"
                                :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                                <div :style="{ width: '200px' }">
                                  <span class="disabed-input">{{ item.iosNormalParams.paymentPrice }}</span>
                                </div>
                              </el-form-item>
                            </div>
                            <el-form-item label="iOS订阅周期"
                              :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                                iOS订阅周期
                                <el-tooltip content="iOS订阅不存在订阅总期数，用户取消订阅则订阅终止，该订阅周期为每次扣款周期" placement="top-start"
                                  :style="{ color: '#409eff' }">
                                  <i class="el-icon-question" style="font-size: 14px" />
                                </el-tooltip>
                              </span>
                              <div :style="{ width: '200px' }">
                                <span class="disabed-input">{{ item.iosNormalParams.subscriptionPeriod }}</span>
                              </div>
                            </el-form-item>
                            <el-form-item label="免费试用周期"
                              v-if="item.appleProductId && showIosInfo({ appleProductId: item.appleProductId }).trialStatus == 1"
                              :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                              <div :style="{ width: '200px' }">
                                <span class="disabed-input">{{ showIosInfo({
                                  appleProductId: item.appleProductId
                                }).subscriptionTrialPeriod
                                  }}</span>
                              </div>
                            </el-form-item>
                          </div>
                          <div v-if="item.iosNormalParams.discountPrice > 0"
                            :style="{ display: 'inline-block', width: '390px' }">
                            <el-form-item label="首期促销价" :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                              <div :style="{ width: '200px' }">
                                <span class="disabed-input">{{ item.iosNormalParams.discountPrice }}</span>
                              </div>
                            </el-form-item>
                          </div>
                        </template>

                      </template>
                      <!-- 普通会员 -->
                      <div v-if="item.packageType == 0">
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员名称" :prop="`membershipPackages[${index}][name]`"
                            :rules="addRules.common" :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.name" placeholder="请输入会员名称" maxlength="10" />
                            </div>
                          </el-form-item>
                          <span>{{ item.name ? item.name.length : 0 }}/10</span>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员类型" :prop="`membershipPackages[${index}][userType]`"
                            :rules="addRules.common" :style="{ display: 'inline-block' }">
                            <el-select v-model="item.userType" placeholder="请选择会员类型"
                              @change="clickUserType(item.userType, index)">
                              <el-option :key="1" label="永久会员" :value="1">永久会员</el-option>
                              <el-option :key="2" label="非永久会员" :value="2">非永久会员</el-option>
                            </el-select>
                          </el-form-item>
                        </div>
                        <div v-if="!(addForm.os === 1 && item.packageType == 2)"
                          :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item v-if="item.expiryDay != '-1'" label="会员有效期"
                            :prop="`membershipPackages[${index}][expiryDay]`" :rules="addRules.common"
                            :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.expiryDay" placeholder="请输入会员有效期，单位：天" />
                            </div>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员原价" :prop="`membershipPackages[${index}][originalPrice]`"
                            :rules="addRules.common" :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.originalPrice" placeholder="请输入会员原价，单位：元" />
                            </div>
                          </el-form-item>
                        </div>
                        <!--只有ios时隐藏-->
                        <div v-if="addForm.os !== 1" :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item :label="memberPayLabelAndDisabled.label"
                            :prop="`membershipPackages[${index}][paymentPrice]`" :rules="addRules.common"
                            :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.paymentPrice" :disabled="memberPayLabelAndDisabled.disabled"
                                placeholder="请输入会员支付价，单位：元" />
                            </div>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item :label="addForm.os === 0 ? '安卓月单价' : '月单价'"
                            :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input
                                :value="item.expiryDay == '-1' ? '-' : ((Number(item.paymentPrice) / Number(item.expiryDay) * 30).toFixed(2) + '/月')"
                                placeholder="请输入月单价" disabled />
                            </div>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员标签" :prop="`membershipPackages[${index}][tag]`"
                            :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.tag" placeholder="请输入会员标签" maxlength="10" />
                            </div>
                          </el-form-item>
                          <span>{{ item.tag ? item.tag.length : 0 }}/10</span>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员营销文案" :prop="`membershipPackages[${index}][content]`"
                            :style="{ 'display': 'inline-block' }">
                            <div :style="{ 'display': 'inline-block', 'margin-right': '10px', width: '200px' }">
                              <el-input v-model="item.content" placeholder="请输入会员营销文案" maxlength="20" />
                            </div>
                            <span>{{ item.content ? item.content.length : 0 }}/20</span>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="支付按钮角标文案" :prop="`membershipPackages[${index}][buttonMark]`"
                            :style="{ 'display': 'inline-block' }">
                            <div :style="{ 'display': 'inline-block', 'margin-right': '10px', width: '200px' }">
                              <el-input v-model="item.buttonMark" placeholder="请输入支付按钮角标文案" maxlength="20" />
                            </div>
                            <span>{{ item.buttonMark ? item.buttonMark.length : 0 }}/20</span>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="是否默认选中" :prop="`membershipPackages[${index}][defaultChoose]`"
                            :style="{ 'display': 'inline-block' }" :rules="addRules.common">
                            <el-radio-group v-model="item.defaultChoose"
                              @change="changeDefaultChoose(item.defaultChoose, item.checkInMember, item.expiryDay, index)">
                              <el-radio :label="false">否</el-radio>
                              <el-radio :label="true">是</el-radio>
                            </el-radio-group>
                            <!-- <span>{{ item.buttonMark?item.buttonMark.length:0 }}/20</span> -->
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="是否在支付面板中显示" label-width="180px"
                            :prop="`membershipPackages[${index}][checkInMemberShow]`"
                            :style="{ 'display': 'inline-block' }" :rules="addRules.common">
                            <span slot="label">
                              <span
                                style="font-size: 14px;color: #606266;font-weight: 700;line-height: 40px;">是否在支付面板中显示</span>
                              <el-tooltip style="color: #409EFF;" class="item-el-tooltip" placement="top">
                                <i class="el-icon-question" />
                                <div slot="content">
                                  -：默认选中显示<br />
                                  -：选中不显示时，对应的sku不会在常规的支付面板中出现（引导支付页抽屉、首页默认推荐弹窗、会员中心页、键盘页、定制人设支付抽屉、复登）<br />
                                  -：选中显示时，对应的SKU会在客户端的支付面板中显示，顺序值和所有会员混排
                                </div>
                              </el-tooltip>
                            </span>
                            <el-radio-group v-model="item.checkInMemberShow">
                              <el-radio :label="0">不显示</el-radio>
                              <el-radio :label="1">显示</el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </div>
                        <div :style="{ width: '450px' }">
                          <el-form-item label="是否参与会员权益活动" :prop="`membershipPackages[${index}][equityStatus]`"
                            :rules="addRules.common" label-width="180px" :style="{ 'display': 'inline-block' }">
                            <el-radio-group v-model="item.equityStatus">
                              <el-radio :label="0">不显示</el-radio>
                              <el-radio :label="1">显示</el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="会员权益活动" label-width="180px" :style="{ 'display': 'inline-block' }"
                            v-if="item.equityStatus == 1" :prop="`membershipPackages[${index}][equityActivitiesId]`"
                            :rules="addRules.common">
                            <el-select v-model="item.equityActivitiesId" placeholder="请选择会员权益活动"
                              :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                              <el-option :label="item.activityName" :value="item.id"
                                v-for='item in equityActivitiesList' :key="item.id">{{
                                  item.activityName }}</el-option>
                              <!-- <el-option :key="2" label="非永久会员" :value="2">非永久会员</el-option> -->
                            </el-select>
                            <span class="hdyl" @click="hdyl(item.equityActivitiesId)"
                              v-if='item.equityActivitiesId'>活动预览</span>
                          </el-form-item>
                          <el-form-item label="参与期数" label-width="180px" :style="{ 'display': 'inline-block' }"
                            :prop="`membershipPackages[${index}][equityGiveMonth]`" :rules="addRules.common"
                            v-if="item.equityStatus == 1">
                            <el-input v-model="item.equityGiveMonth" placeholder="请输入参与期数" maxlength="20"
                              oninput="value=value.replace(/[^0-9]/g,'')"
                              @blur="item.equityGiveMonth = $event.target.value" />
                            （单个权益会员订单可以参与的活动期数）
                          </el-form-item>
                          <!-- <el-form-item label="首次扣款前是否发放会员权益" label-width="220px" v-if="item.equityStatus == 1"
                            :prop="`membershipPackages[${index}][equityFirstDeductStatus]`" :rules="addRules.common"
                            :style="{ 'display': 'inline-block' }">
                            <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                              首次扣款前是否发放会员权益
                              <el-tooltip content="iOS订阅不存在订阅总期数，用户取消订阅则订阅终止，该订阅周期为每次扣款周期" placement="top-start"
                                :style="{ color: '#409eff' }">
                                <i class="el-icon-question" style="font-size: 14px" />
                              </el-tooltip>
                            </span>
                            <el-radio-group v-model="item.equityFirstDeductStatus">
                              <el-radio :label="0">否</el-radio>
                              <el-radio :label="1">是</el-radio>
                            </el-radio-group>
                          </el-form-item> -->

                        </div>
                      </div>
                      <!-- 普通会员 -->
                      <!-- 打卡会员 -->
                      <div v-if="item.packageType == 1">
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员名称" :prop="`membershipPackages[${index}][name]`"
                            :rules="addRules.common" :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.name" placeholder="请输入会员名称" maxlength="10" />
                            </div>
                          </el-form-item>
                          <span>{{ item.name ? item.name.length : 0 }}/10</span>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员类型" :prop="`membershipPackages[${index}][userType]`"
                            :rules="addRules.common" :style="{ display: 'inline-block' }">
                            <el-select v-model="item.userType" placeholder="请选择会员类型"
                              @change="clickUserType(item.userType, index)">
                              <el-option :key="1" label="永久会员" :value="1">永久会员</el-option>
                              <el-option :key="2" label="非永久会员" :value="2">非永久会员</el-option>
                            </el-select>
                          </el-form-item>
                        </div>
                        <div v-if="!(addForm.os === 1 && item.packageType == 2)"
                          :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item v-if="item.expiryDay != '-1'" label="会员有效期"
                            :prop="`membershipPackages[${index}][expiryDay]`" :rules="addRules.common"
                            :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.expiryDay" placeholder="请输入会员有效期，单位：天" />
                            </div>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员原价" :prop="`membershipPackages[${index}][originalPrice]`"
                            :rules="addRules.common" :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.originalPrice" placeholder="请输入会员原价，单位：元" />
                            </div>
                          </el-form-item>
                        </div>
                        <div v-if="addForm.os !== 1" :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item :label="memberPayLabelAndDisabled.label"
                            :prop="`membershipPackages[${index}][paymentPrice]`" :rules="addRules.common"
                            :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.paymentPrice" :disabled="memberPayLabelAndDisabled.disabled"
                                placeholder="请输入会员支付价，单位：元" />
                            </div>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item :label="addForm.os === 0 ? '安卓月单价' : '月单价'"
                            :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input
                                :value="item.expiryDay == '-1' ? '-' : ((Number(item.paymentPrice) / Number(item.expiryDay) * 30).toFixed(2) + '/月')"
                                placeholder="请输入月单价" disabled />
                            </div>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员标签" :prop="`membershipPackages[${index}][tag]`"
                            :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.tag" placeholder="请输入会员标签" maxlength="10" />
                            </div>
                          </el-form-item>
                          <span>{{ item.tag ? item.tag.length : 0 }}/10</span>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员营销文案" :prop="`membershipPackages[${index}][content]`"
                            :style="{ 'display': 'inline-block' }">
                            <div :style="{ 'display': 'inline-block', 'margin-right': '10px', width: '200px' }">
                              <el-input v-model="item.content" placeholder="请输入会员营销文案" maxlength="20" />
                            </div>
                            <span>{{ item.content ? item.content.length : 0 }}/20</span>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="支付按钮角标文案" :prop="`membershipPackages[${index}][buttonMark]`"
                            :style="{ 'display': 'inline-block' }">
                            <div :style="{ 'display': 'inline-block', 'margin-right': '10px', width: '200px' }">
                              <el-input v-model="item.buttonMark" placeholder="请输入支付按钮角标文案" maxlength="20" />
                            </div>
                            <span>{{ item.buttonMark ? item.buttonMark.length : 0 }}/20</span>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="打卡时间（天）" :prop="`membershipPackages[${index}][checkInDay]`"
                            :style="{ 'display': 'inline-block' }" :rules="addRules.common">
                            <div :style="{ 'display': 'inline-block', 'margin-right': '10px', width: '200px' }">
                              <el-input v-model="item.checkInDay" placeholder="请输入打卡时间"
                                onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" />
                            </div>
                            <!-- <span>{{ item.buttonMark?item.buttonMark.length:0 }}/20</span> -->
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="是否默认选中" :prop="`membershipPackages[${index}][defaultChoose]`"
                            :style="{ 'display': 'inline-block' }" :rules="addRules.common">
                            <el-radio-group v-model="item.defaultChoose"
                              @change="changeDefaultChoose(item.defaultChoose, item.checkInMember, item.expiryDay, index)">
                              <el-radio :label="false">否</el-radio>
                              <el-radio :label="true">是</el-radio>
                            </el-radio-group>
                            <!-- <span>{{ item.buttonMark?item.buttonMark.length:0 }}/20</span> -->
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="是否在支付面板中显示" label-width="180px"
                            :prop="`membershipPackages[${index}][checkInMemberShow]`"
                            :style="{ 'display': 'inline-block' }" :rules="addRules.common">
                            <span slot="label">
                              <span
                                style="font-size: 14px;color: #606266;font-weight: 700;line-height: 40px;">是否在支付面板中显示</span>
                              <el-tooltip style="color: #409EFF;" class="item-el-tooltip" placement="top">
                                <i class="el-icon-question" />
                                <div slot="content">
                                  1、默认选中不显示<br>2、选中不显示时，对应的sku不会在常规的支付面板中出现（引导支付页抽屉、首页默认推荐弹窗、会员中心页、键盘页、定制人设支付抽屉）<br>3、选中显示时，对应的SKU会在客户端的支付面板中显示，顺序值和常规会员混排
                                </div>
                              </el-tooltip>
                            </span>
                            <el-radio-group v-model="item.checkInMemberShow">
                              <el-radio :label="0">不显示</el-radio>
                              <el-radio :label="1">显示</el-radio>
                            </el-radio-group>
                          </el-form-item>

                        </div>
                        <div :style="{ width: '450px' }">
                          <el-form-item label="是否参与会员权益活动" label-width="180px" :style="{ 'display': 'inline-block' }">
                            <el-radio-group v-model="item.equityStatus">
                              <el-radio :label="0">不显示</el-radio>
                              <el-radio :label="1">显示</el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="会员权益活动" label-width="180px" :style="{ 'display': 'inline-block' }"
                            v-if="item.equityStatus == 1" :prop="`membershipPackages[${index}][equityActivitiesId]`"
                            :rules="addRules.common">
                            <el-select v-model="item.equityActivitiesId" placeholder="请选择会员权益活动"
                              :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                              <el-option :label="item.activityName" :value="item.id"
                                v-for='item in equityActivitiesList' :key="item.id">{{
                                  item.activityName }}</el-option>
                              <!-- <el-option :key="2" label="非永久会员" :value="2">非永久会员</el-option> -->
                            </el-select>
                            <span class="hdyl" @click="hdyl(item.equityActivitiesId)"
                              v-if='item.equityActivitiesId'>活动预览</span>
                          </el-form-item>
                          <el-form-item label="参与期数" label-width="180px" :style="{ 'display': 'inline-block' }"
                            v-if="item.equityStatus == 1" :prop="`membershipPackages[${index}][equityGiveMonth]`"
                            :rules="addRules.common">
                            <el-input v-model="item.equityGiveMonth" placeholder="请输入参与期数" maxlength="20"
                              oninput="value=value.replace(/^\D*([0-9]\d*)?.*$/,'$1')"
                              @blur="membershipPackages[index][equityGiveMonth] = $event.target.value" />
                            （单个权益会员订单可以参与的活动期数）
                          </el-form-item>
                          <!-- <el-form-item label="首次扣款前是否发放会员权益" label-width="220px" v-if="item.equityStatus == 1"
                            :style="{ 'display': 'inline-block' }">
                            <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                              首次扣款前是否发放会员权益
                              <el-tooltip content="iOS订阅不存在订阅总期数，用户取消订阅则订阅终止，该订阅周期为每次扣款周期" placement="top-start"
                                :style="{ color: '#409eff' }">
                                <i class="el-icon-question" style="font-size: 14px" />
                              </el-tooltip>
                            </span>
                            <el-radio-group v-model="item.equityFirstDeductStatus">
                              <el-radio :label="0">否</el-radio>
                              <el-radio :label="1">是</el-radio>
                            </el-radio-group>
                          </el-form-item> -->

                        </div>
                      </div>
                      <!-- 打卡会员 -->
                      <!-- 续费会员 -->
                      <div v-if="item.packageType == 2">
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员名称" :prop="`membershipPackages[${index}][name]`"
                            :rules="addRules.common" :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.name" placeholder="请输入会员名称" maxlength="10" />
                            </div>
                          </el-form-item>
                          <span>{{ item.name ? item.name.length : 0 }}/10</span>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员类型" :prop="`membershipPackages[${index}][userType]`"
                            :rules="addRules.common" :style="{ display: 'inline-block' }">
                            <el-select v-model="item.userType" placeholder="请选择会员类型"
                              @change="clickUserType(item.userType, index)">
                              <!-- <el-option
                                :key="1"
                                label="永久会员"
                                :value="1"
                              >永久会员</el-option> -->
                              <el-option :key="2" label="非永久会员" :value="2">非永久会员</el-option>
                            </el-select>
                          </el-form-item>
                        </div>
                        <div v-if="!(addForm.os === 1 && item.packageType == 2)"
                          :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item v-if="item.expiryDay != '-1'" label="会员有效期"
                            :prop="`membershipPackages[${index}][expiryDay]`" :rules="addRules.common"
                            :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.expiryDay" placeholder="请输入会员有效期，单位：天" />
                            </div>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员原价" :prop="`membershipPackages[${index}][originalPrice]`"
                            :rules="addRules.common" :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.originalPrice" placeholder="请输入会员原价，单位：元" />
                            </div>
                          </el-form-item>
                        </div>
                        <div v-if="addForm.os !== 1" :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item :label="memberPayLabelAndDisabled.label"
                            :prop="`membershipPackages[${index}][paymentPrice]`" :rules="addRules.common"
                            :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.paymentPrice" :disabled="memberPayLabelAndDisabled.disabled"
                                placeholder="请输入会员支付价，单位：元" />
                            </div>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item :label="addForm.os === 0 ? '安卓月单价' : '月单价'"
                            :style="{ display: 'inline-block' }">
                            <div :style="{ width: '200px' }">
                              <el-input
                                :value="item.expiryDay == '-1' ? '-' : ((Number(item.paymentPrice) / Number(item.expiryDay) * 30).toFixed(2) + '/月')"
                                placeholder="请输入月单价" disabled />
                            </div>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员标签" :prop="`membershipPackages[${index}][tag]`"
                            :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                            <div :style="{ width: '200px' }">
                              <el-input v-model="item.tag" placeholder="请输入会员标签" maxlength="10" />
                            </div>
                          </el-form-item>
                          <span>{{ item.tag ? item.tag.length : 0 }}/10</span>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="会员营销文案" :prop="`membershipPackages[${index}][content]`"
                            :style="{ 'display': 'inline-block' }">
                            <div :style="{ 'display': 'inline-block', 'margin-right': '10px', width: '200px' }">
                              <el-input v-model="item.content" placeholder="请输入会员营销文案" maxlength="20" />
                            </div>
                            <span>{{ item.content ? item.content.length : 0 }}/20</span>
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="支付按钮角标文案" :prop="`membershipPackages[${index}][buttonMark]`"
                            :style="{ 'display': 'inline-block' }">
                            <div :style="{ 'display': 'inline-block', 'margin-right': '10px', width: '200px' }">
                              <el-input v-model="item.buttonMark" placeholder="请输入支付按钮角标文案" maxlength="20" />
                            </div>
                            <span>{{ item.buttonMark ? item.buttonMark.length : 0 }}/20</span>
                          </el-form-item>
                        </div>
                        <renewDayConfig v-if="addForm.os !== 1" :key="index" :ref="'itemForm' + index" :item="item"
                          @getRenewConfig="setMembershipPackagesForm($event, index)" />
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="是否默认选中" :prop="`membershipPackages[${index}][defaultChoose]`"
                            :style="{ 'display': 'inline-block' }" :rules="addRules.common">
                            <el-radio-group v-model="item.defaultChoose"
                              @change="changeDefaultChoose(item.defaultChoose, item.checkInMember, item.expiryDay, index)">
                              <el-radio :label="false">否</el-radio>
                              <el-radio :label="true">是</el-radio>
                            </el-radio-group>
                            <!-- <span>{{ item.buttonMark?item.buttonMark.length:0 }}/20</span> -->
                          </el-form-item>
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="是否在支付面板中显示" label-width="180px"
                            :prop="`membershipPackages[${index}][checkInMemberShow]`"
                            :style="{ 'display': 'inline-block' }" :rules="addRules.common">
                            <span slot="label">
                              <span
                                style="font-size: 14px;color: #606266;font-weight: 700;line-height: 40px;">是否在支付面板中显示</span>
                              <el-tooltip style="color: #409EFF;" class="item-el-tooltip" placement="top">
                                <i class="el-icon-question" />
                                <div slot="content">
                                  -：默认选中显示<br />
                                  -：选中不显示时，对应的sku不会在常规的支付面板中出现（引导支付页抽屉、首页默认推荐弹窗、会员中心页、键盘页、定制人设支付抽屉、复登）<br />
                                  -：选中显示时，对应的SKU会在客户端的支付面板中显示，顺序值和所有会员混排
                                </div>
                              </el-tooltip>
                            </span>
                            <el-radio-group v-model="item.checkInMemberShow">
                              <el-radio :label="0">不显示</el-radio>
                              <el-radio :label="1">显示</el-radio>
                            </el-radio-group>
                          </el-form-item>

                        </div>
                        <div :style="{ width: '450px' }">
                          <el-form-item label="是否参与会员权益活动" label-width="180px" :style="{ 'display': 'inline-block' }">
                            <el-radio-group v-model="item.equityStatus">
                              <el-radio :label="0">不显示</el-radio>
                              <el-radio :label="1">显示</el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="会员权益活动" label-width="180px" :style="{ 'display': 'inline-block' }"
                            v-if="item.equityStatus == 1" :prop="`membershipPackages[${index}][equityActivitiesId]`"
                            :rules="addRules.common">
                            <el-select v-model="item.equityActivitiesId" placeholder="请选择会员权益活动"
                              :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                              <el-option :label="item.activityName" :value="item.id"
                                v-for='item in equityActivitiesList' :key="item.id">{{
                                  item.activityName }}</el-option>
                              <!-- <el-option :key="2" label="非永久会员" :value="2">非永久会员</el-option> -->
                            </el-select>
                            <span class="hdyl" @click="hdyl(item.equityActivitiesId)"
                              v-if='item.equityActivitiesId'>活动预览</span>
                          </el-form-item>
                          <el-form-item label="参与期数" label-width="180px" :style="{ 'display': 'inline-block' }"
                            v-if="item.equityStatus == 1" :prop="`membershipPackages[${index}][equityGiveMonth]`"
                            :rules="addRules.common">
                            <el-input v-model="item.equityGiveMonth" placeholder="请输入参与期数" maxlength="20"
                              oninput="value=value.replace(/^\D*([0-9]\d*)?.*$/,'$1')"
                              @blur="membershipPackages[index][equityGiveMonth] = $event.target.value" />
                            （单个权益会员订单可以参与的活动期数）
                          </el-form-item>
                          <el-form-item label="首次扣款前是否发放会员权益" label-width="220px" v-if="item.equityStatus == 1"
                            :style="{ 'display': 'inline-block' }"
                            :prop="`membershipPackages[${index}][equityFirstDeductStatus]`" :rules="addRules.common">
                            <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                              首次扣款前是否发放会员权益
                              <el-tooltip content="iOS订阅不存在订阅总期数，用户取消订阅则订阅终止，该订阅周期为每次扣款周期" placement="top-start"
                                :style="{ color: '#409eff' }">
                                <i class="el-icon-question" style="font-size: 14px" />
                              </el-tooltip>
                            </span>
                            <el-radio-group v-model="item.equityFirstDeductStatus">
                              <el-radio :label="0">否</el-radio>
                              <el-radio :label="1">是</el-radio>
                            </el-radio-group>
                          </el-form-item>

                        </div>
                      </div>
                      <!-- 续费会员 -->
                      <div>
                        <div class="ljLine" />
                        <div class="form_package_title2">
                          支付拦截信息
                        </div>
                        <div :style="{ display: 'inline-block', width: '390px' }">
                          <el-form-item label="是否配置拦截" :prop="`membershipPackages[${index}][interceptPayFlag]`"
                            :style="{ 'display': 'inline-block' }" :rules="addRules.common">
                            <el-radio-group v-model="item.interceptPayFlag"
                              :disabled="item.appleProductId && showIosInfo({ appleProductId: item.appleProductId }).trialStatus == 1">
                              <el-radio :label="0">否</el-radio>
                              <el-radio :label="1">是</el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </div>
                        <template v-if="item.interceptPayFlag == 1">
                          <div :style="{ display: 'inline-block', width: '390px' }">
                            <el-form-item label="拦截类型" :prop="`membershipPackages[${index}][interceptPayType]`"
                              :rules="addRules.common" :style="{ display: 'inline-block' }">
                              <el-select v-model="item.interceptPayType" placeholder="请选择拦截类型"
                                @change="changeInterceptType(index)">
                                <el-option :key="1" label="普通支付拦截" :value="1"
                                  :disabled="item.packageType == 2">普通支付拦截</el-option>
                                <el-option :key="2" label="自动续费支付拦截" :value="2"
                                  :disabled="item.userType == 1 || item.packageType == 1">自动续费支付拦截</el-option>
                              </el-select>
                            </el-form-item>
                          </div>
                          <!--ios拦截相关-->
                          <template v-if="addForm.os === 0 || addForm.os === 1">
                            <el-form-item label="拦截iOS商品" :prop="`membershipPackages[${index}].interceptAppleProductId`"
                              :rules="addRules.common">
                              <div :style="{ width: '200px' }">
                                <el-select v-model="item.interceptAppleProductId" placeholder="请选择拦截iOS商品"
                                  @change="itemAppleProChange({ appleProductId: item.interceptAppleProductId, index, key: 'iosCycleParams' })">
                                  <template v-if="item.interceptPayType == 2">
                                    <el-option v-for="el in ljCycleProduct" :key="el.id" :label="el.productName"
                                      :value="el.id">{{ el.productName }}</el-option>
                                  </template>
                                  <template v-else>
                                    <el-option v-for="el in ljNormalProduct" :key="el.id" :label="el.productName"
                                      :value="el.id">{{ el.productName }}</el-option>
                                  </template>
                                </el-select>
                              </div>
                            </el-form-item>
                            <template v-if="item.interceptAppleProductId && item.iosCycleParams">
                              <div :style="{ display: 'inline-block', width: '390px' }">
                                <el-form-item v-if="item.interceptPayType != 2" label="iOS支付价"
                                  :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                                  <div :style="{ width: '200px' }">
                                    <span class="disabed-input">{{ item.iosCycleParams.paymentPrice }}</span>
                                  </div>
                                </el-form-item>
                              </div>
                              <template v-if="item.interceptPayType == 2 && item.interceptAppleProductId">
                                <div v-if="item.iosCycleParams.discountPrice > 0"
                                  :style="{ display: 'inline-block', width: '390px' }">
                                  <el-form-item label="首期促销价"
                                    :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                                    <div :style="{ width: '200px' }">
                                      <span class="disabed-input">{{ item.iosCycleParams.discountPrice }}</span>
                                    </div>
                                  </el-form-item>
                                </div>
                                <div>
                                  <div :style="{ display: 'inline-block', width: '390px' }">
                                    <el-form-item label="iOS订阅金额"
                                      :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                                      <div :style="{ width: '200px' }">
                                        <span class="disabed-input">{{ item.iosCycleParams.paymentPrice }}</span>
                                      </div>
                                    </el-form-item>
                                  </div>
                                  <el-form-item label="iOS订阅周期"
                                    :style="{ 'display': 'inline-block', 'margin-right': '10px' }">
                                    <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                                      iOS订阅周期
                                      <el-tooltip content="iOS订阅不存在订阅总期数，用户取消订阅则订阅终止，该订阅周期为每次扣款周期" placement="top-start"
                                        :style="{ color: '#409eff' }">
                                        <i class="el-icon-question" style="font-size: 14px" />
                                      </el-tooltip>
                                    </span>
                                    <div :style="{ width: '200px' }">
                                      <span class="disabed-input">{{ item.iosCycleParams.subscriptionPeriod }}</span>
                                    </div>
                                  </el-form-item>
                                </div>
                              </template>
                            </template>

                          </template>
                          <div :style="{ display: 'inline-block', width: '390px' }">
                            <el-form-item label="拦截减价金额" :prop="`membershipPackages[${index}][ticketAmount]`"
                              :rules="addRules.common" :style="{ display: 'inline-block' }">
                              <div :style="{ width: '200px' }">
                                <el-input v-model="item.ticketAmount" :disabled="addForm.os === 1"
                                  placeholder="请输入拦截减价金额，单位：元" />
                              </div>
                            </el-form-item>
                          </div>
                          <template v-if="addForm.os !== 1">
                            <div v-if="item.interceptPayType === 2"
                              :style="{ display: 'inline-block', width: '390px' }">
                              <el-form-item label="拦截扣款金额" :prop="`membershipPackages[${index}][interceptAmount]`"
                                :rules="addRules.common" :style="{ display: 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                  <el-input v-model="item.interceptAmount" placeholder="请输入拦截扣款金额，单位：元" />
                                </div>
                              </el-form-item>
                            </div>
                            <renewDayConfig v-if="item.interceptPayType === 2 && item.packageType != 2" :key="index"
                              :ref="'itemForm' + index" :item="item" :disLxkf="true"
                              @getRenewConfig="setMembershipPackagesForm($event, index)" />
                          </template>
                        </template>
                      </div>
                    </div>
                    <div class="czView">
                      <span v-if="(index + 1) == addForm.membershipPackages.length" class="addButton"
                        @click="addUserType"><i class="el-icon-circle-plus" />添加SKU</span>
                      <span v-if="addForm.membershipPackages.length > 1" class="scButton" @click="delPakcage(index)"><i
                          class="el-icon-remove" />删除SKU</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="form_view">
              <div class="form_view_title">
                <span>支付配置</span>
              </div>
              <el-form-item label="支付方式" prop="paymentOption" :rules="addRules.common">
                <el-checkbox-group v-model="addForm.paymentOption" @change="changePaymentOption">
                  <el-checkbox label="1">微信支付</el-checkbox>
                  <el-checkbox label="2">支付宝支付</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="默认选中" prop="defaultPayment" :rules="addRules.common">
                <el-radio-group v-model="addForm.defaultPayment">
                  <el-radio v-if="addForm.paymentOption.includes('1')" :label="1">微信支付</el-radio>
                  <el-radio v-if="addForm.paymentOption.includes('2')" :label="2">支付宝支付</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div class="form_view">
              <div class="form_view_title">
                <span>UI显示方案</span>
              </div>
              <el-form-item label="UI显示方案" label-width="100px" prop="uiType" :rules="addRules.common">
                <el-select v-model="addForm.uiType" placeholder="UI显示方案">
                  <el-option v-for="item in uiTypeList" :key="item.value" :label="item.label" :value="item.value">
                    {{ item.label }}
                  </el-option>
                </el-select>
                <button class="img-btn-template">
                  查看示意图
                  <el-image class="btn-image" :src="uiTypeImg[0]" :preview-src-list="uiTypeImg" />
                </button>
              </el-form-item>
            </div>
            <div class="form_view">
              <div class="form_view_title">
                <span>
                  卸载拦截配置</span> <button class="img-btn-template">
                  查看示意图
                  <el-image class="btn-image" :src="'https://picture.ttshengbei.com/qutaosh/qjjp/qjjp_01.jpg'"
                    :preview-src-list="['https://picture.ttshengbei.com/qutaosh/qjjp/qjjp_01.jpg']" />
                </button>
              </div>
              <unloadIntecept ref="unloadFormRef" :add-form-data="addForm" />
            </div>
            <div class="form_view">
              <div class="form_view_title">
                <span>模板状态</span>
              </div>
              <el-form-item label="状态" prop="status" :rules="addRules.common">
                <el-switch v-model="addForm.status" :active-value="1" :inactive-value="0" @change="switchChange" />
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div class="view_button">
          <el-button @click="drawer = false">取消</el-button>
          <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
        </div>
      </div>
    </el-drawer>

    <el-drawer v-if="drawerShow" :visible.sync="drawerShow" direction="rtl" size="50%" :modal-append-to-body="false"
      :with-header="false" :wrapper-closable="false">
      <div class="close_button">
        <i class="el-icon-close" @click="drawerShow = false" />
      </div>
      <div class="drawer_package">
        <div class="drawer_title">
          <span>会员权益活动预览</span>
        </div>
        <div class="addForm_package">
          <div class="form_view">
            <div class="form_view_title">
              <span>活动页UI</span>
            </div>
            <div class="form_imgView">
              <div class="imgView imgView1">
                <div class="imgView_title">权益会员页</div>
                <div class="img_package">
                  <el-image style="width: 187px" :src="img1" :preview-src-list="[img1]" class="imgView_main">
                  </el-image>
                </div>
              </div>
              <div class="imgView imgView2">
                <div class="imgView_title">权益会员抽屉</div>
                <div class="img_package">
                  <el-image style="width: 187px" :src="img2" :preview-src-list="[img2]" class="imgView_main">
                  </el-image>
                </div>
              </div>
              <div class="imgView imgView3">
                <div class="imgView_title">普通会员页</div>
                <div class="img_package">
                  <el-image style="width: 187px" :src="img3" :preview-src-list="[img3]" class="imgView_main">
                  </el-image>
                </div>
              </div>
              <div class="imgView imgView4">
                <div class="imgView_title">普通会员抽屉</div>
                <div class="img_package">
                  <el-image style="width: 187px" :src="img4" :preview-src-list="[img4]" class="imgView_main">
                  </el-image>
                </div>
              </div>
            </div>
            <div class="form_view_title">
              <span>活动显示场景</span>
            </div>
            <div class="form_table_view">
              <el-table :data="hdView" border style="width: 100%">
                <el-table-column prop="checkScene" label="已显示场景">
                </el-table-column>
                <el-table-column prop="unCheckScene" label="未显示场景">
                </el-table-column>
              </el-table>
              <div class="form_table_txt" @click="hdxq">活动详情</div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { areaList as vantAreaData } from '@vant/area-data'
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import {
  getmembershipTemplate,
  membershipTemplateCreate,
  membershipTemplateUpdate,
  membershipTemplateexport,
  relationshipChannel
} from '@/qjjpApi/payManage'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import editTemplate from './modules/editTemplate.vue'
import Sortable from 'sortablejs'
import { osListForm } from '@/qjjpViews/appVersion/basicParams'
import { getIosProduct, AppleCycle } from '@/qjjpViews/payManage/basicParams'
import renewDayConfig from './components/paymentTemplateChildren/renewDayConfig'
import unloadIntecept from './components/paymentTemplateChildren/unloadIntecept'
import { equityActivitiesPage } from '@/qjjpApi/operate'
import img1 from '@/assets/img/zf_img/img_1.png'
import img2 from '@/assets/img/zf_img/img_2.jpg'
import img3 from '@/assets/img/zf_img/img_3.jpg'
import img4 from '@/assets/img/zf_img/img_4.jpg'
const oneMonthToDays = 30
const defaultMemberShipKeys = {
  id: '',
  name: '',
  userType: '',
  expiryDay: '',
  originalPrice: '',
  paymentPrice: '',
  tag: '',
  content: '',
  buttonMark: '',
  checkInMember: false,
  checkInDay: '',
  packageType: 0,
  renewType: '',
  renewPayPrice: '',
  interceptPayFlag: 0,
  interceptPayType: '',
  interceptAmount: '',
  ticketAmount: '',
  renewFirstDeductDay: '',
  renewFirstDeductDayType: null,
  checkInMemberShow: 1,
  interceptAppleProductId: null,
  uninstallInterceptorSkuKey: 1,
  appleProductId: null,
  equityStatus: 0,
  iosNormalParams: {
    paymentPrice: null, // 正常支付方式
    renewPayPrice: null, // 订阅支付金额
    discountPrice: null, // 促销
    subscriptionPeriod: null// 订阅周期
  },
  iosCycleParams: {
    paymentPrice: null, // 正常支付方式
    renewPayPrice: null, // 订阅支付金额
    discountPrice: null, // 促销
    subscriptionPeriod: null// 订阅周期
  }
}
export default {
  name: 'paymentTemplate',
  components: {
    page,
    editTemplate,
    renewDayConfig,
    unloadIntecept
  },
  props: {},
  data() {
    var validRenewFirstDay = (rule, value, callback) => {
      if (!value) {
        callback(new Error('必填'))
      } else if (Number(value) < 1) {
        callback(new Error('不能小于1'))
      } else if (!(/^[1-9]\d*$/.test(value))) {
        callback(new Error('类型必须为整数'))
      } else {
        callback()
      }
    }
    function initAreaData() {
      const list = []
      for (const a in vantAreaData.province_list) {
        list.push({
          id: a,
          label: vantAreaData.province_list[a],
          children: []
        })
      }
      return list
    }
    return {
      img1,
      img2,
      img3,
      img4,
      hdView: [
        {
          checkScene: '',
          unCheckScene: ''
        }
      ],
      hdCheck: {},
      hdId: 0,
      activitySceneList: [
        {
          id: '35,36,37',
          name: '权益会员'
        },
        {
          id: '4,11,12',
          name: '站内承接'
        },
        {
          id: '0',
          name: '主推弹窗'
        },
        {
          id: '26',
          name: '次推弹窗'
        },
        {
          id: '14',
          name: '待支付页'
        },
        {
          id: '23,24,25',
          name: '复登'
        },
        {
          id: '1,2,8',
          name: '会员中心'
        },
        {
          id: '6,7,9',
          name: '引导体验页'
        },
        {
          id: '27,28,29',
          name: 'AI识图'
        },
        {
          id: '10',
          name: '0元打卡页（活动底导）'
        },

      ],
      drawerShow: false,
      equityActivitiesList: [],
      subscriptionTrialPeriodList: [
        {
          value: 'THREE_DAYS',
          label: '3天',
          dayValue: 3
        },
        {
          value: 'ONE_WEEK',
          label: '1周',
          dayValue: 7
        },
        {
          value: 'TWO_WEEK',
          label: '2周',
          dayValue: 14
        },
        {
          value: 'ONE_MONTH',
          label: '1个月',
          dayValue: oneMonthToDays
        },
        {
          value: 'TWO_MONTH',
          label: '2个月',
          dayValue: oneMonthToDays * 2
        },
        {
          value: 'THREE_MONTH',
          label: '3个月',
          dayValue: oneMonthToDays * 3
        },
        {
          value: 'SIX_MONTH',
          label: '6个月',
          dayValue: oneMonthToDays * 6
        },
        {
          value: 'ONE_YEAR',
          label: '1年',
          dayValue: oneMonthToDays * 12
        }
      ],
      siteIdsList: [],
      sort: true,
      DataLoading: false,
      flexArr: [],
      isflex: false,
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }],
        validRenewFirstDay: [{ validator: validRenewFirstDay, trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      addForm: {
        defaultTemplate: false,
        paymentOption: ['1'],
        membershipPackages: [],
        defaultPayment: 1,
        uiType: '',
        os: '',
        uninstallInterceptorSkuId: ''
      },
      drawer: false,
      listQuery: {
        templateName:this.$route.query.templateName||""
      },
      uiTypeList: [
        { value: 0, label: '默认方案', imgList: ['https://axure-file.lanhuapp.com/md5__c282c3065b646cefcd2c48769207952e.svg', 'https://picture.ttshengbei.com/qutaosh/qjjpPage/20240625/Dingtalk_20240625170526.jpg'] },
        { value: 1, label: '月均价方案', imgList: ['https://axure-file.lanhuapp.com/md5__b75a2cdb6dae496959ac5a1f8695e822.svg'] },
        { value: 2, label: '天均价方案', imgList: ['https://axure-file.lanhuapp.com/md5__8642f8b8784b445a0035f4d2e5af72c9.png'] },
        { value: 3, label: '简约方案', imgList: ['https://picture.ttshengbei.com/qutaosh/qjjpPage/2024112901/example.webp'] }
      ],
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await getmembershipTemplate(this.listQuery)
          await count_channel_application_list().then(res => {
            if (res.code === 200) {
              this.siteIdsList = res.data
            }
          })
          await equityActivitiesPage({ pageNumber: 1, pageSize: 999, activityStatus: 1 }).then(res => {
            this.equityActivitiesList = res.data.records
          })
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      },
      areaList: initAreaData(),
      dialogVisible: false,
      dialogParams: {
        templateId: '',
        title: '编辑/新增',
        templateName: '',
        areaList: []
      },
      vipTypeList: [{
        value: 0,
        label: '普通会员'
      },
      {
        value: 1,
        label: '打卡会员'
      },
      {
        value: 2,
        label: '续费会员'
      }
      ],
      osListForm,
      productsIos: {
        normalProduct: [],
        cycleProduct: []
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          clearable: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '会员名称',
          key: 'packageName',
          type: formItemType.input,
          width: 120,
          search: true,
          tableHidden: true
        },
        {
          title: '模板名称',
          key: 'name',
          type: formItemType.input,
          width: 120
        },
        {
          title: '模板名称',
          key: 'templateName',
          type: formItemType.input,
          val: this.listQuery.templateName || '',
          width: 120,
          search: true,
          tableHidden: true
        },
        {
          title: '应用类型',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: osListForm,
          search: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [{
            value: 1,
            label: '启用'
          },
          {
            value: 0,
            label: '禁用'
          }
          ],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        {
          title: '会员名称',
          key: 'name',
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.name}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '会员原价',
          key: 'originalPrice',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.originalPrice}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '安卓会员支付价',
          key: 'paymentPrice',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.paymentPrice}</div>
                })}
              </div >
            )
          }
        },
        {
          title: 'ios会员支付价',
          key: 'iosPaymentPrice',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.iosPaymentPrice || '-'}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '月单价',
          key: 'shippingType1',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.expiryDay == '-1' ? '-' : (Number((Number(item.paymentPrice) / Number(item.expiryDay) * 30).toFixed(2)) + '元/月')}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '拦截优惠金额',
          key: 'ticketAmount',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.ticketAmount} </div>
                })}
              </div >
            )
          }
        },

        {
          title: '会员标签',
          key: 'tag',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.tag}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '营销文案',
          key: 'content',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.content}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '是否打卡会员',
          key: 'checkInMember',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.checkInMember ? '是' : '否'}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '打卡天数',
          key: 'checkInDay',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.checkInDay ? (item.checkInDay + '天') : '-'}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '支付方式',
          key: 'paymentOption',
          render: (h, params) => {
            if (!params.data.row.paymentOption) {
              return h('span', '-')
            }
            return h('span', (params.data.row.paymentOption.includes(1) ? '微信' : '') + (params.data.row.paymentOption.includes(2) ? ',支付宝' : ''))
          }
        },
        {
          title: '更新时间',
          key: 'updateTime',
          tableView: tableItemType.tableView.date,
          options: {
            format: 'YYYY-MM-DD HH:mm:ss'
          }
        },
        {
          title: '更新人员',
          key: 'updateUser'
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return h('span', params.data.row.status == 0 ? '禁用' : '启用')
          }
        },
        {
          type: tableItemType.active,
          width: 180,
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: async ($index, item, params) => {
                await this.siteIdChange(params.siteId)
                // this.reloadAddform()
                this.popType = 'edit'
                this.handlerEditDataForm(params, 'edit')
                this.drawer = true
                this.$forceUpdate()
              }
            },
            {
              text: '复制',
              key: 'edit',
              type: tableItemType.activeType.event,
              theme: 'success',
              click: ($index, item, params) => {
                this.handlerEditDataForm(params, 'add')
                this.addForm.id = null
                this.popType = 'add'
                this.drawer = true
              }
            }
          ]
        }
      ]
    },
    ljNormalProduct() {
      let arr = []
      for (let i = 0; i < this.productsIos.normalProduct.length; i++) {
        if (this.productsIos.normalProduct[i].trialStatus == 0) {
          arr.push(this.productsIos.normalProduct[i])
        }
      }
      console.info(this.productsIos.normalProduct, 'ljNormalProduct')
      return arr
    },
    ljCycleProduct() {
      let arr = []
      for (let i = 0; i < this.productsIos.cycleProduct.length; i++) {
        if (this.productsIos.cycleProduct[i].trialStatus == 0) {
          arr.push(this.productsIos.cycleProduct[i])
        }
      }
      return arr
    },
    uiTypeImg() {
      return this.uiTypeList.find(item => item.value === this.addForm.uiType)?.imgList ?? this.uiTypeList[0].imgList
    },
    memberPayLabelAndDisabled() {
      return {
        label: this.addForm.os === 0 ? '安卓会员支付价' : '会员支付价',
        disabled: this.addForm.os === 1
      }
    },
    hdList() {
      let arr = {}
      for (let i = 0; i < this.equityActivitiesList.length; i++) {
        if (this.equityActivitiesList[i].id == this.hdId) {
          arr = this.equityActivitiesList[i]
        }
      }
      return arr
    }
  },

  watch: {
    drawerShow: {
      handler(val) {
        if (val) {
          let str = this.hdList.activityScene.split(',')
          let arr = []
          let arr1 = []
          for (let i = 0; i < this.activitySceneList.length; i++) {
            let arrs = this.activitySceneList[i].id.split(',')
            let commonCount = this.countCommonValues(str, arrs);
            if (commonCount != 0) {
              arr.push(this.activitySceneList[i].name)
            } else {
              arr1.push(this.activitySceneList[i].name)
            }
          }
          console.info(arr, arr1, 'arrarr')
          this.hdView[0].checkScene = arr.join(',')
          this.hdView[0].unCheckScene = arr1.join(',')
        }
      }
    },
    drawer: {
      handler(val) {
        if (val) {
          setTimeout(() => {
            if (this.categoryListsort) {
              this.categoryListsort.destroy()
            }
            this.drawSort()
          })
        } else {
          this.isflex = false
        }
      }
    }
  },
  async created() {
    // this.productsIos = await getIosProduct()
  },
  methods: {
    toFn(id,list){
      const  productName = list.find(n=>n.id==id).productName
      const routeUrl = this.$router.resolve({
        path: '/qjjp/payManage/productForIos',
        query: {
          productName: productName
        }
      })
      window.open(routeUrl.href, '_blank')
    },
    countCommonValues(arr1, arr2) {
      return arr1.filter(item => arr2.includes(item)).length;
    },
    hdxq() {
      let href = location.origin + '/#/qjjp/customScript/eventManagementDetail?type=edit&id=' + this.hdId
      window.open(href);
      // this.$router.push({
      //   path: '/qjjp/customScript/eventManagementDetail',
      //   query: {
      //     type: 'edit',
      //     id: this.hdId
      //   }
      // })
    },
    hdyl(id) {
      this.hdId = id
      // this.drawer = false
      this.drawerShow = true
    },
    drawSort() {
      const tbody = document.querySelector('#categoryList')
      const that = this
      console.info(tbody, 'tbody')
      this.categoryListsort = new Sortable(tbody, {
        animation: 150,
        sort: true,
        handle: '.el-icon-rank',
        draggable: '.form_package_package', // 设置可拖拽行的类名(el-table自带的类名)
        forceFallback: true,
        ghostClass: 'blue-background-class',
        async onEnd(evt) {
          that.isflex = true
          const arr = JSON.parse(JSON.stringify(that.addForm.membershipPackages))
          const curr = arr.splice(evt.oldIndex, 1)[0]
          arr.splice(evt.newIndex, 0, curr) // 把被拖拽的元素添加到checkQaCategoryList中
          console.info(curr, arr, 'ids', evt)
          that.flexArr = JSON.parse(JSON.stringify(arr))
          that.addForm.membershipPackages.splice(0, that.addForm.membershipPackages.length)
          for (let i = 0; i < arr.length; i++) {
            that.addForm.membershipPackages.push(arr[i])
            // that.$set(that.addForm, 'membershipPackages', arr)
          }
          that.sort = false
          that.categoryListsort.destroy()
          setTimeout(() => {
            that.sort = true
            setTimeout(() => {
              that.drawSort()
            }, 0)
          }, 500)
          console.info(that.addForm.membershipPackages, 'that.addForm.membershipPackages')
          // that.addForm.membershipPackages
          // that.$set(that.addForm, 'membershipPackages', JSON.parse(JSON.stringify(arr)))
          that.$forceUpdate()
        }
      })
    },
    handleCommand(type, item) {

      item.packageType = type
      item.checkInMember = String(type) === '1'
      // 自动续费不能配普通拦截 不能配永久会员
      if (item.packageType == 2) {
        item.checkInMemberShow = 1
        if (item.userType == 1) {
          item.userType = ''
          item.expiryDay = ''
        }
        if (item.interceptPayType == 1) {
          item.interceptPayType = ''
        }
      }
      // 打卡会员不能配自动续费拦截
      if (item.packageType == 1) {
        item.checkInMemberShow = 0
        if (item.interceptPayType == 2) {
          item.interceptPayType = ''
        }
      }
      if (item.packageType == 0) {
        item.checkInMemberShow = 1
      }
      item.appleProductId = null
      item.interceptAppleProductId = null
    },
    // item.defaultChoose,item.checkInMember,item.expiryDay,index
    changeDefaultChoose(val, checkInMember, expiryDay, index) {
      console.info(val)
      if (val) {
        this.addForm.membershipPackages.forEach((_, i) => {
          if (index !== i) {
            this.$set(this.addForm.membershipPackages[i], 'defaultChoose', false)
          }
        })
      }
    },
    switchChange(e) {
      if (String(e) !== '1' && this.addForm.defaultTemplate) {
        this.$message.error('改模板为默认支付模板，不可操作禁用')
        this.addForm.status = 1
        return
      }
      if (e == '0') {
        relationshipChannel({ id: this.addForm.id }).then(res => {
          if (res.code == 200 && res.data) {
            this.$confirm('当前支付模板已存在关联渠道，禁用后已关联渠道将使用默认模板，是否确认禁用?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
            }).catch(() => {
              this.$set(this.addForm, 'status', 1)
            })
          }
        })
      }
    },
    changePaymentOption(e) {
      console.info(e)
      if (e.length < 2) {
        this.$set(this.addForm, 'defaultPayment', Number(e[0]))
      }
    },
    clickUserType(type, index) {
      // 永久会员不能配续费拦截
      console.info(this.addForm, type, index, this.addForm.membershipPackages)
      if (type == 1) {
        this.$set(this.addForm.membershipPackages[index], 'expiryDay', -1)
        if (this.addForm.membershipPackages[index]?.interceptPayType == 2) {
          this.$set(this.addForm.membershipPackages[index], 'interceptPayType', '')
        }
      } else {
        if (this.addForm.membershipPackages[index].expiryDay == -1) {
          this.$set(this.addForm.membershipPackages[index], 'expiryDay', '')
        }
      }
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = membershipTemplateexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    handleAdd() {
      this.drawer = true
      this.popType = 'add'
      this.reloadAddform()
    },
    cancel() {
      this.dialogVisible = false
      this.dialogParams = {
        title: '编辑/新增',
        templateName: '',
        areaList: []
      }
      this.areaList.forEach(area => {
        area.shippedAmount = ''
      })
    },
    confirm() {
      this.drawer = true
    },
    delPakcage(index) {
      this.$confirm(`删除后将不再保存编辑信息，是否确认删除`).then(_res => {
        this.addForm.membershipPackages.splice(index, 1)
        this.$message.success('操作成功')
      })
    },
    addUserType() {
      // 如果套餐里面有默认选中 新增套餐的默认选中项默认false  uninstallInterceptorSkuKey---识别卸载拦截sku唯一标识 index+1
      const membershipPackages = [...this.addForm.membershipPackages]
      const packageType = defaultMemberShipKeys.packageType
      const validpackageType = packageType == 0 || packageType == 2 ? ['0', '2'] : ['1']
      const isHaveSelect = membershipPackages?.find(item =>
        item.defaultChoose && validpackageType.includes(String(item.packageType))
      )
      this.addForm.membershipPackages.push({
        ...defaultMemberShipKeys,
        ...(isHaveSelect ? { defaultChoose: false } : {}),
        uninstallInterceptorSkuKey: membershipPackages.length + 1 // 用于新增识别sku唯一 标识
      })
    },
    handMessageStyleListAdd(formName) {
      const membershipPackages = this.addForm?.membershipPackages ?? []
      let formValid = true
      if (membershipPackages && membershipPackages.length) {
        let checkInMemberShowList = []
        for (let i = 0; i < membershipPackages.length; i++) {
          checkInMemberShowList.push(membershipPackages[i].checkInMemberShow)
          if (this.$refs[`itemForm${i}`] && this.$refs[`itemForm${i}`][0] && this.$refs[`itemForm${i}`][0].$children && this.$refs[`itemForm${i}`][0].$children[0]) {
            if (typeof this.$refs[`itemForm${i}`][0].$children[0].validate === 'function') {
              this.$refs[`itemForm${i}`][0].$children[0].validate(valid => {
                if (!valid) {
                  formValid = false
                  return
                }
              })
            }
          }
        }
        if (checkInMemberShowList.indexOf(1) == -1) {
          this.$message.error(`在支付面板中显示配置项不能全部为不显示`)
          return
        }
      }
      if (this.$refs?.unloadFormRef && this.$refs.unloadFormRef.$children) {
        this.$refs.unloadFormRef.$children[0].validate(valid => {
          if (!valid) {
            formValid = false
          }
        })
      }
      this.$refs[formName].validate(valid => {
        if (valid && formValid) {
          const { uninstallIntercept = 0, uninstallInterceptorSkuKey = '', uninstallInterceptDiscount = '' } = this.$refs?.unloadFormRef?.itemForm ?? {}
          this.addForm = {
            ...this.addForm, uninstallIntercept,
            uninstallInterceptorSkuKey,
            uninstallInterceptDiscount
          }
          const a = this.addForm.membershipPackages
          const hasNormalMemberConfig = a.some(item => String(item.packageType) === '0')
          if (!hasNormalMemberConfig) {
            this.$message.error(`提醒：未配置常规会员，请配置`)
            return
          }

          const aArry = []
          const bArry = []
          for (let i = 0; i < a.length; i++) {
            if (Number(a[i].expiryDay) >= 0 && a[i].checkInDay && a[i].checkInDay > Number(a[i].expiryDay)) {
              console.info(a[i].expiryDay >= 0, 'a[i].expiryDay >= 0')
              console.info(a[i].checkInDay, 'a[i].checkInDay')
              console.info(a[i].checkInDay > a[i].expiryDay, a[i].expiryDay, 'a[i].checkInDay > a[i].expiryDay')
              this.$message.error('打卡时间不能大于会员有效期')
              return
            }
            if (a[i].checkInMember && a[i].defaultChoose) {
              aArry.push(a[i].name)
            } else if (a[i].expiryDay == -1 && a[i].defaultChoose) {
              bArry.push(a[i].name)
            }
          }
          if (aArry.length > 1) {
            this.$message.error(`打卡会员默认选中只能唯一,重复类型：'${aArry}'`)
            return
          }
          if (bArry.length > 1) {
            this.$message.error(`普通会员默认选中只能唯一,重复类型：'${bArry}'`)
            return
          }
          const payList = this.addForm.paymentOption.toString()
          const fn = this.popType == 'edit' ? membershipTemplateUpdate : membershipTemplateCreate
          if (this.isflex) {
            this.$set(this.addForm, 'membershipPackages', this.flexArr)
          }
          fn({ ...this.addForm, paymentOption: payList, iosNormalParams: null, iosCycleParams: null }).then(res => {
            if (res.code == 200) {
              this.drawer = false
              this.$message.success('操作成功')
              this.$store.dispatch('tableRefresh', this)
            }
          })
        }
      })
    },
    reloadAddform() {
      const arr = {
        defaultTemplate: false,
        siteId: '',
        id: '',
        name: '',
        paymentOption: ['1'],
        defaultPayment: 1,
        status: 1,
        os: '',
        membershipPackages: [{
          ...defaultMemberShipKeys
        }],
        uninstallInterceptorSkuId: '',
        uninstallIntercept: 1, // 卸载拦截 1-开启 0-关闭
        uninstallInterceptorSkuKey: '', // 卸载拦截sku自定义skuKey
        uninstallInterceptDiscount: '' // 折扣
      }
      this.productsIos = []
      for (const key in arr) {
        this.$set(this.addForm, key, arr[key])
      }
    },
    changeInterceptType(index) {
      this.addForm.membershipPackages[index].interceptAmount = null
      this.addForm.membershipPackages[index].interceptAppleProductId = null
    },
    showPic() {

    },
    /**
     *禁用、未匹配到的返回为null
     */
    showIosInfo({ appleProductId }) {
      const isNotundefined = (value) => {
        return value !== '' && value !== undefined && value !== null
      }
      const list = this.productsIos.allProduct
      const info = list?.find?.(item => String(item.id) === String(appleProductId)) ?? ({})
      const { paymentPrice, discount, discountPrice, subscriptionPeriod, status, trialStatus, subscriptionTrialPeriod } = info
      let a = this.subscriptionTrialPeriodList.find(item => item.value == subscriptionTrialPeriod)?.label ?? ''
      console.info(a, 'a')
      return (String(status) !== '1' || !isNotundefined(paymentPrice) ? null : {
        paymentPrice,
        status,
        trialStatus,
        subscriptionTrialPeriod: a,
        renewPayPrice: discount == 1 && isNotundefined(discountPrice) ? discountPrice : paymentPrice,
        discountPrice: discount == 1 && isNotundefined(discountPrice) ? discountPrice : 0,
        subscriptionPeriod: AppleCycle.find(item => item.value == subscriptionPeriod)?.label ?? '',
        dayValue: AppleCycle.find(item => item.value == subscriptionPeriod)?.dayValue ?? 1
      })
    },
    itemAppleProChange({ appleProductId, index, key }) {
      const resultInfo = this.showIosInfo({ appleProductId })
      this.$set(this.addForm.membershipPackages[index], key, { ...resultInfo })
      if (this.addForm.os === 1) {
        // 只有ios的时候，计算减价金额
        const { dayValue = 1, paymentPrice = 0 } = this.addForm.membershipPackages?.[index]?.iosNormalParams ?? {}
        const originRenewPay = this.addForm.membershipPackages?.[index]?.iosNormalParams?.renewPayPrice ?? 0
        const reduceRenewPay = this.addForm.membershipPackages?.[index]?.iosCycleParams?.renewPayPrice ?? 0
        this.addForm.membershipPackages[index].paymentPrice = paymentPrice
        const ticketAmount = Number((+originRenewPay - +reduceRenewPay).toFixed(2))
        this.addForm.membershipPackages[index].ticketAmount = this.addForm.membershipPackages[index].interceptPayFlag == 1 ? ticketAmount : ''
        if (this.addForm.membershipPackages[index].packageType == 2) {
          this.addForm.membershipPackages[index].expiryDay = dayValue
        }
      }
      if (key == 'iosNormalParams') {
        resultInfo.trialStatus == 1 && this.$set(this.addForm.membershipPackages[index], 'interceptPayFlag', 0)
        console.info(resultInfo, 'resultInfo')
      }
    },
    /**
     * 用于表单编辑回显当前选项的数据
     */
    handlerEditDataForm(params, popType) {
      this.addForm = JSON.parse(JSON.stringify(params))
      this.$set(this.addForm, 'membershipPackages', [])
      for (let i = 0; i < params.membershipPackageVos?.length; i++) {
        const item = { ...params.membershipPackageVos[i] }
        const iosNormalParams = item.appleProductId && this.showIosInfo({ appleProductId: item.appleProductId })
        const iosCycleParams = item.interceptAppleProductId && this.showIosInfo({ appleProductId: item.interceptAppleProductId })
        this.addForm.membershipPackages.push({
          ...JSON.parse(JSON.stringify(item)),
          userType: item.expiryDay == '-1' ? 1 : 2,
          iosNormalParams,
          iosCycleParams,
          appleProductId: iosNormalParams ? item.appleProductId : null,
          interceptAppleProductId: iosCycleParams ? item.interceptAppleProductId : null,
          id: popType === 'add' ? null : item.id,
          uninstallInterceptorSkuKey: i + 1 // 自定义sku用于匹配外层选中sku
        })
      }
      this.$set(this.addForm, 'paymentOption', JSON.parse(JSON.stringify(params.paymentOption.split(','))))
      this.$set(this.addForm, 'uiType', params?.uiType ?? 0)
    },
    resetMembershipPackages() {
      const memberNameList = this.addForm?.membershipPackages ?? []
      for (let i = 0; i < memberNameList.length; i++) {
        this.addForm.membershipPackages.splice(i, 1, { ...defaultMemberShipKeys })
      }
    },
    async siteIdChange(val) {
      this.addForm.membershipPackages = [{
        ...defaultMemberShipKeys
      }]
      this.productsIos = await getIosProduct(val)
    },
    setMembershipPackagesForm(val, index) {
      const item = { ...this.addForm.membershipPackages[index] }
      this.addForm.membershipPackages.splice(index, 1, { ...item, ...val })
      console.log(val, this.addForm.membershipPackages[index], '...2333333')
    }
  }
}
</script>
<style scoped lang='scss'>
.czView {
  text-align: right;
}

.addButton {
  cursor: pointer;
  color: #409EFF;
}

.scButton {
  padding-left: 20px;
  cursor: pointer;
  color: #909399;
}

::v-deep .el-drawer__body {
  overflow: scroll;
  padding: 0 30px 20px;
  /* overflow-x: auto; */
}

::v-deep .el-drawer__header {
  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 30px;
    text-align: left;
    font-style: normal;
  }

}

::v-deep .el-drawer__container ::-webkit-scrollbar {
  display: none;
}

body {}

.position_sticky {

  /*21.隐藏滚动条，太丑了*/

}

.form_view {
  margin: 0 0px 20px;
  background-color: rgb(189, 184, 184, 0.2);
  border: 1px solid rgba(0, 0, 0, 0.2);
  width: 100%;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;

  .form_view_title {
    position: relative;
    margin-bottom: 20px;

    &::before {
      content: "";
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: -5px;
      width: 2px;
      height: 15px;
      background-color: #66b1ff;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }

  .form_table_view {
    margin: 20px auto 0;

    .form_table_txt {
      margin-top: 20px;
      text-align: right;
      text-decoration: underline;
      color: #409eff;
      cursor: pointer;
    }
  }

  .form_package {
    margin: 10px 0;
    padding: 10px 0;
    border: 1px solid gray;
    position: relative;

    .delPakcage {
      position: absolute;
      top: 20px;
      left: 120px;
    }

    .form_package_title_package {
      position: relative;

      .el-icon-rank {
        width: 20px;
        height: 20px;
        font-size: 30px;
        padding-right: 10px;
        position: absolute;
        left: 10px;
        top: 10px;
        z-index: 2;
      }
    }

    .ljLine {
      height: 1px;
      border: 1px dashed #000000;
    }

    .form_package_title2 {
      color: #409EFF;
      padding: 10px 10px 10px 40px;
      font-family: MiSans, MiSans;
      font-weight: 600;
      font-size: 16px;
      line-height: 26px;
      font-style: normal;
    }

    .form_package_title {
      color: #409EFF;
      cursor: pointer;
      padding: 10px 10px 10px 40px;
      font-family: MiSans, MiSans;
      font-weight: 600;
      font-size: 20px;
      // color: #333333;
      line-height: 26px;
      font-style: normal;
    }
  }
}

.flex {
  display: flex;
  align-items: center;
}

::v-deep .el-transfer-panel {
  width: 250px !important;
}

::v-deep .el-drawer__body {
  overflow: scroll;
  // padding-bottom: 20px;
  padding: 0 30px 20px;
  position: relative;
  /* overflow-x: auto; */
}

::v-deep .el-drawer__header {
  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 30px;
    text-align: left;
    font-style: normal;
  }

}

::v-deep .el-drawer__body {}

.close_button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background-color: rgb(0, 0, 0, 1);
  text-align: center;
  cursor: pointer;

  i {
    color: white;
    line-height: 40px;
  }
}

.drawer_package {
  height: 100%;
  position: relative;

  .drawer_title {
    padding: 10px 20px 5px;
    vertical-align: middle;

    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }

  }
}

.addForm_package {
  background-color: rgb(189, 184, 184, 0.1);
  padding: 15px;
  min-height: 100%;

  .demo-ruleForm {
    background-color: #ffffff;
    width: 100%;
    min-height: 100%;
    position: relative;
    padding-bottom: 50px;

    &::v-deep .el-form-item__label {
      white-space: nowrap;
    }
  }
}

.view_button {
  background-color: #ffffff;
  padding: 20px;
  border-top: 1px dashed #000000;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 50%;
  text-align: right;

  ::v-deep .el-button {
    margin: 0 10px;
  }
}

.form_view {
  // margin: 0 0px 20px;
  background-color: #ffffff;
  border: none;
  width: 100%;
  padding: 15px;
  border-radius: 5px;
  margin: 0;

  .form_view_title {
    margin-bottom: 20px;

    .title_line {
      width: 2px;
      height: 10px;
      background-color: #66b1ff;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      padding-left: 5px;
      vertical-align: middle;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }
}

.active-command-item {
  color: #409EFF;
}

.img-btn-template {
  background: transparent;
  text-decoration: underline;
  position: relative;
  text-decoration: underline;
  margin-left: 10px;

  .btn-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    ::v-deep .el-image__preview {
      opacity: 0;
    }

    // visibility:hidden;
    ::v-deep .el-icon-circle-close {
      font-size: 30px;
      color: #fff;
    }
  }
}

.disabed-input {
  padding: 0 8px;
  display: inline-block;
  height: 40px;
  background-color: #F5F7FA;
  border-color: #E4E7ED;
  color: #C0C4CC;
  cursor: not-allowed;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  height: 40px;
  line-height: 40px;
  width: 100%;
  font-size: 15px;
}

.sufixed-span {
  display: 'inline-block';
  margin-right: '10px';
  width: '200px'
}

.hdyl {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
}

.form_imgView {
  margin: 20px auto;

  .imgView {
    padding-top: 20px;
    margin: auto;
    width: 50%;
    vertical-align: middle;
    display: inline-block;
    text-align: center;
    padding-bottom: 20px;

    .imgView_title {
      font-weight: 600px;
      text-align: center
    }

    .imgView_main {
      margin: 20px auto 20px;
    }

    .img_package {
      height: 308px;
      overflow: hidden;
    }
  }

  .imgView1 {
    border-right: 1px dashed #000000;
    border-bottom: 1px dashed #000000;
  }

  .imgView2 {
    // border-right: 1px solid #000000;
    border-bottom: 1px dashed #000000;
  }

  .imgView3 {
    // border-right: 1px solid #000000;
    border-right: 1px dashed #000000;
  }
}
</style>
