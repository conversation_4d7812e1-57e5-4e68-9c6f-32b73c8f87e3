/*
 * @Author: 陈小豆
 * @Date: 2022-04-20 17:33:24
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-05-14 16:54:50
 */
import CONSTANT from '@/config/constant.conf'
import { get, post } from '@/libs/axios.package'
import qs from 'qs'

// 会员卡退款类型
export const getMemberRefundType = obj => {
  return get('/order/refund/type', obj)
}

// 会员卡申请退款列表
export const getMemberRefundApplyList = obj => {
  return get('/order/refund/apply/list', obj)
}

// 管理员处理退款申请
export const solveMemberRefundApply = obj => {
  return post('/order/refund/apply/solve', obj)
}

// 管理员批量处理退款申请
export const solveMemberRefundApplyBatch = obj => {
  return post('/order/refund/apply/solve/batch', obj)
}

// 已处理退款订单列表
export const memberRefundSolvedList = obj => {
  return get('/order/refund/list', obj)
}

// 已处理退款订单列表导出
export const memberRefundListExport = data => CONSTANT.publicPath + '/order/refund/list/export?' + qs.stringify(data)

// 已处理策略退款订单列表导出
export const memberStrategyRefundListExport = data =>
  CONSTANT.publicPath + '/order/refund/strategy/list/export?' + qs.stringify(data)

// 会员卡退款统计
export const getMemberRefundStatistics = obj => {
  return get('/order/refund/statistics', obj)
}

// 会员卡退款统计导出
export const memberRefundExport = data => CONSTANT.publicPath + '/order/refund/export?' + qs.stringify(data)

// 会员卡退款统计详情
export const getMemberRefundStatisticsDetail = obj => {
  return get('/order/refund/detail/statistics', obj)
}

// 会员卡退款统计详情导出
export const memberRefundDetailExport = data =>
  CONSTANT.publicPath + '/order/refund/detail/export?' + qs.stringify(data)

// 退款申请列表查询导出
export const memberRefundApplyListExport = data =>
  CONSTANT.publicPath + '/order/refund/apply/list/export?' + qs.stringify(data)

// 退款申请列表退款查询导出
export const memberRefundApplyListRefundExport = data =>
  CONSTANT.publicPath + '/order/refund/apply/list/refundExport?' + qs.stringify(data)

// 客服后台为用户提交退款申请
export const memberRefundApplyByService = obj => {
  return post('/order/refund/admin/apply', obj)
}

export const refundPaypalList = obj => {
  return get('/ali/complaint/record/list', obj)
}

export const refundPaypalDetailList = obj => {
  return get('/ali/complaint/record/detail/list', obj)
}

export const refundPaypalBytime = obj => {
  return post('/ali/complaint/record/process/bytime', obj)
}

export const refundPaypalbyIds = obj => {
  return post('/ali/complaint/record/process/byIds', obj)
}
// 退款域名列表-分页查询
export const domainConfigPage = obj => {
  return get('/domain/config/page', obj)
}
// 退款域名列表-修改
export const domainConfigUpdate = obj => {
  return post('/domain/config/update', obj)
}

// 支付宝投诉订单列表-分页查询
export const aliComplaintRefundList = obj => {
  return get('/ali/complaint/record/complaintRefundList', obj)
}
// 支付宝投诉订单-导出
export const complaintRefundListExport = data =>
  CONSTANT.publicPath + '/ali/complaint/record/complaintRefundListExport?' + qs.stringify(data)
// 支付宝上传凭证
export const complaintUpdate = obj => {
  return post('/ali/complaint/record/supplementaryDocuments', obj)
}
// 支付宝投诉文案说明参数
export const complaintContentParams = obj => {
  return get('/ali/complaint/record/getUseRightsDetail', obj)
}
// 支付宝上传图片
export const aliUploadImg = obj => {
  return post('/ali/complaint/record/complainImage', obj)
}
// 本地图片地址
export const ownUploadImg = obj => {
  return post('/upload/image', obj)
}
// 支付宝自有投诉退款
export const refundOne = obj => {
  return post('/ali/complaint/record/refund', obj)
}
// 支付宝自有投诉批量退款
export const refundMore = obj => {
  return post('/ali/complaint/record/batch/refund', obj)
}

// 策略退款订单列表
export const strategyList = params => {
  return get(`/order/refund/strategy/list`, params)
}

// 退款信息
export const crowdGoodsOrderMsg = obj => {
  return get('/crowdGoodsOrder/infoMsg', obj)
}
// 会员退全款
export const orderFullRefund = obj => {
  return post('/order/refund/fullRefund', obj)
}
// 一键通过
export const refundOneClickPass = obj => {
  return get('/order/refund/oneClickPass', obj)
}
// 线下退款申请
export const refundOfflineSubmit = obj => {
  return post('/vip/order/refund/offline/submit', obj)
}
// 获取客诉来源枚举
export const getCustomerComplaintSource = obj => {
  return get('/userVisit/strategy/getCustomerComplaintSource', obj)
}

export const getRecordPage = obj => {
  return get('/user/visit/record/page', obj)
}

// 获取用户权限
export const getAccessPermission = obj => {
  return get('/vipOrder/VipOrderRefundQuery/accessPermission', obj)
}

// 提交退款
export const applyRefundNew = obj => {
  return post('/vipOrderRefundEdit/applyRefund', obj)
}

export const OrderRefundEditGit = obj => {
  return get('/vipOrderRefundEdit/get', obj)
}
// 撤销退款
export const cancelVipOrderRefund = obj => {
  return get('/vipOrder/VipOrderRefundQuery/cancelVipOrderRefund', obj)
}
// 继续退款
export const continueVipOrderRefund = obj => {
  return get('/vipOrder/VipOrderRefundQuery/continueVipOrderRefund', obj)
}

// 重新发起退款
export const RefundApplyRefund = (id, obj) => {
  return post(`/vipOrderRefundEdit/continueVipOrderRefund/${id}`, obj)
}
// 权益修改
export const recountRights = obj => {
  return post('/vipOrder/VipOrderRefundQuery/recountRights', obj)
}
// 商品修改
export const vipOrderRefundEdit = (id, obj) => {
  return post(`/vipOrderRefundEdit/change/refundType/${id}`, obj)
}

// 分页查询问答
export const getQaQuestionList = obj => {
  return get('/qa/question/page', obj)
}

// 新增或修改问答
export const QaQuestionAddOrUpdate = obj => {
  return post('/qa/question/addOrUpdate', obj)
}

// 问答详情
export const getQaQuestionDetail = id => {
  return get(`/qa/question/detail/${id}`)
}

// 分页查询预约记录
export const getReservationList = obj => {
  return get(`/qa/service/reservation/page`, obj)
}

// 查询方案列表
export const getPlanList = obj => {
  return get(`/qa/plan/list`, obj)
}

// 新增或修改方案
export const addOrUpdate = obj => {
  return post(`/qa/plan/addOrUpdate`, obj)
}

// 查询方案下的问答
export const getPlanQuestionList = obj => {
  return get(`/qa/plan/list/planQuestion`, obj)
}

// 删除方案
export const deletePlan = id => {
  return get(`/qa/plan/delete/${id}`)
}

// 查询方案未添加的问答
export const getQuestionsList = obj => {
  return get(`/qa/plan/list/questions`, obj)
}

// 新增方案下的问答
export const addPlanQuestion = obj => {
  return post(`/qa/plan/addQuestion`, obj)
}

export const getCategoryList = obj => {
  return get(`/qa/plan/category/list`, obj)
}

// 删除方案下的问答
export const delQuestion = obj => {
  return get(`/qa/plan/delQuestion`, obj)
}

// 新增或修改分类
export const addCategoryOrUpdate = obj => {
  return post(`/qa/plan/category/addOrUpdate`, obj)
}

// 删除分类
export const deleteCategory = id => {
  return get(`/qa/plan/category/del/${id}`)
}

// 分类排序
export const categorySort = obj => {
  return post(`/qa/plan/category/sort`, obj)
}

export const sortQuestion = obj => {
  return post(`/qa/plan/sortQuestion`, obj)
}

// 查询用户反馈信息
export const feedback = userId => {
  return get(`/qa/service/reservation/feedback/${userId}`)
}

export const reservation = obj => {
  return post(`/qa/service/reservation/handle`, obj)
}

export const reservationExport = data =>
  CONSTANT.publicPath + '/qa/service/reservation/export?' + qs.stringify(data)

// 用户详情页面-更新会员订单客服备注
export const updateCustomServiceRemark = (id, obj) => {
  return post(`/vipOrders/updateCustomServiceRemark/${id}`, obj)
}

