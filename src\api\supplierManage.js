import {put, get, post, del} from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/*
 * 供应商管理列表
 * */
export const GET_SUMPPLIER_LIST = obj => {
  return get('/crowd/supplier/page', obj, false)
}

/*
 * 查询角色为ROLE_GYS的管理员
 * */
export const GET_SUMPPLIER_ADMIN_LIST = obj => {
  return get('/crowd/supplier/admin/list', obj, false)
}

/**
 * 新增或修改供应商
 * */
export const UPDATE_SUMPPLIER = obj => {
  return post(`/crowd/supplier/addOrUpdate`, obj, null)
}

/**
 * 供应商列表导出
 */
export const EXPORT_ORDERSTATISTIC_EXPORTBYCODE = data =>
  CONSTANT.publicPath + '/orderStatistic/profitCountExport?' + qs.stringify(data)

/*
 * 查询所有供应商
 * */
export const GET_ALL_SUMPPLIER_LIST = obj => {
  return get('/crowd/supplier/all', obj, false)
}

/*
 * 商品订单列表
 * */
export const GET_SUMPPLIER_ORDER_LIST = obj => {
  return get('/crowdGoodsOrder/supplier/page', obj, false)
}
/*
 * 商品售后订单列表
 * */
export const GET_SUMPPLIER_REFUNDORDER_LIST = obj => {
  return get('/crowdGoodsOrderRefund/supplier/page', obj, false)
}
/**
 * 商品订单导出
 */
export const SUMPPLIER_ORDER_EXPORT = data =>
 CONSTANT.publicPath + '/crowdGoodsOrder/supplier/export?' + qs.stringify(data)
