<template>
  <div>
    <page
      :request="request"
      :list="list"
      table-title="充值订单列表"
      table-type="selection"
      :table-select-methods="tableSelectMethods"
      @selectionChange="selectionChange"
    >
      <div slot="searchContainer" style="display: inline-block">
        <el-button type="danger" plain size="small" @click="dialogFormVisible2 = true">批量退款</el-button>
      </div>
    </page>

    <SDialog :dialog-form-visible.sync="dialogFormVisible2" :data="dialogOps2">
      <refunds :order-ids="checkList" @success2="success2" @close2="close2" />
    </SDialog>

    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="dialogOps">
      <refund :order-id="orderId" @success="success" @close="close" />
    </SDialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { order_chongduoduo_order_list, orderRefunds } from '@/api/costManagement'
import moment from 'moment'
import SDialog from '@/components/restructure/dialog'
import refund from './components/refund'
import refunds from './components/refunds'
import { count_channel_application_list } from '@/api/NewChannel'

export default {
  components: {
    page,
    SDialog,
    refund,
    refunds
  },
  data() {
    return {
      tableSelectMethods: {
        selectable: data => {
          return data.status != 2
        }
      },
      checkList: [],
      rendered: true,
      dialogFormVisible: false,
      dialogFormVisible2: false,
      dialogOps: {
        title: '退款'
      },
      dialogOps2: {
        title: '批量退款'
      },
      listQuery: {
        startDate: moment().subtract(6, 'd').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      orderId: 0,
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then(res => {
              if (res.code === 0) {
                this.siteIds = res.data
                this.listQuery.siteId = res.data[0].siteId
              }
            })
          }

          return order_chongduoduo_order_list(this.listQuery)
        }
      },
      siteIds: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '订单号',
          key: 'orderNo',
          search: true,
          type: formItemType.input
        },
        {
          title: '用户账号',
          key: 'userAccount',
          search: true,
          type: formItemType.input
        },
        {
          title: '充值手机号',
          key: 'userPhone',
          search: true,
          type: formItemType.input
        },
        {
          title: '充值金额',
          key: 'goodsPrice'
        },
        {
          title: '满减券',
          key: 'fullReduction'
        },
        {
          title: '减价金额',
          key: 'deductAmount'
        },
        {
          title: '实付金额',
          key: 'amount'
        },

        {
          key: 'paySuccessTime',
          title: '充值时间',
          type: formItemType.datePickerDaterangeGai,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          search: true,
          tableHidden: true,
          val: [moment().subtract(6, 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        },
        {
          key: 'paySuccessTime',
          title: '充值时间',
          render: (h, params) => {
            const data = params.data.row
            if (data.paySuccessTime) {
              return h('span', {}, moment(data.paySuccessTime).format('YYYY-MM-DD HH:mm:ss'))
            } else {
              return h('span', {}, '--')
            }
          },
          formHidden: true
        },
        {
          title: '充值状态',
          key: 'transStatus',
          search: false
        },
        {
          title: '充值状态',
          key: 'transStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          list: [
            {
              label: '待支付',
              value: 'WAITPAY'
            },
            {
              label: '充值成功',
              value: 'FINISHED'
            },
            {
              label: '充值中',
              value: 'WAITSEND'
            },
            {
              label: '充值失败',
              value: 'CHARGEFAILURE'
            },
            {
              label: '已取消',
              value: 'CANCELED'
            },
            {
              label: '已退款',
              value: 'BACK'
            }
          ],
          search: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: false,
          tableHidden: true
        },
        {
          title: '三方充值平台',
          key: 'orderTypeId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          list: [
            {
              label: '三方充值平台',
              value: 2
            },
            {
              label: '充多多',
              value: 1
            },
            {
              label: '净蓝',
              value: 3
            },
            {
              label: '陆优',
              value: 4
            },
            {
              label: '心链',
              value: 5
            },
            {
              label: '润鼎',
              value: 6
            }
          ],
          search: true,
          tableHidden: true
        },
        {
          title: '退款操作人员',
          key: 'adminName'
        },
        {
          type: tableItemType.active,
          width: 100,
          headerContainer: false,
          activeType: [
            {
              text: '退款',
              key: 'edit',
              theme: 'warning',
              hidden: params => {
                return params.transStatus != '充值中' && params.transStatus != '充值失败'
              },
              click: ($index, item, params) => {
                this.orderId = params.id
                this.dialogFormVisible = true
              }
            }
          ]
        }
      ]
    }
  },
  mounted() {},
  methods: {
    success() {
      this.$store.dispatch('tableRefresh', this)
      this.dialogFormVisible = false
    },
    success2() {
      this.$store.dispatch('tableRefresh', this)
      this.dialogFormVisible2 = false
    },
    close() {
      this.dialogFormVisible = false
    },
    close2() {
      this.dialogFormVisible2 = false
    },
    refunds() {
      const ids = this.checkList.map(item => item.id)
      orderRefunds({
        orderIdList: ids,
        reason: ''
      }).then(res => {
        if (res.code === 0) {
          this.$message({
            message: '批量退款成功',
            type: 'success'
          })
          this.$store.dispatch('tableRefresh', this)
        }
      })
    },
    selectionChange(selection) {
      this.checkList = selection
      // this.checkSlection = []
      // for (let i = 0; i < selection.length; i++) {
      //   this.checkSlection.push(selection[i].id)
      // }
      // this.$emit('selectionChange', selection)
    }
  }
}
</script>

<style scoped></style>
