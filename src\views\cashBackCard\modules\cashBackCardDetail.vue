<template>
  <div>
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm" size="mini">
      <el-form-item label="类型" prop="couponType">
        <el-select v-model="ruleForm.couponType" placeholder="请选择类型">
          <el-option label="返现卡" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="couponName">
        <el-input v-model="ruleForm.couponName" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="面额" prop="couponAmount">
        <el-input v-model="ruleForm.couponAmount" placeholder="请输入面额" />
      </el-form-item>
      <el-form-item label="消费金额" prop="couponLimitAmount">
        <el-input v-model="ruleForm.couponLimitAmount" placeholder="请输入消费金额" />
      </el-form-item>
      <el-form-item label="有效期" prop="couponLimitData.date">
        <el-input v-model="ruleForm.couponLimitData.date" style="width: 50px;" />
        <el-select v-model="ruleForm.couponLimitData.type" style="width: 70px; margin-left: 5px">
          <el-option label="天" :value="0" />
          <el-option label="月" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="PID" prop="pid">
        <el-input v-model="ruleForm.pid" placeholder="请输入PID" />
      </el-form-item>
      <el-form-item label="选品组" prop="favouriteId">
        <el-select v-model="ruleForm.favouriteId" filterable placeholder="请选择选品组">
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :label="item.favoritesTitle"
            :value="item.favoritesId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="详细信息" prop="couponDetail">
        <el-input v-model="ruleForm.couponDetail" type="textarea" placeholder="请输入详细信息" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="ruleForm.status">
          <el-radio :label="0">启用</el-radio>
          <el-radio :label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div style="text-align: right;padding: 10px 30px 0 0;">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="sure">确定</el-button>
    </div>
  </div>
</template>

<script>
import { EDIT_PRESENT_COUPON_DETAIL, GET_PRESENT_COUPON_DETAIL } from '@/api/cashBackCard'
import { GET_PRODUCT_LIST } from '@/api/activity'

export default {
  components: {},
  props: {
    cardId: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    ruleForm: {
      id: '',
      couponType: '',
      couponName: '',
      pid: '',
      favouriteId: '',
      type: [],
      status: '',
      couponAmount: '',
      couponLimitAmount: '',
      couponDetail: '',
      couponLimitData: {
        date: '',
        type: 0
      }
    },
    rules: {
      couponName: [
        { required: true, message: '请输入名称', trigger: 'blur' }
      ],
      couponType: [
        { required: true, message: '请选择类型', trigger: 'change' }
      ],
      couponAmount: [
        { required: true, message: '请输入面额', trigger: 'blur' }
      ],
      couponLimitAmount: [
        { required: true, message: '请输入消费金额', trigger: 'blur' }
      ],
      'couponLimitData.date': [
        { required: true, message: '请输入有效期', trigger: 'blur' }
      ],
      favouriteId: [
        { required: true, message: '请选择选品组', trigger: 'change' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    },
    typeList: []
  }),
  mounted() {
    GET_PRODUCT_LIST().then(res => {
      if (res.code === 0) this.typeList = res.data
    })
    if (this.type === 'edit') {
      this.$store.dispatch('editSubmission')
      // 做详情的请求
      this._bydDetail()
    }
  },
  methods: {
    _bydDetail() {
      GET_PRESENT_COUPON_DETAIL({ id: this.cardId }).then(res => {
        this.ruleForm = res.data
      })
    },
    close() {
      this.$emit('close')
    },
    sure() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          const postData = this.ruleForm
          if (this.type === 'add') {
            delete postData.id
          }
          EDIT_PRESENT_COUPON_DETAIL(postData).then(res => {
            this.$message.success('操作成功')
            this.$emit('success')
            this.close()
          })
        }
      })
    }
  }
}
</script>

<style lang="" scoped>
</style>
