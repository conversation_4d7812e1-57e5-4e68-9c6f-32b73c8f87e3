<template>
  <div>
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="160px" class="demo-ruleForm">
      <el-form-item label="代理名称" prop="proxyName" :rules="rules.common">
        <el-input v-model="ruleForm.proxyName" placeholder="代理名称" />
      </el-form-item>
      <el-form-item label="代理返点（单位：%）" prop="discount" :rules="rules.common">
        <el-input v-model="ruleForm.discount" placeholder="代理返点" @blur="ruleForm.discount = $event.target.value" onkeyup="value=value.replace(/[^\d\.]/g, '').replace(/\.{2,}/g, '.').replace(/(\-)*(\d+)\.(\d\d).*/g,'$1$2.$3');value=value>=100?99.99:value"  onblur="value=Number(value.replace(/[^\d\.]/g, '').replace(/\.{2,}/g, '.').replace(/(\-)*(\d+)\.(\d\d).*/g,'$1$2.$3'));value=value>=100?99.99:value" />
      </el-form-item>
      <el-form-item label="状态" prop="status" :rules="rules.common">
        <el-radio-group v-model="ruleForm.status">
          <el-radio label="1">启用</el-radio>
          <el-radio label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
        <el-button @click="$emit('cancle')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import {
  proxyConfigSaveOrUpdate,
  proxyConfigCheckProxyName
} from '@/api/deliveryManage'
export default {
  name: 'AccountEdit',
  props: {
    currentParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogFormVisible: false,
      ruleForm: {
        proxyName: '',
        discount: '',
        status: null
      },
      parentMediaAccountList: [],
      rules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      }
    }
  },
  watch: {
    currentParams(val) {
      this.initFn(val)
    }
  },
  created() {
    this.initFn(this.currentParams)
  },
  methods: {
    initFn(value) {
      Object.assign(this.ruleForm, value)
    },
    submitForm(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          await proxyConfigCheckProxyName({ proxyName: this.ruleForm.proxyName }).then(res => {
            if (res.code === 0 && !res.data) {
              return
            }
            if (res.code === 0 && res.data) {
              return this.$confirm(
                `您当前输入代理名称已存在近似值：${res.data}，可能存在重复创建，请确认是否继续创建`,
                '温馨提示'
              )
            }
            throw new Error('接口异常')
          })
          proxyConfigSaveOrUpdate(this.ruleForm).then(res => {
            console.log(res)
            if (res.code == 200) {
              this.$message.success(this.ruleForm.id ? '更新成功' : '新增成功')
              this.$emit('success')
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.flex_item {
  display: flex;

  .reduce {
    width: 200px;
  }

  .f_time {
    width: 300px;
  }
}

.cascader {
  ::v-deep .el-cascader__tags {
    font-size: 0;
  }
}
</style>
