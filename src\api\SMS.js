import CONSTANT from '@/config/constant.conf'
import { get, post } from '@/libs/axios.package'
import qs from 'qs'

export const sms_pageList = obj => {
  return get('/smsPlatformAccount/pageList', obj)
}
export const sms_add = obj => {
  return post('/smsPlatformAccount/add', obj)
}
export const sms_edit = obj => {
  return post('/smsPlatformAccount/edit', obj)
}
export const sms_detail = id => {
  return get('/smsPlatformAccount/detail/' + id)
}
export const sms_del = obj => {
  return get('/smsPlatformAccount/del/' + obj.id)
}
export const sms_count_list = obj => {
  return get('/count/sms/list', obj)
}
export const smsPlatformTask_add = obj => {
  return post('/smsPlatformTask/add', obj)
}
export const smsPlatformTask_pageList = obj => {
  return get('/smsPlatformTask/pageList', obj)
}
export const smsPlatformTask_analysisShortChain = obj => {
  return get('/smsPlatformTask/analysisShortChain', obj)
}
export const smsPlatformTask_detail = id => {
  return get('/smsPlatformTask/detail/' + id)
}
export const smsPlatformTask_edit = obj => {
  return post('/smsPlatformTask/edit', obj)
}
export const smsPlatformTask_editEnable = id => {
  return get('/smsPlatformTask/editEnable/' + id)
}
export const smsPlatformTask_testSend = obj => {
  return post('/smsPlatformTask/testSend', obj)
}
export const smsPlatformTask_task_list = obj => {
  return get('/smsPlatformTask/task/list', obj)
}
export const export_count_sms_scene_list = data => CONSTANT.publicPath + '/count/sms/list/export?' + qs.stringify(data)
export const export_count_sms_list_batch = data => CONSTANT.publicPath + '/count/sms/list/batch/export?' + qs.stringify(data)
