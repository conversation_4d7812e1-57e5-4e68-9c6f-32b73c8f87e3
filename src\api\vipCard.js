import { get, post } from '@/libs/axios.package'

/**
* 渠道会员卡新增
* */
export const add_vipCard = obj => {
  return post('/vipCard/saveVipCard', obj, null)
}

/**
* 渠道会员卡查询列表
* */
export const get_vipCard_list = obj => {
  return get('/vipCard/get_page_list', obj, null)
}

/**
* 渠道会员卡查询列表
* */
export const get_vipCard_list_no_page = obj => {
  return get('/vipCard/get_list', obj, null)
}

/**
 * 渠道会员卡价格详情
 * */
export const get_vipCard_edit = obj => {
  return get('/vipCard/selectById', obj, null)
}

/**
 * 编辑渠道会员卡
 * */
export const put_vipCard_edit = obj => {
  return post('/vipCard/updateVipCard', obj, null)
}

/**
 * 删除vipcard
 * */
export const del_vipCard = obj => {
  return post('/vipCard/deleteVipCard', obj, null)
}
