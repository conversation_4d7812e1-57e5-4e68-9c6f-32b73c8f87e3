<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :direction="direction"
    :size="size"
    :destroy-on-close="true"
    :wrapperClosable="false"
    :before-close="handleClose"
  >
    <!-- 内容区域 -->
    <div class="drawer_content">
      <div class="drawer_content_body">
        <slot></slot>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <div class="drawer_footer">
      <slot name="footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </slot>
    </div>
  </el-drawer>
</template>

<script>
import FormSection from './FormSection.vue'

export default {
  name: 'BaseDrawer',
  components: {
    FormSection
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    size: {
      type: String,
      default: '50%'
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    handleCancel() {
      this.$emit('cancel')
      this.handleClose()
    },
    handleConfirm() {
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__header {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #EBEEF5;

  > span {
    font-size: 20px;
  }

  .el-drawer__close-btn {
    i {
      font-size: 30px;
    }
  }
}

::v-deep .el-drawer__body {
  height: calc(100% - 55px);
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drawer_content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.drawer_content_body {
  // background-color: rgb(189, 184, 184, 0.1);
  background-color: rgb(195, 195, 195, 0.1);
  // padding: 15px;
  margin-right: 2px;
  height: 100%;
  overflow-y: auto;

  ::v-deep .el-form {
    // background-color: #ffffff;
    padding: 15px;
    border-radius: 4px;
  }

  ::v-deep .form_view {
    background-color: #ffffff;
    width: 100%;
    // padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.drawer_footer {
  padding: 10px 20px;
  border-top: 1px solid #EBEEF5;
  text-align: center;
  flex-shrink: 0;
  background: #fff;

  .el-button {
    margin-left: 20px;
  }
}
</style> 