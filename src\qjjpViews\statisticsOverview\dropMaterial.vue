<template>
  <Page :key="groupType" :request="request" :list="list" table-title="投放素材监控表" />
</template>

<script>
import Page from '@/components/restructure/page/v8.vue'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { materialReportReport } from '@/qjjpApi/statisticsOverview'
import { count_channel_application_list, mediaAll } from '@/qjjpApi/NewChannel'
import {
  get_admin_list
} from '@/api/system'
import moment from 'moment'
export default {
  name: 'dropMaterial',
  components: {
    Page
  },
  data() {
    return {
      siteIds: [],
      medias: [],
      admins: [],
      listQuery: {
        startDate: moment().subtract(7, 'd').format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD'),
        transformGoal: 0,
        materialType: 0
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, groupType: this.groupType, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then(res => {
              if (res.code === 200) {
                this.siteIds = res.data
              }
            })
          }
          if (!this.medias.length) {
            await mediaAll().then(res => {
              if (Array.isArray(res.data)) {
                this.medias = res.data
              }
            })
          }
          if (!this.admins.length) {
            await get_admin_list({ pageSize: 1000 }).then(res => {
              if (Array.isArray(res.data)) {
                this.admins = res.data
              }
            })
          }
          const list = await materialReportReport(this.listQuery)
          const { records, total } = list.data

          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    /**
     * 表格类型
     * 日期维度：date；
     * 素材维度：groupType
     */
    groupType() {
      const path = this.$route.path
      if (path === '/qjjp/statisticsOverview/dropMaterial/material') {
        return 'adMaterialId'
      }

      return 'date'
    },
    list() {
      return [
        {
          key: 'dateSearch',
          title: '日期',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          pickerDay: 90,
          search: true,
          tableHidden: true,
          val: [this.listQuery.startDate, this.listQuery.endDate]
        },
        {
          title: '投放媒体',
          key: 'apiCode',
          type: formItemType.select,
          list: this.medias,
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          search: true,
          tableHidden: true,
          clearable: true
        },
        {
          title: '素材ID',
          key: 'materialId',
          type: formItemType.input,
          search: true,
          tableHidden: true
        },
        {
          title: '渠道ID',
          key: 'channelCode',
          type: formItemType.input,
          search: true,
          tableHidden: true
        },
        {
          title: '素材名称',
          key: 'materialName1',
          searchKey: 'materialName',
          type: formItemType.input,
          search: true,
          tableHidden: true
        },
        {
          title: '优化师',
          key: 'optimizer',
          type: formItemType.select,
          list: this.admins,
          listFormat: {
            label: 'nickname',
            value: 'id'
          },
          search: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '应用类型',
          key: 'os',
          type: formItemType.select,
          list: [{ label: 'iOS&&Android', value: 0 }, { label: 'iOS', value: 1 }, { label: 'Android', value: 2 }],
          search: true,
          tableHidden: true
        },
        {
          title: '转化目标',
          key: 'transformGoal',
          type: formItemType.select,
          list: [{ label: '浅层转化目标', value: 0 }, { label: '深层转化目标', value: 1 }],
          val: this.listQuery.transformGoal,
          search: true,
          clearable: false,
          tableHidden: true
        },
        {
          title: '素材类型',
          key: 'materialType',
          type: formItemType.select,
          list: [{ label: '竖版视频', value: 0 }],
          val: this.listQuery.materialType,
          search: true,
          clearable: false,
          tableHidden: true
        },
        {
          title: '基础素材信息',
          key: 'base',
          isCustom: true,
          children: [
            ...(this.groupType === 'date' ? [
              {
                title: '日期',
                key: 'day',
                tips: '素材的消耗日期'
              },
              {
                title: '上线素材数',
                key: 'onLineMaterialNum',
                tips: '消耗大于0的素材数'
              }
            ] : [
              {
                title: '素材名称',
                key: 'materialName'
              },
              {
                title: '素材ID',
                key: 'adMaterialId'
              }
              // {
              //   title: '素材内容',
              //   key: 'materialContent',
              //   tableView: tableItemType.tableView.clipboard,
              //   tips: '视频素材的链接'
              // }
            ]),
            {
              title: '上线项目数',
              key: 'onLineProjectNum',
              tips: '素材上线的项目数，上线定义：消耗金额大于0的素材，按照项目ID去重'
            },
            {
              title: '累计上线项目数',
              key: 'onLineProjectTotalNum',
              tips: '素材累计上线的项目数'
            },
            {
              title: '上线计划数',
              key: 'onLineAdNum',
              tips: '素材上线的广告计划数，按照计划ID去重'
            },
            {
              title: '累计上线计划数',
              key: 'onLineAdTotalNum',
              tips: '素材累计上线的广告计划数'
            },
            {
              title: '户均上线计划数',
              key: 'onLineAvgNum',
              tips: '户均上线计划数，计算公式：上线计划数/上线项目数'
            },
            {
              title: '累计户均上线计划数',
              key: 'onLineAvgTotalNum',
              tips: '累计户均上线计划数，计算公式：累计上线计划数/累计上线项目数'
            },
            ...(this.groupType === 'date' ? [
              {
                title: '冷启动通过数',
                key: 'coldStartPassNum',
                tips: '素材的冷启动通过数，冷启动通过定义：素材累计转化数大于20，转化数包括浅层目标&深层目标，优先使用深层目标'
              },
              {
                title: '冷启动通过率',
                key: 'coldStartPassRatio',
                tips: '上线素材的冷启动通过占比，计算公式：冷启动通过数/上线素材数'
              },
              {
                title: '冷启动通过平均时长（H）',
                key: 'coldStartPassAvgDuration',
                tips: '广告计划中素材的冷启动通过平均时长，∑冷启动通过时长/冷启动通过数'
              }
            ] : [
              {
                title: '冷启动通过时长（H）',
                key: 'coldStartPassTotalDuration',
                tips: '素材的冷启动通过时长，冷启动定义：累计转化数大于20，转化数包括浅层目标&深层目标，优先使用深层目标；冷启动未通过时展示为-',
                render(h, params) {
                  if (!params.data.row.coldStartPassTotalDuration) {
                    return h('span', '-')
                  }

                  const minutes = params.data.row.coldStartPassTotalDuration
                  const hours = Number(Number(minutes) / 60).toFixed(2)
                  return h('span', hours)
                }
              },
              {
                title: '衰退周期（天）',
                key: 'recessionCycle',
                tips: '素材的衰退持续天数，衰退定义：连续3天每天消耗金额下降15%，当日消耗金额为0不计入；衰退查询周期（90天）'
              },
              {
                title: '平均出价（元）',
                key: 'averageBid',
                tips: '广告计划中素材的平均出价，包含浅层目标出价&深层目标出价，计算公式：∑目标出价/上线计划数；出价金额取实时值'
              }
            ])
          ].map(this.renderHeaderTransfer)
        },
        {
          title: '展示数据',
          key: 'present',
          isCustom: true,
          children: [
            {
              title: '总消耗（元）',
              key: 'materialTotalCost',
              tips: '素材的总消耗'
            },
            {
              title: '展示数',
              key: 'showNum',
              tips: '素材展示数'
            },
            {
              title: '点击数',
              key: 'clickNum',
              tips: '素材点击数'
            },
            {
              title: '转化数',
              key: 'transformNum',
              tips: '素材转化数，包括浅层转化数、深层转化数'
            },
            {
              title: 'CTR',
              key: 'ctr',
              tips: '素材展示点击率，计算公式：点击数/展示数'
            },
            {
              title: 'CVR',
              key: 'cvr',
              tips: '素材点击转化率，计算公式：转化数/点击数数'
            },
            {
              title: 'PVR',
              key: 'pvr',
              tips: '素材的广告效果容积，计算公式：CTR*CVR*1000'
            },
            {
              title: 'CPM',
              key: 'cpm',
              tips: '素材的千次展示成本，计算公式：总消耗/展示数*1000'
            },
            ...(this.groupType === 'date' ? [] : [
              {
                title: 'ECPM',
                key: 'ecpm',
                tips: '素材的千次展示收益，计算公式：CTR*CVR*平均出价*1000'
              }
            ])
          ].map(this.renderHeaderTransfer).map(item => ({
            ...item,
            sortable: 'custom'
          }))
        },
        {
          title: '转化数据(媒体侧统计数据)',
          key: 'transform',
          isCustom: true,
          children: [
            {
              title: '激活数',
              key: 'activateNum',
              tips: '素材产生的激活设备数，按照设备去重'
            },
            {
              title: '激活成本（元）',
              key: 'activateCost',
              tips: '素材的激活成本，计算公式：总消耗/激活数'
            },
            {
              title: '付费数',
              key: 'payNum',
              tips: '素材产生的付费设备数，按照设备去重'
            },
            {
              title: '付费金额（元）',
              key: 'payAmount',
              tips: '素材产生的付费金额'
            },
            {
              title: '付费成本（元）',
              key: 'payCost',
              tips: '素材的付费成本，计算公式：总消耗/付费数'
            },
            {
              title: 'ROI',
              key: 'roi',
              tips: '素材的投资回报率，计算公式：付费金额/总消耗'
            }
          ].map(this.renderHeaderTransfer).map(item => ({
            ...item,
            sortable: 'custom'
          }))
        }, {
          title: '视频互动数据',
          key: 'show',
          isCustom: true,
          children: [
            {
              title: '播放数',
              key: 'playNum',
              tips: '视频素材的播放数，播放时间大于0S的数量'
            },
            {
              title: '有效播放数',
              key: 'effectivePlayNum',
              tips: '视频素材的有效播放数，竞价广告播放时间大于等于10秒的数量，如果视频总时长不足10秒，则记录播放完成的次数'
            },
            {
              title: '有效播放率',
              key: 'effectivePlayRatio',
              tips: '视频素材的有效播放率，计算公式：有效播放数/播放数'
            },
            {
              title: '投诉数',
              key: 'complainNum',
              tips: '素材的投诉数，用户认为广告质量较差而点击举报的行为数量'
            },
            {
              title: '投诉率',
              key: 'complainRatio',
              tips: '素材的投诉率，计算公式：投诉数/播放数'
            },
            {
              title: '不感兴趣数',
              key: 'notInterested',
              tips: '素材的不感数，用户认为广告质量较差而点击举报的行为数量'
            },
            {
              title: '不感兴趣率',
              key: 'notInterestedRatio',
              tips: '素材的不感兴趣率，计算公式：不感兴趣数/播放数'
            }
          ].map(this.renderHeaderTransfer).map(item => ({
            ...item,
            sortable: 'custom'
          }))
        },
        ... (this.groupType === 'date' ? [{
          title: '操作栏',
          width: '100px',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          fixed: 'right',
          activeType: [
            {
              text: '素材详情',
              key: 'edit1',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: ($index, item, params) => {
                this.$router.push({
                  path: '/qjjp/statisticsOverview/dropMaterial/material',
                  query: {
                    ...this.listQuery
                  }
                })
              }
            }
          ]
        }] : [])
      ]
    }
  },
  created() {
    // const query = this.$route.query
    // this.listQuery = { ...this.listQuery, ...query }
  },
  methods: {
    renderHeaderTransfer(item) {
      const obj = { ...item }
      if (obj.tips) {
        obj.renderHeader = (...args) => this.renderHeader(...args, obj.tips.split('|'))
      }
      obj.isCustom = true
      return obj
    },
    renderHeader(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h(
            'div',
            {
              slot: 'content'
            },
            [textArr.map(item => h('div', null, item))]
          ),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    }
  }
}
</script>

<style lang="scss" scoped>
.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
