<template>
  <div>
    <page :request="request" :list="list" table-title="CPS佣金比率管理" />
  </div>
</template>
<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { EDIT_CPS, GET_CPS_LIST } from '@/api/cps'

export default {
  components: {
    page
  },
  data() {
    return {
      request: {
        getListUrl: GET_CPS_LIST,
        updateHttp: EDIT_CPS
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '平台',
          key: 'name',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '爵士卡用户比率',
          key: 'vipRebateRatio',
          type: formItemType.input,
          reg: ['required'],
          options: {
            placeholder: '输入爵士卡用户佣金比率'
          }
        },
        {
          title: '普通用户比率',
          key: 'commonRebateRation',
          type: formItemType.input,
          reg: ['required'],
          options: {
            placeholder: '输入普通用户佣金比率'
          }
        },
        // {
        //   title: "分享订单佣金比例",
        //   key: "shareRebateRation",
        //   type: formItemType.input,
        //   reg: ['required'],
        //   options: {
        //     placeholder: '分享订单佣金比例'
        //   }
        // },
        {
          title: '技术服务费比率',
          key: 'shareFee',
          type: formItemType.input,
          reg: ['required'],
          options: {
            placeholder: '输入技术服务费比率'
          },
          tableHidden: true
        },
        {
          type: tableItemType.active,
          width: 180,
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              type: tableItemType.activeType.detailsDialog
            }
          ]
        }
      ]
    }
  },
  methods: {}
}
</script>
