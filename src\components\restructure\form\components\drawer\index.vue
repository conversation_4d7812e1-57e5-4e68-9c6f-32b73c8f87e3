<template>
  <div class="drawer-container">
    <el-drawer
      :title="getTitle"
      :visible.sync="drawerVisibleInternal"
      :size="drawerSize"
      :direction="direction"
      :destroy-on-close="true"
      :wrapperClosable="false"
      :before-close="handleClose"
    >
      <!-- 内容区域 -->
      <div class="drawer-content">
        <SectionForm
          ref="formNode"
          :results="data"
          :label-width="labelWidth"
          :form-item-list="formItemListCopy"
          @formMount="getById"
        />
      </div>

      <!-- 底部按钮区域 -->
      <div class="drawer-footer">
        <div v-if="dialogFooterState === dialogFooterStateConfig.common">
          <el-button size="medium" @click="$emit('update:drawerVisible', false)">取 消</el-button>
          <el-button size="medium" type="primary" :loading="loading" @click="submit">确 定</el-button>
        </div>
        <div v-if="dialogFooterState === dialogFooterStateConfig.close" style="text-align: center">
          <el-button size="medium" @click="$emit('update:drawerVisible', false)">关 闭</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import SectionForm from '../SectionForm'
import {Message} from 'element-ui'
import {dialogFooterState, error, submission, updateIgnoreKey} from '@/config/sysConfig'
import dialogFormMixins from '../dialog/mixins/index'
import {copy} from '@/config/basicsMethods'

export default {
  name: 'DrawerForm',
  components: {
    SectionForm
  },
  mixins: [dialogFormMixins],
  props: {
    drawerVisible: {
      type: Boolean,
      default: false
    },
    drawerSize: {
      type: String,
      default: '50%'
    },
    direction: {
      type: String,
      default: 'rtl'
    }
  },
  data() {
    return {
      dialogFooterStateConfig: dialogFooterState,
      loading: false,
      drawerVisibleInternal: this.drawerVisible
    }
  },
  computed: {
    getSubHttp: function () {
      if (this.basics.isNull(this.urls.insertHttp) && this.basics.isNull(this.urls.updateHttp))
        return () => Promise.resolve()
      return this.$store.state.submission.submitType === submission.insert ? this.urls.insertHttp : this.urls.updateHttp
    },
    getTitle: function () {
      return this.$store.state.submission.submitType === submission.insert ? this.title + '添加' : this.title + '详情'
    },
    formItemListCopy() {
      return this.formItemList ? copy(this.formItemList.filter(item => item.type)) : []
    }
  },
  watch: {
    drawerVisibleInternal(to) {
      if (!to) {
        this.$emit('update:drawerVisible', to)
      }
    },
    drawerVisible(to) {
      this.drawerVisibleInternal = to
    },
    data(to) {
      this.getById()
    },
  },
  methods: {
    handleClose() {
      this.drawerVisibleInternal = false
    },
    submit() {
      this.$refs.formNode
        .submitForm()
        .then(
          data => {
            this.loading = true
            let getData = {...{}, ...this.data, ...data}
            updateIgnoreKey.forEach(item => {
              delete getData[item]
            })
            getData = {...getData, ...this.beforeUpdate(copy(getData))}
            this.getSubHttp(getData, copy(this.data))
              .then(msg => {
                this.loading = false
                this.drawerVisibleInternal = false
                this.$emit('getSubSuccess', true)
              })
              .catch(msg => {
                this.loading = false
              })
          },
          msg => {
            Message({
              message: '格式填写错误',
              type: 'error'
            })
          }
        )
        .catch(msg => {
          error(msg)
        })
    },
    /*
     * 获取詳情
     * */
    getById() {
      if (this.$store.state.submission.submitType !== submission.update) {
        return false
      }
      const http = this.data
      if (this.basics.isObj(http)) {
        this.getByIdData(http)
      } else {
        if (this.basics.isFunction(http)) {
          http().then(msg => {
            this.getByIdData(msg)
          })
        }
      }
    },
    /*
     * 获取详情遍历
     * */
    getByIdData(setData) {
      this.formItemListCopy.forEach(item => {
        if (item.childKey && this.basics.isArray(item.childKey)) {
          item.childKey.map((childKeyItem, index) => {
            item.val[index] = this.basics.isNull(setData[childKeyItem]) ? '' : setData[childKeyItem]
          })
        } else {
          item.val = this.basics.isNull(setData[item.key]) ? '' : setData[item.key]
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-container {
  ::v-deep .el-drawer__header {
    display: flex;
    flex-direction: row-reverse;
    padding: 0;
  }

  ::v-deep .el-dialog__close {
    padding: 6px;
    color: #fff;
    background-color: #000;
    margin-right: 10px;
  }

  ::v-deep .el-drawer__close-btn i {
    font-size: 26px !important;
  }
}

.drawer-content {
  padding: 20px;
  height: calc(100% - 80px);
  overflow-y: auto;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  border-top: 1px solid #ebeef5;
  background: #fff;
  text-align: right;
}

::v-deep .el-drawer__header {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;

  > span {
    font-size: 20px;
  }

  .el-drawer__close-btn {
    i {
      font-size: 30px;
    }
  }
}

::v-deep .el-drawer__body {
  padding: 0;
  position: relative;
}
</style>
