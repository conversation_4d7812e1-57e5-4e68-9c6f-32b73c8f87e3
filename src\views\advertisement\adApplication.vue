<template>
  <div>
    <page :request="request" :list="list" table-title="广告应用管理" />
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import {
  put_application,
  post_application,
  get_application_page,
  get_ad_list
} from '@/api/advertisement'
import {count_channel_application_list} from '@/api/NewChannel'
import { formItemType, tableItemType } from '@/config/sysConfig'
const validatorInt = (rule, value, callback) => {
  if (!/^[0-9]*$/.test(value)) {
    callback(new Error('需输入整数'))
  } else {
    callback()
  }
}
export default {
  components: {
    page
  },
  props: {},
  data() {
    return {
      listQuery: {
        siteId:'d6c4a4bbd1f748f89e879c00d60edd8e'
      },
      tabs: [],
      activeName: '',
      reFresh: false,
      adIdList: [],
      request: {
        updateHttp: put_application,
        insertHttp: post_application,
        getListUrl: async data => {
          this.listQuery = {...this.listQuery,...data}
          if(!this.siteIds.length){
            await count_channel_application_list().then((response) => {
              this.siteIds = response.data
            })
          }          
          return get_application_page(this.listQuery)
        }
      },
      siteIds:[]
    }
  },
  computed: {
    list() {
      return [
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId',
          },
          val:'d6c4a4bbd1f748f89e879c00d60edd8e',
          reg: ['required'],
          clearable:false,
          search: true,
          tableHidden:true
        },
        {
          title: '应用名称',
          key: 'siteName',
          formHidden:true,
        },
        {
          title: '应用备注',
          key: 'name',
          type: formItemType.input,
          search: true,
          reg: ['required']
        },
        {
          title: '应用ID',
          key: 'appId',
          reg: ['required'],
          type: formItemType.input
        },
        {
          title: '系统设备',
          key: 'device',
          type: formItemType.select,
          search: true,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          list: this.$utils.getOsList(),
          options: {
            placeholder: '请选择设备系统'
          }
        },
        {
          title: '供应商',
          key: 'adName',
          formHidden: true,
          type: formItemType.input
        },
        {
          title: '供应商',
          key: 'adId',
          tableHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.adIdList,
          reg: ['required'],
          options: {
            placeholder: '请选择设备系统'
          }
        },
        {
          title: '状态',
          key: 'status',
          list: [
            { label: '启用', value: true },
            { label: '停用', value: false }
          ],
          type: formItemType.radio,
          reg: ['required'],
          tableView: tableItemType.tableView.text,
          options: {
            valueType: 'Boolean'
          }
        },
        {
          type: tableItemType.active,
          width: '100px',
          headerContainer: true,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.detailsDialog,
              theme: 'warning'
            }
          ]
        }
      ]
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      get_ad_list().then(res => {
        this.adIdList = res.data.map(item => {
          return {
            label: item.name,
            value: item.id
          }
        })
      })
    }
  }
}
</script>

<style lang="" scoped></style>
