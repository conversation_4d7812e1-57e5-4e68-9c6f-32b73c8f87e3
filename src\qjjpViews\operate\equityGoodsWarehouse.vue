<template>
    <div>
        <page ref="equityGoodsWarehouse" :request="request" :list="list" table-title="权益商品库" @rowClick="rowClick">
            <div v-if="params.pageType == 'page'" slot="searchContainer" style="display: inline-block">
                <el-button type="primary" size="small" class="search-btn" @click="syncThirdGoodsFN">同步商品数据</el-button>
                <el-button type="primary" plain size="small" class="search-btn"
                    @click="addDetail('add')">添加话费</el-button>
                <!-- <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button> -->
            </div>
        </page>
        <el-drawer v-if="drawer" :visible.sync="drawer" direction="rtl" size="50%" :with-header="false"
            :wrapper-closable="false">
            <div class="close_button">
                <i class="el-icon-close" @click="drawer = false" />
            </div>
            <div class="drawer_package">
                <div class="drawer_title">
                    <span>商品编辑/新增</span>
                </div>
                <div class="addForm_package">
                    <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="180px" class="demo-ruleForm">
                        <div class="form_view">
                            <div class="form_view_title">
                                <div class="title_line" /><span>基础信息</span>
                            </div>
                            <el-form-item label="商品名称" prop="name" :rules="addRules.common">
                                <div :style="{ width: '200px', 'display': 'inline-block', 'margin-right': '10px' }">
                                    <el-input v-model="addForm.name" placeholder="请输入商品名称" maxlength="20"
                                        :disabled="Number(addForm.type) == 0" />
                                </div>
                                <span>{{ addForm.name ? addForm.name.length : 0 }}/20</span>
                            </el-form-item>
                            <el-form-item label="权益类型" prop="type" :rules="addRules.common"
                                :style="{ 'display': 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-select v-model="addForm.type" placeholder="请选择权益类型"
                                        :disabled="Number(addForm.type) == 0">
                                        <el-option v-for="item in typeList" :key="item.id" :label="item.name"
                                            :value="item.id" :disabled="Number(item.id) == 0" />
                                    </el-select>
                                </div>
                            </el-form-item>
                            <el-form-item label="供应商" prop="goodsSupplier" :rules="addRules.common"
                                :style="{ 'display': 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-select v-model="addForm.goodsSupplier" placeholder="请选择供应商"
                                        :disabled="Number(addForm.type) == 0">
                                        <el-option v-for="item in supplierListData" :key="item.value"
                                            :label="item.label" :value="item.value"
                                            :disabled="Number(item.type) == 0" />
                                        <!-- <el-option v-for="item in supplierListData" :key="item.value">{{ item.type
                                            }}</el-option> -->
                                    </el-select>
                                </div>
                            </el-form-item>
                            <el-form-item label="话费面额" prop="billFaceAmount" :rules="addRules.common"
                                v-if="addForm.type == 1" :style="{ 'display': 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-select v-model="addForm.billFaceAmount" placeholder="请选择话费面额"
                                        :disabled="Number(addForm.type) == 0">
                                        <el-option v-for="item in hfList" :key="item.id" :label="item.name"
                                            :value="item.id" />
                                    </el-select>
                                </div>
                            </el-form-item>
                            <el-form-item label="充值类型" prop="goodsType" :rules="addRules.common"
                                :style="{ 'display': 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-select v-model="addForm.goodsType" placeholder="请选择充值类型"
                                        :disabled="Number(addForm.type) == 0">
                                        <el-option v-for="item in goodsTypeList" :key="item.id" :label="item.name"
                                            :value="item.id" />
                                    </el-select>
                                </div>
                            </el-form-item>
                            <el-form-item label="成本价" prop="price" :rules="addRules.common">
                                <div :style="{ width: '200px', 'display': 'inline-block', 'margin-right': '10px' }">
                                    <el-input v-model="addForm.price" placeholder="请输入成本价"
                                        :disabled="Number(addForm.type) == 0"
                                        oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')"
                                        @blur="addForm.price = $event.target.value" />
                                </div>
                                <span>（元）</span>
                            </el-form-item>
                            <el-form-item label="原价" prop="officialPrice" :rules="addRules.common">
                                <div :style="{ width: '200px', 'display': 'inline-block', 'margin-right': '10px' }">
                                    <el-input v-model="addForm.officialPrice" placeholder="请输入原价"
                                        :disabled="Number(addForm.type) == 0"
                                        oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')"
                                        @blur="addForm.officialPrice = $event.target.value" />
                                </div>
                                <span>（元）</span>
                            </el-form-item>
                            <el-form-item label="售价" prop="salePrice" :rules="addRules.common">
                                <div :style="{ width: '200px', 'display': 'inline-block', 'margin-right': '10px' }">
                                    <el-input v-model="addForm.salePrice" placeholder="请输入售价"
                                        oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')"
                                        @blur="addForm.salePrice = $event.target.value" />
                                </div>
                                <span>（元）</span>
                            </el-form-item>
                            <el-form-item label="封面图" prop="url" :rules="addForm.common">
                                <uploadFile v-model="addForm.url" :width="180" :height="120" :multiple="false"
                                    :size="imgSize" :limit="1">
                                    <div class="addDiv">
                                        <i class="el-icon-plus" />
                                        <span class="txt">上传图标(.jpg/.png/.gif) <br>图片大小{{ filterSize(imgSize) }}</span>
                                    </div>
                                </uploadFile>
                            </el-form-item>
                        </div>

                        <div class="form_view">
                            <div class="form_view_title">
                                <div class="title_line" /><span>商品状态</span>
                            </div>
                            <el-form-item label="状态" prop="sellStatus" :rules="addRules.common">
                                <el-switch v-model="addForm.sellStatus" :active-value="1" :inactive-value="0" />
                            </el-form-item>
                        </div>
                        <div :style="{ 'text-align': 'right', width: '100%' }" class="view_button">
                            <el-button @click="drawer = false">取消</el-button>
                            <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
                        </div>
                    </el-form>
                </div>
            </div>
        </el-drawer>
    </div>
</template>
<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getmerchant } from '@/yjgjApi/statisticsOverview'
// import { syncThirdGoods, thirdGoodsPage, supplierList } from '@/yjgjApi/equityCommodity'
import { thirdGoodsPage, syncThirdGoods, supplierList, thirdGoodsCreate, thirdGoodsUpdate } from '@/qjjpApi/operate'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
    name: 'equityGoodsWarehouse',
    components: {
        page,
        uploadFile: () => import('@/components/yc-upload/handleUploadImage')
    },
    props: {
        params: {
            type: Object,
            default: () => {
                return {
                    pageType: 'page'
                }
            }
        }
    },
    data() {
        return {
            goodsTypeList2: [],
            goodsTypeList: [
                {
                    id: -1,
                    name: '客服直冲'
                },
                {
                    id: 6,
                    name: 'api充值'
                },
            ],
            imgSize: 500 * 1024,
            hfList: [
                {
                    name: '10',
                    id: 10
                },
                {
                    name: '20',
                    id: 20
                },
                {
                    name: '30',
                    id: 30
                },
            ],
            gysList: [
                {
                    name: '蓝色兄弟',
                    id: 0
                },
            ],
            typeList: [
                {
                    name: '话费券',
                    id: 1
                },
                {
                    name: '影音会员',
                    id: 0
                }
            ],
            addRules: {
                common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
            },
            type: false,
            addForm: {
                id: '',
                goodsSupplier: '',
                sellStatus: 1,
                billFaceAmount: '',
                officialPrice: '',
                price: '',
                salePrice: '',
                updateUserId: '',
                updateUserName: '',
                url: ''
            },
            drawer: false,
            stautsList: [
                {
                    label: '启用',
                    value: 1
                },
                {
                    label: '禁用',
                    value: 0
                }
            ],
            typeListData: [
                {
                    name: '话费券',
                    id: 1
                },
                {
                    name: '影音会员',
                    id: 0
                }
            ],
            supplierListData: [],
            listQuery: {
                ...(this.params.pageType != 'page' ? { sellStatus: 1, goodsSupplier: this.params.goodsSupplier } : {})
            },
            request: {
                getListUrl: async data => {
                    this.listQuery = { ...this.listQuery, ...data }
                    if (this.supplierListData.length == 0) {
                        const { data: supplierListData } = await supplierList()
                        this.supplierListData = supplierListData.map(n => ({ label: n.name, value: n.code, type: n.type }))
                    }
                    const list = await thirdGoodsPage(this.listQuery)
                    const { records, total } = list.data
                    let dataList = []
                    if (records && records.length) {
                        dataList = {
                            total: total,
                            rows: records
                        }
                    }
                    const result = {
                        data: dataList
                    }
                    return result
                }
            }
        }
    },
    computed: {
        list() {
            return [
                {
                    title: '商品名称',
                    key: 'name',
                    type: formItemType.input,
                    search: true,
                    clearable: true,
                    tableHidden: true,
                },
                {
                    title: '供应商',
                    key: 'goodsSupplier',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.supplierListData,
                    search: true,
                    tableHidden: true,
                    clearable: true
                },
                {
                    title: '状态',
                    key: 'sellStatus',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.stautsList,
                    search: true,
                    tableHidden: true,
                    clearable: true
                },
                {
                    title: '序号',
                    key: 'sort',
                    render: (h, params) => {
                        const { pageNumber = 1, pageSize = 10 } = this.listQuery
                        return h('span', params.data.$index + 1 + (pageNumber - 1) * pageSize)
                    }
                },
                {
                    title: '供应商',
                    key: 'goodsSupplier',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.supplierListData,
                    clearable: true
                },
                {
                    title: '权益类型',
                    key: 'type',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.typeListData,
                    clearable: true,
                    listFormat: {
                        label: 'name',
                        value: 'id'
                    }
                },
                {
                    title: '产品编号',
                    key: 'goodsNo'
                },
                {
                    title: '商品名称',
                    key: 'name',
                    type: formItemType.input
                },
                {
                    title: '商品封面图',
                    key: 'url',
                    type: formItemType.upload,
                    tableView: tableItemType.tableView.picture
                },
                {
                    title: '充值类型',
                    key: 'goodsType',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.goodsTypeList,
                    listFormat: {
                        label: 'name',
                        value: 'id'
                    }
                },
                {
                    title: '话费面额',
                    key: 'billFaceAmount'
                },
                {
                    title: '成本价',
                    key: 'price'
                },
                {
                    title: '原价',
                    key: 'officialPrice'
                },
                {
                    title: '售价',
                    key: 'salePrice'
                },
                {
                    title: '创建时间',
                    key: 'createTime',
                    render: (h, params) => {
                        if (!params.data.row.createTime) {
                            return h('span', '--')
                        }
                        return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
                    }
                },
                {
                    title: '更新时间',
                    key: 'updateTime',
                    render: (h, params) => {
                        if (!params.data.row.updateTime) {
                            return h('span', '--')
                        }
                        return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
                    }
                },
                {
                    title: '更新人',
                    key: 'updateUserName'
                },
                {
                    title: '状态',
                    key: 'sellStatus',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.stautsList,
                    listFormat: {
                        label: 'label',
                        value: 'value'
                    }
                },
                {
                    title: '操作',
                    type: tableItemType.active,
                    headerContainer: false,
                    key: 'operation',
                    activeType: [
                        {
                            text: '编辑',
                            key: 'edit1',
                            // type: tableItemType.activeType.detailsDialog
                            type: tableItemType.activeType.event,
                            click: ($index, item, params) => {
                                console.log(params)
                                this.addDetail('edit', params)
                            }
                        }
                    ]
                }
                // {
                //   key: 'dateSearch',
                //   title: '日期',
                //   type: formItemType.datePickerDaterangeGai,
                //   options: {
                //     format: 'YYYY-MM-DD',
                //     valueFormat: 'yyyy-MM-dd'
                //   },
                //   childKey: ['startDate', 'endDate'],
                //   formHidden: true,
                //   search: true,
                //   val: [currentDate, currentDate]
                // },
                // {
                //   title: '商户平台',
                //   key: 'way',
                //   type: formItemType.select,
                //   tableView: tableItemType.tableView.text,
                //   list: this.list1,
                //   listFormat: {
                //     label: 'name',
                //     value: 'id'
                //   },
                //   val: this.listQuery.siteId,
                //   reg: ['required'],
                //   search: true,
                //   clearable: true
                // },
            ]
        }
    },
    methods: {
        filterSize(size) {
            const pow1024 = (num) => {
                return Math.pow(1024, num)
            }
            if (!size) return ''
            if (size < pow1024(1)) return size + ' B'
            if (size < pow1024(2)) return (size / pow1024(1)).toFixed(0) + ' KB'
            if (size < pow1024(3)) return (size / pow1024(2)).toFixed(0) + ' MB'
            if (size < pow1024(4)) return (size / pow1024(3)).toFixed(0) + ' GB'
            return (size / pow1024(4)).toFixed(2) + ' TB'
        },
        handMessageStyleListAdd(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    if (!this.addForm.url) {
                        this.$message.error('请上传封面图');
                        return
                    }
                    if (this.type == 'edit') {
                        thirdGoodsUpdate({ ...this.addForm }).then(res => {
                            if (res.code == 200) {
                                this.drawer = false
                                this.$message.success('操作成功')
                                this.$store.dispatch('tableRefresh', this)
                            }
                        })
                    } else {
                        thirdGoodsCreate({ ...this.addForm }).then(res => {
                            if (res.code == 200) {
                                this.drawer = false
                                this.$message.success('操作成功')
                                this.$store.dispatch('tableRefresh', this)
                            }
                        })
                    }
                }
            })
        },
        addDetail(type, params = {}) {
            this.type = type
            if (type == 'add') {
                params = {
                    id: '',
                    goodsSupplier: '',
                    sellStatus: 1,
                    billFaceAmount: '',
                    officialPrice: '',
                    price: '',
                    salePrice: '',
                    updateUserId: '',
                    updateUserName: '',
                    url: ''
                }
            }
            this.drawer = true
            this.addForm = { ...params }
            // this.$confirmDialog({
            //     options: {
            //         className: 'addCommodity',
            //         title: `${type == 'add' ? '添加' : '编辑'}话费`,
            //         width: '800px'
            //         // customModal: true,
            //         // customModalIndex: 1000
            //     },
            //     component: () => import('@/yjgjViews/equityCommodity/addCommodity'),
            //     params: {
            //         ...params
            //     }
            // }).then(res => {
            //     if (res.action === 'confirm') {
            //         this.$refs.equityGoodsWarehouse.getSubSuccess({ isCurrent: type == 'edit' })
            //     }
            // })
        },
        syncThirdGoodsFN() {
            syncThirdGoods().then(res => {
                if (res.code == 200) {
                    this.$refs.equityGoodsWarehouse.getSubSuccess()
                }
            })
        },
        rowClick(e) {
            if (this.params.pageType == 'dialog') {
                this.$emit('action', 'confirm', e)
            }
        }
    }
}
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__body {
    overflow: scroll;
    // padding-bottom: 20px;
    padding: 0 30px 20px;
    position: relative;
    /* overflow-x: auto; */
}

::v-deep .el-drawer__header {
    span {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        line-height: 30px;
        text-align: left;
        font-style: normal;
    }

}

::v-deep .el-drawer__body {}

.close_button {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    background-color: rgb(0, 0, 0, 1);
    text-align: center;
    cursor: pointer;

    i {
        color: white;
        line-height: 40px;
    }
}

.drawer_package {
    height: 100%;
    position: relative;

    .drawer_title {
        padding: 10px 20px 5px;
        vertical-align: middle;

        span {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 20px;
            color: #333333;
            line-height: 30px;
            text-align: left;
            font-style: normal;
        }

    }
}

.addForm_package {
    background-color: rgb(189, 184, 184, 0.1);
    padding: 15px;
    height: 100%;

    .demo-ruleForm {
        background-color: #ffffff;
        width: 100%;
        height: 100%;
        position: relative;

        .view_button {
            position: absolute;
            bottom: 0;
        }
    }
}

.view_button {
    background-color: #ffffff;
    padding: 20px;
    border-top: 1px dashed #000000;

    ::v-deep .el-button {
        margin: 0 10px;
    }
}

.form_view {
    // margin: 0 0px 20px;
    background-color: #ffffff;
    // border: 1px solid rgba(0,0,0,0.2);
    width: 100%;
    padding: 15px;
    border-radius: 5px;

    .form_view_title {
        margin-bottom: 20px;

        .title_line {
            width: 2px;
            height: 10px;
            background-color: #66b1ff;
            display: inline-block;
            vertical-align: middle;
        }

        span {
            padding-left: 5px;
            vertical-align: middle;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 20px;
            color: #333333;
            line-height: 30px;
            text-align: left;
            font-style: normal;
        }
    }
}

.excel-upload {
    text-align: center;

    &::v-deep.el-upload-dragger {
        width: 225px;
    }
}

.fail_list {
    margin-top: 10px;

    .tip {
        width: 100%;
        height: 20px;
        background: #e4e7ed;
        text-align: center;
        line-height: 20px;
        margin: 10px 0;
    }
}

.copy-btn {
    cursor: pointer;
}

::v-deep .activeButton {
    .el-button {
        margin-right: 5px;
        padding: 7px 6px;
    }
}

.addDiv {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .el-icon-plus {
        font-size: 16px;
        color: #409EFF;
    }

    .txt {
        width: 100%;
        height: 20px;
        font-size: 12px;
        color: #409EFF;
        text-align: center;
        line-height: 1.5;
        transform: scale(0.8);
    }
}
</style>