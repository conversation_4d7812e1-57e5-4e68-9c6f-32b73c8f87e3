<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 14:33:57
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-10-08 11:34:40
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="开放者账号列表">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small"
          @click="handleAdd">新增账号</el-button>
      </div>

    </page>
    <el-drawer v-if="drawer" :visible.sync="drawer" direction="rtl" size="50%" :with-header="false"
      :wrapper-closable="false">
      <div class="close_button">
        <i class="el-icon-close" @click="drawer = false" />
      </div>
      <div class="drawer_package">
        <div class="drawer_title">
          <span>模型编辑/新增</span>
        </div>
        <div class="addForm_package">
          <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="180px" class="demo-ruleForm">
            <div class="form_view">
              <div class="form_view_title">
                <div class="title_line" /><span>基础信息</span>
              </div>
              <el-form-item label="应用名称" prop="siteId" :rules="addRules.common">
                <el-select v-model="addForm.siteId" placeholder="请选择模型所属平台">
                  <el-option v-for="item in siteIdsList" :key="item.siteId" :label="item.name" :value="item.siteId" />
                </el-select>
              </el-form-item>
              <el-form-item label="媒体平台" prop="platform" :rules="addRules.common">
                <el-select v-model="addForm.platform" placeholder="请选择模型所属平台">
                  <el-option v-for="item in list5" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
              <div v-if="addForm.platform == 2">
                <el-form-item label="开发账号主体" prop="subject" :rules="addRules.common"
                  :style="{ display: 'inline-block' }">
                  <div :style="{ width: '200px' }">
                    <el-input v-model="addForm.subject" placeholder="请输入开发者账号主体" maxlength="30" />
                  </div>
                </el-form-item>
                <span :style="{ padding: '0 0 0 10px' }">{{ addForm.subject.length }}/30</span>
              </div>
              <div>
                <el-form-item label="appID" prop="appId" :rules="addRules.common" :style="{ display: 'inline-block' }">
                  <div :style="{ width: '200px' }">
                    <el-input v-model="addForm.appId" placeholder="appID" maxlength="60" />
                  </div>
                </el-form-item>
                <span :style="{ padding: '0 0 0 10px' }">{{ addForm.appId.length }}/60</span>
              </div>
              <div>
                <el-form-item label="secretID" prop="secretId" :rules="addRules.common"
                  :style="{ display: 'inline-block' }">
                  <div :style="{ width: '200px' }">
                    <el-input v-model="addForm.secretId" placeholder="secretID" maxlength="60" />
                  </div>
                </el-form-item>
                <span :style="{ padding: '0 0 0 10px' }">{{ addForm.secretId.length }}/60</span>
              </div>

              <div class="form_view">
                <div class="form_view_title">
                  <div class="title_line" /><span>开发账号状态</span>
                </div>
                <el-form-item label="状态" prop="status" :rules="addRules.common">
                  <el-switch v-model="addForm.status" :active-value="1" :inactive-value="0" />
                </el-form-item>
              </div>
              <div :style="{ 'text-align': 'right', width: '100%' }" class="view_button">
                <el-button @click="drawer = false">取消</el-button>
                <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { undertakeList, addOrUpdate, undertakeListexport, modelRepositorypage, modelRepositoryplatform, modelRepositoryexit } from '@/qjjpApi/operate'
import { applicationList } from '@/qjjpApi/adver'
import { accountpage, accountaddOrUpdate } from '@/qjjpApi/deliveryManage'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      addForm: {
        id: '',
        platform: '',
        appId: '',
        secretId: '',
        subject: '',
        status: 1
      },
      mediaList: [],
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      drawer: false,
      list4: [
        {
          id: 1,
          name: '会员用户'
        },
        {
          id: 0,
          name: '非会员用户'
        }
      ],
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      list2: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ],
      list3: [
        {
          id: 1,
          name: '落地页'
        }
      ],
      list5: [
        {
          id: 1,
          name: '字节'
        },
        {
          id: 2,
          name: '快手'
        },
        {
          id: 3,
          name: '百度'
        },
        {
          id: 4,
          name: '腾讯'
        }
      ],
      siteIdsList: [],
      siteIds: [],
      listQuery: {
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await applicationList({ status: 1, pageSize: 999 }).then(res => {
              if (res.code === 200) {
                if (res.data.records && res.data.records.length) {
                  this.siteIds = res.data.records
                }
              }
            })
            await count_channel_application_list().then(res => {
              if (res.code === 200) {
                this.siteIdsList = res.data
              }
            })
          }

          const list = await accountpage(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '媒体平台',
          key: 'platform',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list5,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        // {
        //   title: '投放应用',
        //   key: 'siteId',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.siteIds,
        //   listFormat: {
        //     label: 'applicationName',
        //     value: 'id'
        //   },
        //   reg: ['required'],
        //   search: true,
        //   clearable: true,
        //   tableHidden: true
        // },
        {
          title: 'appID',
          type: formItemType.input,
          search: true,
          clearable: true,
          searchKey: 'appId',
          tableHidden: true
        },
        {
          title: 'secretID',
          type: formItemType.input,
          search: true,
          clearable: true,
          searchKey: 'secretId',
          tableHidden: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '媒体平台',
          key: 'platformStr'
        },
        {
          title: '开发账号主体',
          key: 'subject'
        },
        {
          title: 'appID',
          key: 'appId'
        },
        {
          title: 'secretID',
          key: 'secretId'
        },
        {
          title: '创建人',
          key: 'creatorName'
        },
        {
          title: '创建时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required']
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',

              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.addForm = JSON.parse(JSON.stringify(params))
                this.drawer = true
              }
            }
          ]
        }
      ]
    }
  },
  async mounted() {
    // const list1 = await messageStyleList()
    // const { records, total } = list1.data
    // let dataList = []
    // console.info(records, 'records')
    // if (records && records.length) {
    //   dataList = [total, ...records]
    // }
    // const result = {
    //   data: dataList
    // }
    // console.info(result, 'result')
  },
  created() { },
  methods: {
    renderHeaders(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h('div', { slot: 'content' }, [textArr.map(text => h('div', null, text))]),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    },
    handMessageStyleListAdd(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          accountaddOrUpdate({ ...this.addForm }).then(res => {
            if (res.code == 200) {
              this.drawer = false
              this.$message.success('操作成功')
              this.$store.dispatch('tableRefresh', this)
            }
          })
        }
      })
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = undertakeListexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    reloadAddform() {
      this.addForm = {
        id: '',
        platform: '',
        appId: '',
        secretId: '',
        subject: '',
        status: 1
      }
    },
    handleAdd() {
      this.reloadAddform()
      this.drawer = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  overflow: scroll;
  // padding-bottom: 20px;
  padding: 0 30px 20px;
  position: relative;
  /* overflow-x: auto; */
}

::v-deep .el-drawer__header {
  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 30px;
    text-align: left;
    font-style: normal;
  }

}

::v-deep .el-drawer__body {}

.close_button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background-color: rgb(0, 0, 0, 1);
  text-align: center;
  cursor: pointer;

  i {
    color: white;
    line-height: 40px;
  }
}

.drawer_package {
  height: 100%;
  position: relative;

  .drawer_title {
    padding: 10px 20px 5px;
    vertical-align: middle;

    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }

  }
}

.addForm_package {
  background-color: rgb(189, 184, 184, 0.1);
  padding: 15px;
  height: 100%;

  .demo-ruleForm {
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    position: relative;

    .view_button {
      position: absolute;
      bottom: 0;
    }
  }
}

.view_button {
  background-color: #ffffff;
  padding: 20px;
  border-top: 1px dashed #000000;

  ::v-deep .el-button {
    margin: 0 10px;
  }
}

.form_view {
  // margin: 0 0px 20px;
  background-color: #ffffff;
  // border: 1px solid rgba(0,0,0,0.2);
  width: 100%;
  padding: 15px;
  border-radius: 5px;

  .form_view_title {
    margin-bottom: 20px;

    .title_line {
      width: 2px;
      height: 10px;
      background-color: #66b1ff;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      padding-left: 5px;
      vertical-align: middle;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }
}

.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
