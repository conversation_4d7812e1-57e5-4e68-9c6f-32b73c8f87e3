<template>
  <div class="drawermain">
    <div class="close_button" @click="$emit('action','close')">
      <i class="el-icon-close" />
    </div>
    <div class="drawer_package">
      <div class="drawer_title">
        <span>识图分类{{ params.type=='add'?'新增':'编辑' }}</span>
      </div>
      <div class="addForm_package">
        <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="150px" class="demo-ruleForm">
          <div class="form_view">
            <div class="form_view_title">
              <div class="title_line" /><span>基础信息</span>
            </div>
            <el-form-item label="所属分类" prop="categoryType" :rules="addRules.common">
              <el-select v-if="categoryTypeList.length" v-model="addForm.categoryType" style="width:300px" placeholder="请选择识图分类">
                <el-option
                  v-for="itemData in categoryTypeList"
                  :key="itemData.code"
                  :label="itemData.desc"
                  :value="itemData.code"
                  :disabled="(addForm.categoryType!=itemData.code || !addForm.id) && itemData.selectType==0"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="分类名称" prop="categoryName" :rules="addRules.common">
              <el-input
                v-model="addForm.categoryName"
                style="width:300px"
                placeholder="请输入分类名称"
                maxlength="10"
              />
              <span style="margin-left: 10px;">{{ addForm.categoryName.length }}/10</span>
            </el-form-item>
            <el-form-item label="分类描述" prop="categoryDescription" :rules="addRules.common">
              <el-input
                v-model="addForm.categoryDescription"
                style="width:300px"
                placeholder="请输入分类描述"
                maxlength="30"
              />
              <span style="margin-left: 10px;">{{ addForm.categoryDescription.length }}/30</span>
            </el-form-item>
            <el-form-item label="调用智能体" prop="botId" :rules="addRules.common">
              <el-select v-model="addForm.botId" style="width:300px" placeholder="请选择分类使用的智能体">
                <el-option
                  v-for="item in botIdList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="模型平台" prop="platform" :rules="addRules.common">
              <el-select v-model="addForm.platform" placeholder="请选择模型平台">
                <el-option
                  v-for="item in platformList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="addForm.platform&&addForm.platform!=''" label="模型名称" prop="modelRepositoryId" :rules="addRules.common">
              <el-select v-model="addForm.modelRepositoryId" placeholder="请选择模型名称" filterable clearable>
                <el-option
                  v-for="item in modelList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="人物参数" prop="personArgument">
              <el-select v-model="addForm.personArgument" multiple style="width:300px" placeholder="请选择需要智能体分析的人物参数">
                <el-option
                  v-for="item in personList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="模型引导词" class="content-item" prop="callWord" :rules="addRules.common">
              <el-tooltip placement="top" :style="{ color: '#409eff' }">
                <div slot="content">
                  模型传入参数=模型引导词+清洗要求<br>
                  1.1、清洗要求：<br>
                  ##输出的内容按照json字符串格式<br>
                  1 其中 E,S,T,J 作为对应的key值展示得分情况<br>
                  2 MBTI作为key值赋值MBTI人格描述,要求描述的开头直接给出MBTI的结果，再做分析<br>
                </div>
                <i class="el-icon-question content-tooltip" style="font-size: 14px" />
              </el-tooltip>
              <el-input
                v-model="addForm.callWord"
                style="width:300px"
                type="textarea"
                :rows="3"
                placeholder="请输入智能体提示词"
                maxlength="1000"
              />
              <span style="margin-left: 10px;">{{ addForm.callWord.length }}/1000</span>
            </el-form-item>
          </div>
          <div class="form_view">
            <div class="form_view_title">
              <div class="title_line" /><span>启用状态</span>
            </div>
            <el-form-item label="状态" prop="configStatus" :rules="addRules.common">
              <el-switch
                v-model="addForm.configStatus"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div :style="{'text-align': 'right', width: '100%'}" class="view_button">
        <el-button @click="$emit('action','close')">取消</el-button>
        <el-button type="primary" @click="submitFn('addForm')">确认</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { getKnowCategoryTypeList, knowImageConfigGetBots, knowImageConfigUpdate, knowImageConfigCreate, modelRepositoryselect, modelRepositoryplatform } from '@/qjjpApi/operate'
export default {
  name: 'identifyMarganEdit',
  props: {
    params: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      categoryTypeList: [],
      botIdList: [],
      platformList: [],
      modelList: [],
      personList: [
        {
          label: '性格特点',
          value: 0
        },
        {
          label: '兴趣爱好',
          value: 1
        },
        {
          label: '聊天话题',
          value: 2
        }
      ],
      addForm: {
        categoryType: '',
        categoryName: '',
        categoryDescription: '',
        botId: '',
        platform: '',
        modelRepositoryId: '',
        personArgument: [],
        callWord: '',
        configStatus: 1
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      }
    }
  },
  watch: {
    'addForm.platform': {
      immediate: true,
      handler(newVal) {
        if (!newVal) return
        modelRepositoryselect(newVal).then(res => {
          if (res.code === 200) {
            this.modelList = res.data
          }
        })
      }
    }
  },
  created() {
    getKnowCategoryTypeList().then(res => {
      if (res.code === 200 && res.data) {
        this.categoryTypeList = res.data
      }
    })
    knowImageConfigGetBots().then(res => {
      if (res.code === 200 && res.data) {
        this.botIdList = res.data.map(n => ({ label: n.botName, value: n.botId }))
      }
    })
    modelRepositoryplatform().then(res => {
      if (res.code === 200) {
        if (res.data && res.data.length) {
          this.platformList = res.data
        }
      }
    })

    if (Object.keys(this.params.data || {}).length) {
      this.addForm = JSON.parse(JSON.stringify(this.params.data))
      this.addForm.personArgument = this.addForm.personArgument.split(',')
      this.$forceUpdate()
      console.log(this.addForm, this.params.data)
    }
  },
  methods: {
    submitFn(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const fn = this.addForm.id ? knowImageConfigUpdate : knowImageConfigCreate
          const params = JSON.parse(JSON.stringify(this.addForm))
          params.personArgument = params.personArgument.join(',')
          fn(params).then(res => {
            if (res.code == 200) {
              this.$message.success('操作成功')
              this.$emit('action', 'confirm')
            }
          })
        } else {
          this.$nextTick(() => {
            // 获取错误节点
            const isError = this.$refs[formName].$el.getElementsByClassName('is-error')
            isError[0].scrollIntoView({
              // 滚动到指定节点
              // 值有start,center,end，nearest，当前显示在视图区域中间
              block: 'center',
              // 值有auto、instant,smooth，缓动动画（当前是慢速的）
              behavior: 'smooth'
            })
          })
          return
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.drawermain{
  width: 100%;
  height: 100vh;
}
.close_button{
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background-color: rgb(0, 0, 0,1);
  text-align: center;
  cursor: pointer;
  i{
    color: white;
    line-height: 40px;
  }
}
.drawer_package{
  width: calc(100% - 40px);
  margin-left: 40px;
  position: relative;
  background-color: #f2f2f2;
  height: 100%;
  .view_button{
    position: absolute;
    bottom: 0;
    background-color: #ffffff;
    padding: 15px;
  }
  .addForm_package{
    width: 100%;
    height: calc(100% - 110px);
    overflow: auto;
  }
  .drawer_title{
    padding: 5px 20px 5px;
    vertical-align: middle;
    background-color: #ffffff;
    span{
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }

  }
  .demo-ruleForm{
    width: 100%;
    min-height: 100%;
    position: relative;
    overflow: hidden;
    .form_view{
      background-color: #ffffff;
      width: calc(100% - 36px);
      margin: 18px;
      border-radius: 5px;
      padding:  15px;
      .form_view_title{
          margin-bottom: 20px;
          .title_line{
            width: 2px;
            height: 10px;
            background-color:#66b1ff ;
            display: inline-block;
            vertical-align: middle;
          }
          span{
            padding-left: 5px;
            vertical-align: middle;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 20px;
            color: #333333;
            line-height: 30px;
            text-align: left;
            font-style: normal;
          }
      }
    }
  }
}
::v-deep .el-form-item{
  &.content-item{
    position: relative;
    .el-form-item__label{
      width: 135px !important;
    }
    .content-tooltip{
      position: absolute;
      top: 13px;
      left: -25px;
    }
  }
}
</style>
