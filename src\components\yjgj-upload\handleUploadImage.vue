<!--
 * @Author: 陈小豆
 * @Date: 2024-07-12 17:15:44
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-07-12 17:15:52
-->
<template>
  <div class="uploadFileMain" :style="{'--height':`${height}px`,'--width':`${width}px`}">
    <draggable v-model="fileList" :draggable="isDraggable ? '.draggable-item' : ''" style="display: flex;flex-wrap: wrap">
      <div v-for="(item, index) in fileList" :key="index" class="upload-list draggable-item" :class="[limit == 1 ? 'upload-lis1' : '']">
        <div v-if="switchIndex == index && showProgress" v-loading="true" class="loadingView" element-loading-background="rgba(0, 0, 0, 0.8)" />
        <viewer :images="[item.url]" :style="{ 'height': `100%`, 'width': `100%` }">
          <img :id="`${idName}_image_${index}`" class="el-upload-listImg" :src="getShowImage(item.url, `${idName}_image_${index}`)" alt="">
        </viewer>
        <span class="el-actions">
          <span
            class="el-upload-icon"
            @click="handlePictureCardPreview(index)"
          >
            <i class="el-icon-zoom-in" />
          </span>
          <span
            class="el-upload-icon"
            @click="switchFn(index)"
          >
            <i class="el-icon-sort" style="transform: rotate(90deg);" />
          </span>
          <span
            class="el-upload-icon"
            @click="delRemove(index)"
          >
            <i class="el-icon-delete" />
          </span>
        </span>
      </div>
      <el-upload
        v-show="!limit || fileList.length < limit"
        :ref="`${idName}_upload`"
        :show-file-list="false"
        :multiple="multiple"
        :limit="limit ? limit + 1 : limit"
        :action="`${this.$CONSTANT.qjjpPath}/upload/image`"
        list-type="picture-card"
        :headers="{ Authorization: $utils.getToken() }"
        :accept="acceptArray.length > 0 ? getAcceptArray : '*'"
        :file-list="fileList"
        :before-upload="beforeUpload"
        :on-progress="progressFn"
        :on-success="uploadSuccess"
      >
        <div v-if="switchIndex == -1 && showProgress" v-loading="true" :style="{ 'height': `${height}px`, 'width': `${width}px` }" class="loadingView" element-loading-background="rgba(0, 0, 0, 0.8)" />
        <div v-else :id="`${idName}_uploadClick`" slot="trigger" class="uploadClick" @click="changeIndex(-1)">
          <i class="el-icon-plus" />
        </div>
      </el-upload>
    </draggable>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
export default {
  components: {
    draggable
  },
  props: {
    // sendUrl: {
    //   type: String,
    //   default: ''
    // },
    value: {
      type: [Array, String],
      default: ''
    },
    isDraggable: {
      type: Boolean,
      default: false
    },
    width: {
      type: [Number, String],
      default: 80
    },
    height: {
      type: [Number, String],
      default: 80
    },
    multiple: {
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: null
      // default: 4
    },
    // 大小限制：10 * 1024 * 1024 = 10MB
    size: {
      type: Number,
      default: -1
    },
    //   限制类型，按照acceptType数组里面来
    acceptArray: {
      type: Array,
      default: () => {
        return ['png', 'jpg', 'jpeg', 'gif']
      }
    },
    gifSize: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      acceptType: {
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'pdf': 'application/pdf',
        'csv': '.csv',
        'txt': 'text/plain',
        'image': 'image/*',
        'png': 'image/png',
        'gif': 'image/gif',
        'jpg': 'image/jpg',
        'jpeg': 'image/jpeg'
      },
      idName: `${Math.random().toString(36).slice(-8)}_${new Date().getTime()}`,
      switchIndex: -1,
      fileList: [],
      dataArray: {
        1: [],
        2: []
      },
      showProgress: false
    }
  },
  computed: {
    getAcceptArray() {
      const dataArray = this.acceptArray.map(n => this.acceptType[n]).filter(n => n)
      if (this.acceptArray.indexOf('video') != -1) {
        dataArray.push('video/*')
      }
      return dataArray.join(',')
    }
  },
  watch: {
    value: {
      handler(value) {
        if (Array.isArray(value)) {
          this.fileList = value.filter(n => n != '').map(n => {
            return {
              url: n
            }
          })
        } else {
          this.fileList = value.split(',').filter(n => n != '').map(n => {
            return {
              url: n
            }
          })
        }
      },
      deep: true,
      immediate: true
    },
    fileList: {
      handler(value) {
        if (value.length > 0) {
          this.toFileImg(value)
        }
      },
      deep: true,
      immediate: true
    },
    dataArray: {
      handler(value) {
        // 1 和 2 相等表示这次上传成功的数量相同，会添加到数组里面
        if (value[1].length != 0 && value[2].length != 0 && value[1].length == value[2].length) {
          value[2].forEach(e => {
            if (e.code == 0) {
              // 有更换就变化
              if (this.switchIndex != -1) {
                this.fileList.splice(this.switchIndex, 1, {
                  status: 'success',
                  url: e.data
                })
              } else {
                if ((this.limit && this.fileList.length < this.limit) || !this.limit) {
                  // 没有更好追加
                  this.fileList.push({
                    status: 'success',
                    url: e.data
                  })
                }
              }
            } else {
              this.$message.error(e.message || '上传失败')
            }
          })
          setTimeout(() => {
            this.showProgress = false
            this.switchIndex = -1
          }, 300)
          this.dataArray = {
            1: [],
            2: []
          }
          // 清除组件上传类别
          this.$refs[`${this.idName}_upload`].clearFiles()
        }
      },
      deep: true
    }
  },
  methods: {
    filterSize(size) {
      const pow1024 = (num) => {
        return Math.pow(1024, num)
      }
      if (!size) return ''
      if (size < pow1024(1)) return size + ' B'
      if (size < pow1024(2)) return (size / pow1024(1)).toFixed(0) + ' KB'
      if (size < pow1024(3)) return (size / pow1024(2)).toFixed(0) + ' MB'
      if (size < pow1024(4)) return (size / pow1024(3)).toFixed(0) + ' GB'
      return (size / pow1024(4)).toFixed(2) + ' TB'
    },
    // 上传之前放到1
    beforeUpload(e) {
      const fileSize = e.size
      if (this.gifSize > 0) {
        if (e.type.indexOf('gif') != -1 && fileSize > this.gifSize) {
          this.$message.error(`gif最大上传${this.filterSize(this.gifSize)}`)
          return false
        }
      } else {
        if (this.size > 0 && fileSize > this.size) {
          this.$message.error(`最大上传${this.filterSize(this.size)}`)
          return false
        }
      }
      this.dataArray[1].push({
        status: 'uploading',
        ...e
      })
      return true
    },
    // 通过 slot="trigger" ，区分模拟点击，表示这次时人为点击的
    changeIndex(index) {
      if (index == -1) {
        this.switchIndex = -1
      }
    },
    progressFn(e) {
      this.showProgress = true
    },
    // 更换图片，模拟点击
    switchFn(index) {
      document.getElementById(`${this.idName}_uploadClick`).click(this.switchIndex)
      setTimeout(() => {
        this.switchIndex = index
      }, 0)
    },
    // 查看图片
    handlePictureCardPreview(index) {
      // console.log(index, document.getElementById(`${this.idName}_image_${index}`))
      document.getElementById(`${this.idName}_image_${index}`).click()
    },
    // 成功后放到2
    uploadSuccess(e) {
      this.dataArray[2].push({
        ...e
      })
    },
    // 传递图片
    toFileImg(value) {
      if (Array.isArray(this.value)) {
        this.$emit('input', value.map(n => n.url))
      } else {
        this.$emit('input', value.map(n => n.url).join(','))
      }
    },
    delRemove(index) {
      this.fileList.splice(index, 1)
      if (this.fileList.length == 0) {
        this.toFileImg(this.fileList)
      }
    },
    getVideoImg(url, time = 0) {
      return new Promise((r, j) => {
        const video = document.createElement('video') // 创建video对象
        video.src = url // url地址
        const canvas = document.createElement('canvas') // 创建 canvas 对象
        const ctx = canvas.getContext('2d') // 绘制2d
        video.crossOrigin = 'anonymous' // 解决跨域问题，也就是提示污染资源无法转换视频
        video.currentTime = 1 // 第一秒帧
        video.oncanplay = () => {
          canvas.width = video.videoWidth
          canvas.height = video.videoHeight
          // 利用canvas对象方法绘图
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
          r(canvas.toDataURL('image/png'))
        }
      })
    },
    getShowImage(url, id) {
      const ext = url.split('.').at(-1)
      if (ext.indexOf('mp4') != -1 || ext.indexOf('avi') != -1) {
        this.getVideoImg(url).then(res => {
          if (document.getElementById(id)) {
            document.getElementById(id).src = res
          }
        })
      } else {
        return url
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-upload--picture-card{
  width: var(--width);
  display:flex;
  justify-content:center;
  align-items:center;
  height: var(--height);
}
.uploadClick{
  width: var(--width);
  display:flex;
  justify-content:center;
  align-items:center;
  height: var(--height);
}
::v-deep .el-upload-list--picture-card .el-upload-list__item{
  width: var(--width);
  display:flex;
  align-items:center;
  height: var(--height);
  transition: none !important;
}
::v-deep .el-upload-list__item .el-icon-check{
  position: absolute;
  margin-top: 0px;
  top: 10px;
  right: 14px;
}
::v-deep .el-loading-spinner{
  width: 100%;
  height: 100%;
  top: 0;
  margin-top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
::v-deep .el-upload-list,::v-deep .el-upload-list--picture-card{
  //display: none;
}
.uploadFileMain{
  display: flex;
  flex-wrap: wrap;
  .upload-list{
    flex-shrink:0;
    width: var(--width);
    border:1px solid #0000005d;
    box-sizing: border-box;
    height: var(--height);
    margin-right: 20px;
    margin-bottom: 10px;
    &.upload-lis1{
      margin-bottom: 0px;
    }
    overflow: hidden;
    border-radius: 8px;
    position: relative;
    .el-actions{
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background-color: rgba(0,0,0,0.5);
      align-items: center;
      justify-content: center;
      display: none;
      .el-upload-icon{
        margin: 5px;
        i{
          color: #ffffff;
          cursor: pointer;
        }
      }
    }
    &:hover{
      .el-actions{
        display: flex;
      }
    }
  }
  .el-upload-listImg{
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.loadingView{
  width: 100%;
  height: 100%;
}
</style>
