import CONSTANT from '@/config/constant.conf'
import { del, get, post, put } from '@/libs/axios.package'
import qs from 'qs'

// 大盘数据监控 - 数据面板
export const getDataPanel = obj => post('/count/dataPanel', obj)

// 大盘数据监控 - 列表
export const getList = obj => get('/count/big/panel/page', obj)

// 大盘数据监控 - 列表子项列表
export const getDetailList = obj => get('/count/big/panel/page/dynamic', obj)

// 大盘数据监控 - 列表导出
export const listExport = data => CONSTANT.publicPath + '/count/big/panel/page/export?' + qs.stringify(data)


// 大盘数据监控 - 渠道列表
export const getChannelCodeList = obj => get('/count/big/panel/channel', obj)

// 大盘数据监控 - 渠道列 表子项列表
export const getChannelCodeDetailList = obj => get('/count/big/panel/channel/dynamic', obj)

// 大盘数据监控 - 渠道列表导出
export const channelCodeListExport = data => CONSTANT.publicPath + '/count/big/panel/channel/export?' + qs.stringify(data)
