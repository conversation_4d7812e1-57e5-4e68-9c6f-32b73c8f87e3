/*
 * 统计总览子路由
 * */

const landingPage = [
  {
    path: '/landingPage/landingPageList',
    name: 'landingPage_landingPageList',
    meta: {
      title: '落地页列表'
    },
    // component: () => import("@/views/vipCard/page/vipCardList"),
    component: () => import('@/views/landingPage/page/landingPageList')
  },
  {
    path: '/landingPage/landingPageStatistics',
    name: 'landingPage_landingPageStatistics',
    meta: {
      title: '落地页统计'
    },
    // component: () => import("@/views/vipCard/page/vipCardList"),
    component: () => import('@/views/landingPage/page/landingPageStatistics')
  },
  {
    path: '/landingPage/matchingConfig',
    name: 'matchingConfig',
    meta: {
      title: '撞库配置'
    },
    component: () => import('@/views/landingPage/page/matchingConfig')
  },
  {
    path: '/landingPage/domainSwitch',
    name: 'domainSwitch',
    meta: {
      title: '域名切换'
    },
    component: () => import('@/views/landingPage/page/domainSwitch')
  },
  {
    path: '/landingPage/unionLoginDataMonitor',
    name: 'unionLoginDataMonitor',
    meta: {
      title: '联盟登录数据监控'
    },
    component: () => import('@/views/landingPage/page/unionLoginDataMonitor')
  },
  {
    path: '/landingPage/unionLoginDataMonitorDetail',
    name: 'unionLoginDataMonitorDetail',
    meta: {
      title: '联盟登录数据监控详情',
      parentTitle: '联盟登录数据监控',
      activeMenu: '/landingPage/unionLoginDataMonitor'
    },
    component: () => import('@/views/landingPage/page/unionLoginDataMonitorDetail')
  },
  {
    path: '/landingPage/unionLoginMediaConfig',
    name: 'unionLoginMediaConfig',
    meta: {
      title: '联登媒体配置'
    },
    component: () => import('@/views/landingPage/page/unionLoginMediaConfig')
  }
]

export default landingPage
