
<template>
  <Page :request="request" :list="list" table-title="AI识图示例图片管理" :table-pagination-state="false" />
</template>

<script>
import Page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { knowImageConfigExampleImageConfig, knowImageConfigUpdateExampleImage } from '@/qjjpApi/operate'

export default {
  name: 'aiTemplate',
  components: {
    Page
  },
  data() {
    return {
      request: {
        getListUrl: knowImageConfigExampleImageConfig,
        updateHttp: knowImageConfigUpdateExampleImage
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: 'ID',
          key: 'id',
          formHidden: true
        },
        {
          title: '示例图片地址',
          key: 'imageUrl',
          type: formItemType.upload,
          tableView: tableItemType.tableView.picture,
          reg: ['required']
        },
        {
          title: '图片对话内容',
          key: 'imageContent',
          type: formItemType.input,
          reg: ['required'],
          renderCustom: (h, params, vm) => {
            return (
              <el-input
                type='textarea'
                autosize={{ minRows: 2, maxRows: 4 }}
                placeholder='请输入内容'
                value={vm.value}
                onInput={value => {
                  vm.$emit('input', value)
                }}
              />
            )
          }
        },
        {
          title: '话术库问题',
          key: 'imageQuestion',
          type: formItemType.input,
          reg: ['required'],
          renderCustom: (h, params, vm) => {
            return (
              <el-input
                type='textarea'
                autosize={{ minRows: 2, maxRows: 4 }}
                placeholder='请输入内容'
                value={vm.value}
                onInput={value => {
                  vm.$emit('input', value)
                }}
              />
            )
          }
        },

        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.detailsDialog
            }
          ]
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>

.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}

</style>

<style lang="scss">
.temperatures{
    color:#409eff;
    cursor: pointer;
  }
.styleManagePop{
  width: 340px;
  .styleManagePopTooltip{
    color:#409eff;
  }
  .popTitle{
    font-weight: 600;
  }
  .toolView{
    width: 170px;
    display: inline-block;
  }
  .popView{
    color: #409eff;
    margin: 0 20px 0 3px;
    padding: 5px 0;
    display: inline-block;
    cursor: pointer;
  }
}

</style>
