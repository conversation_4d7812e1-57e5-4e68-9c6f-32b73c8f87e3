<template>
  <div>
    <page :list="list" :request="request" table-title="减价链路" />
  </div>
</template>

<script>
import { reduceStatisticsV274 } from '@/api/costManagement'
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import moment from 'moment'
import { count_channel_application_list } from '@/api/NewChannel'
export default {
  components: {
    page
  },
  data() {
    return {
      listQuery: {
        startDate: moment().subtract(6, 'd').format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD'),
        siteId: ''
      },
      request: {
        getListUrl: async(data) => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = res.data
              }
            })
          }
          return Promise.all([
            reduceStatisticsV274(this.listQuery)
          ]).then((res) => {
            return Promise.resolve(res[0])
          })
        }
      },
      siteIds: []
    }
  },
  computed: {
    list() {
      return [
        {
          key: 'date',
          title: '时间',
          type: formItemType.datePickerDaterangeGai,
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['startDate', 'endDate'],
          search: true,
          val: [moment().subtract(6, 'd').format('YYYYMMDD'), moment().format('YYYYMMDD')]
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: '',
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true,
          options: {
            placeholder: '综合'
          }
        },
        {
          title: '渠道ID',
          key: 'channelCode',
          type: formItemType.input,
          search: true,
          tableHidden: true
        },
        {
          title: '首次看视频减价',
          key: 'firstreduce',
          children: [

            {
              title: '活动访问（总/新）',
              key: 'visitTotalNew'
            },
            {
              title: '首次引导弹窗曝光（总/新）',
              key: 'firstExposureTotalNew'
            },
            {
              title: '首次引导弹窗点击（总/新）',
              key: 'firstClickTotalNew'
            },
            {
              title: '二次引导弹窗曝光（总/新）',
              key: 'secondExposureTotalNew'
            },
            {
              title: '二次引导弹窗点击（总/新）',
              key: 'secondClickTotalNew'
            },
            {
              title: '关闭拦截弹窗曝光（总/新）',
              key: 'closeExposureTotalNew'
            },
            {
              title: '关闭拦截弹窗点击（总/新）',
              key: 'closeClickTotalNew'
            },
            {
              title: '三次引导弹窗曝光（总/新）',
              key: 'thirdExposureTotalNew'
            },
            {
              title: '三次引导弹窗点击（总/新）',
              key: 'thirdClickTotalNew'
            },
            {
              title: '减价成功（总/新）',
              key: 'reduceSuccessTotalNew'
            }
          ]
        },
        {
          title: '普通看视频减价',
          key: 'ptreduce',
          children: [

            {
              title: '普通减价引导弹窗曝光（总/新）',
              key: 'commonExposureTotalNew'
            },
            {
              title: '普通减价引导弹窗点击（总/新）',
              key: 'commonClickTotalNew'
            },
            {
              title: '减价成功弹窗曝光（总/新）',
              key: 'reduceSuccessExposureTotalNew'
            },
            {
              title: '减价成功弹窗点击（总/新）',
              key: 'reduceSuccessClickTotalNew'
            },
            {
              title: '活动页减价点击（总/新）',
              key: 'activePageClickTotalNew'
            },
            {
              title: '减价成功数（总/新）',
              key: 'commonReduceSuccessTotalNew'
            }
          ]
        },

        {
          title: '观看视频用户数（总/新）',
          key: 'adShowUserTotalNew'
        },
        {
          title: '观看视频次数（总/新）',
          key: 'adShowTotalNew'
        },
        {
          title: '访问人均（总/新）',
          key: 'perCapita',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '观看广告次数/活动访问')
          }
        },
        {
          title: '首次引导转化（总/新）',
          key: 'firstGuidedTransformation',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '首次引导弹窗点击/首次引导弹窗曝光')
          }

        },
        {
          title: '关闭拦截转化（总/新）',
          key: 'closeInterceptTransform',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '关闭拦截弹窗点击/关闭拦截弹窗曝光')
          }
        },
        {
          title: '二次引导转化（总/新）',
          key: 'secondGuidedTransformation',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '二次引导弹窗点击/二次引导弹窗曝光')
          }
        },
        {
          title: '三次引导转化（总/新）',
          key: 'thirdGuidedTransformation',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '三次引导弹窗点击/三次引导弹窗曝光')
          }
        },
        {
          title: '普通减价引导转化（总/新）',
          key: 'commonReduceTransform',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '普通减价引导弹窗点击/普通减价引导弹窗曝光')
          }
        },
        {
          title: '减价成功转化（总/新）',
          key: 'reduceSuccessTransform',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '减价成功弹窗点击/减价成功弹窗曝光')
          }
        }
      ]
    }
  },
  methods: {
    renderHeaders(h, column, text) {
      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            props: {
              content: text
            }
          },
          [
            h('i', {
              class: 'el-icon-question',
              style: 'color:#409eff;margin-left:5px;font-size: 16px;'
            })
          ]
        )
      ])
    }
  }
}
</script>

<style></style>
