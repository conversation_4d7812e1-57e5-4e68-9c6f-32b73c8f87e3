<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 10:43:33
 * @LastEditors: 蒋雪 <EMAIL>
 * @LastEditTime: 2024-08-06 14:52:17
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="授权记录">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
    <section>
      <el-dialog
        title="
          退款确认
        "
        width="520px"
        :visible.sync="dialogFormVisible"
        :show-close="false"
      >
        <div class="dialog_tips">请确认是否发起退款申请，发起退款后该笔订单将全额退款给用户!</div>
        <el-form
          ref="ruleForm"
          :model="refundForm"
          :rules="rules"
          label-width="120px"
          class="demo-ruleForm"
        >
          <el-form-item label="投诉来源：" prop="refundSourceFrom" class="form-item" :rules="rules.common">
            <el-select v-model="refundForm.refundSourceFrom" placeholder="请选择投诉来源：">
              <el-option
                v-for="item in optionsList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="投诉原因：" prop="refundReason" class="form-item" :rules="rules.common">
            <el-input
              v-model="refundForm.refundReason"
              autocomplete="off"
              type="textarea"
              placeholder="请填写退款原因"
              maxlength="200"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible=false">取 消</el-button>
          <el-button
            type="primary"
            :loading="btn_disabled"
            :disabled="time!=0"
            @click="userUpdated('1', 'ruleForm')"
          >{{ time!=0?'确 定'+'（'+ time +'s）':'确 定' }}</el-button>
        </div>
      </el-dialog>
    </section>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { user_list } from '@/qjjpApi/user'
import { count_channel_application_list, mediaAll } from '@/qjjpApi/NewChannel'

import { membershipOrder, doRefund, membershipOrderexport, membershipOrdercycle, periodProductTypes } from '@/qjjpApi/orders'

import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      btn_disabled: false,
      refundForm: {
        id: '',
        refundSourceFrom: '',
        refundReason: ''
      },
      time: 5,
      currentParams: {},
      ruleForm: {},
      rules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      dialogFormVisible: false,
      optionsList: [
        {
          label: '商户号',
          value: 1
        },
        {
          label: '媒体',
          value: 2
        },
        {
          label: '工商',
          value: 3
        }
      ],
      list1: [
        {
          id: 1,
          name: '奇迹键盘'
        }
      ],
      list2: [
        {
          id: 'waitPay',
          name: '待支付'
        },
        {
          id: 'paid',
          name: '已支付'
        },
        {
          id: 'cancel',
          name: '已取消'
        },
        {
          id: 'refund',
          name: '已退款'
        },
        {
          id: 'expired',
          name: '已到期'
        }
      ],
      list3: [
        {
          id: '0',
          name: '待开通'
        },
        {
          id: '1',
          name: '开通'
        },
        {
          id: '2',
          name: '已关闭'
        }
      ],
      list4: [
        {
          id: '0',
          name: '先签协议再扣款'
        },
        {
          id: '1',
          name: '先扣款并签协议'
        }
      ],
      qyTypeList: [
        {
          id: 1,
          name: '支付并签约'
        },
        {
          id: 2,
          name: '签约后扣款'
        }
      ],
      list5: [
        {
          id: 1,
          name: '第1期'
        },
        {
          id: 2,
          name: '第2期'
        },
        {
          id: 3,
          name: '第3期'
        },
        {
          id: 4,
          name: '第4期'
        },
        {
          id: 5,
          name: '第5期'
        },
        {
          id: 6,
          name: '第6期'
        },
        {
          id: 7,
          name: '第7期'
        },
        {
          id: 8,
          name: '第8期'
        },
        {
          id: 9,
          name: '第9期'
        },
        {
          id: 10,
          name: '第10期'
        },
        {
          id: 11,
          name: '第11期'
        },
        {
          id: 12,
          name: '第12期'
        }
      ],
      list9: [
        {
          id: 4,
          name: '站内落地页'
        },
        {
          id: 1,
          name: '支付页'
        },
        {
          id: 2,
          name: '支付页拦截'
        },
        {
          id: 0,
          name: '首页付费弹窗'
        },
        {
          id: 3,
          name: '悬浮窗'
        },
        {
          id: 5,
          name: '专属人设定制'
        },
        {
          id: 6,
          name: '引导使用支付抽屉'
        },
        {
          id: 7,
          name: '引导使用支付弹窗'
        },
        {
          id: 8,
          name: '支付页返回打卡会员'
        },
        {
          id: 9,
          name: '引导使用返回拦截打卡会员'
        },
        {
          id: 10,
          name: '打卡会员支付页'
        },
        {
          id: 11,
          name: '承接落地页返回拦截(站内落地页-用户自定义风格链路)'
        },
        {
          id: 12,
          name: '承接落地页拉起支付返回拦截(站内落地页-用户自定义风格链路)'
        }
      ],
      authNameList: [],
      mediaList: [],
      siteIds: [],
      listQuery: {
        startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          await mediaAll().then(res => {
            if (res.code === 200) {
              this.mediaList = res.data
            }
          })
          if (!this.authNameList.length) {
            periodProductTypes().then(res => {
              if (res.code === 200) {
                if (res.data && res.data.length) {
                  this.authNameList = res.data
                }
              }
            })
          }
          if (!this.siteIds.length) {
            await count_channel_application_list().then(res => {
              if (res.code === 200) {
                if (res.data && res.data.length) {
                  this.siteIds = res.data
                }
              }
            })
          }
          const list = await membershipOrdercycle(this.listQuery)
          const { records, total } = list.data
          console.info(list, 'list')
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list8() {
      return this.listQuery.payType == 3 ? [
        {
          id: 1,
          name: '第1期'
        },
        {
          id: 2,
          name: '第2期'
        },
        {
          id: 3,
          name: '第3期'
        },
        {
          id: 4,
          name: '第4期'
        },
        {
          id: 5,
          name: '第5期'
        },
        {
          id: 6,
          name: '第6期'
        },
        {
          id: 7,
          name: '第7期'
        },
        {
          id: 8,
          name: '第8期'
        },
        {
          id: 9,
          name: '第9期'
        },
        {
          id: 10,
          name: '第10期'
        },
        {
          id: 11,
          name: '第11期'
        },
        {
          id: 12,
          name: '第12期'
        }
      ] : []
    },
    list() {
      return [
        {
          key: 'dateSearch',
          title: '日期',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          search: true,
          val: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
          tableHidden: true,
          pickerDay: 30
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          tableHidden: true,
          clearable: true
        },
        {
          title: '授权类型',
          key: 'agreementType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '渠道ID',
          key: 'channelCode',
          type: formItemType.input,
          search: true,
          tableHidden: true
        },
        {
          title: '用户账号',
          key: 'useAccount',
          type: formItemType.input,
          search: true,
          tableHidden: true
        },
        {
          title: '用户Id',
          key: 'userId1',
          searchKey: 'userCode',
          type: formItemType.input,
          search: true,
          tableHidden: true
        },
        {
          title: '授权状态',
          key: 'periodStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          tableHidden: true,
          clearable: true
        },
        {
          title: '授权名称',
          key: 'periodProductCodes',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.authNameList,
          listFormat: {
            label: 'value',
            value: 'key'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteName'
        },
        {
          title: '渠道ID',
          key: 'channelCode'
        },
        {
          title: '用户账户',
          key: 'userAccount'
        },
        {
          title: '用户Id',
          key: 'userCode'
        },
        {
          title: '授权类型',
          key: 'agreementType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '授权名称',
          key: 'periodProductName'
        },
        {
          title: '授权期数',
          key: 'totalPayments'
        },
        {
          title: '授权状态',
          key: 'periodStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '授权日期',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '扣款数/金额',
          key: 'deduct'
        },
        {
          title: '退款数/金额',
          key: 'refund'
        },
        {
          title: '投诉量',
          key: 'complaintNum'
        }
      ]
    }
  },
  watch: {
    dialogFormVisible: {
      handler(val) {
        if (val) {
          this.time = 5
          this.setTime = setInterval(() => {
            this.time -= 1
            if (this.time == 0) {
              clearInterval(this.setTime)
            }
          }, 1000)
        } else {
          clearInterval(this.setTime)
        }
      }
    }
  },
  created() {},
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = membershipOrderexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    userUpdated(type, formName) {
      this.$refs[formName].validate(valid => {
        if (valid && !this.btn_disabled) {
          this.btn_disabled = true
          doRefund(this.refundForm).then(res => {
            this.btn_disabled = false
            if (res.code == 200) {
              this.$message.success('退款成功！')
              this.$store.dispatch('tableRefresh', this)
              this.dialogFormVisible = false
            }
          })
        }
      })
    },
    renderHeader(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h(
            'div',
            {
              slot: 'content'
            },
            [textArr.map(item => h('div', null, item))]
          ),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog_tips{
    font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 18px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
padding-bottom: 20px;
}
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
