<template>
  <page :request="request" :list="list" table-title="供应商管理" />
</template>

<script>
import page from '@/components/restructure/page'
import { get_ad_page, put_ad } from '@/api/advertisement'
import { formItemType, tableItemType } from '@/config/sysConfig'

export default {
  name: 'AdList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      request: {
        updateHttp: put_ad,
        getListUrl: get_ad_page
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: 'ID',
          key: 'id',
          formHidden: true
        },
        {
          title: '名称',
          type: formItemType.input,
          key: 'name',
          reg: ['required'],
          search: true
        },
        {
          title: '状态',
          key: 'status',
          list: [
            { label: '启用', value: true },
            { label: '停用', value: false }
          ],
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          options: {
            valueType: 'Boolean'
          }
        },
        {
          type: tableItemType.active,
          width: '100px',
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.detailsDialog,
              theme: 'warning'
            }
          ]
        }
      ]
    }
  },
  watch: {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped></style>
