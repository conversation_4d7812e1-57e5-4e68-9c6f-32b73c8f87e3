<template>
  <div class="question-box-for">
    <div v-for="(item,index) in questionList" :key="'item'+index" class="form-item">
      <div class="form-label">文案{{ index+1 }}:</div>
      <div class="form-val">
        <el-input
          v-model="questionList[index]"
          type="text"
          placeholder="请输入"
          maxlength="20"
          show-word-limit
        /></div>
    </div>
    <div class="tips">说明：默认文案将在客户端的引导使用页显示，请注意，
      <button class="img-btn">
        查看说明图片
        <el-image
          class="btn-image"
          src="https://picture.ttshengbei.com/qutaosh/landingPage/2024052801/Dingtalk_20240528095623.png"
          :preview-src-list="['https://picture.ttshengbei.com/qutaosh/landingPage/2024052801/Dingtalk_20240528095623.png']"
        />
      </button>
    </div>
    <div class="submit-btn">
      <el-button @click="$emit('input',!value)">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
    </div>
  </div>
</template>
<script>
import { defaultQuestionPost, defaultQuestionGet } from '@/qjjpApi/operate'
export default {
  name: 'customQuestion',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      questionList: [],
      loading: false
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.getDetial()
        }
      },
      immediate: true
    }
  },
  methods: {
    getDetial() {
      this.questionList = ['', '', '']
      defaultQuestionGet().then(res => {
        if (res.code == 200) {
          this.questionList = res?.data ?? []
        }
      })
    },
    submit() {
      const formData = this.questionList.filter(item => !!item)
      if (!formData || formData.length === 0) {
        this.$message.error('至少填写一条问题！')
        return
      }
      this.loading = true
      defaultQuestionPost(this.questionList).then(res => {
        if (res.code == 200) {
          this.$message.success('添加成功')
          this.$emit('input', !this.value)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.question-box-for{
.form-item{
  display:flex;
  align-items:center;
  margin-bottom:10px;
  .form-label{
    width:70px;
    flex-shrink:0;
  }
  .form-val{
    flex-grow:1;
  }
}
.tips{
  padding:5px 0 0 70px;

}

.img-btn{
  background: transparent;
  text-decoration: underline;
  position:relative;
  .btn-image{
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
    ::v-deep .el-image__preview{
      opacity: 0;
    }
    // visibility:hidden;
  ::v-deep .el-icon-circle-close{
      font-size:30px;
      color:#fff;
    }
  }
}
.submit-btn{
  margin:15px auto 0;
  width:100%;
  display:flex;
  justify-content:center;
}
}
</style>
