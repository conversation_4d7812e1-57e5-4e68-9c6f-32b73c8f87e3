import { put, get, post, del } from '@/libs/axios.package'

/*
 * 系统管理
 * */
export const get_admin_list = obj => {
  return get('/admin/list', obj, false)
}

/*
 * 角色列表
 * */
export const get_role_list = () => {
  return get('admin/list', null, false)
}

export const get_admin_role_list = () => {
  return get('/admin/role/list', null, false)
}

export const get_permission_list = () => {
  return get('/admin/permission/list', null, false)
}

export const get_menus_permission = () => {
  return get('/admin/role/menus/permission', null, false)
}

export const get_role_by_id = id => {
  return get(`/admin/role/${id}`, null, false)
}

export const get_permission_by_id = id => {
  return get(`/admin/permission/${id}`, null, false)
}

/*
 * 添加管理员
 * */
export const add_admin = obj => {
  return post('admin/add', obj, false)
}

export const add_role = obj => {
  return post('/admin/role/addOrUpDate', obj, false)
}

export const add_permission = obj => {
  return post('/admin/permission', obj, false)
}

export const deployRelationPermission = obj => {
  return post('/admin/role/relation', obj, false)
}

/*
 * 删除角色
 * */

export const del_role = id => {
  return del(`/admin/role/${id}`, null, false)
}

export const del_permission = id => {
  return del(`/admin/permission/${id}`, null, false)
}

/**
 *
 * 修改用户状态
 */
export const update_admin_locked = id => {
  return put('admin/locked/' + id, null, false)
}

export const update_role = obj => {
  return post(`/admin/role/addOrUpDate`, obj, false)
}

export const update_permission = obj => {
  return put(`/admin/permission/`, obj, false)
}

/**
 * 添加菜单
 */
export const add_menu = obj => {
  return post('/admin/menu', obj, false)
}

/**
 * 修改菜单
 */
export const put_menu = obj => {
  return put('/admin/menu', obj, false)
}

/**
 * 菜单列表
 */
export const get_menu_list = obj => {
  return get('/admin/menu/list', obj, false)
}

/**
 * 删除菜单
 */
export const del_menu = obj => {
  return del(`/admin/menu/${obj.id}`, obj, false)
}

/**
 * 菜单详情
 */
export const get_menu_edit = obj => {
  return get(`/admin/menu/${obj.id}`, obj, false)
}

/**
 * 菜单详情
 */
export const admin_relationMenu = obj => {
  return post(`/admin/role/relationMenu`, obj, false)
}

/**
 * 修改管理员
 */
export const put_admin = obj => {
  return put(`/admin/update`, obj, false)
}

/**
 * 删除管理员
 */
export const del_admin = id => {
  return del(`/admin/delete/${id}`, null, false)
}

/**
 * 参数设置 1.4.1
 */
export const GET_SYSTEM_LIST = obj => {
  return get(`/systemParam/list`, obj)
}

/**
 * 参数设置 详情 1.4.1
 */
export const GET_SYSTEM_DETAIL = id => {
  return get(`/systemParam/byId?paramId=${id}`)
}

/**
 * 参数设置 编辑 1.4.1
 */
export const POST_SYS_DETAIL = obj => {
  return post(`/systemParam/updateById`, obj)
}

/**
 * 商户号列表
 */
export const GET_MERCHANTSMANAGE_LIST = obj => {
  return get(`/v1/channel/pay/conf`, obj)
}
/**
 * 商户号列表
 */
export const merchantPayConfMerchantList = obj => {
  return get(`/merchantPayConf/merchantList`, obj)
}

/**
 * 查询channelCode是否已经被其余商户使用
 */
export const CHECK_MERCHANTSMANAGE_CHANNEL = obj => {
  return post(`/v1/channel/verify/channel`, obj)
}

/**
 * 更新商户号状态
 */
export const UPDATE_MERCHANTSMANAGE_LIST = obj => {
  return post(`/v1/channel/pay/conf`, obj)
}

/**
 * 获取所有角色列表
 */
export const adminRolelist = obj => {
  return get(`/admin/role/list`, obj)
}
/**
 * 获取大盘数据权限
 */
export const adminRoleMarketPermissionList = obj => {
  return get(`/adminRoleMarketPermission/getList`, obj)
}
/**
 * 获取用户对应的大盘数据权限
 */
export const getUserPermissionList = obj => {
  return get(`/adminRoleMarketPermission/getUserPermission`, obj)
}
/**
 * 大盘数据权限添加/修改
 */
export const rmPcreateOrUpdate = obj => {
  return post(`/adminRoleMarketPermission/createOrUpdate`, obj)
}
/**
 * 删除大盘数据权限
 */
export const delAdminRoleMarketPermission = obj => {
  return del(`/adminRoleMarketPermission/${obj.id}`)
}
/**
 * 获取审核数据
 */
export const getAuditManageList = obj => {
  return get(`app/version/package/list`, obj)
}
/**
 * 审核添加/修改
 */
export const auditManageUpdate = obj => {
  return post(`/app/version/package/addorupdate`, obj)
}
/**
 * 审核详情
 */
export const auditManageDetail = id => {
  return get(`/app/version/package/detail?id=${id}`)
}
/**
 * 删除审核
 */
export const delAuditManage = obj => {
  return get(`/app/version/package/delete`, obj)
}
/**
 * 查询jsApi支付配置
 */
export const jsapiList = obj => {
  return get(`/jsApi/pay/conf/list`, obj)
}

/**
 * 修改jsApi支付配置
 */
export const jsapiUpdate = obj => {
  return post(`/jsApi/pay/conf/edit`, obj)
}

/**
 * 新增jsApi支付配置
 */
export const jsapiAdd = obj => {
  return post(`/jsApi/pay/conf/add`, obj)
}

/**
 * 查询分页查询支付域名
 */
export const alipayH5List = obj => {
  return get(`/channel/pay/domain/page`, obj)
}

/**
 * 修改分页查询支付域名
 */
export const alipayH5Update = obj => {
  return post(`/channel/pay/domain/update`, obj)
}

/**
 * 新增分页查询支付域名
 */
export const alipayH5Add = obj => {
  return post(`/channel/pay/domain/create`, obj)
}

/**
 * 新增分页查询支付域名
 */
export const domainUnRisk = obj => {
  return post(`/channel/pay/domain/unRisk`, obj)
}
// 底导配置获取
export const crowdDiscountConfigGet = obj => {
  return get('crowdDiscountConfig/get', obj)
}
// 底导配置更新
export const crowdDiscountConfigUpdate = obj => {
  return post('crowdDiscountConfig/update', obj)
}

/*
 * 获取投放人员接口
 * */
export const get_all_list = obj => {
  return get('/all/list ', obj, false)
}
// 多次退款配置查询
export const get_multiStepRefundConfig = obj => {
  return get('/systemConfig/get/multiStepRefundConfig', obj)
}
// 多次退款配置更新
export const update_multiStepRefundConfig = obj => {
  return post('/systemConfig/update/multiStepRefundConfig', obj)
}

/**
 * 获取IP白名单列表
 */
export const getIpWhiteList = obj => {
  return get('/white/list/ip', obj)
}

/**
 * 更新IP白名单状态
 */
export const updateIpWhiteList = obj => {
  return put('/white/list/ip', obj)
}

