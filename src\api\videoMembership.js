import CONSTANT from '@/config/constant.conf'
import { get, post } from '@/libs/axios.package'
import qs from 'qs'
// 活动商品选择spu
export const activityVideoSpu = (obj) => {
  return get('/activity/video/spu', obj)
}
// 活动商品选择sku接口
export const activityVideoSku = (obj) => {
  return get('/activity/video/sku', obj)
}
// 创建充值套餐接口
export const videoVipRechargeAdd = (obj) => {
  return post('/videoVip/recharge/add', obj)
}
// 分页查询充值套餐接口
export const videoVipRechargeList = (obj) => {
  return get('/videoVip/recharge/list', obj)
}
// 修改充值套餐接口
export const videoVipRechargeUpdate = (obj) => {
  return post('/videoVip/recharge/update', obj)
}
// 删除充值套餐接口
export const videoVipRechargeDelete = (obj) => {
  return get('/videoVip/recharge/delete', obj)
}
// 分页查询会员卡接口
export const videoCardList = (obj) => {
  return get('/videoVip/card/list', obj)
}
// 创建视频会员卡接口
export const videoCardAdd = (obj) => {
  return post('/videoVip/card/add', obj)
}
// 修改视频会员卡接口
export const videoCardUpdate = (obj) => {
  return post('/videoVip/card/update', obj)
}
// 删除视频会员卡接口
export const videoCardDelete = (obj) => {
  return get('/videoVip/card/delete', obj)
}
// 芒果TV充值记录接口
export const getGoodsOrderPage = (obj) => {
  return get('/admin/member/record/getGoodsOrderPage', obj)
}
// 芒果TV领取记录接口
export const getVideoMemberRecordPage = (obj) => {
  return get('/admin/member/record/getVideoMemberRecordPage', obj)
}
// 芒果TV充值埋点查询
export const countVideoCardRecharge = (obj) => {
  return get('/countVideoCard/recharge', obj)
}
//
export const recordSendBlueBrother = (obj) => {
  return post('/admin/member/record/sendBlueBrother', obj)
}
// 视频会员激活埋点数据查询接口
export const videoListCardCount = (obj) => {
  return get('/activity/video/list/card/count', obj)
}
// 视频会员平台收益埋点数据查询接口
export const videoListEarningsCount = (obj) => {
  return get('/activity/video/list/card/earnings/count', obj)
}
// 视频会员开通埋点接口
export const openVideoList = (obj) => {
  return get('/count/open/video/list', obj)
}
// 充值埋点关联活动接口
export const getVideoRechargeType = (obj) => {
  return get('/admin/member/record/getVideoRechargeType', obj)
}
// 会员卡管理 关联活动接口
export const videoVipCardSpu = (obj) => {
  return get('/videoVip/card/spu', obj)
}
// 集合权益埋点分页查询
export const gatherRightsPage = (obj) => {
  return get('/gatherRights/page', obj)
}
export const gatherRightsPageExport = params =>
  CONSTANT.publicPath + '/gatherRights/page/export?' + qs.stringify(params)

// 集合权益埋点明细分页查询
export const gatherRightsPageDetail = (obj) => {
  return get('/gatherRights/page/detail', obj)
}
export const gatherRightsPageDetailExport = params =>
  CONSTANT.publicPath + '/gatherRights/page/export/detail?' + qs.stringify(params)

