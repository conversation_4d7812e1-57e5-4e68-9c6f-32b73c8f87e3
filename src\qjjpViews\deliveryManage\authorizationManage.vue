  <!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 14:33:57
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-10-08 14:06:45
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="投放账号授权列表">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small"
          @click="handleAdd">新增授权</el-button>
      </div>

    </page>
    <el-drawer v-if="drawer" :visible.sync="drawer" direction="rtl" size="50%" :with-header="false"
      :wrapper-closable="false">
      <div class="close_button">
        <i class="el-icon-close" @click="drawer = false" />
      </div>
      <div class="drawer_package">
        <div class="drawer_title">
          <span>模型编辑/新增</span>
        </div>
        <div class="addForm_package">
          <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="180px" class="demo-ruleForm">
            <div class="form_view">
              <div class="form_view_title">
                <div class="title_line" /><span>基础信息</span>
              </div>
              <el-form-item label="应用名称" prop="siteId" :rules="addRules.common">
                <el-select v-model="addForm.siteId" placeholder="请选择应用名称" @change="mediaPlatformChange">
                  <el-option v-for="item in siteIdsList" :key="item.siteId" :label="item.name" :value="item.siteId" />
                </el-select>
              </el-form-item>
              <el-form-item label="媒体平台" prop="mediaPlatform" :rules="addRules.common">
                <el-select v-model="addForm.mediaPlatform" placeholder="请选择模型所属平台" @change="mediaPlatformChange">
                  <el-option v-for="item in list5" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="addForm.mediaPlatform && addForm.mediaPlatform == 2" label="开发账号主体" prop="subject"
                :rules="addRules.common">
                <el-select v-model="addForm.subject" placeholder="请选择开发账号主体" @change="subjectChange">
                  <el-option v-for="item in subjectList" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
              <div>
                <el-form-item label="授权账号" prop="accountName" :rules="addRules.common"
                  :style="{ display: 'inline-block' }">
                  <div :style="{ width: '200px' }">
                    <el-input v-model="addForm.accountName" placeholder="请输入需要授权的账号" maxlength="30" />
                  </div>
                </el-form-item>
                <span :style="{ padding: '0 0 0 10px' }">{{ addForm.accountName.length }}/30</span>
              </div>
              <div>
                <el-form-item label="授权账号ID" prop="accountId" :rules="addRules.common"
                  :style="{ display: 'inline-block' }">
                  <div :style="{ width: '200px' }">
                    <el-input v-model="addForm.accountId" placeholder="请输入需要授权的账号ID" maxlength="60" />
                  </div>
                </el-form-item>
                <span :style="{ padding: '0 0 0 10px' }">{{ addForm.accountId.length }}/60</span>
              </div>
              <el-form-item v-if="addForm.mediaPlatform != 2 || addForm.subject" label="appID" prop="appId"
                :rules="addRules.common">
                <el-select v-model="addForm.appId" placeholder="请选择开发账号appID" @change="appIdChange">
                  <el-option v-for="item in optionList" :key="item.appId" :label="item.appId" :value="item.appId" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="addForm.mediaPlatform != 2 || addForm.subject" label="secretID" prop="secretId"
                :rules="addRules.common">
                <el-select v-model="addForm.secretId" placeholder="请选择开发账号secretID" @change="asecretIdChange">
                  <el-option v-for="item in optionList" :key="item.secretId" :label="item.secretId"
                    :value="item.secretId" />
                </el-select>
              </el-form-item>
              <div class="form_view">
                <div class="form_view_title">
                  <div class="title_line" /><span>授权状态</span>
                </div>
                <el-form-item label="授权状态" prop="status" :rules="addRules.common">
                  <el-switch v-model="addForm.status" :active-value="1" :inactive-value="0" />
                </el-form-item>
              </div>
              <div :style="{ 'text-align': 'right', width: '100%' }" class="view_button">
                <el-button @click="drawer = false">取消</el-button>
                <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { undertakeList, addOrUpdate, undertakeListexport, modelRepositorypage, modelRepositoryplatform, modelRepositoryexit } from '@/qjjpApi/operate'
import { count_channel_application_list, mediaAll } from '@/qjjpApi/NewChannel'
import { mediaaccountpage, mediaaccountaddOrUpdate, accountoptionList } from '@/qjjpApi/deliveryManage'

import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      addForm: {
        id: '',
        mediaPlatform: '',
        siteId: '',
        accountId: '',
        accountName: '',
        appId: '',
        secretId: '',
        subject: '',
        currentPage: '',
        status: 1
      },
      siteIdsList: [],
      mediaList: [],
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      drawer: false,
      list4: [
        {
          id: 1,
          name: '会员用户'
        },
        {
          id: 0,
          name: '非会员用户'
        }
      ],
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      list2: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ],
      list3: [
        {
          id: 1,
          name: '落地页'
        }
      ],
      list5: [
        {
          id: 1,
          name: '字节'
        },
        {
          id: 2,
          name: '快手'
        },
        {
          id: 3,
          name: '百度'
        },
        {
          id: 4,
          name: '腾讯'
        }
      ],
      list6: [
        {
          id: 0,
          name: '未授权'
        },
        {
          id: 1,
          name: '已授权'
        },
        {
          id: 2,
          name: '占用'
        }
      ],
      siteIds: [],
      optionList: [],
      optionLists: {},
      subjectList: [],
      listQuery: {
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          //   if (!this.siteIds.length) {
          //     await modelRepositoryplatform().then(res => {
          //       if (res.code === 200) {
          //         if (res.data && res.data.length) {
          //           this.siteIds = res.data
          //         }
          //       }
          //     })
          //   }
          await count_channel_application_list().then(res => {
            if (res.code === 200) {
              this.siteIdsList = res.data
            }
          })
          const list = await mediaaccountpage(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '媒体平台',
          key: 'mediaPlatform',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list5,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '授权账号',
          type: formItemType.input,
          search: true,
          clearable: true,
          searchKey: 'accountName',
          tableHidden: true
        },
        {
          title: '授权账号ID',
          type: formItemType.input,
          search: true,
          clearable: true,
          searchKey: 'accountId',
          tableHidden: true
        },
        {
          title: '授权状态',
          key: 'authorizationStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list6,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '媒体平台',
          key: 'mediaPlatformStr'
        },
        {
          title: '开发者账号主体',
          key: 'subject'
        },
        {
          title: '授权账号',
          key: 'accountName'
        },
        {
          title: '授权账号ID',
          key: 'accountId'
        },
        {
          title: 'appid',
          key: 'appId'
        },
        {
          title: 'secretID',
          key: 'secretId'
        },
        {
          title: '授权地址',
          key: 'authorizationUrl',
          render: (h, params) => {
            if (!params.data.row.authorizationUrl) {
              return h('span', '--')
            }
            return h('a', {
              attrs: {
                href: params.data.row.authorizationUrl,
                target: '_blank'
              },
              style: {
                'text-decoration': 'underline',
                'color': 'blue'
              }
            }, params.data.row.authorizationUrl)
          }
        },
        {
          title: '授权状态',
          key: 'authorizationStatusStr'
        },
        // {
        //   title: '投放代理',
        //   key: 'proxyConfigName'
        // },
        // {
        //   title: '代理返点',
        //   key: 'name'
        // },
        {
          title: '创建时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '更新人员',
          key: 'creatorName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required']
        },
        {
          title: '操作',
          width: 140,
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',

              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                
                // 保存原始数据，用于后续回显
                const originalData = params;
                
                // 设置表单数据
                this.addForm = JSON.parse(JSON.stringify(params));
                
                // 先打开抽屉
                this.drawer = true;
                
                // 使用 nextTick 确保 DOM 更新后再执行数据加载
                this.$nextTick(async () => {
                  try {
                    // 先加载媒体平台数据
                    await this.mediaPlatformChange(this.addForm.mediaPlatform, () => {
                      
                      // 设置开发账号主体
                      if (this.addForm.mediaPlatform != 2) {
                        this.subjectChange('-');
                      } else {
                        // 使用原始数据中的 subject
                        this.subjectChange(originalData.subject);
                      }
                      
                      // 确保 appId 和 secretId 保持原始值
                      if (originalData.appId) {
                        this.$set(this.addForm, 'appId', originalData.appId);
                      }
                      
                      if (originalData.secretId) {
                        this.$set(this.addForm, 'secretId', originalData.secretId);
                      }
                    });
                  } catch (error) {
                    console.error('数据加载过程出错:', error);
                  }
                });
              }
            },
            {
              text: '授权详情',
              key: 'edit2',
              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.$router.push({
                  name: 'qjjpAuthorizationDetail',
                  query: {
                    id: params.accountId
                  }
                })
              }
            }
          ]
        }
      ]
    }
  },
  //   watch: {
  //     drawer: {
  //       handler(val) {

  //       }
  //     }
  //   },
  async mounted() {
    await mediaAll().then(res => {
      if (res.code === 200) {
        this.mediaList = res.data
      }
    })
    // const list1 = await messageStyleList()
    // const { records, total } = list1.data
    // let dataList = []
    // console.info(records, 'records')
    // if (records && records.length) {
    //   dataList = [total, ...records]
    // }
    // const result = {
    //   data: dataList
    // }
    // console.info(result, 'result')
  },
  created() { },
  methods: {
    appIdChange(val) {
      if (!val) {
        console.warn('appIdChange: val is empty');
        return;
      }
      console.log('appIdChange - val:', val, 'optionList:', this.optionList);
      
      // 在optionList中查找匹配的appId
      let matchedItem = null;
      if (this.optionList && this.optionList.length > 0) {
        matchedItem = this.optionList.find(item => item.appId === val);
      }
      
      if (matchedItem) {
        this.$set(this.addForm, 'secretId', matchedItem.secretId);
        this.$set(this.addForm, 'appId', matchedItem.appId);
      } else {
        console.warn('未在optionList中找到匹配的appId:', val);
        // 保留当前值，不做修改
      }
    },
    asecretIdChange(val) {
      if (!val) {
        console.warn('asecretIdChange: val is empty');
        return;
      }
      
      // 在optionList中查找匹配的secretId
      let matchedItem = null;
      if (this.optionList && this.optionList.length > 0) {
        matchedItem = this.optionList.find(item => item.secretId === val);
      }
      
      if (matchedItem) {
        this.$set(this.addForm, 'secretId', matchedItem.secretId);
        this.$set(this.addForm, 'appId', matchedItem.appId);
      } else {
        console.warn('未在optionList中找到匹配的secretId:', val);
        // 保留当前值，不做修改
      }
    },
    mediaPlatformChange(val, callback) {
      console.log('mediaPlatformChange 开始执行, platform:', this.addForm.mediaPlatform, 'siteId:', this.addForm.siteId);
      
      // 确保有必要的参数
      if (!this.addForm.mediaPlatform) {
        console.error('mediaPlatform 为空');
        return;
      }

      accountoptionList({ 
        platform: this.addForm.mediaPlatform, 
        siteId: this.addForm.siteId || null 
      }).then(res => {
        console.log('accountoptionList 响应:', res);
        if (res.code == 200) {
          // 清空现有数据
          this.subjectList = [];
          this.optionLists = {};
          
          if (res.data) {
            // 遍历返回的数据，提取主体列表
            for (const key in res.data) {
              this.subjectList.push(key);
            }
            
            // 保存完整的数据结构，其中key是subject，value是对应的optionList数组
            this.optionLists = res.data;
          }
          
          if (callback) {
            callback();
          }
        }
      }).catch(err => {
        console.error('accountoptionList 错误:', err);
      });
    },
    subjectChange(val) {
      console.log('subjectChange - val:', val, 'optionLists:', this.optionLists);
      
      // 设置subject值
      this.$set(this.addForm, 'subject', val);
      
      // 根据subject获取对应的optionList
      if (this.optionLists && this.optionLists[val]) {
        this.optionList = this.optionLists[val];
        console.log('设置 optionList:', this.optionList);
      } else {
        console.warn('未找到对应的 optionList, subject:', val);
        this.optionList = [];
      }
      
      // 如果不是编辑模式，清空appId和secretId
      if (!this.addForm.id) {
        this.$set(this.addForm, 'secretId', '');
        this.$set(this.addForm, 'appId', '');
      }
    },
    renderHeaders(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h('div', { slot: 'content' }, [textArr.map(text => h('div', null, text))]),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    },
    handMessageStyleListAdd(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          mediaaccountaddOrUpdate({ ...this.addForm, currentPage: location.href }).then(res => {
            if (res.code == 200) {
              this.drawer = false
              this.$message.success('操作成功')
              window.open(res.data)
              this.$store.dispatch('tableRefresh', this)
            }
          })
        }
      })
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = undertakeListexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    reloadAddform() {
      this.addForm = {
        id: '',
        mediaPlatform: '',
        siteId: '',
        accountId: '',
        accountName: '',
        appId: '',
        secretId: '',
        subject: '',
        currentPage: location.href,
        status: 1
      }
    },
    handleAdd() {
      this.reloadAddform()
      this.drawer = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  overflow: scroll;
  // padding-bottom: 20px;
  padding: 0 30px 20px;
  position: relative;
  /* overflow-x: auto; */
}

::v-deep .el-drawer__header {
  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 30px;
    text-align: left;
    font-style: normal;
  }

}

::v-deep .el-drawer__body {}

.close_button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background-color: rgb(0, 0, 0, 1);
  text-align: center;
  cursor: pointer;

  i {
    color: white;
    line-height: 40px;
  }
}

.drawer_package {
  height: 100%;
  position: relative;

  .drawer_title {
    padding: 10px 20px 5px;
    vertical-align: middle;

    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }

  }
}

.addForm_package {
  background-color: rgb(189, 184, 184, 0.1);
  padding: 15px;
  height: 100%;

  .demo-ruleForm {
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    position: relative;

    .view_button {
      position: absolute;
      bottom: 0;
    }
  }
}

.view_button {
  background-color: #ffffff;
  padding: 20px;
  border-top: 1px dashed #000000;

  ::v-deep .el-button {
    margin: 0 10px;
  }
}

.form_view {
  // margin: 0 0px 20px;
  background-color: #ffffff;
  // border: 1px solid rgba(0,0,0,0.2);
  width: 100%;
  padding: 15px;
  border-radius: 5px;

  .form_view_title {
    margin-bottom: 20px;

    .title_line {
      width: 2px;
      height: 10px;
      background-color: #66b1ff;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      padding-left: 5px;
      vertical-align: middle;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }
}

.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
