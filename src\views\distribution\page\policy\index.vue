<template>
  <div class="distribution-policy">
    <page :request="request" :list="list" table-title="分发策略列表">
      <div slot="searchContainer" style="display: inline-block; margin-bottom: 15px;">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small"
          @click="handleAdd">添加</el-button>
      </div>
    </page>
    <FormDrawer ref="formDrawer" @success="handleSuccess" />
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import FormDrawer from './components/FormDrawer'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getTriageStrategyList, getUndertakeByType } from '@/api/distribution'
import { undertakelist } from '@/api/operate'
import moment from 'moment'

export default {
  name: 'DistributionPolicy',
  components: {
    page,
    FormDrawer
  },
  data() {
    return {
      // 落地页路由选项
      undertakeList: [],
      // 投放链路选项
      linkTypeList: [
        {
          id: 1,
          name: 'APK链路'
        },
        {
          id: 2,
          name: 'H5链路'
        }
      ],
      // 分发对象选项
      triageTypeList: [
        {
          id: 1,
          name: '不限'
        },
        {
          id: 2,
          name: '自定义'
        }
      ],
      // 分发限制选项
      triageLimitTypeList: [
        {
          id: 1,
          name: '不限'
        },
        {
          id: 2,
          name: '自定义'
        }
      ],
      // 状态选项
      statusList: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ],
      // 查询参数
      listQuery: {},
      // API请求配置
      request: {
        getListUrl: async data => {
          // 处理查询参数，只保留需要的字段
          const params = {
            strategyName: data.name,
            linkType: data.linkType,
            routeId: data.routeId,
            status: data.status,
            ...data // 保留分页参数
          }
          
          // 发起列表请求
          const res = await getTriageStrategyList(params)
          const { records, total } = res.data
          return {
            data: {
              total: total,
              rows: records
            }
          }
        }
      }
    }
  },
  created() {
  },
  computed: {
    list() {
      return [
        {
          title: '策略名称',
          key: 'name',
          search: true,
          titleHidden: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '策略名称'
          }
        },
        {
          title: '投放链路',
          key: 'deliveryLinkType',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.linkTypeList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          options: {
            on: () => {
              return {
                change: e => {
                  // 重新获取落地页路由列表
                  this.fetchUndertakeList(Number(e))

                  // 清空落地页路由的选择
                  setTimeout(() => {
                    const input = document.querySelector('input[placeholder="落地页路由"]');
                    if (input) {
                      input.value = '';
                      input.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                  }, 1000);
                }
              }
            }
          }
        },
        {
          title: '落地页路由',
          key: 'routerId',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.undertakeList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.statusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '策略名称',
          key: 'strategyName'
        },
        {
          title: '投放路径',
          key: 'deliveryLinkType',
          render: (h, params) => {
            const linkType = params.data.row.deliveryLinkType
            const item = this.linkTypeList.find(item => item.id === linkType)
            return h('span', item ? item.name : '--')
          }
        },
        {
          title: '落地页路由',
          key: 'deliveryRouterName'
        },
        {
          title: '分发对象',
          key: 'triageType',
          render: (h, params) => {
            const type = params.data.row.triageType
            const item = this.triageTypeList.find(item => item.id === type)
            return h('span', item ? item.name : '--')
          }
        },
        {
          title: '分发限制',
          key: 'triageLimitType',
          render: (h, params) => {
            const type = params.data.row.triageLimitType
            const item = this.triageLimitTypeList.find(item => item.id === type)
            return h('span', item ? item.name : '--')
          }
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            const status = params.data.row.status
            const statusText = status === 1 ? '启用' : '禁用'
            return h('span', statusText)
          }
        },
        {
          title: '更新人',
          key: 'updateByName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              click: (index, item, params) => {
                this.$refs.formDrawer.open(params)
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleAdd() {
      this.$refs.formDrawer.open()
    },
    // 获取落地页路由选项（用于列表查询）
    async fetchUndertakeList(type) {
      try {
        const res = await getUndertakeByType({ type })
        if (res.code === 200) {
          this.undertakeList = res.data || []
        }
      } catch (error) {
        console.error('获取落地页路由选项失败:', error)
        this.$message.error('获取落地页路由选项失败')
      }
    },
    // 创建成功后刷新列表
    handleSuccess() {
      this.$store.dispatch('tableRefresh', this)
    }
  }
}
</script>

<style lang="scss" scoped>
.distribution-policy {
  padding: 20px;
  
  ::v-deep .activeButton {
    .el-button {
      margin-right: 5px;
      padding: 7px 6px;
    }
  }
}
</style> 