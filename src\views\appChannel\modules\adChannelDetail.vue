<template>
  <div style="width:700px;margin:20px auto 0;">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="186px"
      class="demo-ruleForm"
    >
      <el-form-item label="名称：" prop="name">
        <el-input v-model="ruleForm.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="广告类型：" prop="adType">
        <el-select
          v-model="ruleForm.adType"
          placeholder="请选择广告类型"
          @change="handleAdType($event)"
        >
          <el-option
            v-for="(item, i) in advertisementTypeList"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="展示规则：" prop="ruleId">
        <el-select
          v-model="ruleForm.ruleId"
          placeholder="请选择展示规则"
          @change="handleRule($event)"
        >
          <el-option
            v-for="(item, i) in ruleIdList"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="代码位">
        <el-row
          v-for="(item, itemIndex) in ruleForm.codeSeats"
          :key="itemIndex"
          style="margin-bottom:10px"
        >
          <el-form-item :label="`${item.adName}`" label-width="80px">
            <el-select
              v-model="ruleForm.codeSeats[itemIndex].codeSeatManagerStr"
              placeholder="请选择ruleId"
            >
              <el-option
                v-for="(item, i) in ruleForm.codeSeats[itemIndex]
                  .originSeatList"
                :key="i"
                :label="item.name"
                :value="`${item.id}-${item.name}`"
              />
            </el-select>
          </el-form-item>
        </el-row>
      </el-form-item>
      <el-form-item label="展示渠道：">
        <el-button type="primary" @click="chooseChannel">选择渠道</el-button>
        <div v-if="this.multipleSelection.length > 0">
          <span style="font-weight:bold">已选渠道: </span>{{ this.multipleSelection.join(' ; ') }}
        </div>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-radio-group v-model="ruleForm.status">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
        >确定</el-button>
        <el-button @click="back">返回</el-button>
      </el-form-item>
    </el-form>
    <el-dialog title="选择渠道" :visible.sync="channalVisible" width="40%">
      <el-row class="search-row" style="margin-top: 0;" type="flex">
        <el-col class="search-col">
          <span class="search-label">渠道:</span>
          <el-input
            v-model="parameterObj.channelName"
            class="search-maxInput"
            clearable
            placeholder="请输入渠道名称或ID"
          />
          <el-button type="primary" @click="getChannelData">搜索</el-button>
        </el-col>
      </el-row>
      <el-table
        ref="multipleTable"
        :data="tableData"
        :row-key="
          row => {
            return row.id
          }
        "
        border
        :reserve-selection="true"
        style="width: 100%; margin: 30px 0"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :reserve-selection="true"
        />
        <el-table-column prop="channelCode" label="渠道ID" />
        <el-table-column prop="name" label="渠道名称" />
      </el-table>
      <el-pagination
        :current-page="parameterObj.pageNumber"
        :page-sizes="[10, 20, 30]"
        :page-size="parameterObj.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="parameterObj.total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <div style="text-align:center">
        <el-button type="primary" @click="handleChooseChannel">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  get_code_seat_manager_list,
  get_rule_list,
  get_type_list
} from '@/api/advertisement'
import {
  post_ad_channel,
  put_ad_channel,
  get_adChannel_ById
} from '@/api/appChannel'
import { GET_CHANNEL_LIST_NEW } from '@/api/operate'
export default {
  data() {
    return {
      projectList: [],
      ruleForm: {
        name: '',
        adType: '',
        ruleId: '',
        status: true,
        codeSeats: []
      },
      DataLoading: false,
      rules: {
        name: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        adType: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        ruleId: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '此项不能为空', trigger: 'blur' }]
      },
      ruleIdList: [],
      codeSeatIdList: [],
      advertisementTypeList: [],
      channalVisible: false,
      multipleSelection: [],
      multipleSelectionObj: {},
      tableData: [],
      parameterObj: {
        channelName: '',
        total: 0,
        pageNumber: 1,
        pageSize: 10
      }
    }
  },
  // 'type', 'id', 'params'
  created() {
    this.init()
  },
  mounted() {
    const { type, id } = this.$route.query
    this.type = type
    this.id = id
    if (this.type === 'edit') {
      this.getDataDetail()
    }
  },
  methods: {
    init() {
      get_type_list().then(res => {
        this.advertisementTypeList = res.data.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.id
          }
        })
      })
    },
    getRuleList() {
      get_rule_list({
        adType: this.ruleForm.adType
      }).then(res => {
        this.ruleIdList = res.data.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.id
          }
        })
      })
    },
    getCodeManager(param, key) {
      get_code_seat_manager_list(param).then(res => {
        if (res.code == 200) {
          this.$delete(this.ruleForm.codeSeats[key], 'originSeatList')
          this.$set(this.ruleForm.codeSeats[key], 'originSeatList', res.data)
        }
      })
    },
    chooseChannel() {
      this.channalVisible = true
      if (this.tableData.length === 0) {
        this.getChannelData()
      }
    },
    getDataDetail() {
      get_adChannel_ById(this.id).then(res => {
        if (res.code == 200) {
          this.ruleForm = {
            ...res.data
          }
          this.ruleForm.codeSeats = JSON.parse(this.ruleForm.codeSeats)
          this.multipleSelection = this.ruleForm.channelIds
          this.ruleForm.channelIds.forEach(item => {
            this.multipleSelectionObj[item] = false
          })
          this.getRuleList()
          this.ruleForm.codeSeats = this.ruleForm.codeSeats.map(
            (item, index) => {
              item.originSeatList = []
              this.getCodeManager(
                { adId: item.adId, adType: this.ruleForm.adType },
                index
              )
              item.codeSeatManagerStr = `${item.codeSeatManagerId}-${item.codeSeatManagerName}`
              return item
            }
          )
        }
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.multipleSelection.length === 0) {
            this.$message.error('请选择展示渠道')
            return
          }
          let validRule = true
          this.ruleForm.channelIds = this.multipleSelection
          this.ruleForm.codeSeats = this.ruleForm.codeSeats.map(item => {
            if (item.codeSeatManagerStr != '') {
              const arr = item.codeSeatManagerStr.split('-')
              item.codeSeatManagerId = arr[0]
              item.codeSeatManagerName = arr[1]
            } else {
              validRule = false
            }
            return item
          })
          if (!validRule) {
            this.$message.error('请选择代码位信息')
            return
          }
          if (this.type == 'add') {
            post_ad_channel(this.ruleForm).then(res => {
              if (res.code == 200) {
                this.$refs[formName].resetFields()
                this.$message.success('添加成功！')
                this.back()
              }
            })
          } else {
            put_ad_channel(this.ruleForm).then(res => {
              if (res.code == 200) {
                this.$refs[formName].resetFields()
                this.$message.success('编辑成功！')
                this.back()
              }
            })
          }
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$emit('close', 'close')
    },
    getChannelData() {
      GET_CHANNEL_LIST_NEW({
        ...this.parameterObj
      }).then(res => {
        if (res.code == 200) {
          this.tableData = res.data
          this.parameterObj.total = res.totalCount
          if (this.type === 'edit' && this.ruleForm.channelIds) {
            this.$nextTick(() => {
              res.data.forEach(item => {
                if (this.multipleSelection.indexOf(item.channelCode) != -1) {
                  this.multipleSelectionObj[item.channelCode] = true
                  this.$refs.multipleTable.toggleRowSelection(item, true)
                }
              })
            })
          }
        }
      })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.parameterObj.pageNumber = 1
      this.parameterObj.pageSize = page
      this.getChannelData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.parameterObj.pageNumber = page
      this.getChannelData()
    },
    handleSelectionChange(val) {
      console.log(val)
      const newArr = val.map(item => {
        return item.channelCode
      })
      for (const i in this.multipleSelectionObj) {
        if (!this.multipleSelectionObj[i]) {
          newArr.push(i)
        }
      }
      this.multipleSelection = [...newArr]
    },
    handleChooseChannel() {
      this.channalVisible = false
    },
    back() {
      this.$router.go(-1)
    },
    handleAdType(val) {
      this.getRuleList()
      this.ruleForm.ruleId = ''
      this.ruleForm.codeSeats = []
    },
    handleRule(val) {
      let chooseItem = {}
      this.ruleIdList.forEach(item => {
        if (item.id == val) {
          chooseItem = { ...item }
        }
      })
      const ruleArr = JSON.parse(chooseItem.rule)
      this.ruleForm.codeSeats = []
      ruleArr.forEach((item, index) => {
        this.ruleForm.codeSeats.push({
          adId: item.adId,
          adName: item.adName,
          codeSeatManagerStr: '',
          codeSeatManagerId: '',
          codeSeatManagerName: '',
          originSeatList: []
        })
        this.getCodeManager(
          { adId: item.adId, adType: this.ruleForm.adType },
          index
        )
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.search-row {
  .search-label {
    margin-bottom: 0;
    margin-right: 5px;
  }
}
</style>
