<template>
  <page :request="request" :list="list" table-title="续费页统计">
    <div slot="searchContainer" style="display: inline-block">
      <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { countVipRenewPage, countVipRenewPageExport } from '@/api/buryData'
import moment from 'moment'
import { count_channel_application_list } from '@/api/NewChannel'

export default {
  name: 'renewStatistics',
  components: {
    page
  },
  props: {},
  data() {
    const that = this
    return {
      listQuery: {
        startDate: moment()
          .subtract(7, 'days')
          .format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD'),
        siteId: ''
      },
      request: {
        getListUrl: async(data) => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = res.data
              }
            })
          }
          return Promise.all([countVipRenewPage(this.listQuery)]).then(
            res => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      siteIds: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.rangeDatePicker,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          val: [
            moment()
              .subtract(7, 'days')
              .format('YYYYMMDD'),
            moment().format('YYYYMMDD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true,
          titleHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          tableHidden: true,
          titleHidden: true
        },
        {
          title: '渠道ID',
          key: 'channelCode',
          type: formItemType.input,
          tableHidden: true,
          search: true,
          titleHidden: true
        },
        {
          title: '日期',
          key: 'date',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '续费页曝光（pv/uv）',
          key: 'renewPopupS'
        },
        {
          title: '支付点击（pv/uv）',
          key: 'renewPayClickS'
        },
        {
          title: '支付成功（12月/3月/1月）',
          key: 'renewPaySuccessCount'
        },
        {
          title: '支付成功总数/金额',
          key: 'renewPaySuccessS'
        },
        {
          title: '退款数/金额',
          key: 'renewReturnS'
        },
        {
          title: '投诉量',
          key: 'renewComplainCount'
        },
        {
          title: '访问cvr',
          key: 'popupCvr'
        },
        {
          title: '续费页转化',
          key: 'renewPopupClickRatio'
        },
        {
          title: '支付转化',
          key: 'renewPaySuccessRatio'
        },
        {
          title: '退款率',
          key: 'renewReturnRatio'
        }
      ]
    }
  },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = countVipRenewPageExport({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
