<template>
  <div>
    <ycTitle style="margin: 20px 0;" />
    <div style="margin:50px auto;">
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="300px"
        class="demo-ruleForm"
      >
        <el-form-item label="分类名称：" prop="name">
          <el-input
            v-model="ruleForm.name"
            style="width:300px;"
            clearable
            placeholder="请输入商品分类名称"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="上级分类：" prop="parentId">
          <el-cascader
            v-model="ruleForm.parentIds"
            placeholder="请选择,默认一级"
            :options="casCadeData"
            :props="{ checkStrictly: true }"
            clearable
            :disabled="type=='edit'"
          />
        </el-form-item>
        <el-form-item label="排序：" prop="sort">
          <el-input
            v-model.number="ruleForm.sort"
            autocomplete="off"
            style="width:300px;"
            placeholder="请输入排序内容,数字越大越靠前"
          />
        </el-form-item>
        <el-form-item label="是否上架：" prop="status">
          <el-radio v-model="ruleForm.status" label="DISPLAY">上架</el-radio>
          <el-radio v-model="ruleForm.status" label="HIDE">下架</el-radio>
        </el-form-item>
        <el-form-item label="链接类型：" prop="linkType">
          <el-radio v-model="ruleForm.linkType" label="0">商品</el-radio>
          <el-radio v-model="ruleForm.linkType" label="1">外链</el-radio>
        </el-form-item>
        <!-- 外链地址 -->
        <el-form-item v-if="isShowLinkUrl" label="外链地址：" prop="linkUrl">
          <el-input
            v-model.number="ruleForm.linkUrl"
            autocomplete="off"
            style="width:300px;"
            placeholder="请输入外链地址"
          />
        </el-form-item>
        <el-form-item label="分类图标：" prop="logoUrl">
          <div v-show="!ruleForm.logoUrl">
            <el-upload
              ref="upload"
              style="width:300px"
              accept="image/png, image/jpg, image/jpeg, image/gif"
              :headers="headers"
              :action="uploadUrl"
              :before-upload="beforeUpload"
              :on-success="(response, file)=>uploadSuccess(response, file, 'logo')"
              :on-error="uploadError"
              :multiple="false"
              :limit="1"
              list-type="picture"
            >
              <el-button id="uploadEle" icon="el-icon-upload" size="small" plain type="warning">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">(二级必传)只能上传jpg/png格式文件，文件不能超过1M</div>
            </el-upload>
          </div>
          <div v-if="ruleForm.logoUrl">
            <viewer style="display: inline-flex; ">
              <div class="img-wrap">
                <img :src="ruleForm.logoUrl" style="max-width: 100px;max-height: 100px;">
              </div>
            </viewer>
            <div slot="tip" class="el-upload__tip">(二级必传)只能上传jpg/png格式文件，文件不能超过1M</div>
            <p>
              <el-button type="warning" plain size="small" @click="CikUpload('logo')">重新选取</el-button>
            </p>
          </div>
        </el-form-item>
        <el-form-item label="商品详情：" prop="logoUrl">
          <div v-show="!ruleForm.detailUrl">
            <el-upload
              ref="upload2"
              style="width:300px"
              accept="image/png, image/jpg, image/jpeg, image/gif"
              :headers="headers"
              :action="uploadUrl"
              :before-upload="beforeUpload"
              :on-success="(response, file)=>uploadSuccess(response, file, 'detail')"
              :on-error="uploadError"
              :multiple="false"
              :limit="1"
              list-type="picture"
            >
              <el-button id="uploadEle2" icon="el-icon-upload" size="small" plain type="warning">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">(二级必传)只能上传jpg/png格式文件，文件不能超过1M</div>
            </el-upload>
          </div>
          <div v-if="ruleForm.detailUrl">
            <viewer style="display: inline-flex; ">
              <div class="img-wrap">
                <img :src="ruleForm.detailUrl" style="max-width: 100px;max-height: 100px;">
              </div>
            </viewer>
            <div slot="tip" class="el-upload__tip">(二级必传)只能上传jpg/png格式文件，文件不能超过1M</div>
            <p>
              <el-button type="warning" plain size="small" @click="CikUpload('detail')">重新选取</el-button>
            </p>
          </div>
        </el-form-item>
        <el-form-item label="温馨提示：">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            :rows="3"
            style="width:300px;"
            placeholder="请输入提示内容"
          />
        </el-form-item>
        <el-form-item style="margin-top: 40px">
          <el-button plain icon="el-icon-arrow-left" @click="onCallback()">返回</el-button>
          <el-button
            style="width:100px;"
            type="primary"
            :disabled="btn_disabled"
            @click="handUpdated('ruleForm')"
          >提交</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import ycTitle from '@/components/yc-title/title'
import constant from '@/config/constant.conf'
import { getCascade, save, detail, update } from '@/api/goodsType'
import Store from '@/store'

export default {
  components: {
    ycTitle
  },
  data() {
    return {
      headers: {}, // 上传文件的请求头
      editId: '', // 编辑功能  查询的id
      type: '', // 区分编辑还是新增
      btn_disabled: false,
      limit: 1, // 限制上传文件数量
      uploadUrl: '', // 上传文件的url
      casCadeData: [], // 分类列表
      ruleForm: {
        linkType: '0', // 链接类型
        linkUrl: '', // 外链地址
        name: '',
        parentIds: [], // 分类选中的数组
        sort: '', // 排序
        logoUrl: '', // 分类图标
        detailUrl: '', // 详情图片
        status: 'HIDE', // 上下架
        description: ''// 描述
      },
      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
        linkUrl: [{ required: true, message: '外链地址不能为空', trigger: 'blur' }]
      }
    }
  },
  // 当路由进入
  beforeRouteEnter(to, from, next) {
    to.meta.title = to.query.type === 'add' ? '添加分类' : '编辑分类'
    next()
  },
  computed: {
    // 是否显示外链的输入框
    isShowLinkUrl() {
      if (this.ruleForm.linkType == '0') {
        return false
      } else {
        return true
      }
    }
  },
  mounted() {
    // 上传地址
    this.uploadUrl = constant.publicPath + '/upload/image'
    // 请求头
    this.headers = { Authorization: `${Store.getters.authorization}` }
    this.getCascadeData()
    this.type = this.$route.query.type
    if (this.type === 'edit') {
      this.editId = this.$route.query.id
      this.get_detail()
    }
  },
  methods: {
    /** 获取详情 */
    get_detail() {
      detail(this.editId).then(res => {
        if (res.code === 0) {
          this.ruleForm = res.data
          this.ruleForm.linkType = String(this.ruleForm.linkType)
        }
      })
    },
    /** 获取级联的数据 */
    getCascadeData() {
      getCascade().then(res => {
        this.casCadeData = res.data
      })
    },
    /** 提交表单 */
    handUpdated(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.btn_disabled = true
          const param = this.ruleForm
          const ids = this.ruleForm.parentIds
          param.level = ids.length + 1
          // 获取parentId
          if (ids.length === 0) {
            param.parentId = 0
          } else {
            param.parentId = ids[ids.length - 1]
          }
          param.linkUrl = this.ruleForm.linkType == '1' ? this.ruleForm.linkUrl : ''
          if (this.type === 'add') {
            console.log(param, '参数')
            /** 新增 */
            save(param).then(res => {
              this.btn_disabled = false
              if (res.code === 0) {
                this.$message.success('新增成功')
                this.onCallback()
              }
            })
          } else {
            /** 编辑 */
            param.id = this.editId
            console.log(param, '参数')
            update(param).then(res => {
              this.btn_disabled = false
              if (res.code === 0) {
                this.$message.success('编辑成功')
                this.onCallback()
              }
            })
          }
        }
      })
    },

    // 重新选取图片
    CikUpload(type) {
      if (type === 'logo') {
        document.getElementById('uploadEle').click()
        this.$refs.upload.clearFiles()
      }

      if (type === 'detail') {
        document.getElementById('uploadEle2').click()
        this.$refs.upload2.clearFiles()
      }
    },
    beforeUpload(file) {
      // 上传之前
      if (file.size > 4 * 1024 * 1024) {
        this.$message.error('上传图片大小不能超过4M!')
      }
      return file.size
    },
    uploadSuccess: function(res, file, type) {
      // 图片上传成功
      const thar = this
      if (res.code === 0) {
        if (type === 'logo') {
          thar.ruleForm.logoUrl = res.data
        }
        if (type === 'detail') {
          thar.ruleForm.detailUrl = res.data
        }
      }
    },
    uploadError: function(res, file) {
      // 图片上传失败
      this.$message.error('图片上传失败')
    },
    onCallback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
</style>
