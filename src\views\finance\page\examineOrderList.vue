<template>
  <div class="examinOrderList">
    <div class="totalData" style>
      <div>提现申请：{{ dataTotal.num }}</div>
      <div>审核失败：{{ dataTotal.failNum }}</div>
      <div>审核成功：{{ dataTotal.successNum }}</div>
      <div>打款中：{{ dataTotal.payingNum }}</div>
      <div>打款成功：{{ dataTotal.paySuccessNum }}</div>
      <div>打款总金额：{{ dataTotal.sumPay }}</div>
    </div>
    <page :request="request" :list="list" table-title="提现订单">
      <div slot="searchContainer" style="display: inline-block">
        <el-button
          plain
          type="warning"
          size="small"
          icon="el-icon-download"
          @click="handUpload"
        >导出数据</el-button>
      </div>
    </page>
    <el-dialog :visible.sync="dialogVisible" width="60%" title="详情" :before-close="onHide" />
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import {
  get_financeOperate_list,
  get_withdraw_statis,
  EXPORT_Withdraw_Finance
} from '@/api/finance'
import moment from 'moment'

export default {
  components: {
    page
  },
  props: {},
  data() {
    const that = this
    return {
      dataTotal: {
        num: '0',
        failNum: '0',
        successNum: '0',
        withdrawAccountNo: '0',
        paySuccessNum: '0',
        payingNum: '0',
        sumPay: '0'
      },
      approveStatusList: [
        {
          label: '待审核',
          value: 'in-operate-approval'
        },
        {
          label: '运营审核不通过',
          value: 'operate-rejected'
        },
        {
          label: '运营审核通过',
          value: 'in-finance-approval'
        },
        {
          label: '财务拒绝',
          value: 'finance-rejected'
        },
        {
          label: '财务通过',
          value: 'finance-approval'
        }
      ],
      approveProcessList: [
        {
          label: '待打款',
          value: 1
        },
        {
          label: '打款中',
          value: 2
        },
        {
          label: '打款成功',
          value: 3
        },
        {
          label: '打款失败',
          value: 4
        }
      ],
      listQuery: {
        phoneNo: '',
        approvalProcess: '',
        drawStatus: '',
        startDate: moment()
          .subtract(6, 'd')
          .format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: data => {
          this.listQuery = { ...this.listQuery, ...data }
          return Promise.all([get_financeOperate_list(this.listQuery)]).then(
            res => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      dialogVisible: false,
      type: ''
    }
  },
  computed: {
    list() {
      return [
        {
          key: 'phoneNo',
          title: '用户账号',
          type: formItemType.input,
          tableHidden: true,
          search: true
        },
        {
          key: 'date1',
          title: '时间',
          type: formItemType.datePickerDaterangeGai,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          tableHidden: true,
          search: true,
          val: [
            moment()
              .subtract(6, 'd')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ]
        },
        {
          key: 'approvalProcess',
          title: '审核状态',
          type: formItemType.select,
          tableHidden: true,
          search: true,
          list: this.approveStatusList
        },
        {
          key: 'drawStatus',
          title: '打款状态',
          type: formItemType.select,
          tableHidden: true,
          search: true,
          list: [
            {
              label: '待打款',
              value: 1
            },
            {
              label: '打款成功',
              value: 3
            },
            {
              label: '打款失败',
              value: 4
            }
          ]
        },
        {
          title: '订单号',
          key: 'applyNo',
          type: formItemType.input
        },
        {
          title: '用户id',
          key: 'userId',
          type: formItemType.input
        },
        {
          title: '用户账号',
          key: 'mobileNo',
          type: formItemType.input
        },
        {
          title: '提现账号',
          key: 'withdrawAccountNo',
          type: formItemType.input
        },
        {
          title: '提现人姓名',
          key: 'withdrawRealName',
          type: formItemType.input
        },
        {
          title: '提现金额',
          key: 'withdrawAmount',
          type: formItemType.input
        },
        {
          title: '提现手续费',
          key: 'withdrawFee',
          type: formItemType.input
        },
        {
          title: '到账金额',
          key: 'withdrawRealy',
          type: formItemType.input
        },
        {
          title: '提交时间',
          key: 'id',
          type: formItemType.input,
          render: (h, params) => {
            const data = params.data.row
            return h(
              'span',
              moment(data.applyTime).format('YYYY-MM-DD HH:mm:ss')
            )
          }
        },
        {
          title: '审批状态',
          key: 'approveProcess',
          type: formItemType.input,
          render: (h, params) => {
            const data = params.data.row
            return h('span', this.formatApproveProcess(data.approveProcess))
          }
        },
        {
          title: '处理人',
          key: 'withdrawApproverStr',
          type: formItemType.input
        },
        {
          title: '打款状态',
          key: 'drawStatus',
          type: formItemType.input,
          render: (h, params) => {
            const data = params.data.row
            return h('span', this.formatDrawStatus(data.drawStatus))
          }
        },
        {
          title: '打款人',
          key: 'fundApproverStr',
          type: formItemType.input
        },
        {
          title: '失败备注',
          key: 'id',
          type: formItemType.input,
          render: (h, params) => {
            const data = params.data.row
            return h(
              'span',
              data.withdrawContent
                ? data.withdrawContent
                : data.fundWithdrawContent
            )
          }
        },
        {
          title: '处理时间',
          key: 'updateTime',
          type: formItemType.input,
          render: (h, params) => {
            const data = params.data.row
            return h(
              'span',
              data.updateTime
                ? moment(data.updateTime).format('YYYY-MM-DD HH:mm:ss')
                : '--'
            )
          }
        }
      ]
    }
  },
  watch: {
    listQuery: {
      handler: function(val, oldval) {
        this.getTotalData(val)
      },
      deep: true // 对象内部的属性监听，也叫深度监听
    }
  },
  mounted() {
    this.getTotalData(this.listQuery)
  },
  methods: {
    getTotalData(val) {
      get_withdraw_statis(val).then(res => {
        if (res.code == 200) {
          this.dataTotal = {
            ...res.data
          }
        }
      })
    },
    formatApproveProcess(val) {
      let str = ''
      this.approveStatusList.forEach(item => {
        if (item.value == val) {
          str = item.label
        }
      })
      return str
    },
    formatDrawStatus(val) {
      let str = '--'
      this.approveProcessList.forEach(item => {
        if (item.value == val) {
          str = item.label
        }
      })
      return str
    },
    onHide() {
      this.dialogVisible = false
    },
    /** 导出 */
    handUpload() {
      const obj = {
        token: this.$utils.getToken(),
        ...this.listQuery
      }
      window.location.href = EXPORT_Withdraw_Finance(obj)
    }
  }
}
</script>

<style lang="scss" scoped>
.examinOrderList {
  .totalData {
    display: flex;
    margin: 10px 0px 30px;
    justify-content: center;
    div {
      font-size: 22px;
      margin: 0px 20px;
    }
  }
}
</style>
