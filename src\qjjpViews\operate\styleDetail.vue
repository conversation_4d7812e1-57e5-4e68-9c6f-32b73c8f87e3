<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 14:53:10
 * @LastEditors: 蒋雪 <EMAIL>
 * @LastEditTime: 2024-07-25 18:02:53
-->
<template>
  <div id="styleDetail">
    <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="100px" class="demo-ruleForm">
      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>基础信息</span>
        </div>
        <div style="display: flex;flex-wrap: wrap;">
          <el-form-item label="风格名称" prop="styleName" :rules="addRules.common">
            <div :style="{width: '200px'}">
              <el-input v-model="addForm.styleName" placeholder="请输入风格名称" />
            </div>
          </el-form-item>
          <el-form-item label="风格头像" prop="imgUrl" :rules="addRules.common">
            <el-input ref="productUrl" v-model="addForm.imgUrl" style="display: none" />
            <qjjpUpload
              v-if="isShow"
              ele-name="productUrl"
              :pic-url="addForm.imgUrl"
              @uploadSuccess="
                url => {
                  this.addForm.imgUrl = url
                  $refs.productUrl.focus()
                }
              "
            />
          </el-form-item>
          <el-form-item label="风格标签" class="labelImgItem tipItem" prop="labelUrl">
            <uploadFile v-model="addForm.labelUrl" :limit="1" :size="500 * 1024" :gif-size="1024 * 1024" :height="50" :width="170" />
            <div class="tipData">*支持png，jpg，jpeg，gif格式，(上传大小500K,gif大小1M）</div>
          </el-form-item>
        </div>
        <div style="display: flex;">
          <el-form-item
            style="width:70%"
            label="风格描述"
            prop="styleDesc"
            :rules="addRules.common"
          >
            <div>
              <el-input
                v-model="addForm.styleDesc"
                type="textarea"
                :rows="3"
                placeholder="请输入风格描述"
                maxlength="30"
              />
              <span>{{ addForm.styleDesc.length }}/30</span>
            </div>
          </el-form-item>
          <el-form-item label="上传语音" class="tipItem" prop="audioUrl">
            <uploadFile v-model="addForm.audioUrl" class="audioUrl" :send-url="`${$CONSTANT.qjjpPath}/cms/upload/audio`" :accept-array="['mp3']" :limit="1" :size="1024 * 1024" :height="70" :width="170" />
            <div class="tipData">*支持MP3格式，上传大小1M</div>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="恋爱指数" prop="loveExponent" style="display: inline-block;" :rules="addRules.common">
            <div style="display: flex;width:200px">
              <el-input
                v-model="addForm.loveExponent"
                class="noArrowInput"
                type="text"
                onkeyup="this.value=this.value.replace(/[^\d]/g,'');if(this.value!='')this.value=this.value<1?1:this.value>100?100:this.value"
                oninput="this.value=this.value.replace(/[^\d]/g,'');if(this.value!='')this.value=this.value<1?1:this.value>100?100:this.value"
                placeholder="请输入正整数"
                @blur="addForm.loveExponent=$event.target.value"
              />
              <span> (1%~100%)</span>
            </div>
          </el-form-item>
          <el-form-item class="specialize-item" label="擅长" prop="beGoods" style="display: inline-block;" :rules="addRules.beGoods">
            <template v-for="(sItem,sIndex) in addForm.beGoods">
              <el-input
                :key="sIndex"
                v-model="sItem.value"
                style="width: 150px;margin:0 0 5px 5px"
                type="text"
                maxlength="10"
                placeholder="请输入擅长"
              />
            </template>
            <div style="display: inline-block;">
              <span :class="['setBtn',addForm.beGoods.length>=10 && 'noBtn']" @click="addForm.beGoods.length<10 && addsSpecialize()">+</span>
              <span :class="['setBtn',addForm.beGoods.length==1 && 'noBtn']" @click="addForm.beGoods.length>1 && delsSpecialize()">-</span>
            </div>
            <div class="tipData">*恋爱技能标签，例：谈心,撒娇,表白（最多10个）</div>
          </el-form-item>
        </div>
        <div style="margin-top: 20px;">
          <el-form-item label="恋爱性格" style="display: inline-block;" prop="loveDispositions" :rules="addRules.common">
            <el-select v-model="addForm.loveDispositions" multiple clearable placeholder="请选择恋爱性格">
              <el-option
                v-for="item in loveDispositionsList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="适用亲密度" style="display: inline-block;" prop="loveDispositions">
            <el-select v-model="addForm.emotionalStates" multiple clearable placeholder="请选择亲密度">
              <el-option
                v-for="item in emotionalStatesList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>

        <div style="display: flex;align-items: center;flex-wrap: wrap;">
          <el-form-item class="addKeyboard-item" style="display: inline-block;" label="添加到用户键盘" prop="addKeyboard" :rules="addRules.common">
            <el-radio-group v-model="addForm.addKeyboard">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item style="display: inline-block;" label="首页显示" prop="homePageShow" :rules="addRules.common">
            <el-radio-group v-model="addForm.homePageShow">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item style="display: inline-block;" label="标签图片" class="labelImgItem" prop="labelImg">
            <uploadFile v-model="addForm.labelImg" :limit="1" :size="500 * 1024" :height="35" :width="170" />
            <div class="img_tip">
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content">
                  <img class="img_tipImg" src="https://cdn-test.yocyxc.com/labelImg_admin.png">
                </div>
                <div>查看示例(340px*70px)<i style="font-size:14px;color:rgb(64, 158, 255);cursor: pointer;margin-left:5px" class="el-icon-question" /></div>
              </el-tooltip>
            </div>
          </el-form-item>
        </div>

        <el-form-item label="参考对话" prop="referenceConversation">
          <div class="dialogue" @click="dialogueClick">
            <template v-for="(childItem,childIndex) in addForm.referenceConversation">
              <el-select :key="`select_${childIndex}`" v-model="childItem.type" clearable placeholder="请选择" @clear="clearItem(childIndex)">
                <el-option
                  v-for="item in listQA"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <el-input :key="`input_${childIndex}`" :ref="`dialogueInput_${childIndex}`" v-model="childItem.msg" class="dialogue_input" placeholder="请输入对话" @keyup.native="keyupFN($event,childIndex)" />
            </template>

          </div>
        </el-form-item>

      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>风格预设</span>
        </div>
        <div style="display: flex;align-items: center;flex-wrap: wrap;">
          <el-form-item label="性别预设" prop="sex" :rules="addRules.common">
            <el-select v-model="addForm.sex" placeholder="请选择性别预设" @change="sexChange">
              <el-option
                v-for="item in list1"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="风格分类" prop="styleClassify" :rules="addRules.common">
            <el-select v-model="addForm.styleClassify" multiple placeholder="请选择风格分类" @change="styleClassifyListChange">
              <el-option
                v-for="item in styleClassifyList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="风格类型" prop="styleType" :rules="addRules.common">
            <el-select v-model="addForm.styleType" placeholder="请选择风格类型">
              <el-option
                label="免费风格"
                :value="1"
              />
              <el-option
                label="会员风格"
                :value="2"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="sellAlone-item" label="安卓列表解锁按钮文案" prop="unlockText" style="display: inline-block;">
            <el-input
              v-model="addForm.unlockText"
              maxlength="10"
              type="text"
              placeholder="列表解锁按钮文案"
            />
          </el-form-item>
          <el-form-item class="sellAlone-item" label="IOS列表解锁按钮文案" prop="unlockTextIos" style="display: inline-block;">
            <el-input
              v-model="addForm.unlockTextIos"
              maxlength="10"
              type="text"
              placeholder="列表解锁按钮文案"
            />
          </el-form-item>
        </div>
        <div style="display: flex;align-items: center;flex-wrap: wrap;">
          <el-form-item class="sellAlone-item" style="display: inline-block;" label="是否支持单卖" prop="sellAlone" :rules="addRules.common">
            <el-radio-group v-model="addForm.sellAlone" @change="addForm.styleLevel=3,addForm.sellAlonePrice=getOneBuyPrice,addForm.experiencePrice='0.01'">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="addForm.sellAlone==1" style="background: #f2f2f2;padding: 10px 10px;margin-left: 10px;">
            <div style="display: flex;align-items: center;flex-wrap: wrap;">
              <el-form-item class="styleLevel-item" label="风格等级" prop="styleLevel" :rules="addRules.common">
                <el-select v-model="addForm.styleLevel" placeholder="请选择风格等级" @change="addForm.sellAlonePrice = getOneBuyPrice">
                  <el-option
                    v-for="item in styleLevelLisy"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div v-if="codeObject" class="tipData">单独购买人设，体验价格是首次支付金额有效期{{ codeObject.RENEW_FIRST_DEDUCT_HOUR }}时，单买价格是到期后自动扣费金额有效期{{ codeObject.STYLE_CYCLE_VALID_DAY }}天</div>
              </el-form-item>

              <el-form-item class="styleLevel-item" label="安卓单卖价格" prop="sellAlonePrice" style="display: inline-block;" :rules="addRules.common">
                <el-input
                  v-model="addForm.sellAlonePrice"
                  type="text"
                  onkeyup="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')"
                  oninput="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')"
                  placeholder="安卓单卖价格"
                  @blur="addForm.sellAlonePrice=$event.target.value"
                />
              </el-form-item>
              <!-- :rules="addRules.common" -->
              <el-form-item class="styleLevel-item" label="安卓体验价格" prop="experiencePrice" style="display: inline-block;">
                <el-input
                  v-model="addForm.experiencePrice"
                  type="text"
                  onkeyup="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')"
                  oninput="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')"
                  placeholder="安卓体验价格"
                  @blur="addForm.experiencePrice=$event.target.value"
                />
              </el-form-item>
            </div>
            <!--IOS -->
            <el-form-item v-if="addForm.sellAlone==1" label-width="130" label="iOS单卖商品" prop="sellAloneAppleProductId">
              <div class="normal-style style-item">
                <IosProducetTableSelect v-model="addForm.sellAloneAppleProductId" :table-list="productsIos.normalProduct" />
              </div>
              <div class="tipData">iOS商品仅支持配置单卖价格，单卖有效期为{{ codeObject.STYLE_CYCLE_VALID_DAY }}天</div>
            </el-form-item>
          </div>

        </div>

      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>引导内容</span>
        </div>
        <el-form-item label="引导词" prop="guideContent" :rules="addRules.common">
          <el-input
            v-model="addForm.guideContent"
            type="textarea"
            :rows="3"
            placeholder="请输入引导词"
            maxlength="10000"
          />
          <span>{{ addForm.guideContent.length }}/10000</span>
        </el-form-item>
      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>模型信息</span>
        </div>
        <div style="display:flex">
          <el-form-item label="模型平台" prop="platform" :rules="addRules.common">
            <el-select v-model="addForm.platform" placeholder="请选择模型平台" @change="changePla">
              <el-option
                v-for="item in siteIds"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="addForm.platform&&addForm.platform!=''" label="模型名称" prop="modelId" :rules="addRules.common">
            <el-select v-model="addForm.modelId" placeholder="请选择模型名称" filterable>
              <el-option
                v-for="item in pla"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form_view2">
          <div class="form_view_title">
            <div class="title_line" /><span class="form_view_title_view2">模型自定义参数<el-tooltip
              content="模型支持的自定义参数，可辅助模型更好的进行回复"
              placement="top-start"
              :style="{color:'#409eff'}"
            >
              <i class="el-icon-question" style="font-size: 14px" />
            </el-tooltip></span>
          </div>
          <div style="display:flex">
            <el-form-item label="temperature" prop="temperature" :rules="addRules.common" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                temperature
                <el-tooltip
                  content="用于控制输出tokens的多样性，TopP值越大输出的tokens类型越丰富，取值范围0~1，未填写时默认值为1"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.temperature"
                  placeholder="请输入0-1的数值"
                  onkeyup="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1)value=1;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1)value=1;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.temperature=$event.target.value"
                />
              </div>
            </el-form-item>
            <el-form-item label="top_p" prop="topP" :rules="addRules.common" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                top_p
                <el-tooltip
                  content="用于控制输出tokens的多样性，TopP值越大输出的tokens类型越丰富，取值范围0~1，未填写时默认值为0.9"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.topP"
                  placeholder="请输入0-1的数值"
                  onkeyup="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1)value=1;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1)value=1;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.topP=$event.target.value"
                />
              </div>
            </el-form-item>
            <el-form-item label="top_k" prop="topK" :rules="addRules.common" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                top_k
                <el-tooltip
                  content="选择预测值最大的k个token进行采样，取值范围0-1000，0表示不生效，未填写时默认值为0"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.topK"
                  placeholder="请输入0-1000的数值"
                  onkeyup="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1000)value=1000;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^\d.]/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1000)value=1000;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.topK=$event.target.value"
                />
              </div>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="temperature" prop="doSample" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                do_sample
                <el-tooltip
                  content="模型是否采样，未填写时将使用模型平台默认值"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-select v-model="addForm.doSample" placeholder="请选择是否采样">
                  <el-option
                    label="是"
                    value="true"
                  />
                  <el-option
                    label="否"
                    value="false"
                  />
                </el-select>
              </div>
            </el-form-item>
            <el-form-item label="top_p" prop="presencePenalty" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                presence_penalty
                <el-tooltip
                  content="存在惩罚，如果为正，值越大，模型谈论到新话题的概率越大，取值范围为 [-2.0, 2.0] ，未填写时将使用模型平台默认值  "
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.presencePenalty"
                  placeholder="请输入-2-2的数值"
                  onkeyup="this.value=this.value.replace(/[^0-9-.]|(?<=\..*)\.|(?<!\d)\.|(?!^)-/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>2)value=2;if(value<-2)value=-2;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^0-9-]|(?<=\..*)\.|(?<!\d)\.|(?!^)-/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>2)value=2;if(value<-2)value=-2;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.presencePenalty=$event.target.value"
                />
              </div>
            </el-form-item>
            <el-form-item label="top_k" prop="frequencyPenalty" label-width="170px">
              <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                frequency_penalty
                <el-tooltip
                  content="频率惩罚，如果为正，值越大，模型逐字重复同一行的概率越小，取值范围为 [-2.0, 2.0] ，未填写时将使用模型平台默认值"
                  placement="top-start"
                  :style="{color:'#409eff'}"
                >
                  <i class="el-icon-question" style="font-size: 14px" />
                </el-tooltip>
              </span>
              <div :style="{width: '200px'}">
                <el-input
                  v-model="addForm.frequencyPenalty"
                  placeholder="请输入-2-2的数值"
                  onkeyup="this.value=this.value.replace(/[^0-9-.]|(?<=\..*)\.|(?<!\d)\.|(?!^)-/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>2)value=2;if(value<-2)value=-2;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  onafterpaste="this.value=this.value.replace(/[^0-9-]|(?<=\..*)\.|(?<!\d)\.|(?!^)-/g,'');if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>2)value=2;if(value<-2)value=-2;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                  @blur="addForm.frequencyPenalty=$event.target.value"
                />
              </div>
            </el-form-item>
          </div>
        </div>
      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>风格状态</span>
        </div>
        <div style="display: flex;align-items: center;flex-wrap: wrap;">
          <el-form-item label="状态" prop="status" :rules="addRules.common">
            <el-switch
              v-model="addForm.status"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>
          <el-form-item label="键盘排序" prop="sort" :rules="addRules.common">
            <div :style="{width: '200px'}">
              <el-input
                v-model="addForm.sort"
                class="noArrowInput"
                type="number"
                step="1"
                placeholder="请输入排序"
              />
            </div>
          </el-form-item>
          <el-form-item v-if="addForm.classifySort.length" label="分类排序" prop="classifySort" :rules="addRules.classifySortRules">
            <template v-for="(cItem,cIndex) in addForm.classifySort">
              <el-input
                :key="cIndex"
                :value="cItem.data"
                class="noArrowInput"
                type="number"
                style="width:200px;margin-right:5px"
                placeholder="请输入排序"
                @input="classifySortInput($event,cIndex)"
              >
                <template slot="prepend">{{ cItem.label }}</template>
              </el-input>
            </template>
          </el-form-item>
        </div>

      </div>
      <div :style="{'text-align': 'right', width: '100%'}" class="button_view">
        <el-button @click="closeCheckQAShow">取消</el-button>
        <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { messageStyleListAdd, modelRepositoryplatform, modelRepositoryselect, categoryList, messageStyleList } from '@/qjjpApi/operate'
import { systemParamGetByCodes } from '@/qjjpApi/system'
import qjjpUpload from '@/components/qjjp-upload/upload'
import IosProducetTableSelect from '@/qjjpViews/operate/components/IosProducetTableSelect.vue'
import { getIosProduct } from '@/qjjpViews/payManage/basicParams'
export default {
  components: {
    qjjpUpload,
    uploadFile: () => import('@/components/yc-upload/handleUploadImage'),
    IosProducetTableSelect
  },
  data() {
    const dialogueListValidate = (rule, value, callback) => {
      // if (!value || value.length == 0) {
      //   callback(new Error('请完善参考对话'))
      // }
      const valueLength = value.length
      for (let x = 0; x < valueLength; x++) {
        const itemData = value[x]
        console.log(itemData)
        if ((!itemData.type || String(itemData.type) == '') && itemData.msg != '') {
          callback(new Error(`请完善第${x + 1}项参考对话的选项`))
          break
        }
        if (String(itemData.type) != '' && itemData.msg == '') {
          callback(new Error(`请完善第${x + 1}项参考对话的对话`))
          break
        }
      }
      callback()
    }
    const beGoodsValidate = (rule, value, callback) => {
      let isErr = false
      value.forEach(item => {
        if (item.value == '') {
          isErr = true
        }
      })
      if (isErr) {
        callback(new Error(`请完善擅长信息！`))
      }
      callback()
    }
    const classifySortRules = (rule, value, callback) => {
      // const thisValueData = []
      const valueArray = value.map(n => n.data)
      for (let x = 0; x < valueArray.length; x++) {
        if (valueArray[x] == '') {
          callback(new Error(`请完善分类排序！`))
          break
        }
        // if (thisValueData.includes(valueArray[x])) {
        //   callback(new Error(`分类排序不能相同！`))
        //   break
        // }
        // thisValueData.push(valueArray[x])
      }
      callback()
    }
    return {
      codeObject: null,
      pla: [],
      value1: '1',
      loveDispositionsList: [
        {
          label: '被动型',
          value: 'passivity_type'
        },
        {
          label: '逃避型',
          value: 'escape_type'
        },
        {
          label: '慢热型',
          value: 'slow_warm_type'
        },
        {
          label: '平淡型',
          value: 'insipid_type'
        },
        {
          label: '主动型',
          value: 'initiative_type'
        },
        {
          label: '积极型',
          value: 'positive_type'
        },
        {
          label: '浪漫型',
          value: 'romance_type'
        },
        {
          label: '激情型',
          value: 'passion_type'
        }
      ],
      emotionalStatesList: [
        {
          label: '寻觅期',
          value: 1
        },
        {
          label: '有好感',
          value: 2
        },
        {
          label: '暧昧期',
          value: 3
        },
        {
          label: '热恋期',
          value: 4
        },
        {
          label: '平静期',
          value: 5
        }
      ],
      styleClassifyList: [],
      styleLevelLisy: [
        {
          label: '一颗星',
          value: 1,
          price: '4.9'
        },
        {
          label: '二颗星',
          value: 2,
          price: '9.9'
        },
        {
          label: '三颗星',
          value: 3,
          price: '14.9'
        },
        {
          label: '四颗星',
          value: 4,
          price: '19.9'
        },
        {
          label: '五颗星',
          value: 5,
          price: '29.9'
        }
      ],
      addForm: {
        labelUrl: '', // 风格标签
        audioUrl: '', // 上传语音
        loveExponent: '', // 恋爱指数
        beGoods: [ // 擅长
          { value: '' }
        ],
        loveDispositions: [], // 恋爱性格
        emotionalStates: [], // 适用亲密度
        addKeyboard: 0, // 添加到用户键盘

        styleClassify: [], // 风格分类

        sellAlone: 0, // 是否支持单卖
        styleLevel: 3, // 风格等级
        sellAlonePrice: '', // 安卓单卖价格
        experiencePrice: '0.01', // 安卓体验价格
        unlockText: '', // 列表解锁按钮文案
        unlockTextIos: '', // 列表解锁按钮文案

        classifySort: [], // 分类排序

        id: '',
        styleName: '',
        styleDesc: '',
        sex: 1,
        guideContent: '',
        status: 1,
        styleType: 2,
        imgUrl: '',
        sort: '',
        modelId: '',
        platform: '',
        temperature: 1,
        topP: 0.9,
        topK: 0,
        doSample: '',
        presencePenalty: '',
        frequencyPenalty: '',
        category: 1,
        homePageShow: 0,
        labelImg: '',
        referenceConversation: [
          {
            type: '',
            msg: ''
          }
        ],
        sellAloneAppleProductId: null, // 单卖ios商品库id
        experienceAppleProductId: null// experienceAppleProductId
      },
      referenceConversationClick: {

      },
      siteIds: [],
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }],
        referenceConversation: [{
          required: false, trigger: 'submit',
          validator: dialogueListValidate
        }],
        beGoods: [{
          required: true, trigger: 'blur',
          validator: beGoodsValidate
        }],
        classifySortRules: [{
          required: true, trigger: 'blur',
          validator: classifySortRules
        }]
      },
      form: {

      },
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      listQA: [
        {
          id: 'q',
          name: '问'
        },
        {
          id: 'a',
          name: '答'
        }
      ],
      isShow: true,
      productsIos: {
        normalProduct: [],
        cycleProduct: []
      },
      experienceAppleInfo: {}
    }
  },
  computed: {
    getOneBuyPrice() {
      if (this.addForm.styleLevel) {
        const item = this.styleLevelLisy.find(n => n.value == this.addForm.styleLevel)
        return item ? item.price : ''
      } else {
        return ''
      }
    }
  },
  watch: {
    '$route.path': {
      handler() {
        systemParamGetByCodes(['RENEW_FIRST_DEDUCT_HOUR', 'STYLE_CYCLE_VALID_DAY']).then(res => {
          this.codeObject = res.data
        })
      },
      immediate: true,
      deep: true
    }
  },
  async created() {
    this.productsIos = await getIosProduct()
  },
  async mounted() {
    this.addForm.sellAlonePrice = this.getOneBuyPrice
    if (this.$route.query.id) {
      this.styleClassifyList = []
      const paramsData = localStorage.getItem(`styleListDetail`) || null

      if (paramsData) {
        Object.assign(this.addForm, JSON.parse(paramsData))
        this.addForm.platform && this.changePla(this.addForm.platform)
        console.log(this.addForm.styleDesc)
        console.log(this.addForm, 'addForm---')
        this.addForm.styleDesc = !this.addForm.styleDesc || this.addForm.styleDesc == null ? '' : this.addForm.styleDesc
        let referenceConversationData = null
        try {
          referenceConversationData = JSON.parse(this.addForm.referenceConversation)
        } catch (error) {
          console.info(error)
        }
        if (!referenceConversationData || referenceConversationData == '' || (Array.isArray(referenceConversationData) && referenceConversationData.length == 0)) {
          this.addForm.referenceConversation = [
            {
              type: '',
              msg: ''
            }
          ]
        } else {
          this.addForm.referenceConversation = JSON.parse(this.addForm.referenceConversation)
        }

        this.addForm.loveDispositions = this.addForm.loveDispositions ? this.addForm.loveDispositions.split(',') : []
        this.addForm.emotionalStates = this.addForm.emotionalStates ? this.addForm.emotionalStates.split(',').map(n => Number(n)) : []

        // let beGoods = []
        // try {
        //   beGoods = JSON.parse(this.addForm.beGoods)
        // } catch (error) {
        //   console.info(error)
        // }
        // this.addForm.beGoods = beGoods.length > 0 ? beGoods.map(n => { return { value: n } }) : [{ value: '' }]

        this.addForm.beGoods = this.addForm.beGoods && this.addForm.beGoods.length > 0 ? this.addForm.beGoods.map(n => { return { value: n } }) : [{ value: '' }]

        this.addForm.styleClassify = this.addForm.styleCategories ? this.addForm.styleCategories.map(n => n.categoryId) : []
        this.addForm.classifySort = this.addForm.styleCategories ? this.addForm.styleCategories.map(n => { return { value: n.categoryId, data: n.sort } }) : []
        delete this.addForm.styleCategories
        this.styleClassifyListChange()
      }
    } else {
      await this.getCategoryList()
    }
    await modelRepositoryplatform().then(res => {
      if (res.code === 200) {
        if (res.data && res.data.length) {
          this.siteIds = res.data
        }
      }
    })
  },
  methods: {
    // 格式化分类排序
    async styleClassifyListChange() {
      if (this.styleClassifyList.length == 0) {
        await this.getCategoryList()
      }
      const classifySort = this.addForm.classifySort
      const classifySortNew = this.styleClassifyList.map(n => {
        console.log(n)
        if (this.addForm.styleClassify.includes(n.value)) {
          const item = classifySort.find(t => t.value == n.value)
          n['data'] = item && String(item.data) ? item.data : ''
          return n
        }
        return null
      }).filter(n => n)
      this.$set(this.addForm, 'classifySort', classifySortNew)
    },
    async sexChange() {
      this.styleClassifyList = []
      this.addForm.styleClassify = []
      await this.getCategoryList()
      this.styleClassifyListChange()
    },
    // 获取分类
    getCategoryList() {
      return new Promise(async(r) => {
        const { data } = await categoryList({ type: 1, sex: this.addForm.sex })
        this.styleClassifyList = data.map(item => {
          return {
            value: item.id,
            label: item.name
          }
        })
        this.$forceUpdate()
        r(true)
      })
    },
    classifySortInput(e, index) {
      this.$set(this.addForm.classifySort[index], 'data', e)
      this.$forceUpdate()
    },
    addsSpecialize() {
      this.addForm.beGoods.push({ value: '' })
    },
    delsSpecialize() {
      this.addForm.beGoods.pop()
    },
    analysis(str, data, matchArray = ['${', '}']) {
      const regX = RegExp(`\\${matchArray[0]}+.*?([\\s\\S]*?)${matchArray[1]}.*?`, 'g')
      return str.replace(regX, function(word, key) {
        const returnData = data[key] || null
        if (returnData) {
          return returnData
        }
        return word
      })
    },
    changePla(e) {
      modelRepositoryselect(e).then(res => {
        if (res.code == 200) {
          this.pla = res.data
        }
      })
    },
    closeCheckQAShow() {
      // this.isShow = false
      this.$router.push('/qjjp/operate/styleManage')
    },
    handMessageStyleListAdd(formName) {
      if (this.addForm.sellAlone != 1) {
        this.addForm.sellAloneAppleProductId = null
        this.addForm.experienceAppleProductId = null
      }
      // if (this.addForm.sellAlone == 1 && !this.addForm.sellAloneAppleProductId) {
      //   this.$message.error('单卖商品ios必选')
      //   return
      // }
      this.$refs[formName].validate(valid => {
        if (valid) {
          const params = { ...this.addForm }
          const referenceConversation = params.referenceConversation.map(n => {
            if (n.type == '' && n.msg == '') {
              return null
            }
            return n
          }).filter(n => n && n != '')

          params.loveDispositions = params.loveDispositions.join(',')
          params.emotionalStates = params.emotionalStates.join(',')
          params.beGoods = params.beGoods.map(n => n.value)
          params['styleCategories'] = params.styleClassify.map(n => {
            const item = params.classifySort.find(t => t.value == n)
            if (item) {
              return {
                categoryId: n,
                sort: item.data
              }
            }
            return null
          }).filter(n => n)
          params.referenceConversation = referenceConversation.length > 0 ? JSON.stringify(referenceConversation) : JSON.stringify([])
          console.log(params, '---params---')
          messageStyleListAdd(params).then(res => {
            if (res.code == 200) {
              localStorage.removeItem(`styleListDetail`)
              this.$store.dispatch('tagsView/delView', this.$route)
              this.$message.success('添加成功')
              this.$router.push('/qjjp/operate/styleManage')
            }
          })
        } else {
          this.$nextTick(() => {
            // 获取错误节点
            const isError = this.$refs[formName].$el.getElementsByClassName('is-error')
            isError[0].scrollIntoView({
              // 滚动到指定节点
              // 值有start,center,end，nearest，当前显示在视图区域中间
              block: 'center',
              // 值有auto、instant,smooth，缓动动画（当前是慢速的）
              behavior: 'smooth'
            })
          })
          return
        }
      })
    },
    dialogueClick(e) {
      if (e.target.className == 'dialogue') {
        const listIndex = this.addForm.referenceConversation.length - 1
        const Dom = this.$refs[`dialogueInput_${listIndex}`][0] || this.$refs[`dialogueInput_${listIndex}`]
        Dom.focus()
      }
    },
    clearItem(index) {
      if (index != 0) {
        this.addForm.referenceConversation.splice(index, 1)
      }
    },
    keyupFN(event, index) {
      if (event.keyCode == 13 || event.key == 'Enter') {
        if (event.target.value != '') {
          const referenceConversation = this.addForm.referenceConversation
          referenceConversation.push(
            {
              type: '',
              msg: ''
            })
          this.$set(this.addForm, 'referenceConversation', referenceConversation)
          this.$nextTick(() => {
            this.dialogueClick({ target: { className: 'dialogue' }})
            this.referenceConversationClick[index + 1] = true
          })
        }
      }
      if (event.keyCode == 8 || event.key == 'Backspace') {
        if (this.referenceConversationClick[index] && this.addForm.referenceConversation.length > 1) {
          this.clearItem(index)
          this.$nextTick(() => {
            this.dialogueClick({ target: { className: 'dialogue' }})
          })
        }
      }
      if (event.target.value == '') {
        this.referenceConversationClick[index] = true
      } else {
        this.referenceConversationClick[index] = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#styleDetail{
    padding: 15px;
    background-color: rgb(189, 184, 184,0.2);
    .button_view{
      padding: 20px 20px;
      background-color: #fff;
      border-top:1px dashed #000000;
      ::v-deep .el-button{
        margin: 0 10px;
      }
    }
    .form_view,.form_view2{
      margin: 0 0px 0px;
        background-color: #fff;
        // border: 1px solid rgba(0,0,0,0.2);
        width: 100%;
        padding: 15px;
        border-radius: 5px;
        // margin-bottom: 20px;
        .form_view_title{
            margin-bottom: 20px;
            .title_line{
              width: 2px;
              height: 10px;
              background-color:#66b1ff ;
              display: inline-block;
              vertical-align: middle;
            }
            span{
              padding-left: 5px;
              vertical-align: middle;
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
    }
    .form_view2{
      background-color: rgb(189, 184, 184,0.1);
      .form_view_title_view2{
        font-size: 16px !important;
      }
    }
}
.dialogue{
  width: 100%;
  height: 170px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  resize: vertical;
  overflow: auto;
  padding-bottom: 20px;
  ::v-deep .el-select{
    margin: 10px;
    width: 100px;
    height: 30px;
    display: inline-block;
  }
  ::v-deep .dialogue_input{
    width: calc(100% - 130px);
    height: 30px;
    display: inline-block;
    input{
      border-radius: 4px 4px 0 0;
      border: none;
      border-bottom: 1px solid #DCDFE6;
      &.el-input__inner:focus {
        border-color: #409EFF;
      }
    }
  }
}
::v-deep .el-form-item{
  &.labelImgItem .el-form-item__content{
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  &.tipItem .el-form-item__content{
    display: flex;
    align-items: flex-start;
    flex-direction: column;
  }
}
.img_tip{
  margin-left: 5px;
}
.img_tipImg{
  width: 170px;
  height: 35px;
}
.setBtn{
  width: 50px;
  height: 35px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #cccccc;
  margin-left: 5px;
  cursor: pointer;
  &.noBtn{
    background-color: #f2f2f2;
    cursor: not-allowed;
    position: relative;
  }
}
::v-deep .specialize-item{
  position: relative;
  .tipData{
    position: absolute;
    bottom: 0;
    transform: translateY(100%);
    white-space: nowrap;
  }
}
::v-deep .styleLevel-item{
  position: relative;
  margin-bottom: 30px;
  .tipData{
    position: absolute;
    bottom: 0;
    transform: translateY(100%);
    white-space: nowrap;
  }
}
::v-deep .addKeyboard-item{
  .el-form-item__label{
    width: 150px !important;
  }
  .el-form-item__content{
    margin-left: 150px !important;
  }
}
::v-deep .sellAlone-item{
  .el-form-item__label{
    width: 130px !important;
  }
  .el-form-item__content{
    margin-left: 130px !important;
  }
}
::v-deep .audioUrl{
  .el-upload-icon i{
    font-size:20px
  }
}
.style-item{
  margin-top: 15px;
  &:first-child{
    min-width: 300px;
    margin-right: 10px;
  }
  &:last-child{
    width: 50%;
  }
}
.require-icon{
  color: red;
  margin-right: 5px;
}
.item-label{
  font-size: 14px;
  margin-bottom: 5px
}
</style>
