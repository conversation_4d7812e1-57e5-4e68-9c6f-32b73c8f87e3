<template>
  <page :request="request" :list="list" table-title="供应商管理" @sFormDialogChange="beforeUpdate" />
</template>

<script>
import { addDspSupplier, deleteDspSupplier, getDspSupplierList, updateDspSupplier } from '@/api/advertisement'
import page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'

export default {
  components: {
    page
  },
  data() {
    return {
      isEdit: false,

      listQuery: {
        status: ''
      },
      request: {
        getListUrl: data => {
          this.listQuery = { ...this.listQuery, ...data }
          return getDspSupplierList(this.listQuery).then(res => {
            return { data: res.data.records, totalCount: res.data.total }
          })
        },
        insertHttp: addDspSupplier, // 添加
        updateHttp: data => {
          return updateDspSupplier({
            ...data
          }).then(res => {
            if (res.status === 200) {
              this.isEdit = false
              return Promise.resolve(res)
            }
            this.isEdit = true
            return Promise.reject(res)
          })
        } // 编辑
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: 'ID',
          key: 'id',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '供应商',
          key: 'supplierName',
          type: formItemType.inputNext, // inputNext
          reg: ['required'],
          options: {
            disabled: (params) => {
              return !!params?.data?.row?.id
            }
          }
        },
        {
          title: '请求地址',
          key: 'supplierUrl',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          list: [
            {
              label: '启用',
              value: '1'
            },
            {
              label: '禁用',
              value: '0'
            }
          ],
          search: true,
          val: ''
        },
        {
          title: '',
          key: 'supplierUrl2',
          type: formItemType.text,
          val: 'xxx',
          tableHidden: true,
          render: (h, params) => {
            return h('div', { style: 'color: red' }, this.isEdit ? '有运营位使用，谨慎操作' : '')
          }
        },
        {
          type: tableItemType.active,
          headerContainer: true,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.detailsDialog,
              theme: 'warning'
            },
            {
              text: '删除',
              key: 'edit',
              theme: 'danger',
              click: ($index, item, params) => {
                this.$confirm(`${params.status == 1 ? '当前供应商已启用，' : ''}是否删除该商品, 是否继续?`, '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                })
                  .then(async() => {
                    this.submitLoading = true
                    console.log('params :>> ', params)
                    const res = await deleteDspSupplier(params.id)
                    if (res.status == 200) {
                      this.$store.dispatch('tableRefresh', this)
                      this.$message({
                        type: 'success',
                        message: '删除成功'
                      })
                      this.dialogVisible = false
                    } else {
                      this.$message({
                        type: 'warn',
                        message: res.msg
                      })
                    }
                    this.submitLoading = false
                  })
                  .catch(err => {
                    this.$message({
                      type: 'info',
                      message: err.msg || err.message || '已取消删除'
                    })
                  })
              }
            }
          ]
        }
      ]
    }

  },
  methods: {
    beforeUpdate(e) {
      if (!e) {
        this.isEdit = false
      }
    }
  }
}
</script>
