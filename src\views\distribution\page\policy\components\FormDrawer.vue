<template>
  <el-drawer
    title="策略管理"
    v-if="visible"
    :visible.sync="visible"
    size="100%"
    :destroy-on-close="true"
    :wrapperClosable="false"
    :before-close="handleClose"
  >
    <div class="policy-drawer">
      <div class="drawer-content">
        <!-- 左侧表单区域 -->
        <div class="left-form">
          <!-- :disabled="isEdit && !allowEdit" -->
          <el-form 
            ref="form" 
            :model="form" 
            :rules="rules"
            label-width="100px"
          >
            <!-- 基础信息 -->
            <div class="form-section">
              <div class="section-title">基础信息</div>
              <el-form-item label="策略名称" prop="name" class="form-word-item">
                <el-input v-model="form.name" placeholder="请输入策略名称" maxlength="30" show-word-limit />
              </el-form-item>
            </div>

            <!-- 链路信息 -->
            <div class="form-section">
              <div class="section-title">链路信息</div>
              <el-form-item label="投放链路" prop="linkType">
                <el-select 
                  v-model="form.linkType" 
                  placeholder="请选择投放链路"
                  :disabled="isEdit"
                >
                  <el-option
                    v-for="item in linkTypeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="!isEdit" label="落地页使用场景" prop="pageType">
                <el-select v-model="form.pageType" placeholder="请选择落地页使用场景"
                  :disabled="isEdit" @change="($event) => handleLinkTypeChange(form.linkType, $event)">
                  <el-option label="投放落地页" :value="1" />
                  <el-option label="分发落地页" :value="2" />
                </el-select>
              </el-form-item>
              <!-- v-if="form.linkType && form.pageType" -->
              <el-form-item v-if="showRouteIdField" label="落地页路由" prop="routeId">
                <el-select v-model="form.routeId" placeholder="请选择落地页路由" :disabled="isEdit">
                  <el-option
                    v-for="item in undertakeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 分发对象 -->
            <div class="form-section">
              <div class="section-title">分发对象</div>
              <el-form-item label="分发对象" prop="distributeTarget">
                <el-radio-group v-model="form.distributeTarget" size="small">
                  <el-radio-button label="unlimited">不限</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 分发限制 -->
            <div class="form-section">
              <div class="section-title">分发限制</div>
              <el-form-item label="分发限制" prop="distributeLimit">
                <el-radio-group v-model="form.distributeLimit" size="small">
                  <el-radio-button label="unlimited">不限</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 状态 -->
            <div class="form-section">
              <div class="section-title">状态</div>
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio :label="1">启用</el-radio>
                  <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <!-- 右侧内容区域 -->
        <div class="right-content">
          <!-- 图例标题 -->
          <div class="section-title">
            <i class="line"></i>
            图例
          </div>
          <!-- 图例区域 -->
          <div class="legend-section">
            <div class="legend-content">
              <div class="legend-item">
                <legend-shapes 
                  type="rounded-rect" 
                  :width="shapeConfig.rectWidth" 
                  :height="shapeConfig.rectHeight" 
                />
              </div>
              <div class="legend-item">
                <legend-shapes 
                  type="diamond" 
                  :width="shapeConfig.diamondSize" 
                />
              </div>
              <div class="legend-item">
                <legend-shapes 
                  type="rect" 
                  :width="shapeConfig.rectWidth" 
                  :height="shapeConfig.rectHeight" 
                />
              </div>
            </div>
          </div>

          <!-- 分发节点设置 -->
          <div class="section-title" style="margin-top: 24px;">
            <i class="line"></i>
            分发节点设置
          </div>
          <div class="delivery-section">
            <!-- :disabled="isEdit && !allowEdit" -->
            <delivery-nodes 
              ref="deliveryNodes"
              :link-type="form.linkType"
              :route-id="form.routeId"
              :shape-config="shapeConfig"
              @distribution-change="handleDistributionChange"
            />
          </div>
        </div>
      </div>
      <!-- 底部操作按钮 -->
      <div class="drawer-footer">
        <el-button @click="handleClose">取 消</el-button>
        <!-- v-if="!isEdit || allowEdit"  -->
        <el-button 
          type="primary" 
          @click="handleSubmit"
        >确 定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import LegendShapes from './shapes/LegendShapes.vue'
import DeliveryNodes from './DeliveryNodes.vue'
import draggable from 'vuedraggable'
import { getInstitutionConfig, createTriageStrategy, getTriageStrategyDetail, editTriageStrategy, getUndertakeByType } from '@/api/distribution'

export default {
  name: 'PolicyDrawer',
  components: {
    LegendShapes,
    DeliveryNodes,
    draggable
  },
  props: {
    // 移除 undertakeList props
  },
  data() {
    return {
      visible: false,
      currentNode: null,
      isEdit: false,
      editId: null,
      // allowEdit: true, // 添加编辑权限控制标志
      distributionData: [], // 存储分发节点数据
      // 添加 undertakeList 到 data 中
      undertakeList: [],
      // 节点尺寸配置
      shapeConfig: {
        rectWidth: 120,
        rectHeight: 50,
        diamondSize: 60
      },
      initialForm: {
        name: '',
        linkType: '',
        routeId: '',
        distributeTarget: 'unlimited',
        distributeLimit: 'unlimited',
        status: 1
      },
      form: {
        name: '',
        linkType: '',
        routeId: '',
        distributeTarget: 'unlimited',
        distributeLimit: 'unlimited',
        status: 1
      },
      // 投放链路选项
      linkTypeList: [
        { id: 1, name: 'APK链路' },
        { id: 2, name: 'H5链路' }
      ],
      rules: {
        name: [
          { required: true, message: '请输入策略名称', trigger: 'blur' },
          { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
        ],
        linkType: [
          { required: true, message: '请选择投放链路', trigger: 'change' }
        ],
        routeId: [
          { required: true, message: '请选择落地页路由', trigger: 'change' }
        ],
        pageType: [
          { required: true, message: '请选择落地页使用场景', trigger: 'change' }
        ],
        distributeTarget: [
          { required: true, message: '请选择分发对象', trigger: 'change' }
        ],
        distributeLimit: [
          { required: true, message: '请选择分发限制', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    // 移除重复的请求
    // this.getUndertakeList()
  },
  computed: {
    showRouteIdField() {
      if(this.isEdit) {
        return true
      } else {
        if (this.form.linkType && this.form.pageType) {
          return true
        } else {
          return false
        }
      }
    }
  },
  methods: {
    // 移除获取落地页路由列表的方法
    // async getUndertakeList() {
    //   const res = await undertakelist({ status: 0, pageSize: 999, pageNumber: 1 })
    //   if (res.code === 200) {
    //     this.undertakeList = res.data.records
    //   }
    // },
    // 打开抽屉
    async open(row) {
      this.visible = true
      
      // 重置表单
      this.resetForm()

      if (row && row.id) {
        this.isEdit = true
        this.editId = row.id
        // this.allowEdit = false // 设置为不可编辑模式
        // 获取详情数据
        await this.fetchDetail()
        // 获取落地页路由选项
        if (this.form.linkType) {
          this.fetchUndertakeList(this.form.linkType, this.form.pageType)
        }
      } else {
        this.isEdit = false
        this.editId = null
        // this.allowEdit = true // 新建时可编辑
      }
    },
    // 获取详情数据
    async fetchDetail() {
      try {
        const res = await getTriageStrategyDetail(this.editId)
        if (res.code === 200) {
          const detail = res.data
          // 填充表单数据
          this.form = {
            name: detail.strategyName,
            linkType: detail.deliveryLinkType,
            pageType: detail.pageType,
            routeId: detail.deliveryRouterId,
            distributeTarget: detail.triageType === 1 ? 'unlimited' : 'custom',
            distributeLimit: detail.triageLimitType === 1 ? 'unlimited' : 'custom',
            status: detail.status
          }

          // 创建一个Promise来等待表单设置完成和节点加载完成
          await new Promise(resolve => {
            this.$nextTick(async () => {
              // 等待 DeliveryNodes 组件加载节点配置
              if (this.$refs.deliveryNodes) {
                await this.$refs.deliveryNodes.fetchNodes()
                
                // 准备节点数据 - 确保所有ID和引用是正确的
                const nodeData = (detail.triageStrategyNodeReqList || []).map(node => ({
                  id: node.deliveryLinkNodeCode, // 确保ID设置正确
                  nodeCode: node.deliveryLinkNodeCode,
                  nodeName: node.nodeName,
                  expanded: true, // 设置为展开状态
                  distributionType: node.triageType === 1 ? '全量分发' : '拒量分发',
                  institutionDisplayData: (node.triageStrategyAdvertiserList || [])
                    .sort((a, b) => a.sort - b.sort) // 根据 sort 字段排序
                    .map(item => ({
                      id: item.infoCategoryConfigId,
                      triageStrategyNodeId: item.triageStrategyNodeId,
                      priority: `P${item.sort}`,
                      institution: item.advertiserName,
                      infoType: this.getInfoCategoryLabel(item.infoCategory)
                    }))
                }))

                // 确保在节点完全加载后再初始化编辑数据
                setTimeout(() => {
                  this.$refs.deliveryNodes.initEditData(nodeData)
                  resolve()
                }, 100)
              } else {
                resolve()
              }
            })
          })
        }
      } catch (error) {
        console.error('获取分发策略详情失败:', error)
        this.$message.error('获取分发策略详情失败')
      }
    },
    // 重置表单
    resetForm() {
      this.form = JSON.parse(JSON.stringify(this.initialForm))
      this.distributionData = []
      // 重置表单校验状态
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    // 关闭抽屉
    handleClose(done) {
      this.visible = false
    },
    // 提交表单
    handleSubmit() {
      // 如果是编辑模式且不允许编辑，直接关闭抽屉
      // if (this.isEdit && !this.allowEdit) {
      //   this.visible = false
      //   return
      // }
      
      this.$refs.form.validate(async valid => {
        if (valid) {
          if(this.distributionData.length === 0) {
            this.$message.warning('请至少添加一个分发节点')
            return
          }
          const submitData = {
            strategyName: this.form.name,
            deliveryLinkType: this.form.linkType,
            pageType: this.form.pageType,
            deliveryRouterId: this.form.routeId,
            triageType: this.form.distributeTarget === 'unlimited' ? 1 : 2,
            triageLimitType: this.form.distributeLimit === 'unlimited' ? 1 : 2,
            status: this.form.status,
            triageStrategyNodeReqList: this.distributionData.map(node => ({
              deliveryLinkNodeCode: node.nodeCode,
              triageType: node.distributionType === '全量分发' ? 1 : 2,
              triageStrategyAdvertiserList: (node.institutionDisplayData || []).map(item => ({
                triageStrategyNodeId: item.triageStrategyNodeId,
                infoCategoryConfigId: item.id,
                sort: parseInt(item.priority.replace('P', ''))
              }))
            }))
          }

          try {
            // 如果是编辑模式，添加id字段
            if (this.isEdit) {
              submitData.id = this.editId
            }
            console.log('submitData: ', submitData)
            const res = await (this.isEdit ? editTriageStrategy(submitData) : createTriageStrategy(submitData))
            if (res.code === 200) {
              this.$message.success(this.isEdit ? '编辑成功' : '创建成功')
              this.visible = false
              // 触发父组件刷新列表
              this.$emit('success')
            }
          } catch (error) {
            console.error(this.isEdit ? '编辑分发策略失败:' : '创建分发策略失败:', error)
            this.$message.error(this.isEdit ? '编辑分发策略失败' : '创建分发策略失败')
          }
        }
      })
    },
    // 投放链路变更
    handleLinkTypeChange(val, pageType) {
      // 清空落地页路由
      this.form.routeId = ''
      // 获取对应的落地页路由选项
      this.fetchUndertakeList(val, pageType)
    },
    // 获取落地页路由选项
    async fetchUndertakeList(type, pageType) {
      try {
        const res = await getUndertakeByType({ type, pageType })
        if (res.code === 200) {
          this.undertakeList = res.data || []
        }
      } catch (error) {
        console.error('获取落地页路由选项失败:', error)
        this.$message.error('获取落地页路由选项失败')
      }
    },
    // 处理分发节点数据变更
    handleDistributionChange(data) {
      this.distributionData = data
    },
    // 获取信息分类标签
    getInfoCategoryLabel(value) {
      const map = {
        '0': '不限',
        '1': '一类',
        '2': '二类',
        '3': '三类'
      }
      return map[String(value)] || value
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;

  .drawer-content {
    flex: 1;
    display: flex;
    padding: 20px;
    overflow: hidden;

    .left-form {
      width: 300px;
      border-right: 1px solid #EBEEF5;
      padding-right: 20px;
      overflow-y: auto;

      .form-section {
        margin-bottom: 24px;

        .section-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 16px;
          padding-left: 8px;
          border-left: 4px solid #409EFF;
        }
      }
      
      .form-word-item {
        ::v-deep .el-input {
          .el-input__inner{
            padding-right: 46px;
          }
        }
      }

      ::v-deep .el-form-item {
        margin-bottom: 18px;

        .el-select {
          width: 100%;
        }
      }
    }

    .right-content {
      flex: 1;
      margin-left: 20px;
      overflow-y: auto;
      min-width: 0; /* 添加最小宽度为0，防止flex元素溢出 */

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 16px;

        .line {
          display: inline-block;
          width: 4px;
          height: 16px;
          background-color: #409EFF;
        }
      }

      .legend-section {
        width: 100%;
        height: 120px;
        background: #f5f7fa;
        border-radius: 4px;
        padding: 16px;
        display: flex;
        align-items: center;
        box-sizing: border-box; /* 确保padding计入宽度 */
        
        .legend-content {
          align-items: center;
          height: calc(100% - 40px);
          display: flex;
          gap: 50px;

          .legend-item {
            .shape {
              width: 120px;
              height: 36px;
              border: 1px solid #666;
              background-color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              color: #666;

              &.rounded-rect {
                border-radius: 8px;
              }

              &.diamond {
                transform: rotate(45deg);
                width: 60px;
                height: 60px;
                margin: 0 30px;

                span {
                  transform: rotate(-45deg);
                  white-space: nowrap;
                }
              }
            }
          }
        }
      }

      .delivery-section {
        width: 100%;
        background: #f5f7fa;
        border-radius: 4px;
        margin-top: 16px;
        height: calc(100% - 240px);
        box-sizing: border-box; /* 确保padding计入宽度 */
        overflow: auto; /* 添加滚动条，处理内容溢出 */
      }
    }
  }

  .drawer-footer {
    border-top: 1px solid #EBEEF5;
    padding: 20px;
    display: flex;
    justify-content: center;
    gap: 20px;
    
    .el-button {
      margin-left: 8px;
      width: 100px;
    }
  }
}

// 自定义抽屉样式
::v-deep .el-drawer__header {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #EBEEF5;

  > span{
    font-size: 20px;
  }

  .el-drawer__close-btn {
    i{
      font-size: 30px;
    }
  }
}

::v-deep .el-drawer__body {
  height: calc(100% - 55px);
  padding: 0;
}
</style> 