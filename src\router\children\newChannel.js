const newChannel = [
  {
    path: '/newChannel/channelList',
    name: 'channelList',
    meta: {
      title: '渠道列表'
    },
    component: () => import('@/views/newChannel/page/newChannelList')
  },
  {
    path: '/newChannel/newChannelP4Next',
    name: 'newChannelP4Next',
    meta: {
      title: '渠道P4页管理'
    },
    component: () => import('@/views/newChannel/page/newChannelP4Next')
  },
  {
    path: '/newChannel/newChannelListEdit',
    name: 'newChannelListEdit',
    meta: {
      title: '详情',
      parentTitle: '渠道列表',
      activeMenu: '/newChannel/channelList'
    },
    component: () => import('@/views/newChannel/page/newChannelListEdit')
  },
  {
    path: '/newChannel/newChannelPage',
    name: 'newChannelPage',
    meta: {
      title: '落地页列表'
    },
    component: () => import('@/views/newChannel/page/newChannelPage')
  },
  {
    path: '/newChannel/newChannelPageLottie',
    name: 'newChannelPageLottie',
    meta: {
      title: '动效列表'
    },
    component: () => import('@/views/newChannel/page/newChannelPageLottie')
  },
  {
    path: '/newChannel/newChannelType',
    name: 'newChannelType',
    meta: {
      title: '渠道类型'
    },
    component: () => import('@/views/newChannel/page/newChannelType')
  },
  {
    path: '/newChannel/newChannelTotal',
    name: 'newChannelTotal',
    meta: {
      title: '渠道统计'
    },
    component: () => import('@/views/newChannel/page/newChannelTotal')
  },
  {
    path: '/newChannel/newChannelDetailList',
    name: 'newChannelDetailList',
    meta: {
      title: '详情',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/newChannelTotal'
    },
    component: () => import('@/views/newChannel/page/newChannelDetailList')
  },
  {
    path: '/newChannel/newChannelTotalByChannel',
    name: 'newChannelTotalByChannel',
    meta: {
      title: '站内统计',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/newChannelTotal'
    },
    component: () => import('@/views/newChannel/page/newChannelTotalByChannel')
  },
  {
    path: '/newChannel/giftBag',
    name: 'giftBag',
    meta: {
      title: '礼包列表'
    },
    component: () => import('@/views/newChannel/page/giftBag/giftBag')
  },
  {
    path: '/newChannel/giftBagDetail',
    name: 'giftBagDetail',
    meta: {
      title: '礼包详情',
      parentTitle: '礼包列表',
      activeMenu: '/newChannel/giftBag'
    },
    component: () => import('@/views/newChannel/page/giftBag/giftBagDetail')
  },
  {
    path: '/newChannel/getGiftBag',
    name: 'getGiftBag',
    meta: {
      title: '领取礼包'
    },
    component: () => import('@/views/newChannel/page/giftBag/getGiftBag')
  },
  {
    path: '/newChannel/giftCertificate',
    name: 'giftCertificate',
    meta: {
      title: '礼券订单'
    },
    component: () => import('@/views/newChannel/page/giftBag/giftCertificate')
  },
  {
    path: '/newChannel/giftManager',
    name: 'giftManager',
    meta: {
      title: '礼券管理'
    },
    component: () => import('@/views/newChannel/page/giftBag/giftManager')
  },
  {
    path: '/newChannel/channelIp',
    name: 'channelIp',
    meta: {
      title: 'IP统计'
    },
    component: () => import('@/views/newChannel/page/channelIp')
  },
  {
    path: '/newChannel/channelAbnormal',
    name: 'channelAbnormal',
    meta: {
      title: '异常渠道'
    },
    component: () => import('@/views/newChannel/page/channelAbnormal')
  },
  {
    path: '/newChannel/channelAbnormalDetail',
    name: 'channelAbnormalDetail',
    meta: {
      title: '异常渠道详情',
      parentTitle: '异常渠道',
      activeMenu: '/newChannel/channelAbnormal'
    },
    component: () => import('@/views/newChannel/page/channelAbnormalDetail')
  },
  {
    path: '/newChannel/channelIpLoginDetail',
    name: 'channelIpLoginDetail',
    meta: {
      title: '登录明细',
      parentTitle: 'IP统计',
      activeMenu: '/newChannel/channelIp'
    },
    component: () => import('@/views/newChannel/page/channelIpLoginDetail')
  },
  {
    path: '/newChannel/channelIpVisitDetail',
    name: 'channelIpVisitDetail',
    meta: {
      title: '访问明细',
      parentTitle: 'IP统计',
      activeMenu: '/newChannel/channelIp'
    },
    component: () => import('@/views/newChannel/page/channelIpVisitDetail')
  },
  {
    path: '/newChannel/newChannelIp',
    name: 'newChannelIp',
    meta: {
      title: '异常IP(新)'
    },
    component: () => import('@/views/newChannel/page/newChannelIp')
  },
  {
    path: '/newChannel/newChannelIpDetail',
    name: 'newChannelIpDetail',
    meta: {
      title: 'ip明细',
      parentTitle: '异常IP(新)',
      activeMenu: '/newChannel/newChannelIp'
    },
    component: () => import('@/views/newChannel/page/newChannelIpDetail')
  },
  {
    path: '/newChannel/newChannelAbnormal',
    name: 'newChannelAbnormal',
    meta: {
      title: '异常监控(新)'
    },
    component: () => import('@/views/newChannel/page/newChannelAbnormal')
  },
  {
    path: '/newChannel/newChannelAbnormalDetail',
    name: 'newChannelAbnormalDetail',
    meta: {
      title: '渠道明细',
      parentTitle: '异常监控(新)',
      activeMenu: '/newChannel/newChannelAbnormal'
    },
    component: () => import('@/views/newChannel/page/newChannelAbnormalDetail')
  },
  {
    path: '/newChannel/feedback',
    name: 'newChannelListFeedback',
    meta: {
      title: '投诉反馈列表'
    },
    component: () => import('@/views/newChannel/page/newChannelFeedback')
  },
  {
    path: '/newChannel/feedbackManage',
    name: 'newChannelListFeedbackManage',
    meta: {
      title: '投诉反馈列表'
    },
    component: () => import('@/views/newChannel/page/newChannelFeedbackManage')
  },
  {
    path: '/newChannel/popUps',
    name: 'newChannelPopUpsManage',
    meta: {
      title: '弹窗管理'
    },
    component: () => import('@/views/newChannel/page/popUps')
  },
  {
    path: '/newChannel/payPageSettingsList',
    name: 'payPageSettingsList',
    meta: {
      title: '渠道P3页列表'
    },
    component: () => import('@/views/newChannel/page/payPageSettingsList')
  },
  {
    path: '/newChannel/payPageSettingsStatic',
    name: 'payPageSettingsStatic',
    meta: {
      title: '渠道P3页统计'
    },
    component: () => import('@/views/newChannel/page/payPageSettingsStatic')
  },
  {
    path: '/newChannel/payPageSettingsStaticDetail',
    name: 'payPageSettingsStaticDetail',
    meta: {
      title: '渠道P3页统计详情',
      parentTitle: '渠道P3页统计',
      activeMenu: '/newChannel/payPageSettingsStatic'
    },
    component: () => import('@/views/newChannel/page/payPageSettingsStaticDetail')
  },
  {
    path: '/newChannel/adverManage',
    name: 'adverManage',
    meta: {
      title: '第三方广告',
      parentTitle: '广告管理',
      activeMenu: '/newChannel/adverManage'
    },
    component: () => import('@/views/newChannel/page/adverManage')
  },
  {
    path: '/newChannel/adverManageDetail',
    name: 'adverManageDetail',
    meta: {
      title: '广告详情',
      parentTitle: '第三方广告',
      activeMenu: '/newChannel/adverManage'
    },
    component: () => import('@/views/newChannel/module/adverManageDetail')
  },
  {
    path: '/newChannel/adverTotalByAdver',
    name: 'adverTotalByAdver',
    meta: {
      title: '广告统计(广告)',
      parentTitle: '广告管理',
      activeMenu: '/newChannel/adverTotalByAdver'
    },
    component: () => import('@/views/newChannel/page/adverTotalByAdver')
  },
  {
    path: '/newChannel/adverTotalByAdverDetail',
    name: 'adverTotalByAdverDetail',
    meta: {
      title: '广告统计(广告)详情',
      parentTitle: '广告统计(广告)',
      activeMenu: '/newChannel/adverTotalByAdver'
    },
    component: () => import('@/views/newChannel/page/adverTotalByAdverDetail')
  },
  {
    path: '/newChannel/adverTotalByChannel',
    name: 'adverTotalByChannel',
    meta: {
      title: '广告统计(渠道)',
      parentTitle: '广告管理',
      activeMenu: '/newChannel/adverTotalByChannel'
    },
    component: () => import('@/views/newChannel/page/adverTotalByChannel')
  },
  {
    path: '/newChannel/adverTotalByChannelDetail',
    name: 'adverTotalByChannelDetail',
    meta: {
      title: '广告统计(渠道)详情',
      parentTitle: '广告统计(渠道)',
      activeMenu: '/newChannel/adverTotalByChannel'
    },
    component: () => import('@/views/newChannel/page/adverTotalByChannelDetail')
  },
  {
    path: '/newChannel/adverTotalByP1',
    name: 'adverTotalByP1',
    meta: {
      title: '大盘数据',
      parentTitle: '广告管理',
      activeMenu: '/newChannel/adverTotalByP1'
    },
    component: () => import('@/views/newChannel/page/adverTotalByP1')
  },
  {
    path: '/newChannel/adverTotalByP1Channel',
    name: 'adverTotalByP1Channel',
    meta: {
      title: '按渠道统计',
      parentTitle: '大盘数据',
      activeMenu: '/newChannel/adverTotalByP1'
    },
    component: () => import('@/views/newChannel/page/adverTotalByP1Channel')
  },
  {
    path: '/newChannel/adverTotalByP1Adver',
    name: 'adverTotalByP1Adver',
    meta: {
      title: '按广告统计',
      parentTitle: '大盘数据',
      activeMenu: '/newChannel/adverTotalByP1'
    },
    component: () => import('@/views/newChannel/page/adverTotalByP1Adver')
  },
  {
    path: '/newChannel/adverTotalByP1AdverDetail',
    name: 'adverTotalByP1AdverDetail',
    meta: {
      title: '按广告统计详情',
      parentTitle: '大盘数据',
      activeMenu: '/newChannel/adverTotalByP1'
    },
    component: () => import('@/views/newChannel/page/adverTotalByP1AdverDetail')
  },
  {
    path: '/newChannel/adverTotalByP1ChannelDetail',
    name: 'adverTotalByP1ChannelDetail',
    meta: {
      title: '按渠道统计详情',
      parentTitle: '大盘数据',
      activeMenu: '/newChannel/adverTotalByP1'
    },
    component: () => import('@/views/newChannel/page/adverTotalByP1ChannelDetail')
  },
  {
    path: '/newChannel/apiManage',
    name: 'apiManage',
    meta: {
      title: 'API管理'
    },
    component: () => import('@/views/newChannel/page/apiManage')
  },
  {
    path: '/newChannel/productType',
    name: 'productType',
    meta: {
      title: '投放商品类型'
    },
    component: () => import('@/views/newChannel/page/productType')
  },
  {
    path: '/newChannel/newChannelAppEarnings',
    name: 'newChannelAppEarnings',
    meta: {
      title: '渠道站内数据'
    },
    component: () => import('@/views/newChannel/page/newChannelAppEarnings')
  },
  {
    path: '/newChannel/equipmentInit',
    name: 'equipmentInit',
    meta: {
      title: '设备初始化'
      // parentTitle:'回传联调'
    },
    component: () => import('@/views/newChannel/page/equipmentInit')
  },
  {
    path: '/newChannel/comesInit',
    name: 'comesInit',
    meta: {
      title: '回传初始化'
    },
    component: () => import('@/views/newChannel/page/comesInit')
  },
  {
    path: '/newChannel/reviewDeviceInit',
    name: 'reviewDeviceInit',
    meta: {
      title: '审核设备初始化'
    },
    component: () => import('@/views/newChannel/page/reviewDeviceInit')
  },
  {
    // 关联B面页面
    path: '/newChannel/newChannelAssociateB',
    name: 'newChannelAssociateB',
    meta: {
      title: '关联渠道'
    },
    component: () => import('@/views/newChannel/page/newChannelAssociateB')
  },
  {
    // 关联B面编辑页面
    path: '/newChannel/associateBDetail',
    name: 'associateBDetail',
    meta: {
      title: '关联渠道编辑',
      parentTitle: '关联渠道',
      activeMenu: '/newChannel/newChannelAssociateB'
    },
    component: () => import('@/views/newChannel/module/associateBDetail')
  },
  {
    // 落地页列表B面
    path: '/newChannel/newChannelPageB',
    name: 'newChannelPageB',
    meta: {
      title: '落地页列表关联B'
    },
    component: () => import('@/views/newChannel/page/newChannelPageB')
  },
  {
    path: '/newChannel/payPageSettingsListB',
    name: 'payPageSettingsListB',
    meta: {
      title: 'P3支付页列表关联B'
    },
    component: () => import('@/views/newChannel/page/payPageSettingsListB')
  },
  {
    path: '/newChannel/adChannelDock',
    name: 'newChannelAdChannelDock',
    meta: {
      title: '渠道广告(topon)'
    },
    component: () => import('@/views/newChannel/page/adChannelDock')
  },
  {
    path: '/newChannel/editAdChannelDock',
    name: 'newChannelEditAdChannelDock',
    meta: {
      title: '编辑渠道广告(topon)',
      parentTitle: '渠道广告(topon)',
      activeMenu: '/newChannel/adChannelDock'
    },
    component: () => import('@/views/newChannel/module/editAdChannelDock')
  },
  {
    path: '/newChannel/channelLaunchStatistics',
    name: 'channelLaunchStatistics',
    meta: {
      title: '渠道投放统计'
    },
    component: () => import('@/views/newChannel/page/channelLaunchStatistics')
  },
  {
    path: '/newChannel/channelHistoryData',
    name: 'channelHistoryData',
    meta: {
      title: '历史数据',
      parentTitle: '渠道投放统计',
      activeMenu: '/newChannel/channelLaunchStatistics'
    },
    component: () => import('@/views/newChannel/page/channelHistoryData')
  },
  {
    path: '/newChannel/channelLaunchBackDetail',
    name: 'channelLaunchBackDetail',
    meta: {
      title: '回传明细',
      parentTitle: '渠道投放统计',
      activeMenu: '/newChannel/channelLaunchStatistics'
    },
    component: () => import('@/views/newChannel/page/channelLaunchBackDetail')
  },
  {
    path: '/newChannel/h5intercept/interceptManage',
    name: 'interceptManage',
    meta: {
      title: 'h5拦截管理'
    },
    component: () => import('@/views/newChannel/page/h5intercept/interceptManage')
  },
  {
    path: '/newChannel/h5intercept/interceptManageEdit',
    name: 'interceptManageEdit',
    meta: {
      title: 'h5拦截关联',
      parentTitle: 'h5拦截管理',
      activeMenu: '/newChannel/h5intercept/interceptManage'
    },
    component: () => import('@/views/newChannel/page/h5intercept/interceptManageEdit')
  },
  {
    path: '/newChannel/h5intercept/interceptMngDetail',
    name: 'interceptMngDetail',
    meta: {
      title: '拦截配置',
      activeMenu: '/newChannel/h5intercept/interceptManage'
    },
    component: () => import('@/views/newChannel/page/h5intercept/detail')
  },
  {
    path: '/newChannel/h5intercept/advertising',
    name: 'advertising',
    meta: {
      title: '广告统计（广告）'
    },
    component: () => import('@/views/newChannel/page/h5intercept/advertising')
  },
  {
    path: '/newChannel/h5intercept/adverTotalByAdverDetail',
    name: 'h5AdverTotalByAdverDetail',
    meta: {
      title: '广告详情统计详情（广告）',
      parentTitle: '广告统计（广告）',
      activeMenu: '/newChannel/h5intercept/advertising'
    },
    component: () => import('@/views/newChannel/page/h5intercept/adverTotalByAdverDetail')
  },
  {
    path: '/newChannel/h5intercept/channel',
    name: 'channel',
    meta: {
      title: '广告统计（渠道）'
    },
    component: () => import('@/views/newChannel/page/h5intercept/channel')
  },
  {
    path: '/newChannel/h5intercept/channelDetail',
    name: 'channelDetail',
    meta: {
      title: '广告统计（渠道）详情',
      parentTitle: '广告统计（渠道）',
      activeMenu: '/newChannel/h5intercept/channel'
    },
    component: () => import('@/views/newChannel/page/h5intercept/channelDetail')
  },
  {
    path: '/newChannel/h5intercept/adverTotalByChannelDetail',
    name: 'h5AdverTotalByChannelDetail',
    meta: {
      title: '广告详情统计（渠道）',
      parentTitle: '广告统计（渠道）',
      activeMenu: '/newChannel/h5intercept/channel'
    },
    component: () => import('@/views/newChannel/page/h5intercept/adverTotalByChannelDetail')
  },
  {
    path: '/newChannel/h5intercept/grailData',
    name: 'grailData',
    meta: {
      title: '大盘数据'
    },
    component: () => import('@/views/newChannel/page/h5intercept/grailData')
  },
  {
    path: '/newChannel/newChannelDataEarningsNew',
    name: 'newChannelDataEarningsNew',
    meta: {
      title: '渠道收益统计（新）'
    },
    component: () => import('@/views/newChannel/page/newChannelDataEarningsNew')
  },
  {
    path: '/newChannel/newChannelDataEarningsNewCharts',
    name: 'newChannelDataEarningsNewCharts',
    meta: {
      title: '投放数据时段趋势表（投放）',
      parentTitle: '渠道收益统计（新）',
      activeMenu: '/newChannel/newChannelDataEarningsNew'
    },
    component: () => import('@/views/newChannel/page/newChannelDataEarningsNewCharts')
  },
  {
    path: '/newChannel/newChannelDataEarningsAll',
    name: 'newChannelDataEarningsAll',
    meta: {
      title: '渠道收益统计（总）'
    },
    component: () => import('@/views/newChannel/page/newChannelDataEarningsAll')
  },
  {
    path: '/newChannel/newChannelDataEarningsAllCharts',
    name: 'newChannelDataEarningsAllCharts',
    meta: {
      title: '投放数据时段趋势表（投放）',
      parentTitle: '渠道收益统计（总）',
      activeMenu: '/newChannel/newChannelDataEarningsAll'
    },
    component: () => import('@/views/newChannel/page/newChannelDataEarningsNewCharts')
  },
  {
    path: '/newChannel/payLink/h5CommodityPurchaseLink',
    name: 'h5CommodityPurchaseLink',
    meta: {
      title: 'h5商品购买链路'
    },
    component: () => import('@/views/newChannel/page/payLink/h5CommodityPurchaseLink')
  },
  {
    path: '/newChannel/payLink/channelStatistics',
    name: 'payLinkChannelStatistics',
    meta: {
      title: '渠道统计'
    },
    component: () => import('@/views/newChannel/page/payLink/channelStatistics')
  },
  {
    path: '/newChannel/channelStatisticsDetail',
    name: 'channelStatisticsDetail',
    meta: {
      title: '渠道统计每日详情',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/payLink/channelStatistics'
    },
    component: () => import('@/views/newChannel/module/payLink/channelStatisticalDetail')
  },
  {
    path: '/newChannel/channelTimeDetail',
    name: 'channelStatisticsDetail',
    meta: {
      title: '渠道统计时段详情',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/payLink/channelStatistics'
    },
    component: () => import('@/views/newChannel/module/payLink/channelTimeDetail')
  },
  {
    path: '/newChannel/channelAdTimeDetail',
    name: 'channelStatisticsDetail',
    meta: {
      title: '广告位下沉时段详情',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/payLink/channelStatistics'
    },
    component: () => import('@/views/newChannel/module/payLink/channelTimeDetail')
  },
  {
    path: '/newChannel/channelMediaTimeDetail',
    name: 'channelStatisticsDetail',
    meta: {
      title: '媒体下沉时段详情',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/payLink/channelStatistics'
    },
    component: () => import('@/views/newChannel/module/payLink/channelTimeDetail')
  },
  {
    path: '/newChannel/payLinkChannelSinking',
    name: 'payLinkChannelSinking',
    meta: {
      title: '媒体下沉',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/payLink/channelStatistics'
    },
    component: () => import('@/views/newChannel/page/payLink/channelSinking')
  },
  {
    path: '/newChannel/payLinkChannelSinkingDetail',
    name: 'payLinkChannelSinkingDetail',
    meta: {
      title: '媒体下沉每日详情',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/payLink/channelStatistics'
    },
    component: () => import('@/views/newChannel/module/payLink/channelSinkingDetail')
  },
  {
    path: '/newChannel/payLinkMediaSinking',
    name: 'payLinkMediaSinking',
    meta: {
      title: '广告位下沉',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/payLink/channelStatistics'
    },
    component: () => import('@/views/newChannel/page/payLink/mediaSinking')
  },
  {
    path: '/newChannel/payLinkMediaSinkingDetail',
    name: 'payLinkMediaSinkingDetail',
    meta: {
      title: '广告位下沉每日详情',
      parentTitle: '渠道统计',
      activeMenu: '/newChannel/payLink/channelStatistics'
    },
    component: () => import('@/views/newChannel/module/payLink/mediaSinkingDetail')
  },
  {
    path: '/newChannel/channelComplaints',
    name: 'channelComplaints',
    meta: {
      title: '投诉监控'
    },
    component: () => import('@/views/newChannel/page/channelComplaints')
  },
  {
    path: '/newChannel/cycleDeductions/statistics',
    name: 'channelCycleDeductionsStatistics',
    meta: {
      title: '落地页周期扣款统计'
    },
    component: () => import('@/views/newChannel/page/cycleDeductionsStatistics')
  },
  {
    path: '/newChannel/cycleDeductions/orders',
    name: 'channelCycleDeductionsOrders',
    meta: {
      title: '落地页周期扣款订单'
    },
    component: () => import('@/views/newChannel/page/cycleDeductionsOrders')
  },
  {
    path: '/newChannel/dataMonitoringComprehensive',
    name: 'dataMonitoringComprehensive',
    meta: {
      title: '数据监控综合表'
    },
    component: () => import('@/views/newChannel/page/dataMonitoringComprehensive')
  },
  {
    path: '/newChannel/dataMonitoringChannel',
    name: 'dataMonitoringChannel',
    meta: {
      title: '数据监控渠道表'
    },
    component: () => import('@/views/newChannel/page/dataMonitoringChannel')
  },
  {
    path: '/newChannel/afterSalesDataComprehensive',
    name: 'afterSalesDataComprehensive',
    meta: {
      title: '售后数据综合表'
    },
    component: () => import('@/views/newChannel/page/afterSalesData/afterSalesDataComprehensive')
  },
  {
    path: '/newChannel/afterSalesDataChannel',
    name: 'afterSalesDataChannel',
    meta: {
      title: '售后数据渠道表'
    },
    component: () => import('@/views/newChannel/page/afterSalesData/afterSalesDataChannel')
  },
  {
    path: '/newChannel/dataScreeningDetail',
    name: 'dataScreeningDetail',
    meta: {
      title: '客诉综合监控大盘详情',
      parentTitle: '客诉综合监控大盘',
      activeMenu: '/newChannel/dataScreening'
    },
    component: () => import('@/views/newChannel/page/dataScreeningDetail')
  },
  {
    path: '/newChannel/dataMonitoringChannelDetail/:page',
    meta: {
      title: '下沉详情',
      parentTitle: '数据监控渠道表',
      activeMenu: '/newChannel/dataMonitoringChannel'
    },
    component: () => import('@/views/newChannel/page/dataMonitoringChannelDetail')
  },
  {
    path: '/newChannel/smsMonitoring',
    name: 'smsMonitoring',
    meta: {
      title: '短信联调'
    },
    component: () => import('@/views/newChannel/page/sms')
  },
  {
    path: '/newChannel/ladderTable',
    name: 'ladderTable',
    meta: {
      title: '客诉数据阶梯表',
      parentTitle: '数据监控渠道表',
      activeMenu: '/newChannel/dataMonitoringChannel'
    },
    component: () => import('@/views/newChannel/page/customerComplaint/ladderTable')
  },
  {
    path: '/newChannel/ladderTableComprehensive',
    name: 'ladderTableComprehensive',
    meta: {
      title: '客诉数据阶梯表',
      parentTitle: '数据监控综合表',
      activeMenu: '/newChannel/dataMonitoringComprehensive'
    },
    component: () => import('@/views/newChannel/page/customerComplaint/ladderTable')
  },
  {
    path: '/newChannel/lineChart',
    name: 'lineChart',
    meta: {
      title: '客诉趋势折线图',
      parentTitle: '数据监控渠道表',
      activeMenu: '/newChannel/dataMonitoringChannel'
    },
    component: () => import('@/views/newChannel/page/customerComplaint/lineChart')
  },
  {
    path: '/newChannel/lineChartDouble',
    name: 'lineChartDouble',
    meta: {
      title: '数据趋势监控图表（客投）',
      parentTitle: '数据监控渠道表',
      activeMenu: '/newChannel/dataMonitoringChannel'
    },
    component: () => import('@/views/newChannel/page/customerComplaint/lineChartDouble')
  },
  {
    path: '/newChannel/dataScreening',
    name: 'dataScreening',
    meta: {
      title: '客诉综合监控大盘'
    },
    component: () => import('@/views/newChannel/page/customerComplaint/dataScreening')
  },
  {
    path: '/newChannel/interactiveStatistics',
    name: 'interactiveStatistics',
    meta: {
      title: '站外购卡-互动统计',
      activeMenu: '/newChannel'
    },
    component: () => import('@/views/newChannel/page/interactiveStatistics')
  },
  {
    path: '/newChannel/refundPolicy',
    name: 'refundPolicy',
    meta: {
      title: '退款策略管理',
      // activeMenu: '/newChannel'
    },
    component: () => import('@/views/newChannel/page/refundPolicy')
  },
  {
    path: '/newChannel/refundPolicyDetail',
    name: 'refundPolicyDetail',
    meta: {
      title: '退款策略详情',
      activeMenu: '/newChannel/refundPolicy'
    },
    component: () => import('@/views/newChannel/page/refundPolicy/detail')
  },
  {
    path: '/newChannel/payStrategy',
    name: 'payStrategy',
    meta: {
      title: '支付策略'
    },
    component: () => import('@/views/newChannel/page/payStrategy')
  },
  {
    path: '/newChannel/newChannelListAreaMasking',
    name: 'newChannelListAreaMasking',
    meta: {
      title: '渠道地区屏蔽'
    },
    component: () => import('@/views/newChannel/page/newChannelListAreaMasking')
  },
  {
    path: '/newChannel/CVRStatistics',
    name: 'CVRStatistics',
    meta: {
      title: '渠道CVR综合看板'
    },
    component: () => import('@/views/newChannel/page/CVRStatistics/index')
  },
  {
    path: '/newChannel/h5CardPurchaseManagement/redEnvelopeWithdrawal',
    name: 'redEnvelopeWithdrawal',
    meta: {
      title: '红包提现'
    },
    component: () => import('@/views/newChannel/page/h5CardPurchaseManagement/redEnvelopeWithdrawal')
  },
  {
    path: '/newChannel/h5CardPurchaseManagement/luckyDraw',
    name: 'luckyDraw',
    meta: {
      title: '九宫格抽奖'
    },
    component: () => import('@/views/newChannel/page/h5CardPurchaseManagement/luckyDraw')
  },
  {
    path: '/newChannel/h5CardPurchaseManagement/withdrawalRecord',
    name: 'withdrawalRecord',
    meta: {
      title: '提现记录'
    },
    component: () => import('@/views/newChannel/page/h5CardPurchaseManagement/withdrawalRecord')
  },
  {
    path: '/newChannel/h5CardPurchaseManagement/withdrawalSet',
    name: 'withdrawalSet',
    meta: {
      title: '提现设置'
    },
    component: () => import('@/views/newChannel/page/h5CardPurchaseManagement/withdrawalSet')
  },
  {
    path: '/newChannel/h5CardPurchaseManagement/userRecord',
    name: 'userRecord',
    meta: {
      title: '领取记录'
    },
    component: () => import('@/views/newChannel/page/h5CardPurchaseManagement/userRecord')
  },
  {
    path: '/newChannel/payLink/authorizationRecord',
    name: 'authorizationRecord',
    meta: {
      title: '授权记录'
    },
    component: () => import('@/views/newChannel/page/payLink/authorizationRecord')
  },
  {
    path: '/newChannel/payLink/periodicDebitOrder',
    name: 'periodicDebitOrder',
    meta: {
      title: '周期扣款订单'
    },
    component: () => import('@/views/newChannel/page/payLink/periodicDebitOrder')
  },
  {
    path: '/newChannel/periodicCebitLink/retainedData',
    name: 'retainedData',
    meta: {
      title: '留存数据'
    },
    component: () => import('@/views/newChannel/page/periodicCebitLink/retainedData')
  },
  {
    path: '/newChannel/periodicCebitLink/retainedDataChannel',
    name: 'retainedDataChannel',
    meta: {
      title: '留存数据详情',
      activeMenu: '/newChannel/periodicCebitLink/retainedData'
    },
    component: () => import('@/views/newChannel/page/periodicCebitLink/retainedDataChannel')
  },
  {
    path: '/newChannel/periodicCebitLink/retainedDataChannelDay',
    name: 'retainedDataChannelDay',
    meta: {
      title: '留存数据详情',
      activeMenu: '/newChannel/periodicCebitLink/retainedData'
    },
    component: () => import('@/views/newChannel/page/periodicCebitLink/retainedDataChannelDay')
  },
  // {
  //   path: '/newChannel/newChannelP4',
  //   name: 'newChannelP4',
  //   meta: {
  //     title: 'P4页面管理'
  //   },
  //   component: () => import('@/views/newChannel/page/newChannelP4')
  // }
  {
    path: '/newChannel/mediaManagement',
    name: 'mediaManagement',
    meta: {
      title: '全流程媒体管理',
    },
    component: () => import('@/views/newChannel/page/mediaList')
  },
]
export default newChannel
