<template>
  <div>
    <el-row type="flex" class="row-bg" justify="end">
      <el-button
        type="primary"
        plain
        icon="el-icon-circle-plus-outline"
        @click="linkDetails('add')"
      >添加直冲商品</el-button>
      <el-button type="success" plain icon="el-icon-refresh" @click="refresh()">刷新</el-button>
    </el-row>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item,index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.status"
      >
        <div slot="label">
          {{ item.label }}(
          <span style="color:#FF7F50">{{ item.count }}</span>
          )
        </div>
        <ycFlingList ref="ycFlingList" :type-list="typeList" :status="item.status" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  rechargeStatistic,
  selectGoodsTypeName
} from '@/api/goods'
import ycFlingList from '../module/yc-fling-list.vue'
export default {
  components: {
    ycFlingList
  },
  data() {
    return {
      tabs: [],
      activeName: '0',
      typeList: []
    }
  },
  mounted() {
    this.initStatisticData()
    this.getGoodsType()
  },
  methods: {
    initStatisticData() {
      rechargeStatistic().then(res => {
        if (res.code === 0) {
          const arr = [
            { label: '全部商品', status: '0' },
            { label: '已上架', status: '1' },
            { label: '未上架', status: '2' }
          ]
          arr[0].count = res.data.total
          arr[1].count = res.data.up
          arr[2].count = res.data.down
          this.tabs = arr
          this.$nextTick(() => {
            this.$refs.ycFlingList[this.activeName].getData()
          })
        }
      })
    },
    getGoodsType() {
      selectGoodsTypeName().then(res => {
        if (res.code === 0) {
          this.typeList = res.data
        }
      })
    },
    handleClick(name) {
      this.$refs.ycFlingList[this.activeName].getData()
    },
    linkDetails(type) {
      this.$router.push({
        path: '/commodity/fling_detail',
        query: {
          type: type
        }
      })
    },

    refresh() {}
  }
}
</script>

<style scoped>
</style>
