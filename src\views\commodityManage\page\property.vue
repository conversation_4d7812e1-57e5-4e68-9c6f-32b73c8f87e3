<template>
  <!-- 商品属性 -->
  <div>
    <el-row type="flex" class="row-bg" justify="end" style="margin: 0 0 20px;">
      <el-button type="primary" plain icon="el-icon-circle-plus-outline" @click="linkDetails('add')">添加属性</el-button>
      <el-button type="success" plain icon="el-icon-refresh" @click="refresh()">刷新</el-button>
    </el-row>
    <div class="tab-head">
      <span class="title">数据列表</span>
    </div>
    <el-table
      v-loading="DataLoading"
      :data="tableData"
      border
      style="width: 100%;margin-bottom: 30px;"
    >
      <el-table-column label="序列" type="index" />
      <el-table-column prop="username" label="属性名称" />
      <el-table-column prop="created_at" label="商品类型" />
      <el-table-column prop="created_at" label="排序" />
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button type="warning" size="mini" plain @click="linkDetails('edit')">编辑</el-button>
          <el-button type="danger" size="mini" plain>删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 70, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      parameterObj: {
        phone: ''
      },
      DataLoading: false,
      tableData: [1],
      total: 0,
      pageSize: 10,
      currentPage: 1
    }
  },
  mounted() {
  },
  methods: {
    getData() {
      // this.DataLoading = true;
      // get_accountData({
      //   username:this.parameterObj.username,
      //   role_id	:this.parameterObj.role_id,
      //   page: this.currentPage,
      //   per_page: this.pageSize
      // }).then(res =>{
      //   this.loadingFlag = false;
      //   this.DataLoading = false;
      //   if(res.code == 200){
      //     this.tableData = res.data;
      //     this.total = res.meta.total;
      //   }else{
      //     this.$message.error(res.message);
      //   }
      // })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },
    linkDetails(type) {
      this.$router.push({
        path: '/commodity/property_detail',
        query: {
          type: type
        }
      })
    },
    refresh() {
      this.getData()
    }
  }
}
</script>

<style scoped>

</style>
