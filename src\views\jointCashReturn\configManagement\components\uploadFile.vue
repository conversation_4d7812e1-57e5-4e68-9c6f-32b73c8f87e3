<template>
  <div class="uploadFileMain">
    <div v-for="(item,index) in fileList" :key="index" class="upload-list">
      <div v-if="switchIndex==index && showProgress" v-loading="true" class="loadingView" element-loading-background="rgba(0, 0, 0, 0.8)" />
      <viewer :images="[item.url]" style="width: 100%;height:100%">
        <img :id="`${idName}_image_${index}`" class="el-upload-listImg" :src="item.url" alt="">
      </viewer>
      <span class="el-actions">
        <span
          class="el-upload-icon"
          @click="handlePictureCardPreview(index)"
        >
          <i class="el-icon-zoom-in" />
        </span>
        <span
          class="el-upload-icon"
          @click="switchFn(index)"
        >
          <i class="el-icon-sort" style="transform: rotate(90deg);" />
        </span>
        <span
          class="el-upload-icon"
          @click="delRemove(index)"
        >
          <i class="el-icon-delete" />
        </span>
      </span>
    </div>
    <el-upload
      v-show="!limit || fileList.length<limit"
      :ref="`${idName}_upload`"
      :show-file-list="false"
      :multiple="multiple"
      :limit="limit?limit+1:limit"
      :action="$CONSTANT.publicPath + '/upload/image'"
      list-type="picture-card"
      :headers="{Authorization: $utils.getToken()}"
      accept="image/png, image/jpg, image/jpeg, image/gif"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-progress="progressFn"
      :on-success="uploadSuccess"
    >
      <div v-if="switchIndex==-1 && showProgress" v-loading="true" class="loadingView" element-loading-background="rgba(0, 0, 0, 0.8)" />
      <div v-else :id="`${idName}_uploadClick`" slot="trigger" @click="changeIndex(-1)">
        <i class="el-icon-plus" />
      </div>
    </el-upload>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: [Array, String],
      default: ''
    },
    multiple: {
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: null
      // default: 4
    }
  },
  data() {
    return {
      idName: `${Math.random().toString(36).slice(-8)}_${new Date().getTime()}`,
      switchIndex: -1,
      fileList: [],
      dataArray: {
        1: [],
        2: []
      },
      showProgress: false
    }
  },
  watch: {
    value: {
      handler(value) {
        if (Array.isArray(value)) {
          this.fileList = value.filter(n => n != '').map(n => {
            return {
              url: n
            }
          })
        } else {
          this.fileList = value.split(',').filter(n => n != '').map(n => {
            return {
              url: n
            }
          })
        }
      },
      deep: true,
      immediate: true
    },
    fileList: {
      handler(value) {
        if (value.length > 0) {
          this.toFileImg(value)
        }
      },
      deep: true,
      immediate: true
    },
    dataArray: {
      handler(value) {
        if (value[1].length != 0 && value[2].length != 0 && value[1].length == value[2].length) {
          value[2].forEach(e => {
            if (this.switchIndex != -1) {
              this.fileList.splice(this.switchIndex, 1, {
                status: 'success',
                url: e.data
              })
            } else {
              this.fileList.push({
                status: 'success',
                url: e.data
              })
            }
          })
          setTimeout(() => {
            this.showProgress = false
            this.switchIndex = -1
          }, 300)
          this.dataArray = {
            1: [],
            2: []
          }
        }
      },
      deep: true
    }
  },
  methods: {
    beforeUpload(e) {
      this.dataArray[1].push({
        status: 'uploading',
        ...e
      })
      return true
    },
    changeIndex(index) {
      if (index == -1) {
        this.switchIndex = -1
      }
    },
    progressFn(e) {
      this.showProgress = true
    },
    switchFn(index) {
      document.getElementById(`${this.idName}_uploadClick`).click(this.switchIndex)
      setTimeout(() => {
        this.switchIndex = index
      }, 0)
    },
    handlePictureCardPreview(index) {
      // console.log(index, document.getElementById(`${this.idName}_image_${index}`))
      document.getElementById(`${this.idName}_image_${index}`).click()
    },
    uploadSuccess(e) {
      this.dataArray[2].push({
        ...e
      })
    },
    toFileImg(value) {
      if (Array.isArray(this.value)) {
        this.$emit('input', value.map(n => n.url))
      } else {
        this.$emit('input', value.map(n => n.url).join(','))
      }
    },
    delRemove(index) {
      this.fileList.splice(index, 1)
      if (this.fileList.length == 0) {
        this.toFileImg(this.fileList)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-upload--picture-card{
  width: 80px;
  line-height:80px;
  height: 80px;
}
::v-deep .el-upload-list--picture-card .el-upload-list__item{
  width: 80px;
  line-height:80px;
  height: 80px;
  transition: none !important;
}
::v-deep .el-upload-list__item .el-icon-check{
  position: absolute;
  margin-top: 0px;
  top: 10px;
  right: 14px;
}
::v-deep .el-upload-list,::v-deep .el-upload-list--picture-card{
  //display: none;
}
.uploadFileMain{
  display: flex;
  flex-wrap: wrap;
  .upload-list{
    width: 80px;
    height: 80px;
    margin-right: 20px;
    margin-bottom: 10px;
    overflow: hidden;
    border-radius: 8px;
    position: relative;
    .el-actions{
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background-color: rgba(0,0,0,0.5);
      align-items: center;
      justify-content: center;
      display: none;
      .el-upload-icon{
        margin: 5px;
        i{
          color: #ffffff;
          cursor: pointer;
        }
      }
    }
    &:hover{
      .el-actions{
        display: flex;
      }
    }
  }
  .el-upload-listImg{
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.loadingView{
  width: 100%;
  height: 100%;
}
</style>
