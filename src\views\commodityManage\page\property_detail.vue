<template>
  <div>
    <ycTitle style="margin: 20px 0;" />
    <div style="margin:50px auto;">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="300px" class="demo-ruleForm">
        <el-form-item label="属性名称：" prop="title">
          <el-input v-model="ruleForm.title" style="width:300px;" clearable placeholder="请输入属性名称" autocomplete="off" />
        </el-form-item>
        <el-form-item label="商品类型：" prop="title">
          <el-select v-model="ruleForm.title" style="width:300px;" class="search-maxInput" clearable placeholder="请选择类型">
            <el-option label="视频" value="1000001" />
          </el-select>
        </el-form-item>
        <el-form-item label="属性可选值列表：" prop="title">
          <el-input
            v-model="ruleForm.title"
            type="textarea"
            :rows="6"
            style="width:500px;"
            placeholder="换行切换输入下一个属性值"
          />
        </el-form-item>
        <el-form-item label="排序：" prop="order">
          <el-input v-model="ruleForm.order" autocomplete="off" style="width:300px;" placeholder="排序越小排到越前面" />
        </el-form-item>

        <el-form-item style="margin-top: 40px">
          <el-button plain icon="el-icon-arrow-left" @click="onCallback()">返回</el-button>
          <el-button style="width:100px;" type="primary" :disabled="btn_disabled" @click="handUpdated('ruleForm')">提交</el-button>

        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import ycTitle from '@/components/yc-title/title'
export default {
  components: {
    ycTitle
  },
  data() {
    return {
      dialogFormVisible: false,
      btn_disabled: false,
      ruleForm: {
        title: '',
        order: '',
        is_enable: '1'
      },
      rules: {
        title: [
          { required: true, message: '内容不能为空', trigger: 'blur' }
        ],
        order: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    to.meta.title = to.query.type === 'add' ? '添加属性' : '编辑属性'
    next()
  },
  methods: {
    handUpdated() {

    },
    onCallback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>

</style>
