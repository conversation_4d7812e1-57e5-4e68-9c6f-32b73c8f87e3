<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 14:33:57
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-06-03 17:06:46
-->
<template>

  <div>

    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="风格管理" name="first"> <styleList v-if="activeName=='first'" ref="firstList" @classifyManage="classifyManage" /></el-tab-pane>
      <el-tab-pane label="开场白设置" name="second"><openingList v-if="activeName=='second'" ref="secondList" @classifyManage="classifyManage" /></el-tab-pane>
    </el-tabs>
    <ClassifyList :type-list="typeList" :visible.sync="dialogVisible" @updateClassify="updateClassifyFn" />
  </div>
</template>

<script>
export default {
  name: 'qjjpStyleManage',
  components: {
    ClassifyList: () => import('./components/ClassifyList'),
    styleList: () => import('./styleList.vue'),
    openingList: () => import('./openingList.vue')
  },
  props: {},
  data() {
    return {
      activeName: 'first',
      dialogVisible: false,
      typeList: ''
    }
  },
  computed: {

  },
  activated() {
    this.$refs[`${this.activeName}List`].initFN()
  },
  async mounted() {

  },
  created() {},
  methods: {
    updateClassifyFn() {
      this.$refs[`${this.activeName}List`].getCategoryList()
    },
    classifyManage(e) {
      console.log(e)
      this.typeList = e
      this.dialogVisible = true
    },
    handleClick(e) {
      console.log(e)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__header{
  margin: 0;
}
::v-deep .filter-container{
  border-top: none;
}
</style>
