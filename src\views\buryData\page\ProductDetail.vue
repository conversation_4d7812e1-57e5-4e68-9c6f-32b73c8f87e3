<template>
  <page :request="request" :list="list" table-title="商品详情统计">
    <div slot="searchContainer" style="display: inline-block">
      <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import {
  GET_Bury_ProductDetail,
  EXPORT_Bury_ProductDetail
} from '@/api/buryData'
import moment from 'moment'

export default {
  components: {
    page
  },
  props: {},
  data() {
    const that = this
    return {
      listQuery: {
        startDate: moment()
          .subtract(7, 'days')
          .format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: data => {
          this.listQuery = { ...this.listQuery, ...data }
          return Promise.all([GET_Bury_ProductDetail(this.listQuery)]).then(
            res => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      list: [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [
            moment()
              .subtract(7, 'days')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '平台',
          key: 'platform',
          type: formItemType.select,
          list: [
            { label: '淘宝', value: '1' },
            { label: '京东', value: '2' },
            { label: '拼多多', value: '3' }
          ],
          options: {
            placeholder: '请选择广告类型'
          },
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '详情页访问PV/UV',
          key: 'detailVisit'
        },
        {
          title: '普通用户访问PV/UV',
          key: 'commonVisit'
        },
        {
          title: '爵士会员访问PV/UV',
          key: 'jazzVisit'
        },
        {
          title: '普通价格按钮PV/UV',
          key: 'commonPrice'
        },
        {
          title: '爵士价格按钮PV/UV',
          key: 'jazzPrice'
        },
        {
          title: '领券购买PV/UV',
          key: 'collectCouponsShop'
        },
        {
          title: '普通用户购买完成数',
          key: 'commonShop'
        },
        {
          title: '爵士会员购买完成数',
          key: 'jazzShop'
        },
        {
          title: '购买完成总用户数',
          key: 'payOver'
        }
      ]
    }
  },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_Bury_ProductDetail({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
