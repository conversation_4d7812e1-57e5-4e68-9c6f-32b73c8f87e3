const statistics = [
  // 贷款链路监控
  {
    path: '/statistics/loanLinkMonitoring',
    name: 'loanLinkMonitoring',
    meta: {
      title: '贷款链路监控'
    },
    component: () => import('@/views/statistics/page/loanLinkMonitoring/index.vue')
  },
  {
    path: '/statistics/loanLinkMonitoring/channel',
    name: 'loanLinkMonitoringChannel',
    meta: {
      title: '贷款链路监控-渠道',
      activeMenu: '/statistics/loanLinkMonitoring',
      parentTitle: '贷款链路监控'
    },
    component: () => import('@/views/statistics/page/loanLinkMonitoring/index.vue')
  },

  // 投放链路监控
  {
    path: '/statistics/releaseLinkMonitoring',
    name: 'releaseLinkMonitoring',
    meta: {
      title: '投放链路监控'
    },
    component: () => import('@/views/statistics/page/releaseLinkMonitoring/index.vue')
  },
  {
    path: '/statistics/releaseLinkMonitoring/channel',
    name: 'releaseLinkMonitoringChannel',
    meta: {
      title: '投放链路监控-渠道',
      activeMenu: '/statistics/releaseLinkMonitoring',
      parentTitle: '投放链路监控'
    },
    component: () => import('@/views/statistics/page/releaseLinkMonitoring/index.vue')
  },

  // 分发链路监控
  {
    path: '/statistics/distributionLinkMonitoring',
    name: 'distributionLinkMonitoring',
    meta: {
      title: '分发链路监控'
    },
    component: () => import('@/views/statistics/page/distributionLinkMonitoring/index.vue')
  },
  {
    path: '/statistics/distributionLinkMonitoring/channel',
    name: 'distributionLinkMonitoringChannel',
    meta: {
      title: '分发链路监控-渠道',
      activeMenu: '/statistics/distributionLinkMonitoring',
      parentTitle: '分发链路监控'
    },
    component: () => import('@/views/statistics/page/distributionLinkMonitoring/index.vue')
  },

  // 大盘数据监控
  {
    path: '/statistics/marketDataMonitoring',
    name: 'marketDataMonitoring',
    meta: {
      title: '大盘数据监控'
    },
    component: () => import('@/views/statistics/page/marketDataMonitoring/index.vue')
  },
  {
    path: '/statistics/marketDataMonitoring/channel',
    name: 'marketDataMonitoringChannel',
    meta: {
      title: '大盘数据监控-渠道',
      activeMenu: '/statistics/marketDataMonitoring',
      parentTitle: '大盘数据监控'
    },
    component: () => import('@/views/statistics/page/marketDataMonitoring/index.vue')
  },

  // 供应商调用统计
  // {
  //   path: '/statistics/supplierCallMonitoring',
  //   name: 'supplierCallMonitoring',
  //   meta: {
  //     title: '供应商调用统计'
  //   },
  //   component: () => import('@/views/statistics/page/supplierCallMonitoring/index.vue')
  // },
]

export default statistics
