import {
  put,
  get,
  post,
  del
} from '@/libs/axios.package'

/*
* 商品类型列表
*
*/
export const getList = obj => {
  return get('goods/type/list', obj, false)
}

/*
* 删除商品类型
*
*/
export const delGoodsType = id => {
  return del('goods/type/' + id, null, false)
}

/**
 * 获取级联数据
 */
export const getCascade = () => {
  return get('goods/type/cascade', null, false)
}

/**
 * 保存数据
 */
export const save = obj => {
  return post('goods/type', obj, false)
}

/**
 * 更新数据
 */
export const update = obj => {
  return put('goods/type', obj, false)
}

/**
 * 获取详情
 */
export const detail = (id) => {
  return get('goods/type/' + id, null, false)
}
