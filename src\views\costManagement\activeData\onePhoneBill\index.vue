<template>
  <div>
    <page :list="list" :request="request" table-title="登录数据" />
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import {
  phoneBillLoing
} from '@/api/costManagement'
import { count_channel_application_list } from '@/api/NewChannel'
import moment from 'moment'
export default {
  name: 'onephoneBill',
  components: {
    page
  },
  props: {},
  data() {
    return {
      siteIds: [],
      listQuery: {
        startDate: moment()
          .subtract(7, 'days')
          .format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        siteId: '',
        channelCode: ''
      },
      request: {
        getListUrl: async data => {
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                res.data.unshift({
                  siteName: '全部',
                  siteId: ''
                })
                this.siteIds = res.data
                this.listQuery.siteId = res.data[0].siteId
              }
            })
          }
          return this.getData(data)
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [
            moment()
              .subtract(7, 'days')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date'
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: this.listQuery.siteId,
          search: true,
          clearable: false,
          tableHidden: true
        },
        {
          title: '渠道ID',
          key: 'channelCode',
          type: formItemType.input,
          tableView: tableItemType.tableView.text,
          val: this.listQuery.channelCode,
          search: true,
          clearable: false,
          tableHidden: true
        },
        {
          title: '活动访问（总/新）',
          key: 'activityPageTotalAndNew',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '登录前访问')
          }
        },
        {
          title: '引导看视频弹窗曝光（总/新）',
          key: 'guideVideoPopupAndNew'
        },
        {
          title: '引导看视频弹窗点击（总/新）',
          key: 'guideVideoPopupClickAndNew'
        },
        {
          title: 'DEU（总/新）',
          key: 'deuS'
        },
        {
          title: '观看广告次数（总/新）',
          key: 'showVideoCountAndNew'
        },
        {
          title: '视频完播率',
          key: 'watchRateTotalAndNew'
        },
        {
          title: 'ecpm（总/新）',
          key: 'ecpmS'
        },
        {
          title: '广告收益（总/新）',
          key: 'incomeS'
        },
        {
          title: '登录弹窗曝光（总/新）',
          key: 'loginPopupTotalAndNew'
        },
        {
          title: '登录点击（总/新）',
          key: 'loginPopupClickTotalAndNew'
        },
        {
          title: '登录拦截弹窗曝光（总/新）',
          key: 'loginPopupExposeInterceptTotalAndNew'
        },
        {
          title: '登录拦截弹窗点击（总/新）',
          key: 'loginPopupClickInterceptTotalAndNew'
        },
        {
          title: '登录数（总/新）',
          key: 'loginCountAndNew'
        },
        {
          title: '引导弹窗转化（总/新）',
          key: 'guidePopupRate',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '引导看视频弹窗点击/引导看视频弹窗曝光')
          }
        },
        {
          title: '登录弹窗转化（总/新）',
          key: 'loginPopupRate',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '登录点击/登录弹窗曝光')
          }
        },
        {
          title: '登录率（总/新）',
          key: 'loginRate',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '登录数/访问数')
          }
        },
        {
          title: '登录拦截弹窗转化（总/新）',
          key: 'loginPopupClickInterceptRate',
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '登录拦截弹窗点击/登录拦截弹窗曝光')
          }
        }
      ]
    }
  },
  watch: {},
  methods: {
    renderHeaders(h, column, text) {
      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            props: {
              content: text
            }
          },
          [
            h('i', {
              class: 'el-icon-question',
              style: 'color:#409eff;margin-left:5px;font-size: 16px;'
            })
          ]
        )
      ])
    },
    getData(data) {
      this.listQuery = { ...this.listQuery, ...data }
      return Promise.all([
        phoneBillLoing(this.listQuery)
      ]).then(res => {
        return Promise.resolve({ data: res[0].data })
      }).catch(err => {
        console.log(err, 'errr')
      })
    },

    success() {
      this.$store.dispatch('tableRefresh', this)
    }
  }
}
</script>

<style lang="scss" scoped></style>
