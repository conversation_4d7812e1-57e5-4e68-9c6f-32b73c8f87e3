<template>
  <el-form :model="itemForm" :rules="addRules" label-width="130px" class="demo-ruleForm">
    <div :style="{ display: 'inline-block', width: '390px' }">
      <el-form-item label="卸载拦截" prop="uninstallIntercept" :rules="addRules.common">
        <el-radio-group v-model="itemForm.uninstallIntercept" @change="changeStatus">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
        <div>*开启后取默认选中的套餐</div>
      </el-form-item>
    </div>
    <template v-if="itemForm.uninstallIntercept===1">
      <div :style="{ display: 'inline-block', width: '390px' }">
        <el-form-item label="sku" prop="uninstallInterceptorSkuKey" :rules="addRules.common">
          <el-select
            v-model="itemForm.uninstallInterceptorSkuKey"
            placeholder="请选择sku"
          >
            <el-option v-for="item in memberShips" :key="item.uninstallInterceptorSkuKey" :label="pageSkuType[item.packageType||0]+item.name" :value="item.uninstallInterceptorSkuKey">{{
              pageSkuType[item.packageType||0]+item.name }}</el-option>
          </el-select>
        </el-form-item>
      </div>

      <div style="display: flex;flex-wrap: nowrap;">
        <span class="fixed-require-txt">拦截折扣</span>
        <el-form-item v-if="curSelectMembers.packageType!=2" label="" label-width="0" prop="uninstallInterceptDiscount" :rules="addRules.validRate">
          <el-input
            v-model="itemForm.uninstallInterceptDiscount"
            style="width: 210px;"
            placeholder="请输入拦截折扣"
          />
        </el-form-item>
        <span class="disable-input">{{ uninstallInterceptDiscountPrice }}<span>(元)</span></span>
      </div>
    </template>
  </el-form>
</template>
<script>
const basicParams = {
  uninstallIntercept: 1, // 卸载拦截 1-开启 0-关闭
  uninstallInterceptorSkuKey: '', // 卸载拦截sku自定义skuKey
  uninstallInterceptDiscount: '' // 折扣
}
export default {
  name: 'unloadIntecept',
  props: {
    addFormData: {
      typeof: Object,
      default: () => ({})
    }
  },
  data() {
    var validRate = (rule, value, callback) => {
      if (!value) {
        callback(new Error('必填'))
      } else if (Number(value) > 1) {
        callback(new Error('不能大于1'))
      } else if (Number(value) < 0.01) {
        callback(new Error('不能小于0.01'))
      } else if (isNaN(Number(value))) {
        callback(new Error('必须为数字类型'))
      } else {
        const { paymentPrice, packageType } = this.curSelectMembers
        if (paymentPrice && packageType != 2 && Number(value) * Number(paymentPrice) < 0.01) {
          callback(new Error('折扣后价格不能小于0.01元'))
        } else {
          callback()
        }
      }
    }
    return {
      itemForm: {
        ...basicParams,
        ...this.addFormData
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }],
        validRate: [{ validator: validRate, trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      pageSkuType: ['普通会员', '打卡会员', '续费会员']
    }
  },
  computed: {
    memberShips() {
      return this.addFormData?.membershipPackages ?? []
    },
    curSelectMembers() {
      return this.memberShips?.find(item => item.uninstallInterceptorSkuKey == this.itemForm.uninstallInterceptorSkuKey) ?? {}
    },
    uninstallInterceptDiscountPrice() {
      const { paymentPrice, packageType } = this.curSelectMembers
      const uninstallInterceptDiscount = packageType == 2 ? 1 : this.itemForm.uninstallInterceptDiscount
      return Number((Number(uninstallInterceptDiscount) * Number(paymentPrice)).toFixed(4))
    }

  },
  watch: {
    'addFormData.uninstallInterceptorSkuId': {
      handler(val) {
        if (val || val === 0) {
          this.itemForm.uninstallInterceptorSkuKey = this.memberShips.find(item => item.id == this.addFormData.uninstallInterceptorSkuId)?.uninstallInterceptorSkuKey ?? ''
        }
      },
      immediate: true
    },
    'memberShips': {
      handler() {
        this.handleSkuKey()
      }
    }
  },
  methods: {
    resetForm() {
      this.itemForm = { ...basicParams }
    },
    changeStatus() {
      if (this.itemForm.uninstallIntercept !== 1) {
        this.itemForm.uninstallInterceptorSkuKey = ''
        this.itemForm.uninstallInterceptDiscount = ''
      }
    },
    handleSkuKey() {
      if (this.itemForm.uninstallInterceptorSkuKey) {
        const index = this.memberShips.findIndex(item => item.uninstallInterceptorSkuKey === this.itemForm.uninstallInterceptorSkuKey)
        if (index === -1) {
          this.itemForm.uninstallInterceptorSkuKey = ''
        }
      } else {
        const index = this.memberShips.findIndex(item => item.defaultChoose)
        if (index >= 0) {
          this.itemForm.uninstallInterceptorSkuKey = this.memberShips[index].uninstallInterceptorSkuKey
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .disable-input{
    width: 160px;
    display: inline-block;
    padding-right: 30px;
    position: relative;
    height: 40px;
    line-height: 40px;
    background-color: #F5F7FA;
    border:1px solid #E4E7ED;
    color: #C0C4CC;
    margin-left: 10px;
    text-align: center;
    border-radius: 4px;
    >span{
      position: absolute;
      height: 100%;
      right:5px ;
    }
  }
  .fixed-require-txt{
    position: relative;
    flex-shrink: 0;
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 130px;
    font-weight: 700;
    &::after{
      content:'*';
      position: absolute;
      top: 0;
      right:70px;
      color: #ff0000;
    }
  }
</style>
