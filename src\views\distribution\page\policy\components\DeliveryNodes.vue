<template>
  <div class="delivery-nodes">
    <div class="nodes-container" :class="{ 'disabled-container': disabled }">
      <div v-for="(node, index) in sortedNodes" :key="node.id" class="node-wrapper">
        <!-- 节点 -->
        <div class="node" @mouseenter="handleMouseEnter(node)" @mouseleave="handleMouseLeave(node)">
          <!-- 删除按钮 -->
          <el-tooltip content="删除" placement="top" :disabled="!node.showDelete || disabled">
            <i 
              v-show="node.showDelete && !disabled" 
              class="el-icon-delete delete-icon"
              @click="handleDelete(node)"
            />
          </el-tooltip>
          
          <!-- 节点内容 -->
          <legend-shapes 
            :type="node.type || 'rounded-rect'"
            :width="node.type === 'diamond' ? shapeConfig.diamondSize : shapeConfig.rectWidth"
            :height="node.type === 'diamond' ? shapeConfig.diamondSize : shapeConfig.rectHeight"
            :is-selected="hasInstitutionData(node)"
          >
            {{ node.nodeName }}
          </legend-shapes>

          <!-- 添加/删除按钮 -->
          <div v-if="!disabled" class="add-button" @click="toggleNode(node)">
            <i :class="node.expanded ? 'el-icon-minus' : 'el-icon-plus'" />
          </div>

          <!-- 子节点容器 -->
          <transition name="expand">
            <div v-if="node.expanded" class="child-nodes">
              <div class="vertical-connector" :style="{
                marginTop: `${connectorConfig.buttonSpace}px`,
                height: `${connectorConfig.verticalLength}px`
              }">
                <div class="line"></div>
              </div>
              <div class="child-node diamond-node" :style="nodeStyles.diamond">
                <el-popover
                  placement="right"
                  width="200"
                  trigger="click"
                  popper-class="node-popover"
                  :disabled="disabled"
                >
                  <el-select 
                    v-model="node.childNode1Value" 
                    placeholder="请选择分发类型"
                    @change="handleDistributionChange(node, 'childNode1Value', $event)"
                  >
                    <el-option
                      v-for="item in distributionOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.value === '全量分发'"
                    />
                  </el-select>
                  <legend-shapes 
                    slot="reference"
                    type="diamond"
                    :width="shapeConfig.diamondSize"
                    :height="shapeConfig.diamondSize"
                    class="clickable"
                    :is-selected="hasInstitutionData(node)"
                  >
                    <div class="distribution-type">
                      <span class="text">{{ node.childNode1Value || '全量分发' }}</span>
                      <i class="el-icon-caret-bottom arrow" />
                    </div>
                  </legend-shapes>
                </el-popover>
              </div>
              <div class="vertical-connector" :style="{ height: `${connectorConfig.verticalLength}px` }">
                <div class="line"></div>
              </div>
              <div class="child-node rect-node" :style="nodeStyles.rect">
                <el-tooltip content="编辑" placement="top" :value="node.showEditTooltip && !disabled" manual>
                  <i v-if="!disabled" class="el-icon-edit edit-icon" :class="{ 'is-selected-edit-icon': hasInstitutionData(node) }" 
                    @mouseenter="handleEditMouseEnter(node)"
                    @mouseleave="handleEditMouseLeave(node)"
                    @click="handleEditClick(node)" />
                </el-tooltip>
                <legend-shapes 
                  type="rect"
                  :width="shapeConfig.rectWidth"
                  :height="shapeConfig.rectHeight * 1.8"
                  :is-selected="hasInstitutionData(node)"
                >
                  <div class="node2-content">
                    <template v-if="hasInstitutionData(node)">
                      <div>优先级: {{ node.institutionDisplayData[0].priority }}</div>
                      <div>机构: {{ node.institutionDisplayData[0].institution }}</div>
                      <div>信息类型: {{ node.institutionDisplayData[0].infoType }}</div>
                    </template>
                    <template v-else>
                      <div>优先级: --</div>
                      <div>机构: --</div>
                      <div>信息类型: --</div>
                    </template>
                  </div>
                </legend-shapes>
              </div>
            </div>
          </transition>
        </div>

        <!-- 水平连接线 -->
        <div v-if="index < sortedNodes.length - 1" class="connector">
          <div class="line"></div>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="删除确认"
      append-to-body
      :visible.sync="deleteDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <p>该节点已配置分发条件，删除后将不再保存，请确认是否删除</p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="warning" @click="handleDeleteWithNoPrompt">确认删除并且本次不再提示</el-button>
        <el-button type="primary" @click="handleDeleteConfirm">确认删除</el-button>
      </div>
    </el-dialog>

    <!-- 机构设置抽屉 -->
    <institution-drawer 
      ref="institutionDrawer"
      @update="handleInstitutionUpdate"
    />
  </div>
</template>

<script>
import { getDeliveryLinkConfig } from '@/api/distribution'
import LegendShapes from './shapes/LegendShapes.vue'
import InstitutionDrawer from './InstitutionDrawer.vue'

export default {
  name: 'DeliveryNodes',
  components: {
    LegendShapes,
    InstitutionDrawer
  },
  props: {
    linkType: {
      type: [Number, String],
      default: null
    },
    routeId: {
      type: [Number, String],
      default: null
    },
    shapeConfig: {
      type: Object,
      required: true
    },
    connectorConfig: {
      type: Object,
      default: () => ({
        verticalLength: 80, // 调整连接线长度
        arrowSpace: 16, // 调整箭头位置
        buttonSpace: 12 // 调整连接线距离按钮位置
      })
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      nodes: [],
      deleteDialogVisible: false,
      currentNode: null,
      skipSecondConfirm: false,
      expandedNodes: new Set(),
      processedNodes: [],
      distributionOptions: [
        { value: '全量分发', label: '全量分发' },
        { value: '拒量分发', label: '拒量分发' }
      ]
    }
  },
  computed: {
    sortedNodes() {
      return this.processedNodes
    },
    nodeStyles() {
      return {
        diamond: {
          marginTop: `${this.connectorConfig.arrowSpace}px`
        },
        rect: {
          marginTop: `${this.connectorConfig.arrowSpace - 12}px`
        }
      }
    },
    // 判断节点是否有机构数据
    hasInstitutionData() {
      return (node) => node.institutionDisplayData && node.institutionDisplayData.length > 0
    }
  },
  watch: {
    linkType: {
      immediate: true,
      handler(val, oldVal) {
        if (val && val !== oldVal) {
          this.fetchNodes()
        }
      }
    },
    // 添加对 processedNodes 的深度监听，确保在数据变更时正确更新显示
    processedNodes: {
      deep: true,
      handler() {
        this.$nextTick(() => {
          this.emitDistributionData()
        })
      }
    }
  },
  methods: {
    processNodeData() {
      const nodes = this.nodes || []
      this.processedNodes = [...nodes]
        .sort((a, b) => a.sort - b.sort)
        .map((node, index) => {
          // 确保保留已有的节点数据，包括展开状态和机构数据
          const existingNode = this.processedNodes.find(n => n.nodeCode === node.nodeCode)
          return {
            ...node,
            id: node.nodeCode,
            type: 'rounded-rect',
            showDelete: existingNode ? existingNode.showDelete : false,
            expanded: existingNode ? existingNode.expanded : this.expandedNodes.has(node.nodeCode),
            childNode1Value: existingNode ? existingNode.childNode1Value : '拒量分发',
            institutionSettings: existingNode ? existingNode.institutionSettings : { selectedItems: [] },
            institutionDisplayData: existingNode ? existingNode.institutionDisplayData : [],
            showEditTooltip: existingNode ? existingNode.showEditTooltip : false
          }
        })
    },
    async fetchNodes() {
      try {
        const res = await getDeliveryLinkConfig({ type: this.linkType })
        if (res.code === 200) {
          this.nodes = res.data
          // 不在这里清空expandedNodes，以保留编辑状态
          this.processNodeData()
          return true
        }
        return false
      } catch (error) {
        console.error('获取节点配置失败:', error)
        return false
      }
    },
    toggleNode(node) {
      const nodeId = node.id
      const targetNode = this.processedNodes.find(n => n.id === nodeId)
      console.log('targetNode', targetNode)
      if (targetNode) {
        targetNode.expanded = !targetNode.expanded
        targetNode.showDelete = targetNode.expanded || this.hasInstitutionData(targetNode)
        // 不需要在这里触发数据更新，因为节点收起不应该影响数据
      }
    },
    handleMouseEnter(node) {
      if (node.expanded) {
        const targetNode = this.processedNodes.find(n => n.id === node.id)
        if (targetNode) {
          targetNode.showDelete = true
        }
      }
    },
    handleMouseLeave(node) {
      const targetNode = this.processedNodes.find(n => n.id === node.id)
      if (targetNode) {
        targetNode.showDelete = node.expanded || this.hasInstitutionData(node)
      }
    },
    handleDelete(node) {
      if (this.skipSecondConfirm) {
        this.handleDeleteNode(node)
        return
      }
      this.currentNode = node
      this.deleteDialogVisible = true
    },
    handleCancel() {
      this.deleteDialogVisible = false
    },
    handleDeleteWithNoPrompt() {
      this.skipSecondConfirm = true
      this.handleDeleteNode(this.currentNode)
      this.deleteDialogVisible = false
    },
    handleDeleteConfirm() {
      this.handleDeleteNode(this.currentNode)
      this.deleteDialogVisible = false
    },
    // 新增处理节点删除的方法
    handleDeleteNode(node) {
      if (node) {
        const targetNode = this.processedNodes.find(n => n.id === node.id)
        if (targetNode) {
          targetNode.expanded = false
          targetNode.showDelete = false
          targetNode.childNode1Value = '拒量分发'
          targetNode.institutionSettings = { selectedItems: [] }
          targetNode.institutionDisplayData = []
          
          this.expandedNodes.delete(targetNode.id)
          
          this.emitDistributionData()
        }
      }
    },
    handleDistributionChange(node, field, value) {
      const targetNode = this.processedNodes.find(n => n.id === node.id)
      if (targetNode) {
        targetNode[field] = value
        this.emitDistributionData()
      }
    },
    getDistributionData() {
      return this.processedNodes
        .filter(node => this.hasInstitutionData(node)) // 只过滤有机构数据的节点，不管是否展开
        .map(node => ({
          nodeCode: node.id,
          nodeName: node.nodeName,
          distributionType: node.childNode1Value || '拒量分发',
          institutionDisplayData: node.institutionDisplayData || []
        }))
    },
    emitDistributionData() {
      this.$emit('distribution-change', this.getDistributionData())
    },
    // 处理编辑子节点2
    handleEditNode2(node) {
      this.$refs.institutionDrawer.open(node)
    },
    // 处理机构设置更新
    handleInstitutionUpdate(node) {
      const targetNode = this.processedNodes.find(n => n.id === node.id)
      if (targetNode) {
        targetNode.institutionSettings = node.institutionSettings
        targetNode.institutionDisplayData = node.institutionSettings.selectedItems
        this.emitDistributionData()
      }
    },
    // 初始化编辑数据
    initEditData(nodeData) {
      // 先将所有节点重置为未选中状态
      this.processedNodes.forEach(node => {
        if (!nodeData.some(editNode => editNode.nodeCode === node.nodeCode)) {
          node.expanded = false
          node.showDelete = false
          this.expandedNodes.delete(node.id)
        }
      })
      
      // 遍历编辑数据，找到对应的节点并设置数据
      nodeData.forEach(editNode => {
        const targetNode = this.processedNodes.find(node => node.nodeCode === editNode.nodeCode)
        if (targetNode) {
          console.log('Setting data for node:', targetNode.nodeCode)
          // 展开节点
          targetNode.expanded = true
          this.expandedNodes.add(targetNode.id)
          
          // 设置分发类型
          targetNode.childNode1Value = editNode.distributionType
          
          // 设置机构数据
          targetNode.institutionDisplayData = editNode.institutionDisplayData || []
          targetNode.showDelete = true
        } else {
          console.warn('Node not found:', editNode.nodeCode)
        }
      })
      
      // 强制更新视图
      this.$forceUpdate()
      
      // 触发数据更新
      this.$nextTick(() => {
        this.emitDistributionData()
      })
    },
    handleEditMouseEnter(node) {
      node.showEditTooltip = true
    },
    handleEditMouseLeave(node) {
      node.showEditTooltip = false
    },
    handleEditClick(node) {
      node.showEditTooltip = false
      this.$refs.institutionDrawer.open({
        ...node,
        linkType: this.linkType,
        routeId: this.routeId
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.delivery-nodes {
  padding: 20px 0;

  .nodes-container {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 0 20px;
    padding-top: 24px;
    
    &.disabled-container {
      cursor: not-allowed;
      
      .clickable {
        cursor: not-allowed !important;
      }
    }
  }

  .node-wrapper {
    display: flex;
    align-items: flex-start;

    .node {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;

      .delete-icon {
        position: absolute;
        top: 4px;
        right: 4px;
        font-size: 16px;
        color: #F56C6C;
        cursor: pointer;
        z-index: 1;
      }

      .add-button {
        position: absolute;
        bottom: -12px;
        left: 50%;
        transform: translateX(-50%);
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #fff;
        border: 1px solid #DCDFE6;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 2;

        &:hover {
          background-color: #F5F7FA;
        }

        i {
          font-size: 12px;
          color: #606266;
        }
      }

      .child-nodes {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 1;

        .child-node {
          position: relative;
          z-index: 2;
          
          &.diamond-node {
            position: relative;
            z-index: 2;
          }
          
          &.rect-node {
            position: relative;
            z-index: 2;
          }
        }

        .vertical-connector {
          width: 2px;
          position: relative;
          z-index: 1;
          
          .line {
            width: 100%;
            height: calc(100% - 6px);
            margin-bottom: 6px;
            background-color: #DCDFE6;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: -6px;
              left: 50%;
              transform: translateX(-50%);
              width: 0;
              height: 0;
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              border-top: 6px solid #DCDFE6;
            }
          }
        }
      }
    }

    .connector {
      width: 50px;
      display: flex;
      align-items: center;
      margin-top: 24px;
      
      .line {
        flex: 1;
        height: 1px;
        background-color: #DCDFE6;
      }
    }
  }
}

::v-deep .el-dialog__body {
  padding-top: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;

  .el-button {
    margin-left: 0;
  }
}

.clickable {
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
}

.distribution-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .text {
    white-space: nowrap;
    margin-top: 18px;
  }
  
  .arrow {
    font-size: 18px;
    color: #606266;
  }
}

// 选中状态下的箭头颜色
::v-deep .is-selected {
  .distribution-type {
    .arrow {
      color: #FFFFFF;
    }
  }
}

.is-selected-edit-icon {
  color: #FFFFFF !important;
}

::v-deep .node-popover {
  .el-select {
    width: 100%;
  }
}

.edit-icon {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 16px;
  color: #409EFF;
  cursor: pointer;
  z-index: 3;
  
  &:hover {
    color: #66b1ff;
  }
}

.node2-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 5px;
  font-size: 12px;
  text-align: left;
}

// 添加展开收起动画
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease;
  transform-origin: top center;
}

.expand-enter,
.expand-leave-to {
  opacity: 0;
  transform: scaleY(0);
}

.expand-enter-to,
.expand-leave {
  opacity: 1;
  transform: scaleY(1);
}
</style> 