/*
 * 投放管理
 * */

const deliveryManage = [
  {
    path: '/deliveryManage/account',
    name: 'accountManage',
    meta: {
      title: '账户管理'
    },
    component: () => import('@/views/deliveryManage/account')
  },
  {
    path: '/deliveryManage/accountNew',
    name: 'accountManage',
    meta: {
      title: '账户管理-新'
    },
    component: () => import('@/views/deliveryManage/accountNew')
  },
  {
    path: '/deliveryManage/accountDetail',
    name: 'accountManage',
    meta: {
      title: '账户管理详情',
      parentTitle: '账户管理-新',
      activeMenu: '/deliveryManage/accountNew'
    },
    component: () => import('@/views/deliveryManage/accountDetail')
  },
  {
    path: '/deliveryManage/channelMatch',
    name: 'channelMatch',
    meta: {
      title: '渠道匹配'
    },
    component: () => import('@/views/deliveryManage/channelMatch')
  },
  {
    path: '/deliveryManage/channelMatchEdit',
    name: 'channelMatch',
    meta: {
      title: '渠道匹配关联',
      parentTitle: '渠道匹配',
      activeMenu: '/deliveryManage/channelMatch'
    },
    component: () => import('@/views/deliveryManage/channelMatchEdit')
  },
  {
    path: '/deliveryManage/proxyManagement',
    name: 'proxyManagement',
    meta: {
      title: '代理管理'
    },
    component: () => import('@/views/deliveryManage/ProxyManagement')
  }
]

export default deliveryManage
