<template>
  <div>
    <!--    <ycTitle style="margin-top: 20px;" />-->

    <section class="list-content">
      <el-row type="flex" justify="end" style=" align-items: center;">
        <el-button type="primary" plain icon="el-icon-arrow-left" @click="onCallback()">返回</el-button>
        <el-button type="success" plain icon="el-icon-refresh" @click="refresh()">刷新</el-button>
      </el-row>

      <!--  查询条件  -->
      <el-row class="search-row" type="flex" style="margin-bottom: 30px;">
        <el-col class="search-col">
          <label class="name">日期：</label>
          <el-date-picker
            v-model="searchObj.timeArr"
            :picker-options="basics.pickerOptions()"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 260px;margin-right: 10px;"
          />
          <label class="name">设备：</label>
          <el-select
            v-model="searchObj.os"
            class="search-maxInput"
            clearable
            placeholder="渠道设备"
            style="width: 130px;"
          >
            <el-option label="android" value="android" />
            <el-option label="ios" value="ios" />
            <!--            <el-option label="其他" value="other"></el-option>-->
          </el-select>
          <el-button type="primary" class="search-btn" icon="el-icon-search" @click="getData">查询</el-button>
        </el-col>
      </el-row>

      <div class="tab-head" style="margin-top:30px;align-items: center;">
        <span class="title">统计明细</span>
        <el-button type="warning" size="small" icon="el-icon-download" @click="handExport">导出数据</el-button>
      </div>
      <el-table
        v-loading="DataLoading"
        :data="tableData"
        border
        style="width: 100%;margin-bottom: 30px;"
      >
        <el-table-column prop="channelName" label="渠道名称" />
        <el-table-column prop="dataResult" label="日期">
          <template slot-scope="scope">
            {{ scope.row.dataResult | formatDate('yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column prop="countChannelUv" label="访问PV/UV">
          <template slot-scope="scope">
            {{ scope.row.countChannelPv }} / {{ scope.row.countChannelUv }}
          </template>
        </el-table-column>
        <el-table-column prop="clickCardPv" label="点击办卡PV/UV">
          <template slot-scope="scope">
            {{ scope.row.clickCardPv }} / {{ scope.row.clickCardUv }}
          </template>
        </el-table-column>
        <el-table-column prop="joinPageRate" label="访问转化率" />
        <el-table-column prop="h5Register" label="H5留资用户数" />
        <el-table-column prop="drawPv" label="提交申请PV/UV">
          <template slot-scope="scope">
            {{ scope.row.drawPv }} / {{ scope.row.drawUv }}
          </template>
        </el-table-column>
        <el-table-column prop="confirmPayPv" label="确认支付PV/UV">
          <template slot-scope="scope">
            {{ scope.row.confirmPayPv }} / {{ scope.row.confirmPayUv }}
          </template>
        </el-table-column>
        <el-table-column prop="visitLoginRate" label="访问留资率" />
        <el-table-column prop="h5LoginRate" label="办卡-H5留资转化" />
        <el-table-column prop="secondPageRate" label="留资购卡率" />
        <el-table-column prop="successBuyCard" label="成功购卡数" />
        <el-table-column prop="wechatUserNum" label="微信支付购卡数/支付宝支付购卡数">
          <template slot-scope="scope">
            {{ scope.row.wechatUserNum }} / {{ scope.row.alipayUserCount }}
          </template>
        </el-table-column>
        <el-table-column prop="wechatTotalFee" label="微信购买金额" />
        <el-table-column prop="alipayTotalFee" label="支付宝购买金额" />
        <el-table-column prop="confirmPayRate" label="确认支付—购卡转化" />
        <el-table-column prop="totalFee" label="购卡总金额" />
        <el-table-column prop="vipCardRate" label="购卡成功率" />
        <!--        <el-table-column prop="vipAppLoginNew" label="购卡会员登录访问首页老/新">-->
        <!--          <template slot-scope="scope">{{scope.row.vipAppLoginOld}} / {{scope.row.vipAppLoginNew}}</template>-->
        <!--        </el-table-column>-->
        <el-table-column prop="activeRate" label="购卡-APP登录转化" />
        <el-table-column prop="p2H5LoginNum" label="登录数" />
        <el-table-column prop="p2InputMobilePv" label="输入手机号PV/UV">
          <template slot-scope="scope">{{ scope.row.p2InputMobilePv }} / {{ scope.row.p2InputMobileUv }}</template>
        </el-table-column>
        <el-table-column prop="p2InputVCodePv" label="输入验证码PV/UV">
          <template slot-scope="scope">{{ scope.row.p2InputVCodePv }} / {{ scope.row.p2InputVCodeUv }}</template>
        </el-table-column>
        <el-table-column prop="p2WechatCopyPv" label="复制微信公众号PV/UV">
          <template slot-scope="scope">{{ scope.row.p2WechatCopyPv }} / {{ scope.row.p2WechatCopyUv }}</template>
        </el-table-column>
        <el-table-column prop="p2LoginRate" label="访问登录率" />
        <el-table-column prop="p2PaidRate" label="P1购卡率" />
        <el-table-column prop="appTotalLogin" label="APP登录(总/新)">
          <template slot-scope="scope">{{ scope.row.appTotalLogin }} / {{ scope.row.appLoginNew }}</template>
        </el-table-column>
        <el-table-column prop="buyTotalNum" label="下单用户数(总/新)">
          <template slot-scope="scope">{{ scope.row.buyTotalNum }} / {{ scope.row.buyNewNum }}</template>
        </el-table-column>
        <el-table-column prop="loginBuyRate" label="登录购买比" />
        <el-table-column prop="orderNum" label="订单数" />
        <el-table-column prop="orderTotalPrice" label="订单总额" />
        <el-table-column prop="commissionPrice" label="平台佣金金额" />
        <el-table-column prop="userRebatePrice" label="用户返利金额" />
        <el-table-column prop="platformRevenue" label="平台收益" />
        <el-table-column prop="firstBuyRate" label="首购率" />
        <el-table-column prop="secondBuyRate" label="复购率" />
      </el-table>
      <!--      <el-pagination-->
      <!--        @size-change="handleSizeChange"-->
      <!--        @current-change="handleCurrentChange"-->
      <!--        :current-page="currentPage"-->
      <!--        :page-sizes="[10, 20, 50, 70, 100]"-->
      <!--        :page-size="pageSize"-->
      <!--        layout="total, sizes, prev, pager, next, jumper"-->
      <!--        :total="total"-->
      <!--        class="pagination"-->
      <!--      ></el-pagination>-->
    </section>
  </div>
</template>

<script>
import ycTitle from '@/components/yc-title/title'
import { getChannelStaticsDetail } from '@/api/dataStatistics'

export default {
  components: {
    ycTitle
  },
  data() {
    return {
      tableData: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      searchObj: {
        timeArr: [],
        os: ''
      },
      channelCode: '',
      DataLoading: false,
      id: 0
    }
  },
  created() {
    this.channelCode = this.$route.query.channelCode

    this.getData()
  },
  methods: {
    getData() {
      this.DataLoading = true
      getChannelStaticsDetail({
        channelCode: this.channelCode,
        os: this.searchObj.os,
        startDate: this.searchObj.timeArr ? this.$utils.getTimer(this.searchObj.timeArr[0]) : '',
        endDate: this.searchObj.timeArr ? this.$utils.getTimer(this.searchObj.timeArr[1]) : '',
        pageNumber: this.currentPage,
        pageSize: this.pageSize
      }).then(res => {
        this.DataLoading = false
        if (res.code === 0) {
          this.tableData = res.data
          this.total = res.totalCount
        }
      })
    },
    // 导出
    handExport() {
      const startDate = this.searchObj.timeArr ? this.$utils.getTimer(this.searchObj.timeArr[0]) : ''
      const endDate = this.searchObj.timeArr ? this.$utils.getTimer(this.searchObj.timeArr[1]) : ''
      const search = 'token=' + this.$utils.getToken() +
          '&channelCode=' + this.channelCode +
          '&startDate=' + startDate +
          '&endDate=' + endDate +
          '&os=' + this.searchObj.os

      window.location.href = this.$CONSTANT.publicPath + '/channelStatics/exploreListByCode?' + search
    },
    // handleSizeChange(page) {
    //   // 每页条数改变时
    //   this.currentPage = 1;
    //   this.pageSize = page;
    //   this.getData();
    // },
    // handleCurrentChange(page) {
    //   // 页码改变
    //   this.currentPage = page;
    //   this.getData();
    // },
    // 点击返回
    onCallback() {
      this.$router.push('/dataStatistics/channelStatistics')
    },
    refresh() {
      this.$utils.removeObj(this.searchObj)
      this.getData()
    }
  }
}
</script>

<style scoped>
</style>
