<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="186px"
      class="demo-ruleForm"
    >
      <el-form-item label="名称：" prop="name">
        <el-input v-model="ruleForm.name" placeholder="请输入任务名称" />
      </el-form-item>
      <el-form-item label="广告应用：" prop="appId">
        <el-select v-model="ruleForm.appId" placeholder="请选择广告类型">
          <el-option
            v-for="(item, i) in applicationList"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="广告类型：" prop="adType">
        <el-select v-model="ruleForm.adType" placeholder="请选择广告类型">
          <el-option
            v-for="(item, i) in advertisementTypeList"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="代码位新：" prop="codeSeatNew">
        <el-input v-model="ruleForm.codeSeatNew" placeholder="请输入代码位新" />
      </el-form-item>
      <el-form-item label="代码位老：" prop="codeSeatOld">
        <el-input v-model="ruleForm.codeSeatOld" placeholder="请输入代码位老" />
      </el-form-item>
      <el-form-item label="用户类型时间：" prop="time">
        <el-input v-model="ruleForm.time" placeholder="请输入用户类型时间" />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-radio-group v-model="ruleForm.status">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
        >确定</el-button>
        <el-button @click="resetForm('ruleForm')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  post_code_seat_manager,
  put_code_seat_manager,
  get_application_list,
  get_type_list
} from '@/api/advertisement'
export default {
  props: ['taskType', 'id', 'params'],
  data() {
    return {
      projectList: [],
      applicationList: [],
      ruleForm: {
        name: '',
        codeSeatNew: '',
        codeSeatOld: '',
        time: 0,
        adType: 1,
        appId: '',
        status: 0
      },
      rules: {
        name: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        codeSeatNew: [
          { required: true, message: '此项不能为空', trigger: 'blur' }
        ],
        codeSeatOld: [
          { required: true, message: '此项不能为空', trigger: 'blur' }
        ],
        time: [
          { required: true, message: '此项不能为空', trigger: 'blur' },
          {
            type: 'number',
            message: '请输入数字',
            trigger: 'blur',
            transform: value => Number(value)
          }
        ],
        adType: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        appId: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        // device: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '此项不能为空', trigger: 'blur' }]
      },
      advertisementProviderList: [],
      advertisementTypeList: [],
      osList: this.$utils.getOsList(),
      paramsTaskLisk: []
    }
  },
  mounted() {
    if (this.taskType == 'edit') {
      this.getDataDetail()
    }
    this.initData()
  },
  methods: {
    initData() {
      get_application_list().then(res => {
        this.applicationList = res.data.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.appId
          }
        })
      })
      get_type_list().then(res => {
        this.advertisementTypeList = res.data.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.id
          }
        })
      })
    },
    getDataDetail() {
      this.ruleForm = {
        ...this.params
      }
    },
    handChange() {
      this.ruleForm.linkType = ''
      if (this.ruleForm.ruleId == '104' && this.ruleForm.totalTimes) {
        this.handleChangeTotalTimes(this.ruleForm.totalTimes)
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.taskType == 'add') {
            post_code_seat_manager(this.ruleForm).then(res => {
              if (res.code == 200) {
                this.$refs[formName].resetFields()
                this.$emit('close', 'add')
                this.$message.success('添加成功！')
              }
            })
          } else {
            put_code_seat_manager(this.ruleForm).then(res => {
              if (res.code == 200) {
                this.$refs[formName].resetFields()
                this.$emit('close', 'add')
                this.$message.success('编辑成功！')
              }
            })
          }
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$emit('close', 'close')
    },
    handleChangeTotalTimes(val) {
      val = Number(val)
      if (val > 0 && this.ruleForm.ruleId == '104') {
        const len = this.paramsTaskLisk.length
        if (val > this.paramsTaskLisk.length) {
          for (let i = 0; i < val - len; i++) {
            this.paramsTaskLisk.push({
              code: '',
              value: ''
            })
          }
        } else {
          for (let i = 0; i < len - val; i++) {
            this.paramsTaskLisk.pop()
          }
        }
      } else {
        this.paramsTaskLisk = []
      }
    }
  }
}
</script>

<style scoped></style>
