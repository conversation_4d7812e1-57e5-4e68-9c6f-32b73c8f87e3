import {get, post} from '@/libs/axios.package'

/**
 * 全媒体列表查询
 */
export const getFullMedia = obj => {
  return post(`/full-media/page`, obj)
}
/**
 * 新增全媒体
 */
export const add_fullMedia = obj => {
  return post('/full-media/save', obj)
}
/**
 * 更新全媒体
 */
export const update_fullMedia = obj => {
  return post('/full-media/update', obj)
}
/**
 * 获取全媒体详情
 * 
 */
export const getFullMediaDetail = obj => {
  return get(`/full-media/load`, obj)
}
