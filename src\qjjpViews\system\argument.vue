<template>
  <div class="role">
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="基础参数" name="first">
        <page v-if="activeName=='first'" ref="page_first" :request="request" :list="list" :table-pagination-state="false" />
      </el-tab-pane>
      <el-tab-pane label="活动参数" name="second">
        <page v-if="activeName=='second'" ref="page_second" :request="request1" :list="list" :table-pagination-state="false" />
      </el-tab-pane>
    </el-tabs>

    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="dialogOps">
      <edit-Argument
        :sysmte-i-d="sysmteID"
        :sysmte-type="sysmteType"
        :type="type"
        @sumbit="success"
        @close="dialogFormVisible = false"
      />
    </SDialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import editArgument from '@/qjjpViews/system/modules/editArgument'
import { GET_SYSTEM_LIST, POST_SYS_DETAIL } from '@/api/system'
import { systemParam, updateById } from '@/qjjpApi/system'
import SDialog from '@/components/restructure/dialog'
import { tableItemType, formItemType } from '@/config/sysConfig'
export default {
  name: 'qjjpArgument',
  components: {
    page,
    SDialog,
    editArgument
  },
  data() {
    return {
      activeName: 'first',
      request: {
        getListUrl: (data) => {
          return systemParam({ type: 1, ...data })
        }
      },
      request1: {
        getListUrl: (data) => {
          return systemParam({ type: 2, ...data })
        }
      },
      sysmteType: '',
      list: [
        {
          key: 'paramId',
          type: formItemType.input,
          tableHidden: true,
          search: true,
          options: {
            placeholder: '请输入参数id/名称'
          }
        },
        {
          key: 'id',
          title: '参数ID',
          type: formItemType.input
        },
        {
          key: 'paramName',
          title: '参数名称',
          type: formItemType.input
        },
        {
          key: 'paramDesc',
          title: '参数说明',
          type: formItemType.input
        },
        {
          key: 'paramValue',
          title: '类型配置',
          type: formItemType.input
        },
        {
          key: 'status',
          title: '状态',
          type: formItemType.input,
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.status == 1 ? '启用' : '禁用'}`)
          }
        },
        {
          type: tableItemType.active,
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: '12',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: ($index, item, params) => {
                this.sysmteType = params.paramType
                this.sysmteID = params.id
                this.type = 'edit'
                this.dialogOps.title = '编辑'
                this.dialogFormVisible = true
              }
            }
          ]
        }
      ],
      type: '',
      sysmteID: '',
      dialogOps: {
        title: '',
        width: '500px'
      },
      dialogFormVisible: false
    }
  },
  methods: {
    success(ruleForm) {
      updateById(ruleForm).then(res => {
        if (res.code == 200) {
          this.$message.success('编辑成功')
          this.$refs[`page_${this.activeName}`].getSubSuccess()
          // this.$store.dispatch('tableRefresh', this)
          this.dialogFormVisible = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .filter-container{
  border-top: none;
}
::v-deep .el-tabs__header{
  margin-bottom: 0;
}
</style>
