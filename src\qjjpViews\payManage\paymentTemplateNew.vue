<template>
  <div id="paymentTemplates" class="position_sticky">
    <page :request="request" :list="list" table-title="会员支付模板" :table-pagination-state="false">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain icon="el-icon-circle-plus-outline" type="warning" size="small" @click="handleAdd">新增</el-button>
        <!-- <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button> -->
      </div>
    </page>

    <el-dialog :visible.sync="dialogVisible" width="1000px" :before-close="cancel" :title="dialogParams.title">
      <editTemplate />
      <div style="text-align: center; margin-top: 20px">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>

    <el-drawer
      v-if="drawer"
      title="会员支付模板编辑/新增"
      :visible.sync="drawer"
      direction="rtl"
      size="50%"
      :modal-append-to-body="false"
    >
      <div class="frawwer">
        <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="130px" class="demo-ruleForm">
          <div class="form_view">
            <div class="form_view_title">
              <span>基础信息</span>
            </div>
            <el-form-item label="模板名称" prop="name" :rules="addRules.common" :style="{'display':'inline-block','margin-right':'30px'}">
              <div :style="{width: '200px'}">
                <el-input v-model="addForm.name" placeholder="请输入模板名称" maxlength="30" />
              </div>
            </el-form-item>
            <span>{{ addForm.name?addForm.name.length:0 }}/30</span>
          </div>

          <div class="form_view">
            <div class="form_view_title">
              <span>会员类型</span>
            </div>
            <el-button type="primary" icon="el-icon-plus" circle @click="addUserType" />
            <div id="categoryList">
              <div v-for="(item,index) in addForm.membershipPackages" :key="'form_package'+index" class="form_package">
                <i class="el-icon-rank" :style="{cursor: 'pointer','font-size': '25px'}" />
                <el-button v-if="addForm.membershipPackages&&addForm.membershipPackages.length>1" type="danger" icon="el-icon-delete" circle class="delPakcage" size="mini" @click="delPakcage(index)" />
                <div class="form_package_title">会员类型{{ index+1 }}</div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="会员名称" :prop="`membershipPackages[${index}][name]`" :rules="addRules.common" :style="{'display':'inline-block','margin-right':'10px'}">
                    <div :style="{width: '200px'}">
                      <el-input v-model="item.name" placeholder="请输入会员名称" maxlength="10" />
                    </div>
                  </el-form-item>
                  <span>{{ item.name?item.name.length:0 }}/10</span>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="会员类型" :prop="`membershipPackages[${index}][userType]`" :rules="addRules.common" :style="{display:'inline-block'}">
                    <el-select v-model="item.userType" placeholder="请选择会员类型" @change="clickUserType(item.userType,index)">
                      <el-option
                        :key="1"
                        label="永久会员"
                        :value="1"
                      >永久会员</el-option>
                      <el-option
                        :key="2"
                        label="非永久会员"
                        :value="2"
                      >非永久会员</el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item v-if="item.expiryDay!='-1'" label="会员有效期" :prop="`membershipPackages[${index}][expiryDay]`" :rules="addRules.common" :style="{display:'inline-block'}">
                    <div :style="{width: '200px'}">
                      <el-input v-model="item.expiryDay" placeholder="请输入会员有效期，单位：天" />
                    </div>
                  </el-form-item>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="会员原价" :prop="`membershipPackages[${index}][originalPrice]`" :rules="addRules.common" :style="{display:'inline-block'}">
                    <div :style="{width: '200px'}">
                      <el-input v-model="item.originalPrice" placeholder="请输入会员原价，单位：元" />
                    </div>
                  </el-form-item>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="会员支付价" :prop="`membershipPackages[${index}][paymentPrice]`" :rules="addRules.common" :style="{display:'inline-block'}">
                    <div :style="{width: '200px'}">
                      <el-input v-model="item.paymentPrice" placeholder="请输入会员支付价，单位：元" />
                    </div>
                  </el-form-item>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="月单价" :style="{display:'inline-block'}">
                    <div :style="{width: '200px'}">
                      <el-input :value="item.expiryDay == '-1' ? '-' : ((Number(item.paymentPrice) / Number(item.expiryDay)).toFixed(2) + '/天')" placeholder="请输入月单价" disabled />
                    </div>
                  </el-form-item>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="会员标签" :prop="`membershipPackages[${index}][tag]`" :style="{'display':'inline-block','margin-right':'10px'}">
                    <div :style="{width: '200px'}">
                      <el-input v-model="item.tag" placeholder="请输入会员标签" maxlength="10" />
                    </div>
                  </el-form-item>
                  <span>{{ item.tag?item.tag.length:0 }}/10</span>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="会员营销文案" :prop="`membershipPackages[${index}][content]`" :style="{'display':'inline-block'}">
                    <div :style="{'display':'inline-block','margin-right':'10px',width: '200px'}">
                      <el-input v-model="item.content" placeholder="请输入会员营销文案" maxlength="20" />
                    </div>
                    <span>{{ item.content?item.content.length:0 }}/20</span>
                  </el-form-item>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="支付按钮角标文案" :prop="`membershipPackages[${index}][buttonMark]`" :style="{'display':'inline-block'}">
                    <div :style="{'display':'inline-block','margin-right':'10px',width: '200px'}">
                      <el-input v-model="item.buttonMark" placeholder="请输入支付按钮角标文案" maxlength="20" />
                    </div>
                    <span>{{ item.buttonMark?item.buttonMark.length:0 }}/20</span>
                  </el-form-item>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="打卡会员" :prop="`membershipPackages[${index}][checkInMember]`" :style="{'display':'inline-block'}" :rules="addRules.common">
                    <el-radio-group v-model="item.checkInMember" @change="item.checkInDay='',changeDefaultChoose(item.defaultChoose,item.checkInMember,item.expiryDay,index)">
                      <el-radio :label="false">否</el-radio>
                      <el-radio :label="true">是</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div v-if="item.checkInMember" :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="打卡时间（天）" :prop="`membershipPackages[${index}][checkInDay]`" :style="{'display':'inline-block'}" :rules="addRules.common">
                    <div :style="{'display':'inline-block','margin-right':'10px',width: '200px'}">
                      <el-input v-model="item.checkInDay" placeholder="请输入打卡时间" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" />
                    </div>
                    <!-- <span>{{ item.buttonMark?item.buttonMark.length:0 }}/20</span> -->
                  </el-form-item>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="是否默认选中" :prop="`membershipPackages[${index}][defaultChoose]`" :style="{'display':'inline-block'}" :rules="addRules.common">
                    <el-radio-group v-model="item.defaultChoose" @change="changeDefaultChoose(item.defaultChoose,item.checkInMember,item.expiryDay,index)">
                      <el-radio :label="false">否</el-radio>
                      <el-radio :label="true">是</el-radio>
                    </el-radio-group>
                    <!-- <span>{{ item.buttonMark?item.buttonMark.length:0 }}/20</span> -->
                  </el-form-item>
                </div>
                <div :style="{display:'inline-block',width:'390px'}">
                  <el-form-item label="拦截优惠金额" :prop="`membershipPackages[${index}][ticketAmount]`" :style="{'display':'inline-block'}">
                    <div :style="{'display':'inline-block','margin-right':'10px',width: '200px'}">
                      <el-input v-model="item.ticketAmount" placeholder="请输入拦截优惠金额" onkeyup="if(isNaN(value)) { value = null } if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}" />
                    </div>
                    <!-- <span>{{ item.buttonMark?item.buttonMark.length:0 }}/20</span> -->
                  </el-form-item>
                </div>

              </div>
            </div>
          </div>

          <div class="form_view">
            <div class="form_view_title">
              <span>支付配置</span>
            </div>
            <el-form-item label="支付方式" prop="paymentOption" :rules="addRules.common">
              <el-checkbox-group v-model="addForm.paymentOption" @change="changePaymentOption">
                <el-checkbox label="1">微信支付</el-checkbox>
                <el-checkbox label="2">支付宝支付</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="默认选中" prop="defaultPayment" :rules="addRules.common">
              <el-radio-group v-model="addForm.defaultPayment">
                <el-radio v-if="addForm.paymentOption.includes('1')" :label="1">微信支付</el-radio>
                <el-radio v-if="addForm.paymentOption.includes('2')" :label="2">支付宝支付</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div class="form_view">
            <div class="form_view_title">
              <span>模板状态</span>
            </div>
            <el-form-item label="状态" prop="status" :rules="addRules.common">
              <el-switch
                v-model="addForm.status"
                :active-value="1"
                :inactive-value="0"
                @change="switchChange"
              />
            </el-form-item>
          </div>
          <div :style="{'text-align': 'right', width: '100%'}">
            <el-button @click="drawer=false">取消</el-button>
            <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
          </div>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { areaList as vantAreaData } from '@vant/area-data'
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import {
  getmembershipTemplate,
  membershipTemplateCreate,
  membershipTemplateUpdate,
  membershipTemplateexport,
  relationshipChannel
} from '@/qjjpApi/payManage'

import editTemplate from './modules/editTemplate.vue'
import Sortable from 'sortablejs'

export default {
  name: 'paymentTemplate',
  components: {
    page,
    editTemplate
  },
  props: {},
  data() {
    function initAreaData() {
      const list = []
      for (const a in vantAreaData.province_list) {
        list.push({
          id: a,
          label: vantAreaData.province_list[a],
          children: []
        })
      }
      return list
    }
    return {
      flexArr: [],
      isflex: false,
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      addForm: {
        paymentOption: [],
        membershipPackages: [],
        defaultPayment: 1
      },
      drawer: false,
      listQuery: {

      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await getmembershipTemplate(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      },
      areaList: initAreaData(),
      dialogVisible: false,
      dialogParams: {
        templateId: '',
        title: '编辑/新增',
        templateName: '',
        areaList: []
      }
    }
  },
  computed: {
    list() {
      return [
        { title: 'ID',
          key: 'id'
        },
        {
          title: '会员名称',
          key: 'packageName',
          type: formItemType.input,
          width: 120,
          search: true,
          tableHidden: true
        },
        {
          title: '模板名称',
          key: 'name',
          type: formItemType.input,
          width: 120
        },
        {
          title: '模板名称',
          key: 'templateName',
          type: formItemType.input,
          width: 120,
          search: true,
          tableHidden: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [{
            value: 1,
            label: '启用'
          },
          {
            value: 0,
            label: '禁用'
          }
          ],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '会员名称',
          key: 'name',
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.name}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '会员原价',
          key: 'originalPrice',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.originalPrice}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '会员支付价',
          key: 'paymentPrice',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.paymentPrice}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '月单价',
          key: 'shippingType1',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.expiryDay == '-1' ? '-' : ((Number(item.paymentPrice) / Number(item.expiryDay)).toFixed(2) + '/天') }</div>
                })}
              </div >
            )
          }
        },
        {
          title: '拦截优惠金额',
          key: 'ticketAmount',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.ticketAmount} </div>
                })}
              </div >
            )
          }
        },

        {
          title: '会员标签',
          key: 'tag',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.tag}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '营销文案',
          key: 'content',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.content}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '是否打卡会员',
          key: 'checkInMember',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.checkInMember ? '是' : '否'}</div>
                })}
              </div >
            )
          }
        },
        {
          title: '打卡天数',
          key: 'checkInDay',
          width: 120,
          render: (h, { data }) => {
            return (
              <div>
                {data.row.membershipPackageVos.map((item, index) => {
                  return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.checkInDay ? (item.checkInDay + '天') : '-'}</div>
                })}
              </div >
            )
          }
        },
        { title: '支付方式',
          key: 'paymentOption',
          render: (h, params) => {
            if (!params.data.row.paymentOption) {
              return h('span', '-')
            }
            return h('span', (params.data.row.paymentOption.includes(1) ? '微信' : '') + (params.data.row.paymentOption.includes(2) ? ',支付宝' : ''))
          }
        },
        {
          title: '更新时间',
          key: 'updateTime',
          tableView: tableItemType.tableView.date,
          options: {
            format: 'YYYY-MM-DD HH:mm:ss'
          }
        },
        {
          title: '更新人员',
          key: 'updateUser'
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return h('span', params.data.row.status == 0 ? '禁用' : '启用')
          }
        },
        {
          type: tableItemType.active,
          width: 180,
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: ($index, item, params) => {
                this.popType = 'edit'
                this.addForm = JSON.parse(JSON.stringify(params))

                this.$set(this.addForm, 'membershipPackages', JSON.parse(JSON.stringify(params.membershipPackageVos)))
                for (let i = 0; i < params.membershipPackageVos?.length; i++) {
                  if (params.membershipPackageVos[i].expiryDay == '-1') {
                    this.$set(this.addForm.membershipPackages[i], 'userType', 1)
                  } else {
                    this.$set(this.addForm.membershipPackages[i], 'userType', 2)
                  }
                }
                this.$set(this.addForm, 'paymentOption', JSON.parse(JSON.stringify(params.paymentOption.split(','))))

                console.info(params.membershipPackageVos?.length, 'this.addForm')
                // this.$set(this.addForm, params)
                this.drawer = true
              }
            }
          ]
        }
      ]
    }
  },
  watch: {
    drawer: {
      handler(val) {
        if (val) {
          setTimeout(() => {
            const tbody = document.querySelector('#categoryList')
            const that = this
            if (this.categoryListsort) {
              this.categoryListsort.destroy()
            }
            console.info(tbody, 'tbody')
            this.categoryListsort = new Sortable(tbody, {
              animation: 150,
              sort: true,
              handle: '.el-icon-rank',
              draggable: '.form_package', // 设置可拖拽行的类名(el-table自带的类名)
              forceFallback: true,
              ghostClass: 'blue-background-class',
              async onEnd(evt) {
                that.isflex = true
                const arr = JSON.parse(JSON.stringify(that.addForm.membershipPackages))
                const curr = arr.splice(evt.oldIndex, 1)[0]
                arr.splice(evt.newIndex, 0, curr) // 把被拖拽的元素添加到checkQaCategoryList中
                // that.categoryIsSort = true
                console.info(curr, arr, 'ids')
                that.flexArr = JSON.parse(JSON.stringify(arr))
                // that.$set(that.addForm, 'membershipPackages', JSON.parse(JSON.stringify(arr)))
                that.$forceUpdate()
              }
            })
          })
        } else {
          this.isflex = false
        }
      }
    }
  },
  methods: {
    // item.defaultChoose,item.checkInMember,item.expiryDay,index
    changeDefaultChoose(val, checkInMember, expiryDay, index) {
      console.info(val)
      if (val) {
        const a = this.addForm.membershipPackages
        for (let i = 0; i < a.length; i++) {
          console.info(checkInMember, a[i].checkInMember, a[i].defaultChoose, index != i, index, i)
          if (checkInMember && a[i].checkInMember && a[i].defaultChoose && index != i) {
            console.info(i, '1')
            this.$set(this.addForm.membershipPackages[i], 'defaultChoose', false)
          } else if (!checkInMember && !a[i].checkInMember && a[i].defaultChoose && index != i) {
            console.info(i, '2')
            this.$set(this.addForm.membershipPackages[i], 'defaultChoose', false)
          }
        }
      }
    },
    switchChange(e) {
      if (String(e) !== '1' && this.addForm.defaultTemplate) {
        this.$message.error('改模板为默认支付模板，不可操作禁用')
        this.addForm.status = 1
        return
      }
      if (e == '0') {
        relationshipChannel({ id: this.addForm.id }).then(res => {
          if (res.code == 200 && res.data) {
            this.$confirm('当前支付模板已存在关联渠道，禁用后已关联渠道将使用默认模板，是否确认禁用?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
            }).catch(() => {
              this.$set(this.addForm, 'status', 1)
            })
          }
        })
      }
    },
    changePaymentOption(e) {
      console.info(e)
      if (e.length < 2) {
        this.$set(this.addForm, 'defaultPayment', Number(e[0]))
      }
    },
    clickUserType(type, index) {
      console.info(this.addForm, type, index, this.addForm.membershipPackages)
      if (type == 1) {
        this.$set(this.addForm.membershipPackages[index], 'expiryDay', -1)
      } else {
        this.$set(this.addForm.membershipPackages[index], 'expiryDay', '')
      }
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = membershipTemplateexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    handleAdd() {
      this.drawer = true
      this.popType = 'add'
      this.reloadAddform()
    },
    cancel() {
      this.dialogVisible = false
      this.dialogParams = {
        title: '编辑/新增',
        templateName: '',
        areaList: []
      }
      this.areaList.forEach(area => {
        area.shippedAmount = ''
      })
    },
    confirm() {
      this.drawer = true
    },
    delPakcage(index) {
      this.$confirm(`删除后将不再保存编辑信息，是否确认删除`).then(_res => {
        this.addForm.membershipPackages.splice(index, 1)
        this.$message.success('操作成功')
      })
    },
    addUserType() {
      // if (this.addForm.membershipPackages.length > 4) {
      //   this.$message.error('最多添加五种！')
      //   return
      // }
      this.addForm.membershipPackages.push({
        id: '',
        name: '',
        userType: 1,
        expiryDay: -1,
        originalPrice: '',
        paymentPrice: '',
        tag: '',
        content: '',
        buttonMark: '',
        checkInMember: false,
        checkInDay: '',
        defaultChoose: false,
        ticketAmount: ''
      })
    },
    handMessageStyleListAdd(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const a = this.addForm.membershipPackages
          const aArry = []
          const bArry = []
          for (let i = 0; i < a.length; i++) {
            if (Number(a[i].expiryDay) >= 0 && a[i].checkInDay && a[i].checkInDay > Number(a[i].expiryDay)) {
              console.info(a[i].expiryDay >= 0, 'a[i].expiryDay >= 0')
              console.info(a[i].checkInDay, 'a[i].checkInDay')
              console.info(a[i].checkInDay > a[i].expiryDay, a[i].expiryDay, 'a[i].checkInDay > a[i].expiryDay')
              this.$message.error('打卡时间不能大于会员有效期')
              return
            }
            if (a[i].checkInMember && a[i].defaultChoose) {
              aArry.push(a[i].name)
            } else if (a[i].expiryDay == -1 && a[i].defaultChoose) {
              bArry.push(a[i].name)
            }
          }
          if (aArry.length > 1) {
            this.$message.error(`打卡会员默认选中只能唯一,重复类型：'${aArry}'`)
            return
          }
          if (bArry.length > 1) {
            this.$message.error(`普通会员默认选中只能唯一,重复类型：'${bArry}'`)
            return
          }
          const payList = this.addForm.paymentOption.toString()
          const fn = this.popType == 'edit' ? membershipTemplateUpdate : membershipTemplateCreate
          if (this.isflex) {
            this.$set(this.addForm, 'membershipPackages', this.flexArr)
          }
          fn({ ...this.addForm, paymentOption: payList }).then(res => {
            if (res.code == 200) {
              this.drawer = false
              this.$message.success('操作成功')
              this.$store.dispatch('tableRefresh', this)
            }
          })
        }
      })
    },
    reloadAddform() {
      const arr = {
        id: '',
        name: '',
        paymentOption: [],
        defaultPayment: '',
        status: 1,
        membershipPackages: [{
          id: '',
          name: '',
          userType: 1,
          expiryDay: -1,
          originalPrice: '',
          paymentPrice: '',
          tag: '',
          content: '',
          buttonMark: '',
          checkInMember: false,
          checkInDay: ''
        }]
      }
      for (const key in arr) {
        this.$set(this.addForm, key, arr[key])
      }
    }
  }
}
</script>
<style scoped lang='scss'>
::v-deep .el-drawer__body {
    overflow: scroll;
    padding: 0 30px 20px;
    /* overflow-x: auto; */
}
::v-deep .el-drawer__header{
  span{
    font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
  }

}
::v-deep .el-drawer__container ::-webkit-scrollbar{
    display: none;
}
body{
}

.position_sticky{

/*2.隐藏滚动条，太丑了*/

}
.form_view{
        margin: 0 0px 20px;
        background-color: rgb(189, 184, 184,0.2);
        border: 1px solid rgba(0,0,0,0.2);
        width: 100%;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        .form_view_title{
            margin-bottom: 20px;
            span{
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
  .form_package{
    margin: 10px 0;
    padding: 10px 0;
    border:1px solid gray;
    position: relative;
    .el-icon-rank{
      width: 20px;
      height: 20px;
      font-size: 30px;
      position: absolute;
      top: 0px;
      left: -25px;
    }
    .delPakcage{
      position: absolute;
      top: 20px;
      left: 120px;
    }
    .form_package_title{
      padding: 10px;
      font-family: MiSans, MiSans;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 26px;
font-style: normal;
    }
  }
    }
.flex {
  display: flex;
  align-items: center;
}

::v-deep .el-transfer-panel {
  width: 250px !important;
}
</style>
