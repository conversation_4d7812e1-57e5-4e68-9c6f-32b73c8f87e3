<template>
  <el-form ref="addForm" class="equip-form" label-width="100px" :model="form" :rules="addRules">
    <el-form-item label="设备" :rules="addRules.common" prop="siteId">
      <el-select
        v-model="form.siteId"
        placeholder="请选择应用"
      >
        <el-option v-for="item in siteIds" :key="item.siteId" :label="item.name" :value="item.siteId">{{
          item.name }}</el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="手机号" :rules="addRules.common" prop="mobileNo">
      <el-input v-model="form.mobileNo" type="tel" maxlength="11" />
    </el-form-item>
    <el-form-item>
      <el-button size="middle" type="primary" @click="submitEquipmentInit">初始化</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import { resetUserChat } from '@/qjjpApi/whitelistInvestigation'
export default {
  data() {
    return {
      siteIds: [],
      form: {
        mobileNo: '',
        siteId: ''
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      equimentInfo: []
    }
  },
  async mounted() {
    await count_channel_application_list().then(res => {
      if (res.code === 200) {
        if (res.data && res.data.length) {
          this.siteIds = res.data
        }
      }
    })
  },
  methods: {
    submitEquipmentInit() {
      this.$refs['addForm'].validate(valid => {
        if (valid) {
          resetUserChat({
            ...this.form
          }).then(res => {
            if (res.code == 200) {
              this.$message.success('操作成功')
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.equip-form {
    width: 400px;
    margin: 50px;
}
</style>
