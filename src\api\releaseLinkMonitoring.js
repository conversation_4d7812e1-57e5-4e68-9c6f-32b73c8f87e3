import CONSTANT from '@/config/constant.conf'
import { del, get, post, put } from '@/libs/axios.package'
import qs from 'qs'

// 投放链路 - 列表
export const getList = obj => get('/count/dataLinkMonitor/page', obj)

// 投放链路 - 列表子项列表
export const getDetailList = obj => get('/count/dataLinkMonitor/page/detail', obj)

// 投放链路 - 列表导出
export const listExport = data => CONSTANT.publicPath + '/count/dataLinkMonitor/page/export?' + qs.stringify(data)


// 投放链路 - 渠道列表
export const getChannelCodeList = obj => get('/count/dataLinkMonitor/channel', obj)

// 投放链路 - 渠道列 表子项列表
export const getChannelCodeDetailList = obj => get('/count/dataLinkMonitor/channel/detail', obj)

// 投放链路 - 渠道列表导出
export const channelCodeListExport = data => CONSTANT.publicPath + '/count/dataLinkMonitor/channel/export?' + qs.stringify(data)
