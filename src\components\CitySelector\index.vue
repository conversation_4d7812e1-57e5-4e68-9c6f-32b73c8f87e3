<template>
  <el-cascader
    ref="citySelector"
    v-model="selectedCities"
    :options="options"
    :props="props"
    :placeholder="placeholder"
    :clearable="clearable"
    :filterable="filterable"
    :collapse-tags="collapseTags"
    @change="handleChange"
  />
</template>

<script>
export default {
  name: 'CitySelector',
  props: {
    // 当前选中的值，可以是单个值或数组，取决于是否多选
    value: {
      type: [String, Array],
      default: () => []
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 占位文本
    placeholder: {
      type: String,
      default: '请选择城市'
    },
    // 是否可清空选项
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否可以搜索
    filterable: {
      type: Boolean,
      default: false
    },
    // 多选时是否折叠显示
    collapseTags: {
      type: Boolean,
      default: true
    },
    // 是否可过滤
    filterable: {
      type: Boolean,
      default: true
    },
    // 自定义API路径
    apiUrl: {
      type: String,
      default: 'https://api.haoxincd.cn/major/city/allCity'
    },
    // 显示全国
    showAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedCities: this.value,
      options: [],
      props: {
        multiple: this.multiple,
        value: 'id',
        label: 'name',
        children: 'cityDtoList',
        emitPath: false
      }
    }
  },
  watch: {
    value(val) {
      this.selectedCities = val
    },
    multiple: {
      handler(val) {
        this.props.multiple = val
      },
      immediate: true
    }
  },
  created() {
    this.fetchCityData()
  },
  methods: {
    /**
     * 获取城市数据
     */
    async fetchCityData() {
      try {
        const res = await fetch(this.apiUrl)
        const data = await res.json()
        if (data && Array.isArray(data.data)) {
          const options = data.data.map(province => ({
            id: province.provinceCode,
            name: province.provinceName,
            cityDtoList: province.cityDtoList?.map(city => ({
              id: city.id,
              name: city.fullName
            }))
          }))

          this.options = this.showAll ? [{id: '-1', name: '全国', cityDtoList: options}] : options
          this.$emit('options', this.options)
        }
      } catch (error) {
        console.error('获取城市数据失败:', error)
      }
    },

    /**
     * 处理级联选择器选中值变化
     */
    handleChange(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    },
    getCheckedNodes() {
      return this.$refs.citySelector?.getCheckedNodes(true)??[]
    }
  }
}
</script>

<style lang="scss" scoped>
/* 解决 添加 filterable placeholder 重影 */
::v-deep {
  .el-cascader__tags input::-webkit-input-placeholder {
    color: white;
    opacity: 0;
  }
}
</style>
