<template>
  <div class="wang-editor">
    <Toolbar
      class="wang-editor-toolbar"
      :editor="editor"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      class="wang-editor-content"
      v-model="content"
      :defaultConfig="editorConfig"
      :mode="mode"
      :style="{ height: height }"
      @onCreated="handleCreated"
      @onChange="handleChange"
      @onDestroyed="handleDestroyed"
      @onFocus="handleFocus"
      @onBlur="handleBlur"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'

export default Vue.extend({
  name: 'WangEditor',
  components: { Editor, Toolbar },
  
  props: {
    value: {
      type: String,
      default: ''
    },
    mode: {
      type: String,
      default: 'default'
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    maxLength: {
      type: Number,
      default: 1000
    },
    height: {
      type: String,
      default: '300px'
    }
  },

  data() {
    return {
      editor: null,
      content: this.value,
      toolbarConfig: {
        excludeKeys: [
          'group-video',
          'group-image',
          'insertImage',
          'uploadImage',
          'insertVideo',
          'uploadVideo',
          'code',
          'codeBlock',
          'fullScreen',
          'todo',
          'emotion',
          'group-more-style'
        ]
      },
      editorConfig: {
        placeholder: this.placeholder,
        maxLength: this.maxLength,
        autoFocus: false
      }
    }
  },

  watch: {
    value(newVal) {
      if (newVal !== this.content) {
        this.content = newVal
      }
    }
  },

  methods: {
    handleCreated(editor) {
      this.editor = Object.seal(editor)
    },
    handleChange(editor) {
      const html = editor.getHtml()
      this.content = html
      this.$emit('input', html)
      this.$emit('change', editor)
    },
    handleFocus(editor) {
      this.$emit('focus', editor)
    },
    handleBlur(editor) {
      this.$emit('blur', editor)
    },
    handleDestroyed(editor) {
      this.editor = null
    }
  },

  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy()
  }
})
</script>

<style lang="scss" scoped>
.wang-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  
  ::v-deep .wang-editor-toolbar {
    border-bottom: 1px solid #dcdfe6;
    border-radius: 4px 4px 0 0;
    background-color: #f5f7fa;
  }

  ::v-deep .w-e-text-container {
    background-color: #fff;
    border-radius: 0 0 4px 4px;
  }
  
  ::v-deep .w-e-text {
    b, strong {
      font-weight: bold !important;
    }
    i {
      font-style: italic !important;
    }
    a {
      color: #1890ff !important;
      text-decoration: underline !important;
      cursor: pointer !important;

      &:hover {
        color: #40a9ff !important;
      }
    }
  }
}
</style> 