<template>
  <!-- 推广商品列表 -->
  <div>
    <!-- 查询条件 -->
    <el-form :inline="true" :model="searchForm" class="demo-form-inline" style="margin-top:23px;">
      <el-form-item label="输入搜索：">
        <el-input v-model="searchForm.name" placeholder="商品名称/商品货号" />
      </el-form-item>
      <el-form-item label="商品分类：">
        <el-cascader
          v-model="searchForm.parentIds"
          placeholder="请选择,默认一级"
          :options="casCadeData"
          :props="optionProps"
          clearable
          @change="handleChooseParentIds"
        />
      </el-form-item>
      <el-form-item label="商品来源：">
        <el-select v-model="searchForm.source" placeholder="请选择来源">
          <el-option label="全部" value />
          <el-option label="淘宝" value="0" />
          <el-option label="京东" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="商品推荐位置：">
        <el-select v-model="searchForm.location" placeholder="请选择推荐位置">
          <el-option
            v-for="(item, index) in locationArray"
            :key="index"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="可见用户：" prop="userAuthority">
        <el-select v-model="searchForm.userAuthority" style="width: 200px" placeholder="请选择可见用户">
          <el-option
            v-for="(item, index) in userAuthorityArray"
            :key="index"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">查询</el-button>
      </el-form-item>
    </el-form>
    <section />
    <section>
      <el-table
        v-loading="DataLoading"
        :data="tableData"
        border
        style="width: 100%;margin: 30px 0;"
      >
        <el-table-column prop="sourceId" label="商品ID" />
        <el-table-column prop="id" label="商品编号" />
        <el-table-column prop="source" label="商品来源" />
        <el-table-column prop="category" label="商品分类" />
        <el-table-column label="商品图片">
          <template slot-scope="scope">
            <viewer v-if="scope.row.mainImage" class="img-wrap" style="margin: auto;">
              <img :src="scope.row.mainImage" style="max-width: 50px;max-height: 50px;">
            </viewer>
          </template>
        </el-table-column>
        <el-table-column prop="goodsName" label="商品名称" min-width="200px" />
        <el-table-column prop="price" label="到手价格" />
        <el-table-column prop="sourcePrice" label="原价" />
        <el-table-column prop="couponPrice" label="优惠券金额" />
        <el-table-column prop="userAuthorities" label="可见用户">
          <template slot-scope="scope">
            <span>{{ getUserAuthorityName(scope.row.userAuthorities) }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column prop="rebateRate" label="返利比例" ></el-table-column>-->
        <el-table-column prop="sort" label="排序" />
        <el-table-column prop="status" label="是否上架">
          <template slot-scope="scope">
            <span v-if="scope.row.status=='0'" style="color:#1ABC9C">上架</span>
            <span v-if="scope.row.status=='1'" style="color:#FF7F50">下架</span>
            <span v-if="scope.row.status=='2' || scope.row.status=='3'" style="color:#FF7F50">{{ scope.row.statusMsg }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="170px" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="warning"
              plain
              size="mini"
              @click="linkDetails('edit', scope.row.id)"
            >编辑</el-button>
            <el-button type="danger" plain size="mini" @click="deletGood(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 70, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </section>
  </div>
</template>

<script>
import {
  getPromotionStoreroomListApi,
  getPromotionStoreroomApi,
  deletPromotionGoodApi,
  getGoodsLocationApi
} from '@/api/goods'
export default {
  props: {
    status: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      locationArray: [], // 商品位置列表
      optionProps: { // 连级选择组件的配置
        value: 'id',
        label: 'name',
        checkStrictly: true
      },
      searchForm: { // 查询条件
        name: '',
        source: '', // 来源
        parentIds: [], // 商品分类选中的数据
        category: '', // 商品分类表格展示
        location: '', // 展示位置
        categoryString: '', // 选中的分类 处理成字符串数据传给后台
        userAuthority: []
      },
      DataLoading: false, // 加载动画开关
      tableData: [], // 表格数据
      total: 0,
      pageSize: 10,
      currentPage: 1,
      casCadeData: [], // 商品分类回显列表
      userAuthorityArray: [
        {
          name: '游客',
          value: 1
        }, {
          name: '普通未参加0元购用户',
          value: 2
        }, {
          name: '普通参加0元购用户',
          value: 3
        }, {
          name: '爵士未参加0元购用户',
          value: 4
        }, {
          name: '爵士已参加0元购用户',
          value: 5
        }
      ]
    }
  },
  mounted() {
  },
  methods: {
    // 删除商品
    deletGood(id) {
      this.$confirm('是否确认删除？','操作提示').then(() => {
        deletPromotionGoodApi(id).then(res => {
          if (res.code === 0) {
            this.$message({
              message: '删除成功！',
              type: 'success'
            })
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      }).catch(() => {
        // 取消
      })
    },
    // 获取推广商品分类
    getPromotionCommoditiesData() {
      getPromotionStoreroomApi().then(res => {
        if (res.code == '0') {
          this.casCadeData = res.data
          const allObj = [
            {
              children: null,
              id: '0',
              logoUrl: '',
              name: '全部',
              sort: '',
              status: 0
            }
          ]
          this.casCadeData = allObj.concat(res.data)
        }
      })
    },
    // 点击查询按钮
    handleSearch() {
      if (this.searchForm.category) {
        let categoryString = ''
        this.searchForm.category.forEach(element => {
          categoryString += element + '-'
        })
        categoryString = categoryString.substr(0, categoryString.length - 1)
        this.searchForm.categoryString = categoryString
      }
      this.getData()
    },
    // 切换商品分类
    handleChooseParentIds(value) {
      this.searchForm.category = value
    },
    /**
     * 获取列表
     */
    getData() {
      this.DataLoading = true
      /** up：上架  down：下架  空：全部*/
      getPromotionStoreroomListApi({
        status: this.status === '1' ? '0' : this.status === '2' ? '1' : null,
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
        name: this.searchForm.name,
        category: this.searchForm.category == 0 ? '' : this.searchForm.categoryString,
        locationId: this.searchForm.location,
        source: this.searchForm.source,
        userAuthority: this.searchForm.userAuthority
      }).then(res => {
        this.DataLoading = false
        if (res.code === 0) {
          this.tableData = res.data
          this.total = res.totalCount
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },
    // 携带参数跳转
    linkDetails(type, id) {
      this.$router.push({
        path: '/commodity/commonList_detail',
        query: {
          type: type,
          id: id
        }
      })
    },
    // 获取商品位置
    getGoodsLocation() {
      getGoodsLocationApi().then(res => {
        if (res.code === 0) {
          const addObj = {
            id: '',
            title: '全部'
          }
          this.locationArray = res.data
          this.locationArray.unshift(addObj)
        }
      })
    },

    getUserAuthorityName(ids) {
      const nameArr = []
      ids.map(item => {
        this.userAuthorityArray.map(child => {
          if (child.value === item) {
            nameArr.push(child.name)
          }
        })
      })
      return nameArr.join(',')
    }
  }
}
</script>

<style scoped>
.table-img {
  width: 50px;
  height: 50px;
}
</style>
