<template>
  <div>
    <div v-show="!picUrl">
      <el-upload
        v-if="uploadShow"
        :ref="eleName"
        style="width: 300px"
        accept="image/png, image/jpg, image/jpeg, image/gif"
        :headers="headers"
        :action="uploadUrl"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-error="uploadError"
        :multiple="false"
        :limit="1"
        list-type="picture"
        :show-file-list="false"
      >
        <el-button :id="eleName" icon="el-icon-upload" size="small" type="warning">点击上传</el-button>
      </el-upload>
    </div>
    <div v-show="picUrl">
      <viewer style="display: inline-flex">
        <div class="item-img">
          <img :src="picUrl">
        </div>
      </viewer>
      <el-button
        v-if="showDelete && picUrl !== ''"
        type="danger"
        icon="el-icon-delete"
        circle
        @click="handleRemove"
      />
      <el-button type="warning" plain size="small" @click="CikUpload()">重新选取</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'upload',
  props: {
    picUrl: {
      // 图片地址
      default: ''
    },
    eleName: {
      // 一个页面如有多个上传图片需求，需传不一样的id名称
      default: 'upload'
    },
    showDelete: {
      default: false
    }
  },
  data() {
    return {
      uploadUrl: '',
      headers: {},
      uploadShow: true
    }
  },
  mounted() {
    // 上传地址
    this.uploadUrl = this.$CONSTANT.yjgjPath + '/cms/upload/image'
    // 请求头
    this.headers = { Authorization: this.$utils.getToken() }
  },
  methods: {
    beforeUpload(file) {
      console.info(file)
      // 上传之前
      if (file.size > 4 * 1024 * 1024) {
        this.$message.error('上传图片大小不能超过4M!')
        this.uploadShow = false
        setTimeout(() => {
          this.uploadShow = true
        }, 20)
      }

      return file.size
    },
    uploadSuccess: function(res, file) {
      // 图片上传成功
      if (res.code === 200) {
        this.picUrl = res.data
        this.$emit('uploadSuccess', this.picUrl)
      }
    },
    uploadError: function(res, file) {
      // 图片上传失败
      this.$message.error('图片上传失败')
    },
    // 重新选取图片
    CikUpload() {
      document.getElementById(this.eleName).click()
      this.$refs[this.eleName].clearFiles()
    },
    handleRemove() {
      this.$emit('remove', this.picUrl)
    }
  }
}
</script>

<style scoped lang="scss">
.item-img {
  width: auto;
  height: auto;
  border: 1px dashed #dfdfdf;
  padding: 10px;
  text-align: center;
  margin-right: 15px;
  border-radius: 4px;

  img {
    width: auto;
    height: auto;
    max-width: 120px;
    max-height: 70px;
  }
}
</style>
