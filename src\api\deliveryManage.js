import {get, post, put} from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'
/**
 * 投放管理
 */

// 分页查询媒体数据
export const pageAccountList = obj => {
  return get('/crawler/media/account/page', obj)
}

// 更新媒体账号
export const pageAccountEdit = obj => {
  return post('/crawler/media/account/saveorupdate', obj)
}

// 删除媒体账号
export const pageAccountDel = params => {
  return get(`/crawler/media/account/delete`, params)
}

// 导入媒体数据
export const post_account_import = obj => {
  return post(`/crawler/media/account/import`, obj)
}

export const EXPORT_PAGE_ACCOUNT = data =>
  CONSTANT.publicPath + '/crawler/media/account/export?' + qs.stringify(data)

export const EXPORT_PAGE_MEDIA = data =>
  CONSTANT.publicPath + '/crawler/media/channel/export?' + qs.stringify(data)

// 媒体渠道关系分页查询
export const channelMatchList = obj => {
  return get('/crawler/media/channel/page', obj)
}

// 单条媒体渠道关联关系
export const channelMatchInquire = obj => {
  return get('/crawler/media/channel/relation', obj)
}

// 新增媒体渠道关联关系
export const addChannelMatchBatch = obj => {
  return post(`/crawler/media/channel/batch`, obj)
}

// 刷新媒体渠道数据
export const refreshChannelData = obj => {
  return post(`/crawler/media/channel/refresh`, obj)
}

// 删除媒体渠道数据
export const deleteAccountChannelData = params => {
  return get(`/crawler/media/channel/delete`, params)
}

// 获取当前媒体类型下的所有一级账号
export const getParentMediaAccountList = params => {
  return get(`/crawler/media/account/getParentMediaAccount`, params)
}

// 代理保存或更新
export const proxyConfigSaveOrUpdate = params => {
  return post(`/proxy/config/saveOrUpdate`, params)
}

// 代理列表查询
export const proxyConfigPageList = params => {
  return get(`/proxy/config/pageList`, params)
}

// 代理下拉选
export const proxyConfigSelector = params => {
  return get(`/proxy/config/selector`, params)
}

// 代理校验代理名称相似
export const proxyConfigCheckProxyName = params => {
  return get(`/proxy/config/checkProxyName`, params)
}

// 分页查询媒体投放账户
export const mediaDeliveryAccountPage = params => {
  return get(`/mediaDeliveryAccount/page`, params)
}
// 投放账户导出
export const mediaDeliveryAccount_export = data =>
  CONSTANT.publicPath + '/mediaDeliveryAccount/export?' + qs.stringify(data)
// 新增或修改媒体投放账户
export const addOrUpdateNew = params => {
  return post(`/mediaDeliveryAccount/addOrUpdate`, params)
}
// 查询媒体和投放应用下的开发者账户
export const optionListNew = params => {
  return get(`/mediaDeveloperAccount/optionList`, params)
}
// 分页查询投放账户下的广告账户
export const advertiserPage = params => {
  return get(`/mediaDeliveryAccount/advertiser/page`, params)
}
// 投放账户下的广告账户导出
export const advertiser_export = data =>
  CONSTANT.publicPath + '/mediaDeliveryAccount/advertiser/export?' + qs.stringify(data)
