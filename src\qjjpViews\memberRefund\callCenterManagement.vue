<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 10:26:39
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-05-09 14:10:35
-->
<template>
  <div class="main">
    <div class="planList">
      <div class="addTips">
        <span>问答方案 </span>
        <el-button type="primary" icon="el-icon-plus" circle @click="addPlan" />
      </div>
      <div>
        <el-input v-model="listQuery.keyWord" placeholder="搜索名称、ID" class="input-with-select">
          <el-button slot="append" type="primary" @click="search"> 查询 </el-button>
        </el-input>
      </div>
      <el-menu default-active="0" class="el-menu-vertical-demo menu" @select="checkPlan">
        <el-menu-item v-for="(item, index) in planList" :key="'planList' + item.id" :index="index">
          <span slot="title">{{ item.name }}</span>
          <i class="el-icon-close delet" @click.stop="deletePlans(item.id)" />
          <span class="deletTips">ID:{{ item.id }}</span>
        </el-menu-item>
      </el-menu>
      <el-dialog title="新增方案" :visible.sync="addPop" width="600px" :close-on-click-modal="false" @close="resetForm">
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="100px" class="demo-ruleForm">
            <el-form-item label="名称" prop="name">
              <div :style="{width: '200px'}">
                <el-input v-model="addForm.name" placeholder="请输入名称" />
              </div>
            </el-form-item>
            <el-form-item label="用户可见性" />
            <el-form-item label="使用场景" prop="scenes">
              <el-checkbox-group v-model="addForm.scenes" @change="scenesChange">
                <el-checkbox :label="1">站内</el-checkbox>
                <el-checkbox :label="2">站外</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item v-if="!refundStatusDisable" label="会员状态" prop="refundStatus">
              <el-checkbox-group v-model="addForm.refundStatus" disabled>
                <el-checkbox :label="1">未提交退款</el-checkbox>
                <el-checkbox :label="2">已提交未退款</el-checkbox>
                <el-checkbox :label="3">已退款</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="应用" prop="siteIds">
              <el-checkbox-group v-model="addForm.siteIds">
                <el-checkbox v-for="item in siteIds" :key="'addForm' + item.siteId" :label="item.siteId">{{
                  item.name
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item>
              <el-button @click="resetForm('addForm')">取消</el-button>
              <el-button type="primary" @click="submitForm('addForm')">确认</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
    </div>
    <div class="mainView">
      <div v-if="planList.length != 0" class="topView">
        <el-form ref="addForm" :model="addForm1" :rules="addRules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="名称" prop="name">
            <div :style="{width: '200px'}">
              <el-input v-model="addForm1.name" placeholder="请输入名称" />
            </div>
          </el-form-item>
          <el-form-item label="用户可见性" />
          <el-form-item label="使用场景" prop="scenes">
            <el-checkbox-group v-model="addForm1.scenes" @change="scenesChange1">
              <el-checkbox label="1">站内</el-checkbox>
              <el-checkbox label="2">站外</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="!refundStatusDisable1" label="会员状态" prop="refundStatus">
            <el-checkbox-group v-model="addForm1.refundStatus" disabled>
              <el-checkbox label="1">未提交退款</el-checkbox>
              <el-checkbox label="2">已提交未退款</el-checkbox>
              <el-checkbox label="3">已退款</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="应用" prop="siteIds">
            <el-checkbox-group v-model="addForm1.siteIds">
              <el-checkbox v-for="item in siteIds" :key="'siteIds' + item.siteId" :label="item.siteId">{{
                item.name
              }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <!-- <el-button @click="resetForm('addForm')">取消</el-button> -->
            <el-button type="primary" @click="submitMainForm('addForm')">确认</el-button>
          </el-form-item>
        </el-form>
        <div class="line" />
        <el-form ref="addIcon" :model="addIcon" :rules="addIconRules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="icon配置" />
          <el-form-item>
            <div id="iconView" v-loading="iconLoading">
              <div v-for="item in checkIconList" :key="'IconList' + item.id" class="iconView">
                <i class="el-icon-close iconDelet" @click="deletIcon(item.id)" />
                <img :src="item.icon">
                <div>{{ item.questionName }}</div>
                <div>ID:{{ item.questionId }}</div>
              </div>
            </div>
            <div v-if="!(checkIconList.length > 19)" class="addIcon" @click="addIcon">
              <i class="el-icon-plus" />
            </div>
          </el-form-item>
          <el-form-item label="问答列表配置" />
          <el-form-item>
            <el-button
              v-if="!(categoryList.length > 19)"
              type="primary"
              @click="addCategoryPop = true"
            >添加分类<i class="el-icon-plus" /></el-button>
            <el-form-item :style="{marginTop: '20px'}">
              <div id="categoryList" v-loading="categoryIsSort">
                <div
                  v-for="item in categoryList"
                  :key="'category' + item.id"
                  class="categoryList"
                  @click="checkCategory(item.id)"
                >
                  <i class="el-icon-rank" :style="{cursor: 'pointer', 'margin-right': '10px', 'font-size': '15px'}" />
                  <span :class="categoryId == item.id ? 'checkCategory categoryName' : 'categoryName'">{{
                    item.name
                  }}</span>
                  <el-dropdown :style="{'vertical-align': 'middle'}" @command="handleCommand">
                    <span class="el-dropdown-link"><i class="el-icon-arrow-down el-icon--right" /> </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="{data: item, type: 'add'}">编辑</el-dropdown-item>
                      <el-dropdown-item :command="{id: item.id, type: 'del'}">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </el-form-item>
            <el-form-item ref="qaCategoryList" :style="{marginTop: '20px'}">
              <div id="qaCategoryListView" v-loading="qaCategoryLoading">
                <div v-for="item in checkQaCategoryList" :key="'QaCategoryList' + item.id" class="qaCategoryList">
                  <div>
                    <i
                      class="el-icon-rank"
                      :style="{cursor: 'pointer', 'margin-right': '10px', 'font-size': '15px'}"
                    />{{ item.questionName }}
                  </div>
                  <div>
                    ID:{{ item.questionId }}
                    <el-dropdown trigger="click" @command="handleCommand1">
                      <span
                        class="el-dropdown-link"
                        :style="{cursor: 'pointer'}"
                      ><i class="el-icon-arrow-down el-icon--right" />
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="{id: item.id, type: 'del'}">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
              </div>
              <el-button
                v-if="categoryList.length != 0 && !(checkQaCategoryList.length > 19)"
                type="primary"
                @click="addQaCategory"
              >添加问答<i class="el-icon-plus" /></el-button>
            </el-form-item>
            <!-- <div class="addCategory">
              <span></span>
            </div> -->
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-dialog
      v-if="addCategoryPop"
      title="问答分类"
      :visible.sync="addCategoryPop"
      :close-on-click-modal="false"
      width="400px"
      @close="closeAddCategoryPop"
    >
      <el-form
        ref="addCategory"
        :model="addCategory"
        :rules="addCategoryRules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item prop="name">
          <div :style="{width: '200px'}">
            <el-input v-model="addCategory.name" placeholder="请输入名称" />
            <el-form-item>
              <el-button @click="closeAddCategoryPop">取消</el-button>
              <el-button type="primary" @click="addCategoryOrUpdates">确认</el-button>
            </el-form-item>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      v-if="checkQAShow"
      title="新增问答"
      :visible.sync="checkQAShow"
      :close-on-click-modal="false"
      @close="closeCheckQAShow"
    >
      <div style="display: flex; align-items: center; margin-bottom: 10px">
        <page
          :request="request"
          :list="list"
          class="complaint-list"
          table-type="selection"
          @selectionChange="selectionChange"
        />
      </div>
      <div :style="{'text-align': 'right', width: '100%'}">
        <el-button @click="closeCheckQAShow">取消</el-button>
        <el-button type="primary" @click="CheckQAList(1)">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="addQaCategoryShow"
      title="新增问答"
      :visible.sync="addQaCategoryShow"
      :close-on-click-modal="false"
      @close="closeQaCategoryShow"
    >
      <div style="display: flex; align-items: center; margin-bottom: 10px">
        <page
          :request="request2"
          :list="list"
          class="complaint-list"
          table-type="selection"
          @selectionChange="selectionChange1"
        />
      </div>
      <div :style="{'text-align': 'right', width: '100%'}">
        <el-button @click="closeQaCategoryShow">取消</el-button>
        <el-button type="primary" @click="CheckQAList(2)">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPlanList,
  addOrUpdate,
  getPlanQuestionList,
  deletePlan,
  getQuestionsList,
  addPlanQuestion,
  getCategoryList,
  delQuestion,
  addCategoryOrUpdate,
  deleteCategory,
  categorySort,
  sortQuestion
} from '@/qjjpApi/memberRefund'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import Sortable from 'sortablejs'
import page from '@/components/restructure/page'
export default {
  components: {
    page
  },
  data() {
    return {
      qaCategoryLoading: false,
      iconLoading: false,
      categoryIsSort: false,
      addQaCategoryList: [],
      addQaCategoryShow: false,
      qaCategoryList: [],
      categoryList: [],
      addCategory: {
        id: '',
        name: ''
      },
      addCategoryPop: false,
      planId: 0,
      request: {
        getListUrl: async data => {
          const list = await getQuestionsList({
            planId: this.planId,
            scene: 1,
            ...data
          })
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = [...records]
          }
          const result = {
            data: dataList
          }
          return result
        }
      },
      request2: {
        getListUrl: async data => {
          const list = await getQuestionsList({
            planId: this.planId,
            categoryId: this.categoryId,
            scene: 2,
            ...data
          })
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = [...records]
          }
          const result = {
            data: dataList
          }
          return result
        }
      },
      checkQAForm: {},
      checkQAShow: false,
      index: 0,
      addForm: {
        id: '',
        name: '',
        scenes: [],
        refundStatus: [1, 2, 3],
        siteIds: []
      },
      addForm1: {},
      addRules: {},
      addIconRules: {},
      addIconList: [],
      //   addRules1: {},
      siteIds: [],
      listQuery: {
        keyWord: ''
      },
      keyWord: '',
      planList: [],
      addPop: false,
      iconPop: {},
      checkIconList: [],
      checkQaCategoryList: [],
      categoryId: ''
    }
  },
  computed: {},
  computed: {
    list() {
      return [
        {
          title: 'ID',
          key: 'id',
          width: 200
        },
        {
          title: '问题',
          key: 'name',
          width: 200
        },
        {
          title: '答案',
          width: 200,
          key: 'totalFee',
          render: (h, params) => {
            const refundEntrance = params.data.row.type
            console.info(params.data.row, 'paramsparamsparamsparamsparamsparams')
            if (refundEntrance == 1) {
              return h('span', {}, !params.data.row.url || params.data.row.url == '' ? '-' : params.data.row.url)
            } else {
              return h('span', {}, '富文本类型请打开编辑查看')
            }
          }
        }
      ]
    },
    refundStatusDisable() {
      const e = this.addForm.scenes
      return e.length == 1 && e.includes(2)
    },
    refundStatusDisable1() {
      const e = this.addForm1.scenes
      return e.length == 1 && e.includes('2')
    }
  },
  watch: {
    'addForm.scenes': {
      handler(n, w) {
        console.info(n, w)
        const e = this.addForm.scenes
        console.info(e.length == 1, e.includes(2))
        if (e.length == 1 && e.includes(2)) {
          this.addForm.refundStatus = []
          this.$forceUpdate()
        } else {
          this.addForm.refundStatus = [1, 2, 3]
        }
      },
      deep: true
    },
    planId: {
      handler(n, w) {
        const e = this.addForm1.scenes
        console.info(e.length == 1, e.includes('2'))
        if (e.length == 1 && e.includes('2')) {
          this.addForm1.refundStatus = []
          this.$forceUpdate()
        } else {
          this.addForm1.refundStatus = ['1', '2', '3']
        }
        const a = this.addForm.scenes
        console.info(a.length == 1, a.includes(2))
        if (a.length == 1 && a.includes(2)) {
          this.addForm.refundStatus = []
          this.$forceUpdate()
        } else {
          this.addForm.refundStatus = [1, 2, 3]
        }
      },
      deep: true
    },
    'addForm1.scenes': {
      handler(n, w) {
        console.info(n, w)
        const e = this.addForm1.scenes
        console.info(e.length == 1, e.includes('2'))
        if (e.length == 1 && e.includes('2')) {
          this.addForm1.refundStatus = []
          this.$forceUpdate()
        } else {
          this.addForm1.refundStatus = ['1', '2', '3']
        }
      },
      deep: true
    }
  },
  async mounted() {
    await count_channel_application_list().then(res => {
      if (res.code === 200) {
        this.siteIds = res.data
      }
    })
    this.getData(this.listQuery, (data, index) => {
      this.checkPlan(index)
    })
  },
  methods: {
    scenesChange(e) {
      if (e.length == 1 && e.includes(2)) {
        this.addForm.refundStatus = []
        this.$forceUpdate()
      } else {
        this.addForm.refundStatus = [1, 2, 3]
      }
    },
    scenesChange1(e) {
      if (e.length == 1 && e.includes('2')) {
        this.addForm1.refundStatus = []
        // this.addForm1.refundStatus = ['1', '2', '3']
        this.$forceUpdate()
      } else {
        this.addForm1.refundStatus = ['1', '2', '3']
      }
    },
    checkCategory(id) {
      this.categoryId = id
      this.getQaCategoryList(this.planId)
    },
    getQaCategoryList(id, index) {
      //   let planId = data[index].id
      getPlanQuestionList({
        planId: id,
        categoryId: this.categoryId,
        scene: 2
      }).then(res => {
        this.qaCategoryList = res.data.records
      })
    },
    handleCommand(command) {
      if (command.type == 'del') {
        this.deleteCategorys(command.id)
      } else {
        this.addCategory = JSON.parse(JSON.stringify(command.data))
        this.addCategoryPop = true
      }
    },
    handleCommand1(command) {
      this.$confirm(`确定删除问答？`).then(_res => {
        delQuestion({ planId: this.planId, questionId: command.id }).then(res => {
          if (res.code == 200) {
            this.getQaCategoryList(this.planId)
            this.$message.success('操作成功')
          }
        })
      })
    },
    getCategoryLists(planId, callback) {
      getCategoryList({ planId: planId }).then(res => {
        if (res.code == 200) {
          this.categoryList = res.data
          const tbody = document.querySelector('#categoryList')
          const that = this
          if (this.categoryListsort) {
            this.categoryListsort.destroy()
          }
          this.categoryListsort = new Sortable(tbody, {
            animation: 150,
            sort: true,
            handle: '.el-icon-rank',
            draggable: '.categoryList', // 设置可拖拽行的类名(el-table自带的类名)
            forceFallback: true,
            ghostClass: 'blue-background-class',
            async onEnd(evt) {
              that.categoryIsSort = true
              const arr = JSON.parse(JSON.stringify(that.categoryList))
              const curr = arr.splice(evt.oldIndex, 1)[0]
              arr.splice(evt.newIndex, 0, curr) // 把被拖拽的元素添加到checkQaCategoryList中
              const ids = arr.map(item => item.id)
              categorySort({
                planId: that.planId,
                categoryIds: ids
              })
                .then(res => {
                  that.categoryIsSort = false
                  if (res.code == 200) {
                  }
                })
                .catch(res => {
                  that.categoryIsSort = false
                })
              console.info(ids, 'ids')
              that.$forceUpdate()
            }
          })
          callback && callback()
        }
      })
    },
    restAddCategoryPop() {
      this.addCategory = {
        id: '',
        name: ''
      }
    },
    closeAddCategoryPop() {
      this.restAddCategoryPop()
      this.addCategoryPop = false
    },
    addCategoryOrUpdates() {
      addCategoryOrUpdate({ ...this.addCategory, planId: this.planId }).then(res => {
        if (res.code == 200) {
          const that = this
          this.restAddCategoryPop()
          this.getCategoryLists(this.planId, function() {
            console.info(that.categoryList.length, '123')
            if (that.categoryList.length == 1) {
              that.categoryId = that.categoryList[0]?.id || null
            }
            that.getQaCategoryList(that.planId)
          })
          this.addCategoryPop = false
          this.$message.success('操作成功')
        }
      })
    },
    updataCategorys(data) {},
    deleteCategorys(id) {
      this.$confirm(`确定删除分类？`).then(_res => {
        deleteCategory(id).then(res => {
          if (res.code == 200) {
            const that = this
            this.getCategoryLists(this.planId, function() {
              that.categoryId = that.categoryList[0]?.id || null
              that.getQaCategoryList(that.planId)
            })
            this.$message.success('操作成功')
          }
        })
      })
    },
    deletIcon(id) {
      this.$confirm(`确定删除Icon？`).then(_res => {
        delQuestion({ planId: this.planId, questionId: id }).then(res => {
          if (res.code == 200) {
            this.getIconList(this.planId)
            this.$message.success('操作成功')
          }
        })
      })
    },
    CheckQAList(type) {
      const questionIds = []
      if (type == 1) {
        if (this.addIconList.length + this.checkIconList.length > 20) {
          this.$message.error('icon最大可配置数量为20')
          return
        }
        for (let i = 0; i < this.addIconList.length; i++) {
          questionIds.push(this.addIconList[i].id)
        }
      } else {
        if (this.checkQaCategoryList.length + this.addQaCategoryList.length > 20) {
          this.$message.error('分类下问答最大可配置数量为20')
          return
        }
        for (let i = 0; i < this.addQaCategoryList.length; i++) {
          questionIds.push(this.addQaCategoryList[i].id)
        }
      }

      addPlanQuestion({
        planId: this.planId,
        categoryId: type == 2 ? this.categoryId : null,
        questionIds: questionIds
      }).then(res => {
        if (res.code == 200) {
          this.getIconList(this.planId)
          this.getQaCategoryList(this.planId)
          this.checkQAShow = false
          this.addQaCategoryShow = false
          this.$message.success('操作成功')
        }
      })
    },
    closeCheckQAShow() {
      this.checkQAShow = false
      this.addIconList = []
    },
    closeQaCategoryShow() {
      this.addQaCategoryShow = false
      this.addQaCategoryList = []
    },
    selectionChange(selection) {
      console.info(selection, 'this.checkIconList')
      // const a = []
      // a.push(selection)
      this.addIconList = selection
    },
    selectionChange1(selection) {
      this.addQaCategoryList = selection
    },
    deletePlans(id) {
      this.$confirm(`确定删除方案？`).then(_res => {
        deletePlan(id).then(res => {
          if (res.code == 200) {
            this.$message.success('操作成功')
            // if (this.planList.length == 1) {
            //   location.reload()
            //   return
            // }
            location.reload()
            // this.resetForm()
            // this.getData(this.listQuery, (data, index) => {
            //   //   this.checkPlan(index)
            //   this.getIconList(data, index)
            //   location.reload()
            // })
          }
        })
      })
    },
    addIcon() {
      this.checkQAShow = true
    },
    addQaCategory() {
      this.addQaCategoryShow = true
    },
    getIconList(id, index) {
      //   let planId = data[index].id
      getPlanQuestionList({
        planId: id,
        scene: 1
      }).then(res => {
        this.checkIconList = res.data
        const tbody = document.querySelector('#iconView')
        const that = this
        if (this.iconViewsort) {
          this.iconViewsort.destroy()
        }
        this.iconViewsort = new Sortable(tbody, {
          animation: 150,
          sort: true,
          draggable: '.iconView', // 设置可拖拽行的类名(el-table自带的类名)
          forceFallback: true,
          ghostClass: 'blue-background-class',
          async onEnd(evt) {
            that.iconLoading = true
            const arr = JSON.parse(JSON.stringify(that.checkIconList))
            const curr = arr.splice(evt.oldIndex, 1)[0]
            arr.splice(evt.newIndex, 0, curr) // 把被拖拽的元素添加到checkQaCategoryList中
            const ids = arr.map(item => item.id)
            sortQuestion({ planId: that.planId, planQuestionIds: ids })
              .then(res => {
                that.iconLoading = false
              })
              .catch(res => {
                that.iconLoading = false
              })
            console.info(ids, 'ids')
            that.$forceUpdate()
          }
        })
      })
    },
    getQaCategoryList(id, index) {
      console.info(Sortable, 'Sortable')
      // new Sortable(example1, {
      //   animation: 150,
      //   ghostClass: 'blue-background-class'
      // })
      //   let planId = data[index].id
      getPlanQuestionList({
        planId: id,
        categoryId: this.categoryId,
        scene: 2
      }).then(res => {
        this.checkQaCategoryList = res.data
        const tbody = document.querySelector('#qaCategoryListView')
        const that = this
        if (this.planQuestionsort) {
          this.planQuestionsort.destroy()
        }
        this.planQuestionsort = new Sortable(tbody, {
          animation: 150,
          sort: true,
          handle: '.el-icon-rank',
          draggable: '.qaCategoryList', // 设置可拖拽行的类名(el-table自带的类名)
          forceFallback: true,
          ghostClass: 'blue-background-class',
          async onEnd(evt) {
            that.qaCategoryLoading = true
            const arr = JSON.parse(JSON.stringify(that.checkQaCategoryList))
            const curr = arr.splice(evt.oldIndex, 1)[0]
            arr.splice(evt.newIndex, 0, curr) // 把被拖拽的元素添加到checkQaCategoryList中
            const ids = arr.map(item => item.id)
            sortQuestion({ planId: that.planId, planQuestionIds: ids })
              .then(res => {
                that.qaCategoryLoading = false
              })
              .catch(res => {
                that.qaCategoryLoading = false
              })
            console.info(ids, 'ids')
            that.$forceUpdate()
          }
        })
      })
    },
    submitMainForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          addOrUpdate({
            ...this.addForm1,
            refundStatus: this.refundStatusDisable1 ? ['1', '2', '3'] : this.addForm1.refundStatus
          }).then(res => {
            if (res.code === 200) {
              this.$message.success('操作成功')
              //   location.reload()
              this.getData(this.listQuery)
            } else {
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          addOrUpdate({
            ...this.addForm,
            refundStatus: this.refundStatusDisable ? ['1', '2', '3'] : this.addForm.refundStatus
          }).then(res => {
            if (res.code === 200) {
              this.$message.success('操作成功')
              //   this.resetForm()
              //   this.getData(this.listQuery)
              location.reload()
            } else {
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm() {
      this.addForm = {
        id: '',
        name: '',
        scenes: [],
        refundStatus: [1, 2, 3],
        siteIds: []
      }
      this.addPop = false
    },
    addPlan() {
      this.addPop = true
    },
    search() {
      this.getData(this.listQuery)
    },
    getData(listQuery, callback) {
      getPlanList(listQuery).then(res => {
        if (res.code == 200 && res.data.length != 0) {
          this.index = 0
          this.planList = res.data
          console.info(res.data, 'res.data')
          this.$forceUpdate()

          //   this.addForm = res.data[this.index]
          callback && callback(res.data, this.index)
        } else {
          this.planList = []
        }
      })
    },

    checkPlan(index, indexPath) {
      const that = this
      this.planId = this.planList[index].id
      this.addForm1 = JSON.parse(JSON.stringify(this.planList[index]))
      this.getIconList(this.planId, index)
      this.getCategoryLists(this.planId, function() {
        that.categoryId = that.categoryList[0]?.id || null
        that.getQaCategoryList(that.planId)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.checkCategory {
  color: #409eff;
}
#categoryList {
  // width: 500px;
  // overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
}
.qaCategoryList {
  // cursor: pointer;
  width: 400px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
.categoryName {
  font-weight: 600;
  font-size: 17px;
  vertical-align: middle;
  display: inline-block;
  max-width: 140px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.categoryList {
  cursor: pointer;
  display: inline-block;
  margin-right: 20px;
  vertical-align: middle;
  font-size: 20px;
}
.addCategory {
  width: 70px;
  height: 30px;
  // background-color: ;
}
.iconView {
  cursor: pointer;
  position: relative;
  width: 80px;
  //   height: 80px;
  // border: 1px solid gray;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  margin: 0 20px 10px;
  .iconDelet {
    font-size: 15px;
    position: absolute;
    right: 5px;
    top: 5px;
  }
  div {
    line-height: 20px;
  }
  img {
    width: 70px;
    height: 70px;
    //   margin-bottom: 10px;
  }
}
.delet {
  top: 18px;
  position: absolute;
  right: 0;
}
.deletTips {
  top: 0px;
  position: absolute;
  right: 40px;
}
.addIcon {
  width: 80px;
  height: 80px;
  display: inline-block;
  vertical-align: middle;
  border: 1px dashed gray;
  text-align: center;
  margin: 0 20px 10px;
  i {
    font-size: 30px;
    // columns: #409eff;
    font-weight: 400;
    line-height: 80px;
  }
}
.addIcon:hover {
  border: 1px dashed #409eff;
}
.line {
  width: 100%;
  height: 1px;
  border: 1px solid gray;
  margin: 30px 0;
}
.addTips {
  padding: 10px 10px 20px 10px;
  display: flex;
  justify-content: space-between;
  span {
    font-size: 22px;
    font-weight: 500;
  }
}
.planList {
  display: inline-block;
  vertical-align: top;
  width: 300px;
  height: 820px;
  padding-bottom: 60px;
  border: 1px solid gray;
}
.menu::-webkit-scrollbar {
  width: 4px;
}
/* 隐藏 IE 和 Edge 浏览器的滚动条 */
.menu::-ms-scrollbar {
  width: 4px;
}
.menu {
  height: 700px;
  overflow-y: scroll;
  overflow-x: hidden;
}
.mainView {
  display: inline-block;
  vertical-align: top;
  width: 1500px;
  min-height: 1000px;
  border: 1px solid gray;
  padding: 30px;
  .el-form-item {
    margin-bottom: 10px;
  }
}
.main {
  display: flex;
  justify-content: space-around;
}
</style>
