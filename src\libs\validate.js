/**
 * Created by jade on 16/11/5.
 */

/* 手机号验证*/
export function validateMobile(rule, value, callback) {
  const reg = /^1\d{10}$/
  if (!value) {
    callback(new Error('请输入手机号'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入正确的手机号码'))
  } else {
    callback()
  }
}

/* 手机验证码*/
export function validateCode(rule, value, callback) {
  const reg = /^[1-9]\d*|0$/
  if (!value) {
    callback(new Error('请输入验证码'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入正确的验证码'))
  } else {
    callback()
  }
}

/* 手机号验证*/
export function validateIdCard(rule, value, callback) {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (!value) {
    callback(new Error('请输入身份证号码'))
  } else if (!reg.test(value)) {
    callback(new Error('请输入正确的身份证号码'))
  } else {
    callback()
  }
}

export function isvalidUsername(str) {
  const validMap = ['admin', 'editor']
  return validMap.indexOf(str.trim()) >= 0
}

/* 合法uri*/
export function validateURL(textval) {
  const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return urlregex.test(textval)
}

/* 小写字母*/
export function validateLowerCase(str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/* 大写字母*/
export function validateUpperCase(str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/* 大小写字母*/
export function validateAlphabets(str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/* 年化利率 0.1~0.24*/
export function validateYear(rule, value, callback) {
  const reg = /^[0]+(\.[1-2][0-4]{1,2})?$/
  if (!value) {
    callback(new Error('请输入年化利率'))
  } else if (!reg.test(value)) {
    callback(new Error('年化利率在0.10~0.24之间'))
  } else {
    callback()
  }
}

/**
 * validate email
 * @param email
 * @returns {boolean}
 */
export function validateEmail(email) {
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return re.test(email)
}

export const commonReg = (reg, msg) => {
  return (rule, value, callback) => {
    if (!(reg.test(value))) {
      callback(new Error(msg))
    } else {
      callback()
    }
  }
}

export function regular(type) {
  switch (type) {
    case 'phone': // 手机号码
      return commonReg(/^1\d{10}$/, '请输入正确的电话号码')
    case 'ID': // 身份证
      return commonReg(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, '请输入正确的身份证号')
    case 'pwd': // 密码以字母开头，长度在6~16之间，只能包含字母、数字和下划线
      return commonReg(/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/, '请输入正确的密码')
    case 'email': // 邮箱
      return commonReg(/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/, '请输入正确的邮箱')
    case 'URL': // 网址
      return commonReg(/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/, '请输入正确的网址')
    case 'IP': // IP
      return commonReg(/((?:(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d))/, '请输入正确的IP')
    case 'number': // 数字
      return commonReg(/^(\d)*$/, '请输入正确的数字')
    case 'float': // 浮点数
      return commonReg(/^\d+(\.\d+)?$/, '请输入正确的数字')
    case 'fullName': // 姓名
      return commonReg(/^([a-zA-Z0-9\u4e00-\u9fa5\·]{1,10})$/, '请输入正确的姓名')
    case 'percentage':
      return commonReg(/^(100|[1-9]?\d(\.\d\d?)?)%$/, '请输入百分数如10%')
    case 'floatingNum':
      return commonReg(/^\d+\.\d{1,2}$/, '输入最多2位小数且是数字')
    case 'floatingPoint':
      return commonReg(/0\.[0-9]+/, '请输入大于0小于1的小数')
    case 'mechanismPwd':
      return commonReg(/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{6,18}$/, '请输入6~18位需要包含字母,数字,特殊字符')
    case 'mechanismUsn': // 账号以字母开头，长度在6~18之间，只能包含字母、数字和下划线
      return commonReg(/^.{6,18}$/, '请输入6~18位字符')
    case 'mechanismFullName': // 姓名
      return commonReg(/^([\u4E00-\u9FA5]{2,8})$/, '请输入正确的姓名')
    case 'couponIds': // 优惠券Id,用","隔开,只能输入数字
      return commonReg(/^[0-9][0-9,]*$/, '优惠券ID只能输入数字,多个ID用","隔开')
    default:
      console.error('No times regular')
      return true
  }
}
