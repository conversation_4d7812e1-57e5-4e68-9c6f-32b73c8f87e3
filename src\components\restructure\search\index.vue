<template>
  <div>
    <template v-if="!basics.isArrNull(seachFormData)">
      <SForm ref="formNode" :form-item-list="formData" :display-state="true" label-width="">
        <template slot="formContainer">
          <!-- H5正向链路统计筛选情况修改  新增一行-->
          <slot name="searchContainerBefor" />
          <!-- H5正向链路统计筛选情况修改 -->
          <el-form-item>
            <el-button class="filter-item" size="small" type="primary" plain icon="el-icon-search" @click="search">{{ searchButtonText }}</el-button>
          </el-form-item>
          <slot name="searchContainer" />
          <!-- 新增扩展插槽 添加多列选择table显示隐藏 -->
          <slot name="searchLine" />
          <slot name="othersSearchLine" />
        </template>
      </SForm>
    </template>
    <template v-else>
      <el-form ref="ruleForm" :inline="true" class="demo-ruleForm" size="small">
        <slot name="searchContainer" />
      </el-form>
    </template>
  </div>
</template>

<script>
import SForm from '../form'
import { Message } from 'element-ui'
import { copy } from '@/config/basicsMethods'

export default {
  name: 'Index',
  components: {
    SForm
  },
  props: {
    seachFormData: {
      type: Array,
      default: () => []
    },
    searchButtonText: {
      type: String,
      default: '搜索'
    }
  },
  data() {
    return {
      formData: copy(this.seachFormData)
    }
  },
  watch: {
    seachFormData() {
      this.formData = copy(this.seachFormData)
    }
  },
  methods: {
    search() {
      this.$refs.formNode.submitForm().then(data => {
        this.$emit('handleSearch', data)
      }, msg => {
        Message({
          message: '格式填写错误',
          type: 'error'
        })
      }).catch(msg => {
        error(msg)
      })
    }
  }
}
</script>

<style scoped>

</style>
