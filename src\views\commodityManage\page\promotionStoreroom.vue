<template>
  <!--  商品库 -->
  <div>
    <el-row type="flex" class="row-bg" justify="end" style="margin: 0 0 20px;">
      <!-- <el-button type="warning" size="small" icon="el-icon-download" plain>导出数据</el-button> -->
      <el-button
        type="primary"
        size="small"
        icon="el-icon-refresh"
        plain
        @click="synThirdGoods()"
      >同步全部数据</el-button>
    </el-row>
    <section>
      <div class="tab-head">
        <span class="title">数据列表</span>
      </div>
      <el-table
        v-loading="DataLoading"
        :data="tableData"
        border
        style="width: 100%;margin-bottom: 30px;"
      >
        <el-table-column label="序列" type="index" />
        <el-table-column prop="goodsNo" label="产品编号" />
        <el-table-column prop="name" label="商品名称" />
        <el-table-column prop="price" label="价格" />
        <el-table-column prop="officialPrice" label="原价" />
        <el-table-column label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.sellStatus=='up'" style="color:#1ABC9C">正常</span>
            <span v-if="scope.row.sellStatus=='down'" style="color:#FF7F50">已下架</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 70, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </section>
  </div>
</template>

<script>
import { getPromotionStoreroomApi, synThirdGoods } from '@/api/goods'
export default {
  data() {
    return {
      parameterObj: {
        phone: ''
      },
      DataLoading: false,
      tableData: [1],
      total: 0,
      pageSize: 10,
      currentPage: 1
    }
  },
  created() {
    this.getData()
  },
  methods: {
    /**
     * 同步商品
     */
    synThirdGoods() {
      this.DataLoading = true
      synThirdGoods().then(res => {
        this.DataLoading = false
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '同步成功'
          })
          this.currentPage = 1
          this.getData()
        }
      })
    },
    /**
     * 获取数据
     */
    getData() {
      this.DataLoading = true
      getPromotionStoreroomApi({
        pageNumber: this.currentPage,
        pageSize: this.pageSize
      }).then(res => {
        this.DataLoading = false
        if (res.code == 200) {
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    }
  }
}
</script>

<style scoped>
</style>
