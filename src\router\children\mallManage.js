/*
* 活动管理
* */

const mallManage = [
  {
    path: '/mallManage/mallTypeList',
    name: 'mallTypeList',
    meta: {
      title: '商城类型管理'
    },
    component: () => import('@/views/mallManage/page/mallTypeList')
  },
  {
    path: '/mallManage/mallCommodityList',
    name: 'mallCommodityList',
    meta: {
      title: '商城商品管理'
    },
    component: () => import('@/views/mallManage/page/mallCommodityList')
  },
  {
    path: '/mallManage/mallCommodityDetail',
    name: 'mallCommodityDetail',
    meta: {
      title: '商城商品详情',
      activeMenu: '/mallManage/mallCommodityList',
      parentTitle: '商城商品管理'
    },
    component: () => import('@/views/mallManage/page/mallCommodityDetail')
  },
  {
    path: '/mallManage/mallShowWindowList',
    name: 'mallShowWindowList',
    meta: {
      title: '商城橱窗管理'
    },
    component: () => import('@/views/mallManage/page/mallShowWindowList')
  },
  {
    path: '/mallManage/mallShowWindowDetail',
    name: 'mallShowWindowDetail',
    meta: {
      title: '商城橱窗详情',
      activeMenu: '/mallManage/mallShowWindowList',
      parentTitle: '商城橱窗管理'
    },
    component: () => import('@/views/mallManage/page/mallShowWindowDetail')
  },
  {
    path: '/mallManage/mallBannerList',
    name: 'mallBannerList',
    meta: {
      title: '商城Banner管理'
    },
    component: () => import('@/views/mallManage/page/mallBannerList')
  },
  {
    path: '/mallManage/mallBannerDetail',
    name: 'mallBannerDetail',
    meta: {
      title: '商城Banner详情',
      activeMenu: '/mallManage/mallBannerList',
      parentTitle: '商城Banner管理'
    },
    component: () => import('@/views/mallManage/page/mallBannerDetail')
  },
  {
    path: '/mallManage/banner_Statistics',
    name: 'mallManage_banner_Statistics',
    meta: {
      title: 'banner点击统计'
    },
    component: () => import('@/views/mallManage/page/dataStatistics/banner_Statistics')
  },
  {
    path: '/mallManage/mallPopupList',
    name: 'mallPopupList',
    meta: {
      title: '商城弹窗管理'
    },
    component: () => import('@/views/mallManage/page/mallPopupList')
  },
  {
    path: '/mallManage/mallPopupDetail',
    name: 'mallPopupDetail',
    meta: {
      title: '商城弹窗详情',
      activeMenu: '/mallManage/mallPopupList',
      parentTitle: '商城弹窗管理'
    },
    component: () => import('@/views/mallManage/page/mallPopupDetail')
  },
  {
    path: '/mallManage/mallRecommendGoods',
    name: 'mallRecommendGoods',
    meta: {
      title: '商城推荐商品'
    },
    component: () => import('@/views/mallManage/page/mallRecommendGoods')
  },
  {
    path: '/mallManage/mallRecommendGoodsDetail',
    name: 'mallRecommendGoodsDetail',
    meta: {
      title: '商城推荐商品详情',
      activeMenu: '/mallManage/mallRecommendGoods',
      parentTitle: '商城推荐商品'
    },
    component: () => import('@/views/mallManage/page/mallRecommendGoodsDetail')
  }
]

export default mallManage
