<template>
  <div>
    <page table-title="渠道匹配" :request="request" :list="list">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handUpload">导出数据</el-button>
        <el-button plain type="primary" size="small" @click="goEdit"> 添加关联 </el-button>
      </div>
    </page>
  </div>
</template>
<script>
import page from '@/components/restructure/page'
import { formItemType, tableItemType } from '@/config/sysConfig'
import moment from 'moment'
import {
  channelMatchList, refreshChannelData, deleteAccountChannelData, EXPORT_PAGE_MEDIA, proxyConfigSelector
} from '@/api/deliveryManage'

export default {
  name: 'ChannelMatch',
  components: { page },
  data() {
    return {
      listQuery: {
        pageSize: 10,
        pageNumber: 1,
        channelCode: '',
        mediaAccount: '',
        startDate: moment().format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: data => {
          return this.getData(data)
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '账号',
          key: 'mediaAccount',
          clearable: true,
          type: formItemType.input,
          search: true
        },
        {
          title: '渠道',
          key: 'channelCode',
          clearable: true,
          type: formItemType.input,
          search: true
        },
        {
          title: '代理名称',
          key: 'proxyName',
          type: formItemType.input,
          search: true,
          clearable: true,
          tableHidden: true,
          formHidden: true
        },
        {
          title: '代理名称',
          key: 'proxyName',
          type: formItemType.select,
          clearable: true,
          requestList: proxyConfigSelector,
          listFormat: {
            label: 'proxyName',
            value: 'proxyName'
          },
          options: {
            placeholder: '请选择'
          }
        },
        {
          title: '创建时间',
          titleHidden: true,
          key: 'date',
          type: formItemType.rangeDatePicker,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [this.listQuery.startDate, this.listQuery.endDate],
          search: true,
          clearable: true,
          formHidden: true,
          tableHidden: true,
          pickerDay: 31
        },
        {
          title: '消耗',
          key: 'cost'
        },
        {
          title: '最后编辑',
          key: 'updateTime',
          render: (h, params) => {
            const data = params.data.row
            return h(
              'span',
              data.updateTime
                ? moment(data.updateTime).format('YYYY-MM-DD HH:mm:ss')
                : '--'
            )
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '刷新',
              key: 'refresh',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                refreshChannelData({ mediaAccount: params.mediaAccount, channelCode: params.channelCode }).then(
                  res => {
                    this.$store.dispatch('tableRefresh', this)
                  }
                )
              }
            },
            {
              text: '编辑',
              key: 'edit1',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.$router.push({
                  path: '/deliveryManage/channelMatchEdit',
                  query: {
                    mediaAccountId: params.mediaAccountId
                  }
                })
              }
            },
            {
              text: '删除',
              key: 'edit',
              theme: 'danger',
              click: ($index, item, params) => {
                this.$confirm('是否删除该关联数据, 是否继续?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                })
                  .then(() => {
                    deleteAccountChannelData({ crawlerMediaId: params.mediaAccountId, channelCode: params.channelCode }).then(val => {
                      if (val.code == 0) {
                        this.$message({
                          type: 'success',
                          message: '删除成功!'
                        })
                        this.$store.dispatch('tableRefresh', this)
                      }
                    })
                  })
                  .catch(() => {
                    this.$message({
                      type: 'info',
                      message: '已取消删除'
                    })
                  })
              }
            }
          ]
        }
      ]
    }
  },
  activated() {
    this.$store.dispatch('tableRefresh', this)
  },
  methods: {
    goEdit() {
      this.$router.push({
        path: '/deliveryManage/channelMatchEdit'
      })
    },
    handUpload() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_PAGE_MEDIA({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    getData(data) {
      return channelMatchList({ ...this.listQuery, ...data }).then(
        res => res
      )
    }
  }
}
</script>
