import { del, get, post } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/**
 * 渠道类型列表(不分页)
 */
export const GET_PUSH_LIST = obj => {
  return get('/jgpush/getPageList', obj)
}

/**
 * 渠道统计列表导出
 */
export const EXPORT_CHANNEL_STATICS = data =>
  CONSTANT.publicPath + '/v1/channel/count/export?' + qs.stringify(data)

/**
 * 渠道会员卡新增
 * */
export const ADD_JGPUSH = obj => {
  return post('/jgpush/add', obj, null)
}

/**
 * 渠道会员卡新增
 * */
export const GET_JGPUSH_BY_ID = id => {
  return get(`/jgpush/getJgPushDetails/${id}`, null, null)
}

/**
 * 渠道会员卡新增
 * */
export const ADD_JGPUSH_EXCEL = obj => {
  return post('/jgpush/addExcel', obj, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 渠道会员卡新增
 * */
export const DEL_PUSH = id => {
  return del(`/jgpush/deletedJgPush/${id}`, {}, {})
}
