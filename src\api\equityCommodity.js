import { put, get, post } from '@/libs/axios.package'

/** ----- 权益商品统计 ----- */
// 权益商品统计-按日统计
export const goods_statisticsByDay = obj => { return get('/goods/statistics/statisticsByDay', obj) }

// 权益商品统计-按商品分类统计
export const goods_statisticsByGoodsType = obj => { return get('/goods/statistics/statisticsByGoodsType', obj) }

// 权益商品统计-按商品分类统计详情
export const goods_statisticsByGoodsTypeDetail = obj => { return get('/goods/statistics/statisticsGoodsTypeDetail', obj) }

// 权益商品统计-按商品统计
export const goods_statisticsByGoods = obj => { return get('/goods/statistics/statisticsByGoods', obj) }

// 权益商品统计--按商品统计详情
export const goods_statisticsGoodsDetail = obj => { return get('/goods/statistics/statisticsGoodsDetail', obj) }

/** ----- 权益商品列表 ----- */
// 商品库列表
export const goods_virtual_library = obj => { return get('/goods/virtual/library', obj) }
// 更新同步
export const goods_chengquan_insertOrUpdate = obj => { return get('/goods/chengquan/insertOrUpdate', obj) }

// 权益商品列表
export const goods_virtual_list = obj => { return get('/goods/virtual/list', obj) }

// 权益商品列表 (权益上下架统计)
export const goods_virtual_count = () => { return get('/goods/virtual/count', null) }

// 权益商品添加/编辑（id 有值为更新）
export const goods_virtual_add = obj => { return post('/goods/virtual/add', obj) }

// 权益商品详情
export const goods_virtual_getGoodsVirtualInfo = obj => { return get('/goods/virtual/getGoodsVirtualInfo', obj) }

/** ----- 权益商品分类管理 ----- */

// 权益商品分类管理列表
export const goods_virtual_category_list = obj => { return get('/goods/virtual/category/list', obj) }

// 权益商品分类添加
export const goods_virtual_category_add = obj => { return post('/goods/virtual/category/add', obj) }

// 权益商品分类更新
export const goods_virtual_category_update = obj => { return post('/goods/virtual/category/update', obj) }

/** ----- 供货商管理 ----- */

// 供货商列表
export const goods_virtual_supplier_list = obj => { return get('/goods/virtual/supplier/list', obj) }

// 供货商添加
export const goods_virtual_supplier_add = obj => { return post('goods/virtual/supplier/add', obj) }

// 供货商更新
export const goods_virtual_supplier_update = obj => { return post('/goods/virtual/supplier/update', obj) }

/** ------   权益商品订单类型管理   --------- */

//
export const goods_virtual_order_type_post = obj => { return post('/goods/virtual/order/type', obj) }

// 编辑
export const goods_virtual_order_type_put = obj => { return put('/goods/virtual/order/type', obj) }

// 添加
export const goods_virtual_order_type_page_get = obj => { return get('/goods/virtual/order/type/page', obj) }

// 查看权益商品
export const goods_virtual_order_type_get = obj => { return get('/goods/virtual/order/type', obj) }

// 获取所有订单分类
export const goods_virtual_order_type_all_get = obj => { return get('/goods/virtual/order/type/all', obj) }
