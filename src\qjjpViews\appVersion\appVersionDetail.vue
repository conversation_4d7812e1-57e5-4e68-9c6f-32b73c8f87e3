<template>
  <div>
    <h1 class="page-title">{{ typeFmt }}版本</h1>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="300px"
      class="demo-ruleForm"
    >
      <el-form-item label="应用类型" prop="os">
        <el-select v-model="ruleForm.os" :disabled="type === 'edit'" placeholder="请选择应用名称" @change="siteIdChange">
          <el-option
            v-for="(item, index) in osList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!os" label="选择应用名称" prop="siteId">
        <el-select v-model="ruleForm.siteId" placeholder="请选择应用名称" @change="siteIdChange">
          <el-option
            v-for="(item, index) in siteIds"
            :key="index"
            :label="item.name"
            :value="item.siteId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="版本号" prop="version">
        <el-input
          v-model="ruleForm.version"
          style="width: 300px"
          placeholder="请输入版本号"
          :disabled="type === 'edit'"
        />
      </el-form-item>
      <el-form-item label="版本描述" prop="description">
        <el-input
          v-model="ruleForm.description"
          type="textarea"
          style="width: 300px"
          :rows="6"
          placeholder="请输入版本描述"
        />
      </el-form-item>
      <el-form-item v-if="!!os" label="版本名称" prop="description">
        <el-input
          v-model="ruleForm.name"
          type="textarea"
          style="width: 300px"
          :rows="6"
          placeholder="请输入版本描述"
        />
      </el-form-item>
      <el-form-item v-if="!os" label="选择应用市场/渠道" prop="relationCode">
        <el-select v-model="ruleForm.relationCode" placeholder="请选择应用市场/渠道">
          <el-option
            v-for="(item, index) in appCodeList"
            :key="index"
            :label="item.title"
            :value="item.appCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!!os" label="选择应用市场/渠道" prop="relationCode">
        <el-radio-group v-model="ruleForm.relationCode">
          <el-radio label="store_ios">APP Store</el-radio>
          <el-radio label="sign_ios">IOS签名包</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否更新" prop="setForce">
        <el-radio-group v-model="ruleForm.setForce">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否强更" prop="setUpdate">
        <el-radio-group v-model="ruleForm.setUpdate">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!!os" label="首页" prop="isIndex">
        <el-radio-group v-model="ruleForm.isIndex">
          <el-radio :label="1">审核页</el-radio>
          <el-radio :label="0">用户页</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!!os" label="个人中心" prop="isPersonal">
        <el-radio-group v-model="ruleForm.isPersonal">
          <el-radio :label="1">审核页</el-radio>
          <el-radio :label="0">用户页</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="ruleForm.status">
          <el-radio :label="0">启用</el-radio>
          <el-radio :label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="ruleForm.os!=1" label="上传APK文件：" prop="upload">
        <!-- <uploader
          ref="uploader"
          :options="options"
          class="uploader-example"
          :file-status-text="statusText"
          @file-complete="fileComplete"
          @file-removed="fileRemoved"
          @file-added="checkFile"
        >
          <uploader-unsupport />
          <uploader-drop>
            <p>拖拽文件上传或者</p>
            <uploader-btn :attrs="attrs">选择文件</uploader-btn>
          </uploader-drop>
          <uploader-list />
        </uploader> -->
        <el-upload
          class="upload-demo"
          :action="uploadUrl"
          :headers="headers"
          :before-upload="befo"
          :on-success="succee"
          :data="fileQuery"
          multiple
          :limit="1"
          :file-list="fileList"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
          <!-- <el-button size="small" type="primary">点击上传</el-button> -->
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handSubmit('ruleForm')">立即创建</el-button>
        <el-button @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  ADD_ANDROID_SAVE,
  GET_ANDROID_DETAILS_BY_ID,
  GET_CHANNEL_LIST,
  apk2,
  ADD_IOS_SAVE,
  PUT_IOS_SAVE,
  GET_IOS_DETAILS_BY_ID
} from '@/qjjpApi/appVersion'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import { osList } from '@/qjjpViews/appVersion/basicParams'
export default {
  components: {},
  props: {},
  data() {
    return {
      fileList: [],
      os: '',
      type: '',
      id: '',
      ruleForm: {
        os: '',
        version: '',
        description: '',
        relationCode: '',
        setUpdate: '',
        setForce: '',
        linkUrl: '',
        extendUrl: '',
        name: '',
        isIndex: 1,
        status: '',
        isPersonal: 1,
        siteId: ''
      },
      appCodeList: [],
      rules: {
        os: [
          { required: true, message: '应用类型不能为空', trigger: 'change' }
        ],
        siteId: [
          { required: true, message: '应用名称不能为空', trigger: 'change' }
        ],
        version: [
          { required: true, message: '版本号不能为空', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '版本描述不能为空', trigger: 'blur' }
        ],
        relationCode: [
          {
            required: true,
            message: '应用市场/渠道不能为空',
            trigger: 'change'
          }
        ],
        setUpdate: [
          { required: true, message: '请选择是否更新', trigger: 'change' }
        ],
        setForce: [
          { required: true, message: '请选择是否强更', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择是否启用', trigger: 'change' }
        ]

      },
      fileQuery: {},
      headers: {},
      uploadUrl: '',
      options: {
        target: `${this.$CONSTANT.publicPath}/cms/upload/apk2`,
        headers: { token: this.$utils.getToken() },
        maxChunkRetries: 1,
        testChunks: false,
        chunkSize: 15 * 1024 * 1024,
        simultaneousUploads: 1,
        query: () => {
          return {
            channelCode: this.ruleForm.relationCode
          }
        }
      },
      statusText: {
        success: '上传成功',
        error: '上传失败',
        uploading: '上传中',
        paused: '暂停中',
        waiting: '等待中'
      },
      attrs: {
        accept: 'application/vnd.android.package-archive'
      },
      canCreate: false,
      siteIds: [],
      osList
    }
  },
  computed: {
    osFmt() {
      if (this.os == 0) {
        return 'android'
      } else {
        return 'ios'
      }
    },
    typeFmt() {
      if (this.type == 'add') {
        return '新增'
      } else {
        return '编辑'
      }
    }
  },
  watch: {},
  mounted() {
    this.uploadUrl = this.$CONSTANT.qjjpPath + '/cms/upload/apk2'
    // 请求头
    this.headers = { Authorization: this.$utils.getToken() }
    this.os = Number(this.$route.query.os)
    this.type = this.$route.query.type
    this.ruleForm.os = this.os === 0 ? 2 : this.os === 1 ? 1 : null
    if (this.type == 'edit') {
      this.id = this.$route.query.id
      if (!this.os) {
        GET_ANDROID_DETAILS_BY_ID(this.id).then(res => {
          this.ruleForm = res.data
          this.canCreate = true
        })
      } else {
        GET_IOS_DETAILS_BY_ID(this.id).then(res => {
          this.ruleForm = res.data
          this.canCreate = true
        })
      }
    }
    if (!this.os) {
      this.$nextTick(() => {
        window.uploader = this.$refs.uploader.uploader
      })
    }
    count_channel_application_list().then(res => {
      if (res.code === 200) {
        this.siteIds = res.data
      }
    })
  },
  methods: {
    siteIdChange() {
      this.getChannelList()
    },
    getChannelList() {
      const obj = {
        status: 0,
        siteId: this.ruleForm.siteId,
        os: this.ruleForm.os
      }
      GET_CHANNEL_LIST(obj).then(res => {
        this.appCodeList = res.data
      })
    },
    checkFile(file) {
      if (this.basics.isNull(this.ruleForm.relationCode)) {
        file.ignored = true
        this.$message.error('请先选择应用市场/渠道！')
      }
    },
    cancel() {
      this.$router.go(-1)
    },
    handSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (!this.os) {
            if (this.canCreate || this.ruleForm.os === 1) {
              if (this.ruleForm.os === 1) {
                this.fileRemoved()// ios不用上传apk文件跟链接
              }
              ADD_ANDROID_SAVE(this.ruleForm).then(res => {
                if (res.code == 200) {
                  this.$message.success(`${this.typeFmt}成功！`)
                  this.$router.go(-1)
                }
              })
            } else {
              this.$message.error('请上传文件！')
              return false
            }
          } else {
            if (this.type == 'add') {
              ADD_IOS_SAVE(this.ruleForm).then(res => {
                if (res.code == 200) {
                  this.$message.success(`${this.typeFmt}成功！`)
                  this.$router.go(-1)
                }
              })
            } else {
              PUT_IOS_SAVE(this.ruleForm).then(res => {
                if (res.code == 200) {
                  this.$message.success(`${this.typeFmt}成功！`)
                  this.$router.push({
                    name: 'appVersionList',
                    query: {
                      os: '1'
                    }
                  })
                }
              })
            }
          }
        } else {
          return false
        }
      })
    },
    // fileComplete(rootFile) {
    //   console.log('file complete', rootFile)
    // }
    succee(res) {
      console.info(res, 'res')
      if (res.code == 200) {
        this.ruleForm.linkUrl = res.data.sourcePath
        this.ruleForm.extendUrl = res.data.channelPath
        this.canCreate = true
      } else {
        this.$message.error(res.message)
      }
    },
    befo(e) {
      console.info(e)
      this.fileQuery = {
        filename: e.name,
        channelCode: this.ruleForm.relationCode
      }
      const promise = new Promise((resolve) => {
        this.$nextTick(function() {
          resolve(true)
        })
      })
      return promise
      // apk2(this.fileQuery).then(res => {
      //   if (res.code == 200) {
      //     this.ruleForm.linkUrl = res.data.sourcePath
      //     this.ruleForm.extendUrl = res.data.channelPath
      //     this.canCreate = true
      //   } else {
      //     this.$message.error(res.message)
      //   }
      // })
      // return
    },
    fileComplete() {
      const file = arguments[0].file
      const obj = {
        filename: file.name,
        identifier: arguments[0].uniqueIdentifier,
        totalSize: file.size,
        type: file.type,
        channelCode: this.ruleForm.relationCode
      }
      apk2(obj).then(res => {
        if (res.code == 200) {
          this.ruleForm.linkUrl = res.data.sourcePath
          this.ruleForm.extendUrl = res.data.channelPath
          this.canCreate = true
        } else {
          this.$message.error(res.message)
        }
      })
    },
    fileRemoved(file) {
      this.ruleForm.linkUrl = ''
      this.ruleForm.extendUrl = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader-example {
  width: 580px;
  padding: 15px;
  font-size: 14px;
  border: 1px dashed #ddd;
  & ::v-deep .uploader-file[status="success"] .uploader-file-remove {
    display: block;
  }
}

.uploader-example .uploader-btn {
  margin-right: 4px;
}

.uploader-example .uploader-list {
  max-height: 440px;
  overflow: auto;
  overflow-x: hidden;
  overflow-y: auto;
}
.page-title {
  font-size: 20px;
  margin: 0 0 15px 0;
}
</style>
