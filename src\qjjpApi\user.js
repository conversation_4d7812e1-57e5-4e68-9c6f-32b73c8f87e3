/*
 * @Author: 陈小豆
 * @Date: 2024-04-29 09:11:58
 * @LastEditors: 陈小豆
 * @LastEditTime: 2025-03-10 18:21:35
 */
import CONSTANT from '@/config/constant.conf'
import { getJjjp, postJjjp } from '@/libs/axios.package'
import qs from 'qs'

// 应用名称列表
export const count_channel_application_list = params => {
  return getJjjp(`/application/vest/list`, params, null)
}
/*
 * 用户列表
 * */
export const user_list = obj => {
  return getJjjp('/user/list', obj)
}
// 编辑或新增商品分类接口
export const crowdProductCategoryEdit = obj => {
  return postJjjp('/crowd-product-category/edit', obj)
}
// 商品类型查询接口
export const crowdProductCategoryList = obj => {
  return getJjjp('/crowd-product-category/list', obj)
}

export const getOrderSourceFrom = params => {
  return getJjjp(`/membershipOrder/getOrderSourceFrom`, params, null)
}
// 承接页统计导出
export const cashbackExportV2 = data => CONSTANT.publicPath + '/count/cashback/export/v2?' + qs.stringify(data)

export const userlistexport = data => CONSTANT.qjjpPath + '/user/list/export?' + qs.stringify(data)
/*
 * 设备列表
 * */
export const device_list = obj => {
  return getJjjp('/user/list', obj)
}
export const devicelistexport = data => CONSTANT.qjjpPath + '/userDevice/export?' + qs.stringify(data)
