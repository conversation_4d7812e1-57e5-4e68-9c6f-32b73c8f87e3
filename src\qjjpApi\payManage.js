/*
 * @Author: 蒋雪 <EMAIL>
 * @Date: 2024-06-13 16:57:13
 * @LastEditors: 陈小豆
 * @LastEditTime: 2025-03-10 14:00:57
 * @FilePath: \qtsh-sale-boss-frontend\src\qjjpApi\payManage.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import CONSTANT from '@/config/constant.conf'
import { getJjjp, postJjjp } from '@/libs/axios.package'
import qs from 'qs'

// 消息风格列表
export const getmembershipTemplate = obj => {
  return getJjjp(`/membershipTemplate/page`, obj)
}
// 编辑或新增商品分类接口
export const crowdProductCategoryEdit = obj => {
  return postJjjp('/crowd-product-category/edit', obj)
}

export const membershipTemplateCreate = obj => {
  return postJjjp('/cms/membershipTemplate/create', obj)
}

export const membershipTemplateUpdate = obj => {
  return postJjjp('/cms/membershipTemplate/update', obj)
}

export const membershipTemplateexport = data => CONSTANT.qjjpPath + '/cms/membershipTemplate/page/export?' + qs.stringify(data)

export const relationshipChannel = obj => {
  return getJjjp(`/cms/membershipTemplate/relationshipChannel`, obj)
}
export const appleProduct = obj => {
  return getJjjp(`/cms/AppleProduct/page`, obj)
}
export const appleProductAdd = obj => {
  return postJjjp(`/cms/AppleProduct/insertOrUpdate`, obj)
}
