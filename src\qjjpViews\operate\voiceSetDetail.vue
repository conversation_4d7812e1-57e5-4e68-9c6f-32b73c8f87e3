<template>
  <div id="voiceSetDetail">
    <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="100px" class="demo-ruleForm">
      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>基础信息</span>
        </div>
        <div style="display: flex;flex-wrap: wrap;">
          <el-form-item label="三方名称" prop="tripartiteName" :rules="addRules.common">
            <div :style="{width: '280px'}">
              <el-input v-model="addForm.tripartiteName" disabled />
            </div>
          </el-form-item>
          <el-form-item label="voice_type" prop="voiceType" :rules="addRules.common">
            <div :style="{width: '280px'}">
              <el-input v-model="addForm.voiceType" disabled />
            </div>
          </el-form-item>
        </div>
        <el-form-item style="width:70%" label="变声名称" prop="name" :rules="addRules.common">
          <el-input
            v-model="addForm.name"
            maxlength="10"
            show-word-limit
            placeholder="请输入变声名称"
          />
        </el-form-item>
        <el-form-item label="头像" class="labelImgItem tipItem" prop="avatar" :rules="addRules.common">
          <uploadFile v-model="addForm.avatar" :limit="1" :size="1024 * 1024" :height="50" :width="170" :send-url="`${$CONSTANT.qjjpPath}/cms/upload/image`" />
          <div class="tipData">*支持png，jpg，jpeg，gif格式，（上传大小1M）</div>
        </el-form-item>
        <div style="display: flex;flex-wrap: nowrap;width:70%">
          <el-form-item
            style="flex-grow: 1;"
            label="示例语音"
            prop="demoText"
            :rules="addRules.common"
          >
            <el-input
              v-model="addForm.demoText"
              type="text"
              placeholder="请输入示例语音"
              maxlength="50"
              show-word-limit
              @change="getVoice"
            />
          </el-form-item>
          <a class="try-voice-btn" :href="addForm.demoUrl" target="_blank">
            试听
            <!-- <audio controls height="40" width="60" style="visibility: hidden;">
              <source :src="addForm.demoUrl" type="audio/mpeg">
              <source :src="addForm.demoUrl" type="audio/ogg">
              您的浏览器不支持该音频格式。
              <embed height="40" width="60" :src="addForm.demoUrl">
            </audio> -->
          </a>
        </div>
        <el-form-item label="展示排序:" prop="sort" :rules="addRules.common">
          <el-input
            v-model.number="addForm.sort"
            style="width:280px"
            placeholder="请输入"
            onkeyup="value=value.replace(/[^\d^]+/g,'')"
            @blur="addForm.sort = $event.target.value"
          />
          <div>输入正整数，数值越大越靠前，数值相同时最近编辑的在前</div>
        </el-form-item>
      </div>
      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>展示规则</span>
        </div>
        <el-form-item label="变声类型" prop="type" :rules="addRules.common">
          <el-select v-model="addForm.type" placeholder="请选择变声类型">
            <el-option
              v-for="item in list1"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="enabled" :rules="addRules.common">
          <el-radio v-model="addForm.enabled" :label="1">启用</el-radio>
          <el-radio v-model="addForm.enabled" :label="0">禁用</el-radio>
        </el-form-item>
      </div>
      <div :style="{'text-align': 'right', width: '100%'}" class="button_view">
        <el-button @click="closeCheckQAShow">取消</el-button>
        <el-button type="primary" @click="handVoiceEdit('addForm')">确认</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { voiceChangerEdit, voiceChangerAudition } from '@/qjjpApi/operate'
export default {
  components: {
    uploadFile: () => import('@/components/yc-upload/handleUploadImage')
  },
  props: {
    curFormData: { type: Object, default: () => ({}) }
  },
  data() {
    return {
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      addForm: {
        id: null,
        tripartiteName: '',
        voiceType: '',
        name: '',
        avatar: '',
        demoText: '',
        demoUrl: '',
        sort: '',
        type: 1,
        enabled: 1
      },
      list1: [{ id: 0, name: '免费' }, { id: 1, name: '付费' }]
    }
  },
  mounted() {
    this.addForm = { ...this.curFormData }
  },
  methods: {
    handVoiceEdit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          voiceChangerEdit(this.addForm).then(res => {
            if (res.code == 200) {
              this.$emit('updateList')
              this.$message.success('编辑成功')
              this.closeCheckQAShow()
            }
          })
        }
      })
    },
    closeCheckQAShow() {
      this.$emit('closeDialog')
    },
    getVoice() {
      const { demoText, id } = this.addForm
      if (!demoText) {
        return
      }
      voiceChangerAudition({ text: demoText, id }).then(res => {
        if (res.data) {
          this.addForm.demoUrl = res.data
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#voiceSetDetail{
    .button_view{
      background-color: #fff;
      //border-top:1px dashed #000000;
      ::v-deep .el-button{
        margin: 0 10px;
      }
    }
    .form_view,.form_view2{
        background-color: #fff;
        width: 100%;
        padding: 10px 0;
        // margin-bottom: 20px;
        .form_view_title{
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #c4c4c4;
            .title_line{
              width: 2px;
              height: 10px;
              background-color:#66b1ff ;
              display: inline-block;
              vertical-align: middle;
            }
            span{
              padding-left: 5px;
              vertical-align: middle;
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
    }
    .form_view2{
      background-color: rgb(189, 184, 184,0.1);
      .form_view_title_view2{
        font-size: 16px !important;
      }
    }
}

::v-deep .el-form-item{
  &.labelImgItem .el-form-item__content{
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  &.tipItem .el-form-item__content{
    display: flex;
    align-items: flex-start;
    flex-direction: column;
  }
}
.try-voice-btn{
  width:60px;
  height:40px;
  margin-left: 5px;
  color: #fff;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  color: #FFF;
  background-color: #409EFF;
  border-color: #409EFF;
  border-radius: 4px;
  &:hover{
    background-color:#66b1ff ;
  }
}
</style>
