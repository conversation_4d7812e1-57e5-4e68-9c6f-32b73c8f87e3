<template>
  <div>
    <page :request="request" :list="list" table-title="渠道列表">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
        <el-button
          plain
          icon="el-icon-circle-plus-outline"
          type="primary"
          size="small"
          @click="handleAdd"
        >新增</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import Clipboard from 'clipboard' // 复制
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { downOrUp } from '@/qjjpApi/statisticsOverview'
import { channelList, channelListexport } from '@/qjjpApi/deliveryManage'
import { count_channel_application_list, undertakeSelector, mediaAll, getAppBindEvents } from '@/qjjpApi/NewChannel'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
import { osListForm } from '@/qjjpViews/appVersion/basicParams'
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      curStatusParams: {
        id: 0,
        status: 0
      },
      list1: [
        {
          id: 1,
          name: '奇迹键盘'
        }
      ],
      list2: [
        {
          id: 1,
          name: 'apk链路'
        },
        {
          id: 2,
          name: 'h5链路'
        }
      ],
      list3: [
        {
          id: 1,
          name: '百度'
        },
        {
          id: 2,
          name: '快手'
        },
        {
          id: 2,
          name: '头条'
        }
      ],
      list4: [
        {
          id: 0,
          name: '启用'
        },
        {
          id: 1,
          name: '禁用'
        }
      ],
      list5: [
        {
          id: 1,
          name: '普通回传（单节点）'
        },
        {
          id: 2,
          name: '关键行为（双节点）'
        },
        {
          id: 3,
          name: '全链路回传（多节点）'
        }
      ],
      list6: [
        {
          id: 0,
          name: '注册'
        },
        {
          id: 1,
          name: '站内购卡'
        }
      ],
      siteIds: [],
      mediaList: [],
      eventsList: [],
      platformCode: null,
      eventsShow: false,
      listQuery: {
      },
      request: {
        getListUrl: async data => {
          await count_channel_application_list().then(res => {
            if (res.code === 200) {
              this.siteIds = res.data
            }
          })
          await mediaAll().then(res => {
            if (res.code === 200) {
              this.mediaList = res.data
            }
          })
          this.listQuery = { ...this.listQuery, ...data }
          const list = await channelList(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        // {
        //   key: 'dateSearch',
        //   title: '日期',
        //   type: formItemType.datePickerDaterangeGai,
        //   options: {
        //     format: 'YYYY-MM-DD',
        //     valueFormat: 'yyyy-MM-dd'
        //   },
        //   childKey: ['startDate', 'endDate'],
        //   formHidden: true,
        //   search: true,
        //   val: [currentDate, currentDate]
        // },

        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIds.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        {
          title: '投放链路',
          key: 'downloadType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '投放平台',
          key: 'apiCode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.mediaList,
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          reg: ['required'],
          clearable: true,
          search: true,
          tableHidden: true,
          options: {
            on: () => {
              return {
                change: e => {
                  this.platformCode = e
                }
              }
            }
          }

        },
        {
          title: '渠道ID',
          key: 'channelCode',
          type: formItemType.input,
          clearable: true,
          search: true,
          tableHidden: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true,
          search: true,
          tableHidden: true
        },
        {
          title: '回传节点',
          key: 'deviceReportType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list5,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true,
          search: true,
          tableHidden: true
        },
        {
          title: '回传媒体事件',
          key: 'keyEvent',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.eventsList,
          listFormat: {
            label: 'eventName',
            value: 'id'
          },
          reg: ['required'],
          clearable: true,
          search: this.eventsShow,
          tableHidden: true
        },
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '渠道id',
          key: 'channelCode'
        },
        {
          title: '应用类型',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: osListForm
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '渠道名称',
          key: 'name'
        },
        {
          title: '投放链接',
          key: 'channelApiDomain',
          render: (h, params) => {
            const monitorLink = params.data.row.channelApiDomain
            return h('el-button', {
              props: {
                type: 'text'
              },
              attrs: {
                'data-clipboard-text': monitorLink
              },
              class: 'copy-btn',
              on: {
                click: () => {
                  this.handCopy()
                }
              }
            }, monitorLink)
          }
        },
        {
          title: '监测链接',
          key: 'monitorLink',
          render: (h, params) => {
            const monitorLink = params.data.row.monitorLink
            return h('el-button', {
              props: {
                type: 'text'
              },
              attrs: {
                'data-clipboard-text': monitorLink
              },
              class: 'copy-btn',
              on: {
                click: () => {
                  this.handCopy()
                }
              }
            }, monitorLink)
          }
        },
        {
          title: '投放链路',
          key: 'downloadType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required']
        },
        {
          title: '投放平台',
          key: 'apiCode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.mediaList,
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '回传节点',
          key: 'deviceReportType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list5,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          clearable: true
        },
        {
          title: '回传事件',
          key: 'keyEventName'
        },
        {
          title: '承接页面',
          key: 'landingPage'
        },
        {
          title: '更新人员',
          key: 'updateAdminName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          tableView: tableItemType.tableView.date,
          options: {
            format: 'YYYY-MM-DD HH:mm:ss'
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          clearable: false
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.$router.push({
                  name: 'qjjpChannelDetail',
                  query: {
                    id: params.id,
                    type: 'edit'
                  }
                })
              }
            },
            {
              text: '复制',
              key: 'fz',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.$router.push({
                  name: 'qjjpChannelDetail',
                  query: {
                    id: params.id,
                    type: 'fz'
                  }
                })
              }
            }
          ]
        }
      ]
    }
  },
  watch: {
    platformCode: {
      handler(val) {
        if (val && this.packageName) {
          getAppBindEvents({ platformCode: this.platformCode, packageSignature: this.packageName }).then(res => {
            if (res.code === 200) {
              this.eventsList = res.data || {}
              this.eventsShow = true
            }
          })
        } else {
          this.eventsShow = false
        }
      }
    },
    packageName: {
      handler(val) {
        if (val && this.platformCode) {
          getAppBindEvents({ platformCode: this.platformCode, packageSignature: this.packageName }).then(res => {
            if (res.code === 200) {
              this.eventsList = res.data || {}
              this.eventsShow = true
            } else {
              this.eventsShow = false
            }
          })
        }
      }
    }
  },
  created() { },
  methods: {
    handCopy() {
      console.info(123)
      const clipboard = new Clipboard('.copy-btn')
      clipboard.on('success', e => {
        this.$message.success('复制链接成功')
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        // 不支持复制
        this.$message.error('该浏览器不支持自动复制')
        // 释放内存
        clipboard.destroy()
      })
    },
    changeSiteId(e) {
      const a = this.siteIds.filter(item => item.siteId == e)
      console.info(a)
      this.packageName = a[0].packageName
    },
    changeApi(e) {

    },
    handleAdd() {
      this.$router.push({
        name: 'qjjpChannelDetail',
        query: {
          type: 'add'
        }
      })
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = channelListexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    changeStatus() {
      const params = this.curStatusParams
      downOrUp({ ...params }).then(res => {
        if (res.code == 200) {
          this.$message.success('更改状态成功')
          this.$store.dispatch('tableRefresh', this)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
