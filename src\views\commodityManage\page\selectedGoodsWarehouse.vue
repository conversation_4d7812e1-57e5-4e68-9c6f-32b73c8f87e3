<template>
  <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
    <el-tab-pane
      v-for="(item, index) in tabs"
      :key="index"
      :label="item.title"
      :name="item.id"
    >
      <page v-if="item.id === activeName" :request="request" :list="list" table-title="商城选品库">
        <div slot="searchContainer" style="display: inline-block;">
          <el-button plain type="success" size="small" icon="el-icon-refresh" @click="handleSynchronousData(item.id)">{{ item.title }}同步商品</el-button>
        </div>
      </page>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { GET_MALL_TYPE_LIST } from '@/api/mall'
import { GET_SYSC, getGoodsPackageApi, getGoodsTableArrayApi } from '@/api/goods'

export default {
  components: {
    page
  },
  data() {
    return {
      tabs: [],
      activeName: '',
      listQuery: {},
      request: {
        getListUrl: data => {
          const obj = {
            ...data,
            mallId: this.activeName
          }
          return getGoodsTableArrayApi(obj)
        }
      },
      userAuthorityList: [
        { label: '游客用户', value: 1 },
        { label: '已参与0元购普通用户', value: 2 },
        { label: '未参与0元购普通用户', value: 3 },
        { label: '已参与0元购 已完成首单红包 爵士会员用户', value: 4 },
        { label: '已参与0元购 未完成首单红包 爵士会员用户', value: 5 },
        { label: '未参与0元购 已完成首单红包 爵士会员用户', value: 6 },
        { label: '未参与0元购 未完成首单红包 爵士会员用户', value: 7 }
      ],
      locationArray: [] // 商品位置列表
    }
  },
  computed: {
    list() {
      return [
        {
          title: '产品编号',
          key: 'itemId',
          type: formItemType.input
        },
        {
          title: '商品库',
          key: 'packageId',
          type: formItemType.select,
          requestList: () => {
            const param = {
              mallId: this.activeName
            }
            return getGoodsPackageApi(param)
          },
          listFormat: {
            label: 'title',
            value: 'id'
          },
          search: true,
          tableHidden: true
        },
        {
          title: '预览图',
          key: 'coverPic',
          type: formItemType.upload,
          tableView: tableItemType.tableView.picture
        },
        {
          title: '商品名称',
          key: 'title',
          type: formItemType.input,
          search: true,
          searchKey: 'goodsTitle'
        },
        {
          title: '原价',
          key: 'originalPrice',
          type: formItemType.input
        },
        {
          title: '优惠券',
          key: 'couponPrice',
          type: formItemType.input
        },
        {
          title: '佣金',
          key: 'commission',
          type: formItemType.input
        }
      ]
    }
  },
  mounted() {
    const localActive = sessionStorage.getItem('selectedGoodsWarehouseTabActive')
    if (localActive) {
      this.activeName = Number(sessionStorage.getItem('selectedGoodsWarehouseTabActive'))
    }
    this.getTypeList()
  },
  methods: {
    handleClick(tab) {
      sessionStorage.setItem('selectedGoodsWarehouseTabActive', tab.name)
    },
    getTypeList() {
      GET_MALL_TYPE_LIST().then(res => {
        this.tabs = res.data
        if (res.data.length > 0 && !sessionStorage.getItem('selectedGoodsWarehouseTabActive')) {
          this.activeName = res.data[0].id
        }
      })
    },

    handleSynchronousData(status) {
      GET_SYSC(status)
    }
  }
}
</script>

<style scoped>
</style>
