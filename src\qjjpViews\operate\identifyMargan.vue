<template>
  <page ref="styleList" :request="request" :list="list" table-title="风格管理">
    <div slot="searchContainer" style="display: inline-block">
      <!-- <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button> -->
      <el-button
        plain
        icon="el-icon-circle-plus-outline"
        type="primary"
        size="small"
        @click="handleAdd('add')"
      >新增分类</el-button>
    </div>
  </page>
</template>
<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { knowImageConfigPage, getKnowCategoryTypeList, knowImageConfigGetBots, modelRepositoryselect, modelRepositoryplatform } from '@/qjjpApi/operate'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'identify<PERSON>argan',
  components: {
    page
  },
  data() {
    return {
      listQuery: {
      },
      categoryTypeList: [],
      botIdList: [],
      platformList: [],
      modelRepositoryList: [],
      personList: [
        {
          label: '性格特点',
          value: 0
        },
        {
          label: '兴趣爱好',
          value: 1
        },
        {
          label: '聊天话题',
          value: 2
        }
      ],
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          if (this.categoryTypeList.length == 0) {
            const { data: categoryTypeList } = await getKnowCategoryTypeList().catch(() => ({ data: [] }))
            if (categoryTypeList) {
              this.categoryTypeList = categoryTypeList
            }
          }
          if (this.botIdList.length == 0) {
            const { data: botIdList } = await knowImageConfigGetBots().catch(() => ({ data: [] }))
            if (botIdList) {
              this.botIdList = botIdList
            }
          }

          const list = await knowImageConfigPage(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        // {
        //   title: '模型平台',
        //   key: 'platform',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.siteIds,
        //   listFormat: {
        //     label: 'name',
        //     value: 'code'
        //   },
        //   val: this.listQuery.siteId,
        //   reg: ['required'],
        //   search: true,
        //   clearable: true,
        //   tableHidden: true
        // },
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '所属分类',
          key: 'categoryType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.categoryTypeList,
          listFormat: {
            label: 'desc',
            value: 'code'
          },
          // reg: ['required'],
          search: true,
          clearable: true
        },
        {
          title: '分类名称',
          key: 'categoryName',
          type: formItemType.input,
          search: true,
          clearable: true
        },
        {
          title: '分类描述',
          key: 'categoryDescription'
        },
        {
          title: '调用智能体',
          key: 'botId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.botIdList,
          listFormat: {
            label: 'botName',
            value: 'botId'
          }
          // search: true,
          // clearable: true
        },
        {
          title: '模型平台',
          key: 'platform',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.platformList,
          listFormat: {
            label: 'name',
            value: 'code'
          }
        },
        {
          title: '调用模型',
          key: 'modelRepositoryId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.modelRepositoryList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '人物参数',
          key: 'personArgument'
        },
        {
          title: '智能体提示词',
          key: 'callWord'
        },
        {
          title: '更新人员',
          key: 'updateAdminName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '状态',
          key: 'configStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [
            {
              label: '关闭',
              value: 0
            },
            {
              label: '开启',
              value: 1
            }
          ],
          listFormat: {
            label: 'label',
            value: 'value'
          },
          search: true,
          clearable: true
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.handleAdd('edit', params)
              }
            }
          ]
        }
      ]
    }
  },
  async mounted() {
  },
  created() {
    modelRepositoryplatform().then(res => {
      this.platformList = res.data
    }).then(() => {
      this.modelRepositoryList = []
      this.platformList.forEach(item => {
        modelRepositoryselect(item.code).then(res => {
          this.modelRepositoryList.push(...res.data)
        })
      })
    })
  },
  methods: {
    initFN() {
      this.$refs.styleList.getSubSuccess()
    },
    handleAdd(type, params = {}) {
      this.$confirmDialog({
        options: {
          className: 'identifyMarganEdit',
          position: 'right',
          showClose: false,
          width: '650px'
        },
        component: () => import('./components/identifyMarganEdit'),
        params: {
          type,
          data: params
        }
      }).then(res => {
        console.log(res)
        if (res.action == 'confirm') {
          this.$refs.styleList.getSubSuccess()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
