<template>
  <div>
    <el-form
      :inline="true"
      :model="searchForm"
      class="demo-form-inline"
      style="margin-top: 23px"
    >

      <el-form-item label="选品库分类">
        <el-select v-if="radioVal" v-model="searchForm.packageId" disabled filterable>
          <el-option
            label="今日必中"
            value=""
          />
        </el-select>
        <el-select v-else v-model="searchForm.packageId" filterable>
          <el-option
            v-for="(item, index) in goodsList"
            :key="index"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="radioVal === 1" label="活动时间">
        <el-date-picker
          v-model="value1"
          type="datetimerange"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
        >查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-if="tableShow"
      v-loading="DataLoading"
      height="500"
      :data="tableData"
      class="flashCommodityTable"
      border
      style="width: 100%; margin: 30px 0"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        :selectable="selectableMethods"
      />
      <el-table-column :prop="radioVal === 0?'itemId':'id'" label="商品编号" />

      <el-table-column v-if="radioVal === 0" prop="title" label="商品来源" width="80px">
        <template>
          <div>淘宝</div>
        </template>
      </el-table-column>
      <el-table-column :prop="radioVal === 0?'title':'goodsName'" label="商品名称" />
      <el-table-column :prop="radioVal === 0?'coverPic':'goodsPicUrl'" label="商品图片">
        <template slot-scope="scope">
          <viewer
            v-if="scope.row.coverPic || scope.row.goodsPicUrl"
            class="img-wrap"
            style="margin: auto"
          >
            <img
              :src="scope.row.coverPic || scope.row.goodsPicUrl"
              style="max-width: 50px; max-height: 50px"
            >
          </viewer>
        </template>
      </el-table-column>
      <template v-if="radioVal === 0">
        <el-table-column prop="normalPrice" label="到手价格 " />
        <el-table-column prop="originalPrice" label="原价 " />

      </template>
      <template v-if="radioVal === 1">
        <el-table-column prop="winnerNumber" label="开奖个数" />
        <el-table-column prop="winThreshold" label="开奖值" />
        <el-table-column prop="winRate" label="累计比" />
        <el-table-column prop="currentWinValue" label="当前累计值" />
        <el-table-column prop="originalPrice" label="活动时间" width="150">
          <template slot-scope="scope">
            {{ scope.row.beginTime }} 至 {{ scope.row.endTime }}
          </template>
        </el-table-column>
        <el-table-column prop="originalPrice" label="活动状态">
          <template slot-scope="scope">
            {{ scope.row.activityStatus==0?'未开始':scope.row.activityStatus==1?'结束-已开奖':scope.row.activityStatus==2?'结束-未开奖':scope.row.activityStatus==3?'进行中':'--' }}
          </template>

        </el-table-column>

      </template>

    </el-table>
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 70, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <div style="text-align: center;margin-top: 16px;">
      <el-button @click="handleCancle">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </div>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import moment from 'moment'
import {
  seckill_goodsstorages_store,
  goods_extension_goodspackage
} from '@/api/jointCashReturn'

export default {
  components: {
    page
  },
  props: {
    categoryList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      searchForm: {
        // 查询条件
        checked: true,
        title: '', // 商品名称
        packageId: '', // 商品分类
        mallId: 1001 // 商城类型ID
      },
      goodsList: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      listQuery: {},
      tableData: [],
      categoryId: '',
      multipleSelection: [],
      DataLoading: true,
      radioVal: 0,
      value1: [
        moment().format('YYYY-MM-DD HH:mm'), moment()
          .subtract(-7, 'days')
          .format('YYYY-MM-DD HH:mm')],
      tableShow: true
    }
  },
  created() {
    this.getCommodityBank()
    this.getCommoditiesList()
  },
  methods: {
    radioValChange(e) {
      this.tableShow = false
      setTimeout(() => {
        this.tableShow = true
      })
      this.pageSize = 10
      this.currentPage = 1
      this.searchForm.packageId = e ? '' : null
      this.getCommoditiesList()
    },
    submitForm() {
      if (this.multipleSelection.length !== 1) {
        this.$message.error('请选择一个商品')
        return
      }
      // const obj = {
      //   type: this.radioVal,
      //   mallId: 1001,
      //   item: this.multipleSelection[0]
      // }

      // post_one_yuan_goods_add(obj).then((res) => {
      //   if (res.status == 200) {
      //     this.$message.success('操作成功')
      //     this.$emit('success')
      //   }
      // })
      this.$emit('choose', this.multipleSelection[0])
    },
    // 获取商品库列表
    getCommodityBank(val) {
      const param = {
        mallId: 1001
      }
      goods_extension_goodspackage(param).then((res) => {
        this.goodsList = res.data
      })
    },
    handleCancle() {
      this.$emit('close')
    },
    handleSearch() {
      this.currentPage = 1
      this.getCommoditiesList()
    },

    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getCommoditiesList()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getCommoditiesList()
    },
    getCommoditiesList() {
      this.DataLoading = true
      this.tableData = []
      seckill_goodsstorages_store({
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
        ...this.searchForm
      }).then((res) => {
        this.DataLoading = false
        if (res.code === 0) {
          this.tableData = res.data
          this.total = res.totalCount
        }
      })
    },
    selectableMethods(row, index) {
      return !row.checked
    },

    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }

}
</script>

<style scoped>
.title-box{
  padding-bottom: 20px;
}
.title-box span{
  font-size: 18px;
  font-weight: bold;
  margin-right: 20px;
}
.el-dialog__body{
  padding-top: 0;
}
.el-table{
  margin-top: 0 !important;
}
</style>
