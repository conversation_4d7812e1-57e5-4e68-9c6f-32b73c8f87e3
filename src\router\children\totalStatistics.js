/*
 * 统计总览子路由
 * */

const totalStatistics = [
  {
    path: '/totalStatistics/firstBuy',
    name: 'totalStatistics_firstBuy',
    meta: {
      title: '首次购买转化统计'
    },
    // component: () => import("@/views/totalStatistics/page/firstBuy"),
    component: () => import('@/views/totalStatistics/page/firstBuy')
  },
  {
    path: '/totalStatistics/banner',
    name: 'banner_firstBuy',
    meta: {
      title: 'banner点击统计'
    },
    component: () => import('@/views/totalStatistics/page/bannerStatistics')
  },
  {
    path: '/totalStatistics/homeClick',
    name: 'homeClick',
    meta: {
      title: '首页点击统计'
    },
    component: () => import('@/views/totalStatistics/page/homeClick')
  },
  {
    path: '/totalStatistics/dataBoard',
    name: 'dataBoard',
    meta: {
      title: '数据看板统计'
    },
    component: () => import('@/views/totalStatistics/page/dataBoard')
  },
  {
    path: '/totalStatistics/broaderMarket',
    name: 'broaderMarket',
    meta: {
      title: '大盘数据'
    },
    component: () => import('@/views/totalStatistics/page/broaderMarket')
  },
  {
    path: '/totalStatistics/profitCount',
    name: 'profitCount',
    meta: {
      title: '收益数据统计'
    },
    component: () => import('@/views/totalStatistics/page/profitCount')
  },
  {
    path: '/totalStatistics/homeLogin',
    name: 'homeLogin',
    meta: {
      title: '登录页统计'
    },
    component: () => import('@/views/totalStatistics/page/homeLogin')
  },
  {
    path: '/totalStatistics/homeLoginDetail',
    name: 'homeLoginDetail',
    meta: {
      title: '登录页统计明细',
      parentTitle: '登录页统计',
      activeMenu: '/totalStatistics/homeLogin'
    },
    component: () => import('@/views/totalStatistics/page/homeLoginDetail')
  },
  {
    path: '/totalStatistics/earnCash',
    name: 'earnCash',
    meta: {
      title: '赚现金页面统计'
    },
    component: () => import('@/views/totalStatistics/page/earnCash')
  },
  {
    path: '/totalStatistics/thirdThematicStatistics',
    name: 'ThirdThematicStatistics',
    meta: {
      title: '第三方专题统计'
    },
    component: () => import('@/views/totalStatistics/page/thirdThematicStatistics')
  },
  {
    path: '/totalStatistics/searchStatistics',
    name: 'searchStatistics',
    meta: {
      title: '搜素统计'
    },
    component: () => import('@/views/totalStatistics/page/searchStatistics')
  },
  {
    path: '/totalStatistics/searchStatisticsNew',
    name: 'searchStatisticsNew',
    meta: {
      title: '搜素统计(新)'
    },
    component: () => import('@/views/totalStatistics/page/searchStatisticsNew')
  },
  {
    path: '/totalStatistics/spreadStatistics',
    name: 'spreadStatistics',
    meta: {
      title: '推广分类统计'
    },
    component: () => import('@/views/totalStatistics/page/spreadStatistics')
  },
  {
    path: '/totalStatistics/TBAuthorizationStatistics',
    name: 'TBAuthorizationStatistics',
    meta: {
      title: '淘宝授权统计'
    },
    component: () => import('@/views/totalStatistics/page/TBAuthorizationStatistics')
  },
  {
    path: '/totalStatistics/classification',
    name: 'classification',
    meta: {
      title: '推广分类统计'
    },
    component: () => import('@/views/totalStatistics/page/classification')
  },
  {
    path: '/totalStatistics/authorizationsStatistics',
    name: 'authorizationsStatistics',
    meta: {
      title: '授权页统计'
    },
    component: () => import('@/views/totalStatistics/page/authorizationsStatistics')
  },
  {
    path: '/totalStatistics/orientationTraining',
    name: 'orientationTraining',
    meta: {
      title: '新人引导统计'
    },
    component: () => import('@/views/totalStatistics/page/orientationTraining')
  },
  {
    path: '/totalStatistics/merchantStatistics',
    name: 'merchantStatistics',
    meta: {
      title: '商户号统计'
    },
    component: () => import('@/views/totalStatistics/page/merchantStatistics')
  },
  {
    path: '/totalStatistics/marketData',
    name: 'marketData',
    meta: {
      title: '新大盘数据'
    },
    component: () => import('@/views/totalStatistics/page/marketData')
  },
  {
    path: '/totalStatistics/adSenseTable',
    name: 'adSenseTable',
    meta: {
      title: '广告联盟下沉表'
    },
    component: () => import('@/views/totalStatistics/page/adSenseTable')
  },
  {
    path: '/totalStatistics/equityTable',
    name: 'equityTable',
    meta: {
      title: '权益下沉表'
    },
    component: () => import('@/views/totalStatistics/page/equityTable')
  },
  {
    path: '/totalStatistics/accretionTable',
    name: 'accretionTable',
    meta: {
      title: ' 增值下沉表'
    },
    component: () => import('@/views/totalStatistics/page/accretionTable')
  },
  {
    path: '/totalStatistics/refundTable',
    name: 'refundTable',
    meta: {
      title: '自主退款统计'
    },
    component: () => import('@/views/totalStatistics/page/refundTable')
  },
  {
    path: '/totalStatistics/complaintsReportsStatistics',
    name: 'complaintsReportsStatistics',
    meta: {
      title: '投诉举报统计'
    },
    component: () => import('@/views/totalStatistics/page/complaintsReportsStatistics')
  },
  {
    path: '/totalStatistics/payLink',
    name: 'totalStatisticsPayLink',
    meta: {
      title: '支付链路监控'
    },
    component: () => import('@/views/totalStatistics/page/payLink'),
    children: [

    ]
  },
  {
    path: '/totalStatistics/payLink/detail',
    name: 'totalStatisticsPayLinkDetail',
    meta: {
      title: '查看明细',
      activeMenu: '/totalStatistics'
    },
    component: () => import('@/views/totalStatistics/page/payLink/detail')
  },
  {
    path: '/totalStatistics/homeLoginDataV2',
    name: 'homeLoginDataV2',
    meta: {
      title: '登录数据监控表'
    },
    component: () => import('@/views/totalStatistics/page/homeLoginDataV2')
  },
  {
    path: '/totalStatistics/advertisingStatistics',
    name: 'AdvertisingStatistics',
    meta: {
      title: '广告收益统计'
    },
    component: () => import('@/views/totalStatistics/page/advertising/advertisingStatistics')
  },
  {
    path: '/totalStatistics/advertisingStatisticsDetail',
    name: 'AdvertisingStatisticsDetail',
    meta: {
      title: '广告收益统计-渠道详情',
      parentTitle: '广告收益统计',
      activeMenu: '/totalStatistics/advertisingStatistics'
    },
    component: () => import('@/views/totalStatistics/page/advertising/advertisingStatisticsDetail')
  }
]

export default totalStatistics
