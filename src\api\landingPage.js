import {get, post, delWithQuery} from '@/libs/axios.package'

/**
 * 渠道会员卡新增
 * */
export const add_landing_page = obj => {
  return post('/channel_landing_page/insert', obj, null)
}

/**
 * 渠道会员卡查询列表
 * */
export const get_landing_page_list = obj => {
  return get('/channel_landing_page/get_page_list', obj, null)
}

/**
 * 落地页查询列表（不分页）
 * */
export const get_landing_page_list_no_page = obj => {
  return get('/channel_landing_page/list', obj, null)
}

/**
 * 落地页流程列表（不分页）
 * */
export const get_channel_process_list_no_page = obj => {
  return get('/channelProcess/getList', obj, null)
}

/**
 * 编辑渠道会员卡
 * */
export const put_landing_page_edit = obj => {
  return post('/channel_landing_page/update', obj, null)
}

/**
 * 获取所有进件城市数据
 * */
export const limitCityAll = obj => {
  return get('/limit/city/all', obj)
}

/**
 * 新增或编辑所有进件城市数据
 * */
export const limitCityAdd = obj => {
  return post('/limit/city/add', obj)
}

/**
 * 撞库年龄配置
 * */
export const get_limit_age = obj => {
  return get('/limit/age', obj)
}
/**
 * 撞库年龄配置
 * */
export const update_limit_age = obj => {
  return post('/limit/age/update', obj)
}
/**
 * 域名轮切列表
 * */
export const domainManagePage = obj => {
  return get('/domain-manage/page', obj)
}

/**
 * 域名轮切新增
 * */
export const domainManageInsert = obj => {
  return post('/domain-manage/insert', obj)
}

/**
 * 域名轮切删除
 * */
export const domainManageDelete = obj => {
  return delWithQuery('/domain-manage/delete', obj)
}

/**
 * 域名轮切更新
 * */
export const domainManageUpdate = obj => {
  return post('/domain-manage/update', obj)
}

/**
 * 域名轮切统计
 */
export const domainManageStaticsList = obj => {
  return get('/domain-manage/statics/list', obj)
}

/**
 * 域名轮切统计
 */
export const domainManageStaticsChannelDetail = obj => {
  return get('/domain-manage/statics/channel-detail', obj)
}

/**
 * 分页查询需有配置的媒体
 */
export const thirdRsaPage = obj => {
  return get('/third/rsa/page', obj)
}

/**
 * 分页查询需有配置的媒体
 */
export const thirdRsaSelector = obj => {
  return get('/third/rsa/selector', obj)
}

/**
 * 更新媒体配置信息
 */
export const thirdRsaUpdate = obj => {
  return post('/third/rsa/update', obj)
}
