import {
  put,
  get,
  post
} from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/**
 *
 * @param obj
 * @returns {Promise | Promise<unknown>}
 * @constructor
 */
export const GET_MOBILE_GOODS_PAGE = obj => {
  return get('/activity/mobile/goods/page', obj)
}

export const ADD_MOBILE_GOODS = obj => {
  return post('/activity/mobile/goods', obj)
}

export const EDIT_MOBILE_GOODS = obj => {
  return put('/activity/mobile/goods', obj)
}

export const GET_MOBILE_BILL_COUPONS_PAGE = obj => {
  return get('/activity/mobile/bill/coupons/page', obj)
}

export const ADD_MOBILE_BILL_COUPONS = obj => {
  return post('/activity/mobile/bill/coupons', obj)
}

export const EDIT_MOBILE_BILL_COUPONS = obj => {
  return put('/activity/mobile/bill/coupons', obj)
}

export const GET_RECHARGE_SELECTSTATIS = obj => {
  return get('/bury/count/phone/recharge/selectStatis', obj)
}

export const GET_ORDER_LIST = obj => {
  return get('/activity/mobile/goods/order', obj)
}

export const EXPORT_MOBILE_ORDER = data => CONSTANT.publicPath + '/activity/mobile/goods/order/export?' + qs.stringify(data)
