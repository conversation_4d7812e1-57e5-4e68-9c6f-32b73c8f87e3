import { formItemType, tableItemType } from '@/config/sysConfig'
import { adType, scenelist } from '@/qjjpApi/adver'
import { count_channel_application_list, mediaAll, undertakeSelector } from '@/qjjpApi/NewChannel'
import moment from 'moment'
import { osList } from '@/qjjpViews/appVersion/basicParams'
export default {
  data() {
    return {
      list1: [
        {
          id: 1,
          name: 'apk链路'
        },
        {
          id: 2,
          name: 'h5链路'
        }
      ],
      list2: [
        {
          id: 1,
          name: '普通回传（单节点）'
        },
        {
          id: 2,
          name: '关键行为（双节点）'
        }
      ],
      mediaList: [],
      siteIds: [],
      adTypes: [],
      scenelists: [],
      undertakeList: []
    }
  },
  created() {
    if (this.siteIds.length == 0) {
      count_channel_application_list({ status: 1, pageSize: 999 }).then(res => {
        if (res.code === 200) {
          if (res.data && res.data.length) {
            this.siteIds = res.data
          }
        }
      })
    }
    if (this.mediaList.length == 0) {
      mediaAll().then(res => {
        if (res.code === 200) {
          this.mediaList = res.data
        }
      })
    }
    if (this.adTypes.length == 0) {
      adType({ status: 1, pageSize: 999 }).then(res => {
        if (res.code === 200) {
          if (res.data && res.data.length) {
            this.adTypes = res.data
          }
        }
      })
    }
    if (this.undertakeList.length == 0) {
      undertakeSelector().then(res => {
        if (res.code === 200) {
          this.undertakeList = res.data
        }
      })
    }

    if (this.scenelists.length == 0) {
      scenelist({ status: 1, pageSize: 999 }).then(res => {
        if (res.code === 200) {
          if (res.data.records && res.data.records.length) {
            this.scenelists = res.data.records
          }
        }
      })
    }
  },
  methods: {
    getSearchList() {
      return [
        {
          key: 'dateSearch',
          title: '日期',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          pickerDay: 30,
          search: true,
          tableHidden: true,
          val: [this.$route.query.startDate || moment().subtract(6, 'days').format('YYYY-MM-DD'), this.$route.query.endDate || moment().format('YYYY-MM-DD')]
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          clearable: true,
          tableHidden: true,
          val: this.listQuery.siteId || '',
          search: true
        },
        {
          title: '渠道ID',
          type: formItemType.input,
          key: 'channelCode',
          tableHidden: true,
          search: true,
          val: this.listQuery.channelCode || '',
          clearable: true
        },
        {
          title: '投放链路',
          key: 'downloadType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list1,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          search: true,
          reg: ['required'],
          val: this.listQuery.downloadType || '',
          tableHidden: true
        },
        {
          title: '投放平台',
          key: 'apiCode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.mediaList,
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          val: this.listQuery.apiCode || '',
          tableHidden: true
        },
        {
          title: '回传节点',
          key: 'deviceReportType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          val: this.listQuery.deviceReportType || '',
          tableHidden: true
        },
        {
          title: '承接页面',
          key: 'landingPage',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.undertakeList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          val: this.listQuery.landingPage || '',
          tableHidden: true
        },
        {
          title: '广告类型',
          key: 'adType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.adTypes,
          listFormat: {
            label: 'value',
            value: 'key'
          },
          reg: ['required'],
          search: true,
          val: this.listQuery.adType || '',
          tableHidden: true
        },
        {
          title: '广告场景',
          key: 'adScene',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.scenelists,
          listFormat: {
            label: 'sceneName',
            value: 'id'
          },
          reg: ['required'],
          val: this.listQuery.adScene || '',
          search: true,
          tableHidden: true
        },
        {
          title: '应用类型',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: osList,
          reg: ['required'],
          search: true,
          tableHidden: true,
          val: this.listQuery.os || ''
        }
      ]
    },
    getListArray({ isShowChannelCode = true } = {}) {
      const listArray = [
        {
          title: '渠道ID',
          key: 'channelCode',
          tableHidden: !isShowChannelCode || !this.listQuery.channelCode
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          tableHidden: !this.listQuery.siteId
        },
        {
          title: '投放平台',
          key: 'apiCode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.mediaList,
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          tableHidden: !this.listQuery.apiCode
        },
        {
          title: '回传节点',
          key: 'deviceReportType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          tableHidden: !this.listQuery.deviceReportType
        },
        {
          title: '承接页面',
          key: 'landingPage',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.undertakeList,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          tableHidden: !this.listQuery.landingPage
        },
        {
          title: '广告类型',
          key: 'adType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.adTypes,
          listFormat: {
            label: 'value',
            value: 'key'
          },
          tableHidden: !this.listQuery.adType
        },
        {
          title: '广告场景',
          key: 'adScene',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.scenelists,
          listFormat: {
            label: 'sceneName',
            value: 'id'
          },
          tableHidden: !this.listQuery.adScene
        },

        {
          title: '行为数据',
          key: 'xwsj',
          children: [
            {
              title: 'DAU',
              key: 'dau',
              // render: (h, params) => {
              //   if (!params.data.row.deviceActiveNum) {
              //     return h('span', '--')
              //   }
              //   return h('span', params.data.row.deviceActiveNum)
              // },
              renderHeader: (...args) => this.renderHeader(...args, ['当日活跃用户数，指访问APP的用户数，包含静默登录，热启动，冷启动，按照设备去重'])
            },
            {
              title: '激活设备数',
              key: 'deviceActiveNum',
              renderHeader: (...args) => this.renderHeader(...args, ['对应日期激活设备数，设备去重'])
            },
            {
              title: '登录用户数',
              key: 'loginNum',
              renderHeader: (...args) => this.renderHeader(...args, ['对应日期登录用户数，设备去重'])
            },
            {
              title: '激活登录转化',
              key: 'deviceActiveLoginRate',
              renderHeader: (...args) => this.renderHeader(...args, ['登录用户数/激活设备数'])
            },
            {
              title: '预估收益',
              key: 'showCpm',
              renderHeader: (...args) => this.renderHeader(...args, ['根据topon数据接口获取到的预估收益'])
            },
            {
              title: '激活IPU',
              key: 'activeIpu',
              renderHeader: (...args) => this.renderHeader(...args, ['激活设备平均观看广告次数，计算公式：广告展示次数/激活设备数'])
            },
            {
              title: '激活arup',
              key: 'arup',
              renderHeader: (...args) => this.renderHeader(...args, ['激活设备平均收益，计算公式：预估收益/激活设备数'])
            }
          ]
        },
        {
          title: '广告数据',
          key: 'ggsj',
          children: [
            {
              title: '场景访问',
              key: 'pageNum',
              renderHeader: (...args) => this.renderHeader(...args, ['场景访问人数，带场景条件查询时按照设备+场景去重，不带场景条件查询时按照设备去重'])
            },
            {
              title: '场景访问率',
              key: 'pageRate',
              renderHeader: (...args) => this.renderHeader(...args, ['对应广告场景的激活访问率，计算公式：场景访问/激活设备数'])
            },
            {
              title: 'DEU',
              key: 'deu',
              renderHeader: (...args) => this.renderHeader(...args, ['观看广告用户数，设备去重'])
            },
            {
              title: '场景渗透率',
              key: 'deuRate',
              renderHeader: (...args) => this.renderHeader(...args, ['对应广告场景的广告渗透率，计算公式：DEU/场景访问'])
            },
            {
              title: '请求数',
              key: 'requestNum',
              renderHeader: (...args) => this.renderHeader(...args, ['前端向广告平台请求广告数'])
            },
            {
              title: '填充数',
              key: 'responseNum',
              renderHeader: (...args) => this.renderHeader(...args, ['向广告平台请求后的填充广告数'])
            },
            {
              title: '填充率',
              key: 'responseRate',
              renderHeader: (...args) => this.renderHeader(...args, ['三方广告平台请求广告的填充率，计算公式：填充数/请求数'])
            },
            {
              title: '填充耗时（s）',
              key: 'responseTimeConsuming',
              renderHeader: (...args) => this.renderHeader(...args, ['广告从请求到填充的平均耗时，计算公式：∑（响应成功时间-请求时间）/响应成功请求数'])
            },
            {
              title: '展示数',
              key: 'showNum',
              renderHeader: (...args) => this.renderHeader(...args, ['广告平台填充后的展示广告数'])
            },
            {
              title: '展示率',
              key: 'showRate',
              renderHeader: (...args) => this.renderHeader(...args, ['三方广告平台填充广告的展示率，计算公式：展示数/填充数'])
            },
            {
              title: '奖励下发数',
              key: 'awardNum',
              renderHeader: (...args) => this.renderHeader(...args, ['激励广告的奖励下发数'])
            },
            {
              title: '奖励下发率',
              key: 'awardRate',
              renderHeader: (...args) => this.renderHeader(...args, ['展示激励广告的奖励下发占比，计算公式：奖励下发数/展示数'])
            },
            {
              title: '点击数',
              key: 'clickNum',
              renderHeader: (...args) => this.renderHeader(...args, ['广告平台展示广告的点击数'])
            },
            {
              title: '点击率',
              key: 'clickRate',
              renderHeader: (...args) => this.renderHeader(...args, ['三方广告平台展示广告的点击率，计算公式：点击数/展示数'])
            },
            {
              title: '访问IPU',
              key: 'pageIpu',
              renderHeader: (...args) => this.renderHeader(...args, ['对应广告场景的访问设备平均观看广告次数，计算公式：广告展示次数/场景访问'])
            },
            {
              title: '渗透IPU',
              key: 'infiltrateIpu',
              renderHeader: (...args) => this.renderHeader(...args, ['对应广告场景的参与设备平均观看广告次数，计算公式：广告展示次数/展示数（设备去重）'])
            },
            {
              title: '访问预估ECPM',
              key: 'ecpm',
              renderHeader: (...args) => this.renderHeader(...args, ['对应广告场景的访问设备预估ecpm，计算公式：预估收益/展示数*1000'])
            },
            {
              title: '访问ARUP值',
              key: 'pageArup',
              renderHeader: (...args) => this.renderHeader(...args, ['对应广告场景的访问设备ARUP值，计算公式：预估收益/场景访问'])
            }
          ]
        }

      ]
      return listArray
    },
    renderHeader(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h(
            'div',
            {
              slot: 'content'
            },
            [textArr.map(item => h('div', null, item))]
          ),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    }
  }
}
