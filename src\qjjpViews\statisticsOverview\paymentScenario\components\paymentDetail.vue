<template>
  <el-popover @show="showFn" placement="bottom" width="270" trigger="click">
    <div class="titleHead">{{ intorData.titleTxt }}</div>
    <div class="tableDiv">
      <el-table v-loading="loadingDetail" :data="showTableData" :show-header="false">
        <el-table-column width="150" property="sceneName">
          <template slot-scope="scope">
            {{ scope.row.sceneName }}：
          </template>
        </el-table-column>
        <el-table-column v-if='intorData.type == 1' width="80" property="visitTotalUv"></el-table-column>
        <el-table-column v-else-if='intorData.type == 2' width="80" property="payTotalUv"></el-table-column>
        <el-table-column v-else width="80" property="orderNum"></el-table-column>
      </el-table>
    </div>
    <span class="title" slot="reference">{{ intorData.titleValue }}</span>
  </el-popover>
</template>
<script>
import { getCountPaySceneDetail } from '@/qjjpApi/statisticsOverview'
export default {
  props: {
    pageData: {
      type: Object,
      default: () => { }
    },
    listQuery: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      loadingDetail: true,
      tableData: []
    }
  },
  computed: {
    showTableData() {
      if (this.intorData.type == 1) {
        const tableData = this.tableData
        return tableData.sort((a, b) => b.visitTotalUv - a.visitTotalUv)
      } else if (this.intorData.type == 2) {
        const tableData = this.tableData
        return tableData.sort((a, b) => b.payTotalUv - a.payTotalUv)
      } else {
        const tableData = this.tableData
        return tableData.sort((a, b) => b.orderNum - a.orderNum)
      }
    },
    intorData() {
      const titleValue = this.pageData.params.data.row[this.pageData.key]
      let titleTxt = ''
      const paramsObj = this.listQuery
      if (this.$route.params.type == 'channelCodeDetail') {
        paramsObj.channelCode = this.pageData.params.data.row.channelCode
        titleTxt = `${this.pageData.params.data.row.eventDate === '汇总' ? '汇总' : this.pageData.params.data.row.channelCode} -${this.pageData.title}`
      } else {
        if (this.pageData.params.data.row.eventDate !== '汇总') {
          paramsObj.startDate = this.pageData.params.data.row.eventDate
          paramsObj.endDate = this.pageData.params.data.row.eventDate
        }
        titleTxt = `${this.pageData.params.data.row.eventDate} -${this.pageData.title}`
      }
      delete paramsObj.pageSize
      delete paramsObj.pageNumber
      delete paramsObj.paySceneCode
      return {
        paramsObj,
        titleValue,
        titleTxt,
        type: this.pageData.type
      }
    }
  },
  methods: {
    async showFn() {
      const { data = [] } = await getCountPaySceneDetail(this.intorData.paramsObj)
      setTimeout(() => {
        this.loadingDetail = false
        this.tableData = data
      }, 300)
    }
  }
}
</script>
<style lang="scss" scoped>
.titleHead {
  width: 100%;
  background-color: #f2f2f2;
  text-align: center;
  line-height: 40px;
}

.tableDiv {
  width: 100%;
  max-height: 300px;
  overflow: auto;
}

.title {
  text-decoration: underline;
  color: rgb(0, 166, 255);
  cursor: pointer;
}

::v-deep .cell {
  padding: 0 !important;
  white-space: nowrap;
}

::v-deep td {
  padding: 3px 0 !important;
}
</style>
