import { get, post } from '@/libs/axios.package'

/**
 * 渠道会员卡新增
 * */
export const add_gift_bag = obj => {
  return post('/channel/package', obj, null)
}

/**
 * 渠道会员卡查询列表
 * */
export const get_gift_bag_list = obj => {
  return get('/channel/package', obj, null)
}

/**
 * 编辑渠道会员卡
 * */
export const put_gift_bag_detail_edit = obj => {
  return post(`/gift/update/${obj.id}`, obj, null)
}

/**
 * 编辑渠道会员卡
 * */
export const put_gift_bag_edit = obj => {
  return post(`/channel/package/update/${obj.id}`, obj, null)
}

/**
 * 渠道会员卡新增
 * */
export const add_gift_bag_detail = obj => {
  return post('/channel/package/gift/add', obj, null)
}

/**
 * 渠道会员卡查询列表
 * */
export const get_gift_bag_detail_list = obj => {
  return post('/channel/package/gift', obj, null)
}

/**
 * 编辑渠道会员卡
 * */
export const GET_GIFT_ALL = obj => {
  return get(`/gift/all`, obj, null)
}

/**
 * 渠道会员卡新增
 * */
export const add_gift = obj => {
  return post('/gift/add', obj, null)
}

/**
 * 渠道会员卡查询列表
 * */
export const get_gift = obj => {
  return get('/gift', obj, null)
}
/**
 * 编辑渠道会员卡
 * */
export const put_gift_edit = obj => {
  return post(`/gift/update/${obj.id}`, obj, null)
}
