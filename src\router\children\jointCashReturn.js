/*
 * 万人拼返现路由
 * */

const jointCashReturn = [
  {
    path: '/jointCashReturn/activityStatistics',
    name: 'jointActivityStatistics',
    meta: {
      title: '活动统计'
    },
    component: () => import('@/views/jointCashReturn/dataStatistics/activityStatistics.vue')
  },
  {
    path: '/jointCashReturn/freeExchangeStatistics',
    name: 'freeExchangeStatistics',
    meta: {
      title: '免费兑换统计'
    },
    component: () => import('@/views/jointCashReturn/dataStatistics/freeExchangeStatistics.vue')
  },
  {
    path: '/jointCashReturn/orderLink',
    name: 'jointActivityStatistics',
    meta: {
      title: '下单链路统计'
    },
    component: () => import('@/views/jointCashReturn/dataStatistics/orderLink.vue')
  },
  {
    path: '/jointCashReturn/undertakingStatistics',
    name: 'undertakingStatistics',
    meta: {
      title: '承接统计'
    },
    component: () => import('@/views/jointCashReturn/dataStatistics/undertakingStatistics.vue')
  },
  {
    path: '/jointCashReturn/commodityType',
    name: 'jointCommodityType',
    meta: {
      title: '商品类型',
      keepAlive: true,
    },
    component: () => import('@/views/jointCashReturn/configManagement/commodityType.vue')
  },
  {
    path: '/jointCashReturn/commodityManagement',
    name: 'jointCommodityManagement',
    meta: {
      title: '商品管理'
    },
    component: () => import('@/views/jointCashReturn/configManagement/commodityManagement.vue')
  },
  {
    path: '/jointCashReturn/commodityManagementDetail',
    name: 'commodityManagementDetail',
    meta: {
      title: '商品管理详情',
      parentTitle: '第三方广告',
      activeMenu: '/jointCashReturn/commodityManagement'
    },
    component: () => import('@/views/jointCashReturn/configManagement/commodityManagementDetail.vue')
  },
  {
    path: '/jointCashReturn/pageDisposition',
    name: 'pageDisposition',
    meta: {
      title: '分发页配置'
    },
    component: () => import('@/views/jointCashReturn/configManagement/pageDisposition.vue')
  },
  {
    path: '/jointCashReturn/warehouseManagement',
    name: 'jointWarehouseManagement',
    meta: {
      title: '商品库管理'
    },
    component: () => import('@/views/jointCashReturn/configManagement/warehouseManagement.vue')
  },
  {
    path: '/jointCashReturn/undertakingAssociation',
    name: 'jointUndertakingAssociation',
    meta: {
      title: '商品承接关联'
    },
    component: () => import('@/views/jointCashReturn/configManagement/undertakingAssociation.vue')
  },
  {
    path: '/jointCashReturn/undertakingAssociationDetail',
    name: 'undertakingAssociationDetail',
    meta: {
      title: '商品承接关联详情',
      parentTitle: '商品承接关联',
      activeMenu: '/jointCashReturn/undertakingAssociation'
    },
    component: () => import('@/views/jointCashReturn/configManagement/undertakingAssociationDetail.vue')
  },
  {
    path: '/jointCashReturn/commodityOrder',
    name: 'jointCommodityOrder',
    meta: {
      title: '商品订单'
    },
    component: () => import('@/views/jointCashReturn/commodityOrder.vue')
  },
  {
    path: '/jointCashReturn/memberSubsidyStatistics',
    name: 'jointMemberSubsidyStatistics',
    meta: {
      title: '会员补贴金统计'
    },
    component: () => import('@/views/jointCashReturn/memberSubsidyStatistics.vue')
  },
  {
    path: '/jointCashReturn/OperatorManagement',
    name: 'OperatorManagement',
    meta: {
      title: '供应商管理'
    },
    component: () => import('@/views/jointCashReturn/OperatorManagement.vue')
  },
  {
    path: '/jointCashReturn/OperatorManagementDetail',
    name: 'OperatorManagementDetail',
    meta: {
      title: '供应商管理',
      activeMenu: '/jointCashReturn/OperatorManagement'
    },
    component: () => import('@/views/jointCashReturn/OperatorManagementDetail.vue')
  },
  {
    path: '/jointCashReturn/dailySaleManagement',
    name: 'dailySaleManagement',
    meta: {
      title: '天天特卖管理'
    },
    component: () => import('@/views/jointCashReturn/configManagement/dailySaleManagement.vue')
  },
  {
    path: '/jointCashReturn/valueAddedOrder',
    name: 'valueAddedOrder',
    meta: {
      title: '增值订单'
    },
    component: () => import('@/views/jointCashReturn/dataStatistics/valueAddedOrder.vue')
  },
  {
    path: '/jointCashReturn/dailySalesStatistics',
    name: 'dailySalesStatistics',
    meta: {
      title: '天天特卖统计'
    },
    component: () => import('@/views/jointCashReturn/dataStatistics/dailySalesStatistics.vue')
  },
  {
    path: '/jointCashReturn/dailySalesCarryOnStatistics',
    name: 'dailySalesCarryOnStatistics',
    meta: {
      title: '天天特卖承接统计'
    },
    component: () => import('@/views/jointCashReturn/dataStatistics/dailySalesCarryOnStatistics.vue')
  },
  {
    path: '/jointCashReturn/goodsSynthesisIncome',
    name: 'goodsSynthesisIncome',
    meta: {
      title: '商品综合收益'
    },
    component: () => import('@/views/jointCashReturn/dataStatistics/goodsSynthesisIncome.vue')
  },
  {
    path: '/jointCashReturn/goodsRetainedData',
    name: 'goodsRetainedData',
    meta: {
      title: '商品综合收益留存数据',
      activeMenu: '/jointCashReturn/goodsSynthesisIncome'
    },
    component: () => import('@/views/jointCashReturn/dataStatistics/goodsRetainedData.vue')
  },
  {
    path: '/jointCashReturn/templateOfFreight',
    name: 'templateOfFreight',
    meta: {
      title: '运费模板'
    },
    component: () => import('@/views/jointCashReturn/configManagement/templateOfFreight.vue')
  },
  {
    path: '/jointCashReturn/AfterSalesRefund',
    name: 'jointAfterSalesRefund',
    meta: {
      title: '售后申请列表'
    },
    component: () => import('@/views/jointCashReturn/AfterSalesRefund.vue')
  },
  {
    path: '/supplierManage/index',
    name: 'supplierManage',
    meta: {
      title: '供应商管理'
    },
    component: () => import('@/views/jointCashReturn/supplierManage/index.vue')
  },
  {
    path: '/supplierManage/commodityOrder',
    name: 'supplierCommodityOrder',
    meta: {
      title: '商品订单'
    },
    component: () => import('@/views/jointCashReturn/supplierManage/commodityOrder.vue')
  },
  {
    path: '/supplierManage/AfterSalesRefund',
    name: 'supplierAfterSalesRefund',
    meta: {
      title: '商品售后订单'
    },
    component: () => import('@/views/jointCashReturn/supplierManage/AfterSalesRefund.vue')
  },
  {
    path: '/supplierManage/allGoodsReconciliation',
    name: 'allGoodsReconciliation',
    meta: {
      title: '总商品对账表'
    },
    component: () => import('@/views/jointCashReturn/goodsBillCheck.vue')
  },
  {
    path: '/supplierManage/goodsBillCheckForSupply',
    name: 'goodsBillCheckForSupply',
    meta: {
      title: '商品对账表'
    },
    component: () => import('@/views/jointCashReturn/supplierManage/goodsBillCheckForSupply.vue')
  },
  {
    path: '/jointCashReturn/goodsHouseManage',
    name: 'jointGoodsHouseManage',
    meta: {
      title: '商品库管理'
    },
    component: () => import('@/views/jointCashReturn/configManagement/goodsHouseManage.vue')
  },
  {
    path: '/jointCashReturn/PurchaseOrder',
    name: 'jointPurchaseOrder',
    meta: {
      title: '采购订单'
    },
    component: () => import('@/views/jointCashReturn/PurchaseOrder.vue')
  },
  {
    path: '/jointCashReturn/productConversionStatistics',
    name: 'productConversionStatistics',
    meta: {
      title: '商品场景转化'
    },
    component: () => import('@/views/jointCashReturn/ProductConversionStatistics.vue')
  },
  {
    path: '/jointCashReturn/tagList',
    name: 'tagList',
    meta: {
      title: '标签池'
    },
    component: () => import('@/views/jointCashReturn/TagList.vue')
  },
  {
    path: '/jointCashReturn/editCommodityTypePage',
    name: 'editCommodityTypePage',
    meta: {
      title: '商品类型详情',
      parentTitle: '第三方广告',
      activeMenu: '/jointCashReturn/commodityType'
    },
    component: () => import('@/views/jointCashReturn/configManagement/editCommodityTypePage.vue')
  }
]

export default jointCashReturn
