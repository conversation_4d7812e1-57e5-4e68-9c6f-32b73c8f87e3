<template>
  <div>
    <page :request="request" :list="list" table-title="APP渠道管理" />
    <SDialog :dialog-form-visible.sync="glPop" :data="form">
      <div>
        <el-form ref="form" :model="form" label-width="80px" :close-on-click-modal="false">
          <el-form-item label="关联品牌">
            <el-checkbox-group v-model="form.branch">
              <el-checkbox label="OPPO" name="OPPO">oppo</el-checkbox>
              <el-checkbox label="xiaomi" name="xiaomi">小米</el-checkbox>
              <el-checkbox label="VIVO" name="VIVO">vivo</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">确定</el-button>
            <el-button @click="onClose">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </SDialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType, submission } from '@/config/sysConfig'
import { GET_APP_CHANNEL_LIST, POST_APP_CHANNEL, update_branch } from '@/qjjpApi/appVersion'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import SDialog from '@/components/restructure/dialog'
import { osList } from './basicParams'
export default {
  components: {
    page,
    SDialog
  },
  props: {},
  data() {
    const that = this
    return {
      listQuery: {},
      form: {
        id: '',
        branch: []
      },
      glPop: false,
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then(response => {
              this.siteIds = response.data
            })
          }
          const list = await GET_APP_CHANNEL_LIST(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        },
        insertHttp: POST_APP_CHANNEL,
        updateHttp: data => {
          const { name, description } = data
          return Promise.all([
            POST_APP_CHANNEL({
              id: data.id,
              title: data.title,
              status: data.status,
              siteId: data.siteId,
              relationCode: data.relationCode,
              storeStatus: data.storeStatus
            })
          ]).then(res => {
            return Promise.resolve(res[0])
          })
        }
      },
      siteIds: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: 'ID',
          key: 'id',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '应用类型',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: osList,
          reg: ['required'],
          search: true,
          disabled: this.$store.state.submission.submitType !== submission.insert
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          // val:'d6c4a4bbd1f748f89e879c00d60edd8e',
          reg: ['required'],
          search: true
        },
        {
          title: '名称',
          key: 'title',
          type: formItemType.input,
          search: true
        },
        {
          title: '创建时间',
          key: 'createTime',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: 'ID',
          key: 'relationCode',
          type: formItemType.input,
          tableHidden: true
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.radio,
          search: false,
          tableHidden: true,
          options: {
            valueType: 'Number'
          },
          list: [
            { label: '启用', value: 0 },
            { label: '禁用', value: 1 }
          ]
        },
        {
          title: '商店上架状态',
          key: 'storeStatus',
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          search: false,
          // tableHidden: true,
          options: {
            valueType: 'Number'
          },
          list: [
            { label: '上架', value: 1 },
            { label: '未上架', value: 0 }
          ]
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          search: true,
          formHidden: true,
          tableHidden: true,
          list: [
            { label: '启用', value: 0 },
            { label: '禁用', value: 1 }
          ],
          options: {
            placeholder: '请选择状态'
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          search: false,
          formHidden: true,
          render: (h, params) => {
            const data = params.data.row
            const text = data.status ? '禁用' : '启用'
            return h('span', text)
          }
        },
        {
          type: tableItemType.active,
          width: '200px',
          headerContainer: true,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.detailsDialog,
              theme: 'primary'
            },
            {
              text: '关联',
              key: 'gl',
              type: tableItemType.activeType.event,
              theme: 'primary',
              hidden(params) {
                return params.relationCode != 'yysc'
              },
              click: ($index, item, params) => {
                this.form.id = params.id
                this.glPop = true
                this.form.branch = params.branch
              }
            }
          ]
        }
      ]
    }
  },
  created() {
    // this.getCountChannelApplicationList()
  },
  methods: {
    onClose() {
      this.glPop = false
      this.resetForm('form')
      Object.keys(this.form).forEach(key => {
        this.$set(this.ruleForm, key, null)
      })
    },
    onSubmit() {
      let { id, branch } = this.form
      branch = branch.toString()
      update_branch({ id, branch }).then(res => {
        if (res.code == 200) {
          this.glPop = false
          this.form = {
            id: '',
            branch: []
          }
          this.$store.dispatch('tableRefresh', this)
        }
      })
    },
    async getCountChannelApplicationList() {
      await count_channel_application_list().then(res => {
        if (res.code === 200) {
          this.siteIds = res.data
          // this.listQuery.siteId = res.data[0].siteId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
