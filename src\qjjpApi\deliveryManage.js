/*
 * @Author: 陈小豆
 * @Date: 2024-04-26 11:53:56
 * @LastEditors: 陈小豆
 * @LastEditTime: 2025-03-07 16:36:47
 */
import CONSTANT from '@/config/constant.conf'
import { getJjjp, postJjjp } from '@/libs/axios.package'
import qs from 'qs'

// 消息风格列表
export const channelList = obj => {
  return getJjjp(`/channel/list`, obj)
}

export const channelListexport = data => CONSTANT.qjjpPath + '/channel/list/export?' + qs.stringify(data)

// 编辑或新增商品分类接口
export const crowdProductCategoryEdit = obj => {
  return postJjjp('/crowd-product-category/edit', obj)
}

export const addChannel = obj => {
  return postJjjp(`/channel/add`, obj)
}

export const channelupdate = obj => {
  return postJjjp(`/channel/update`, obj)
}


export const updateChannel = obj => {
  return postJjjp(`/channel/update`, obj)
}

// 消息风格列表
export const channelDetail = obj => {
  return getJjjp(`/channel/detail`, obj)
}

export const accountpage = obj => {
  return getJjjp(`/media-developer-account/page`, obj)
}

export const accountaddOrUpdate = obj => {
  return postJjjp(`/media-developer-account/addOrUpdate`, obj)
}

export const mediaaccountpage = obj => {
  return getJjjp(`/media-delivery-account/page`, obj)
}

export const mediaaccountaddOrUpdate = obj => {
  return postJjjp(`/media-delivery-account/addOrUpdate`, obj)
}

export const accountoptionList = obj => {
  return getJjjp(`/media-developer-account/optionList`, obj)
}

export const authorizationcallback = obj => {
  return getJjjp(`/media-delivery-account/authorization/callback`, obj)
}

export const advertiserpage = obj => {
  return getJjjp(`/media-delivery-account/advertiser/page`, obj)
}
