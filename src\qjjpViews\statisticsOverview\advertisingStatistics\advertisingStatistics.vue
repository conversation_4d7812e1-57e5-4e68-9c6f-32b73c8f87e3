<template>
  <div>
    <page :request="request" :list="list" table-title="投放链路统计">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getmerchantcount, downOrUp, countrAdvertisingExport, getCountrAdvertising } from '@/qjjpApi/statisticsOverview'
import moment from 'moment'
import advertisingStatisticsAll from './advertisingStatisticsAll'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'advertisingStatistics',
  components: {
    page
  },
  mixins: [advertisingStatisticsAll],
  props: {},
  data() {
    return {
      curStatusParams: {
        id: 0,
        status: 0
      },
      listQuery: {
        startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await getCountrAdvertising(this.listQuery)
          const { records, total } = list.data
          console.info(list, 'list')
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '日期',
          key: 'eventDate',
          fixed: 'left'
        },
        ...this.getSearchList(),
        ...this.getListArray(),
        {
          title: '操作栏',
          width: '100px',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          fixed: 'right',
          activeType: [
            {
              text: '渠道详情',
              key: 'edit1',
              type: tableItemType.activeType.event,
              theme: 'warning',
              hidden: params => {
                return params.eventDate == '汇总'
              },
              click: ($index, item, params) => {
                const paramsData = { ...this.listQuery }
                paramsData['startDate'] = params.eventDate
                paramsData['endDate'] = params.eventDate
                delete paramsData.pageSize
                delete paramsData.pageNumber
                this.$router.push({
                  path: '/qjjp/statisticsOverview/advertisingStatisticsDetailChannel',
                  query: { ...paramsData }
                })
              }
            }
          ]
        }
      ]
    }
  },
  created() {},
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = countrAdvertisingExport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    changeStatus() {
      const params = this.curStatusParams
      downOrUp({ ...params }).then(res => {
        if (res.code == 200) {
          this.$message.success('更改状态成功')
          this.$store.dispatch('tableRefresh', this)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  display: block;
  margin-bottom: 5px;
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
