/*
* 风控系统子路由
* */

const push = [
  {
    path: '/risk/interceptrRule',
    name: 'risk_interceptrRule',
    meta: {
      title: '风控命中规则'
    },
    component: () => import('@/views/riskControlSym/page/interceptrRule')
  },
  {
    path: '/risk/interceptrRuleDetail',
    name: 'risk_interceptrRuleDetail',
    meta: {
      activeMenu: '/risk/interceptrRule',
      title: '风控命中规则详情'
    },
    component: () => import('@/views/riskControlSym/page/interceptrRuleDetail')
  },
  {
    path: '/risk/interceptrRecord',
    name: 'risk_interceptrRecord',
    meta: {
      title: '风控命中纪录'
    },
    component: () => import('@/views/riskControlSym/page/interceptrRecord')
  },
  {
    path: '/risk/userAttrList',
    name: 'risk_userAttrList',
    meta: {
      title: '用户属性列表'
    },
    component: () => import('@/views/riskControlSym/page/userAttrList')
  },
  {
    path: '/risk/userAttrTag',
    name: 'risk_userAttrTag',
    meta: {
      title: '用户属性标记'
    },
    component: () => import('@/views/riskControlSym/page/userAttrTag')
  },
  {
    path: '/risk/userStrategyDisturb',
    name: 'risk_userStrategyDisturb',
    meta: {
      title: '用户策略干扰'
    },
    component: () => import('@/views/riskControlSym/page/userStrategyDisturb')
  },
  {
    path: '/risk/userAttrZb',
    name: 'risk_userAttrZb',
    meta: {
      title: '用户属性占比表'
    },
    component: () => import('@/views/riskControlSym/page/userAttr/index.vue')
  },
  {
    path: '/risk/userAttrChannelZb/index',
    name: 'risk_usersChanneZbSinking_index',
    meta: {
      title: '用户属性占比渠道表'
    },
    component: () => import('@/views/riskControlSym/page/userAttr/channeZb')
  },
  {
    path: '/risk/userAttrChannelZb/sinking',
    name: 'risk_usersChanneZbSinking',
    meta: {
      title: '用户属性占比渠道下沉表',
      activeMenu: '/risk/userAttrChannelZb/index',
      parentTitle: '用户属性占比渠道表'
    },
    component: () => import('@/views/riskControlSym/page/userAttr/channelSinking')
  },
  {
    path: '/risk/userAttrChannelZb/mediaSinking',
    name: 'risk_usersChanneZbSinking_mediaSinking',
    meta: {
      title: '用户属性占比媒体下沉表',
      activeMenu: '/risk/userAttrChannelZb/index',
      parentTitle: '用户属性占比渠道表'
    },
    component: () => import('@/views/riskControlSym/page/userAttr/mediaSinking')
  },
  {
    path: '/risk/riskUsersZb',
    name: 'risk_riskUsersZb',
    meta: {
      title: '风险用户占比表'
    },
    component: () => import('@/views/riskControlSym/page/riskUsers/index.vue')
  },
  {
    path: '/risk/riskUsersChanneZb/index',
    name: 'risk_usersChanneZb_index',
    meta: {
      title: '风险用户占比渠道表'
    },
    component: () => import('@/views/riskControlSym/page/riskUsers/channeZb')
  },
  {
    path: '/risk/riskUsersChanneZb/sinking',
    name: 'risk_usersChanneZb',
    meta: {
      title: '风险用户占比渠道下沉表',
      activeMenu: '/risk/riskUsersChanneZb/index',
      parentTitle: '风险用户占比渠道表'
    },
    component: () => import('@/views/riskControlSym/page/riskUsers/channelSinking')
  },
  {
    path: '/risk/riskUsersChanneZb/mediaSinking',
    name: 'risk_usersChanneZb_mediaSinking',
    meta: {
      title: '风险用户占比媒体下沉表',
      activeMenu: '/risk/riskUsersChanneZb/index',
      parentTitle: '风险用户占比渠道表'
    },
    component: () => import('@/views/riskControlSym/page/riskUsers/mediaSinking')
  },
  {
    path: '/risk/userActionZb',
    name: 'risk_userActionZb',
    meta: {
      title: '用户行为占比表'
    },
    component: () => import('@/views/riskControlSym/page/userAction/index.vue')
  },
  {
    path: '/risk/userActionChanneZb/index',
    name: 'risk_userActionChanneZb_index',
    meta: {
      title: '用户行为占比渠道表'
    },
    component: () => import('@/views/riskControlSym/page/userAction/channeZb')
  },
  {
    path: '/risk/userActionChanneZb/sinking',
    name: 'risk_userAction_sinking',
    meta: {
      title: '用户行为占比渠道下沉表',
      activeMenu: '/risk/userActionChanneZb/index',
      parentTitle: '用户行为占比渠道表'
    },
    component: () => import('@/views/riskControlSym/page/userAction/channelSinking')
  },
  {
    path: '/risk/userActionChanneZb/mediaSinking',
    name: 'risk_userAction_mediaSinking',
    meta: {
      title: '用户行为占比媒体下沉表',
      activeMenu: '/risk/userActionChanneZb/index',
      parentTitle: '用户行为占比渠道表'
    },
    component: () => import('@/views/riskControlSym/page/userAction/mediaSinking')
  }
]

export default push
