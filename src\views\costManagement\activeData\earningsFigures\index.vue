<template>
  <div>   
    <page :list="list" :request="request" table-title="收益数据"> </page>
  </div>
</template>

<script>
import {count_duoduopay_earnings_list} from '@/api/costManagement'
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import moment from 'moment'
import { count_channel_application_list } from '@/api/NewChannel'
export default {
  components: {
    page,
  },
  data() {
    return {
      listQuery: {
        startDate: moment().subtract(6, 'd').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        siteId: '',
      },
      request: {
        getListUrl: async (data) => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = res.data
                this.listQuery.siteId = res.data[0].siteId
              }
            })
          }
          return Promise.all([
            count_duoduopay_earnings_list(this.listQuery)
          ]).then((res) => {
            return Promise.resolve(res[0])
          })
        },
      },
      siteIds: [],
    }
  },
  computed: {
    list() {
      return [
        {
          key: 'date',
          title: '时间',
          type: formItemType.datePickerDaterangeGai,
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
          },
          childKey: ['startDate', 'endDate'],
          search: true,
          val: [moment().subtract(6, 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId',
          },
          val:this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable:false,
          tableHidden: true,
        },
        {
          title: '充值成功人数（50/100/200）',
          key: 'paySuccessNum',
        },
        {
          title: '平台补贴金额',
          key: 'subsidy',
        },
        {
          title: '平台收益',
          key: 'finalEarning',
        },
        {
          title: '实际支付总金额',
          key: 'realPayAmount',
        },
        {
          title: '退款总金额',
          key: 'refundAmount',
        },
        {
          title: '总成本',
          key: 'rechargeCost',
        },
        {
          title: '充值成功成本',
          key: 'rechargeSuccessCost',
        },
        {
          title: '退款成本',
          key: 'rechargeFailCost',
        },
        {
          title: '实际成本',
          key: 'realCost',
        },
      ]
    },
  },
}
</script>

<style></style>
