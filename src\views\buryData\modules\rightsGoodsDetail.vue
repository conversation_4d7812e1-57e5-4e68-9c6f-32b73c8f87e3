<template>
  <page :request="request" :list="list">
    <div slot="searchContainer" style="display: inline-block">
      <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import moment from 'moment'
import { formItemType, tableItemType } from '@/config/sysConfig'
import { count_channel_application_list } from '@/api/NewChannel'
import {
  EXPORT_RIGHTS_GOODS_DETAILS,
  GET_RIGHTS_GOODS_DETAILS
} from '@/api/buryData'
export default {
  components: {
    page
  },
  props: {
    detailId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      request: {
        getListUrl: async(data) => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = [{ siteId: '', siteName: '综合' }, ...res.data]
                this.listQuery.siteId = ''
              }
            })
          }
          const { startTime, endTime, siteId } = this.listQuery
          return Promise.all([GET_RIGHTS_GOODS_DETAILS({ startTime: Number(startTime), endTime: Number(endTime), siteId, ...data, categoryId: this.detailId })]).then(
            (res) => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      listQuery: {
        startTime: moment()
          .subtract(7, 'days')
          .format('YYYYMMDD'),
        endTime: moment().format('YYYYMMDD'),
        siteId: ''
      },
      siteIds: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startTime', 'endTime'],
          options: {
            format: 'YYYYMMDD',
            valueFormat: 'yyyyMMdd'
          },
          val: [
            moment()
              .subtract(7, 'days')
              .format('YYYYMMDD'),
            moment().format('YYYYMMDD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: this.listQuery.siteId,
          clearable: false,
          reg: ['required'],
          search: true,
          tableHidden: true
        },
        {
          title: '平台',
          key: 'platform',
          type: formItemType.select,
          list: [
            {
              label: 'IOS',
              value: 'ios'
            },
            {
              label: 'ANDROID',
              value: 'android'
            }
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '橱窗名称',
          key: 'title'
        },
        {
          title: '橱窗PV/UV',
          key: 'banner'
        },
        {
          title: '状态',
          key: 'status'
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          list: [
            {
              label: '上架',
              value: '0'
            },
            {
              label: '下架',
              value: '1'
            }
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        }
      ]
    }
  },
  watch: {},
  mounted() {
  },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_RIGHTS_GOODS_DETAILS({
        ...data,
        categoryId: this.detailId,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
