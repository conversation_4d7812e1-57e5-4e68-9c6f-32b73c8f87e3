<template>
  <div>
    <page :request="request" :list="list" table-title="投放链路统计">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getmerchantcount, downOrUp, getCountrAdvertisingChannelDetail, countrAdvertisingChannelDetailExport } from '@/qjjpApi/statisticsOverview'
import moment from 'moment'
import advertisingStatisticsAll from './advertisingStatisticsAll'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'AdvertisingStatisticsDetailChannel',
  components: {
    page
  },
  mixins: [advertisingStatisticsAll],
  props: {},
  data() {
    return {
      curStatusParams: {
        id: 0,
        status: 0
      },
      listQuery: {
        startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        ...this.$route.query
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await getCountrAdvertisingChannelDetail(this.listQuery)
          setTimeout(() => {
            if (Object.keys(this.$route.query).length > 0) {
              Object.keys(this.$route.query).forEach(key => {
                this.$setLocaUrlQuery(key, '')
              })
            }
          }, 0)
          const { records, total } = list.data
          console.info(list, 'list')
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '渠道ID',
          key: 'channelCode',
          fixed: 'left'
          // render: (h, params) => {
          //   const channelCode = params.data.row.channelCode
          //   return h('span', channelCode || '汇总')
          // }
        },
        ...this.getSearchList(),
        ...this.getListArray({ isShowChannelCode: false })
      ]
    }
  },
  created() {},
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = countrAdvertisingChannelDetailExport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    changeStatus() {
      const params = this.curStatusParams
      downOrUp({ ...params }).then(res => {
        if (res.code == 200) {
          this.$message.success('更改状态成功')
          this.$store.dispatch('tableRefresh', this)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  display: block;
  margin-bottom: 5px;
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
