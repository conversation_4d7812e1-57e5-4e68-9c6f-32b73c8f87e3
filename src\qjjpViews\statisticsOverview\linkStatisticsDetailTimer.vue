<template>
  <div>
    <page :request="request" :list="list" table-title="投放链路统计">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page/v8'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getmerchantcount, downOrUp, getlinkHourDetail, linkHourDetailExport } from '@/qjjpApi/statisticsOverview'
import moment from 'moment'
import linkStatisticsAll from './linkStatisticsAll'
const currentDate = moment().format('YYYY-MM-DD')
import { checkChannelLook } from '@/qjjpApi/system'
import {
  get_admin_list
} from '@/api/system'
export default {
  name: 'LinkStatisticsDetailTimer',
  components: {
    page
  },
  mixins: [linkStatisticsAll],
  props: {},
  data() {
    return {
      haveQX: false,
      adminList: [],
      curStatusParams: {
        id: 0,
        status: 0
      },

      siteIds: [],
      listQuery: {
        iosServiceChargeType: 1,
        fixedDate: false,
        startDate: this.$route.query.startDate || moment().subtract(1, 'days').format('YYYY-MM-DD'),
        endDate: this.$route.query.endDate || moment().format('YYYY-MM-DD'),
        ...this.$route.query
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await getlinkHourDetail(this.listQuery)
          await checkChannelLook().then(async res => {
            if (res.code === 200) {
              this.haveQX = res.data
              if (res.data) {
                await get_admin_list({ pageSize: 1000 }).then(res => {
                  if (res.code === 0) {
                    this.adminList = res.data
                  }
                })
              }

            }
          })
          setTimeout(() => {
            if (Object.keys(this.$route.query).length > 0) {
              Object.keys(this.$route.query).forEach(key => {
                this.$setLocaUrlQuery(key, '')
              })
            }
          }, 0)
          const { records, total } = list.data
          console.info(list, 'list')
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '时段',
          key: 'eventHourStr',
          fixed: 'left',
          render: (h, params) => {
            const eventHourStr = params.data.row.eventHourStr
            return (
              <div>
                {!eventHourStr || eventHourStr == '' || eventHourStr == '汇总' ? <div>汇总 <el-tooltip
                  content='按照筛选条件进行去重汇总计算'
                  placement='top'
                  style='color:#409eff'
                >
                  <i class='el-icon-question' style='font-size: 14px' />
                </el-tooltip>
                </div> : eventHourStr}
              </div>

            )
          }
        },
        ...this.getListArray({ pickerDay: 0, isShowChannelCode: false, isShowLoginType: false, isShowPayScenee: false, from: 'detailTimer' })
      ]
    }
  },
  created() { },
  methods: {
    fixedDateChange() {
      this.$store.dispatch('tableRefresh', this)
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = linkHourDetailExport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    changeStatus() {
      const params = this.curStatusParams
      downOrUp({ ...params }).then(res => {
        if (res.code == 200) {
          this.$message.success('更改状态成功')
          this.$store.dispatch('tableRefresh', this)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.checkFixedDate {
  margin-left: 20px;
}

.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
