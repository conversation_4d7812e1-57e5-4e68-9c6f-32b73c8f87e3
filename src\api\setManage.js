import {put, get, post, del} from '@/libs/axios.package'

/*
 * 获取红包初始金额
 * */
export const getInitRedRnvelopes = obj => {
  return get('/set/redpack', obj, false)
}
/*
 * 提交红包设置
 * */
export const submitRedPackageSettings = obj => {
  return put('/set/redpack', obj, false)
}
/*
 * 获取金豆百分比初始值
 * */
export const getGoldenPercentageSetApi = obj => {
  return get('/systemConfig/getByParam', obj, false)
}
/*
 * 设置金豆百分比
 * */
export const setGoldenPercentageSetApi = obj => {
  return post('/systemConfig/update', obj, false)
}
/*
 * 获取金豆数量设置
 * */
export const getGoldenNumSetApi = obj => {
  return get('/setSign/list', obj, false)
}
/*
 * 设置金豆数量设置
 * */
export const setGoldenNumSetApi = obj => {
  return post('/setSign/add', obj, false)
}

/*
 * 获取金豆数量设置版本2
 * */
export const getGoldenNumSetApiV2 = obj => {
  return get('/setsigninv/list', obj, false)
}
/*
 * 设置金豆数量设置版本2
 * */
export const setGoldenNumSetApiV2 = obj => {
  return post('/setsigninv/add', obj, false)
}

/*
 * 获取新人任务设置
 * */
export const getNewTaskSetApi = obj => {
  return get('/setNewUserTask/list', obj, false)
}
/*
 * 提交新人任务设置
 * */
export const setNewTaskSetApi = obj => {
  return post('/setNewUserTask/add', obj, false)
}
/*
 * 获取每日任务
 * */
export const getDayTaskSetApi = obj => {
  return get('/setEveryDayTask/list', obj, false)
}
/*
 * 设置每日任务
 * */
export const setDayTaskSetApi = obj => {
  return post('/setEveryDayTask/add', obj, false)
}
/*
 * 获取宝箱设置
 * */
export const getTreasureBoxSettingApi = obj => {
  return get('/set/timing/box/detail', obj, false)
}
/*
 * 设置宝箱设置
 * */
export const setTreasureBoxSettingApi = obj => {
  return get('/set/timing/box', obj, false)
}
/*
 * 提现列表查询
 * */
export const getWithdrawalListApi = obj => {
  return get('/withdrawManager/list', obj, false)
}
/**
 *  提现说明
 */
export const getSystemConfigByParam = obj => {
  return get('/systemConfig/getByParam', {scope: 'withdraw_manager', code: 'desc'}, false)
}
/*
 * 设置极速提现
 * */
export const setSpeedtWithdrawalListApi = obj => {
  return post('/withdrawManager/updateFastWithdraw', obj, false)
}
/*
 * 设置普通提现
 * */
export const setConventionalcWithdrawalApi = obj => {
  return post('/withdrawManager/updateCommonWithdraw', obj, false)
}
/*
 * 设置网赚提现
 * */
export const setEarningWithdrawApi = obj => {
  return post('/withdrawManager/updateEarningWithdraw', obj, false)
}
/*
 * 设置提现说明
 * */
export const setSystemConfigUpdate = obj => {
  return post('/systemConfig/update', obj, false)
}
/*
 * 获取京东返利比列
 * */
export const getRebateApi = obj => {
  return get('/systemConfig/getByParam', obj, false)
}
/*
 * 设置京东返利比列
 * */
export const setRebateApi = obj => {
  return post('/systemConfig/update', obj, false)
}
/*
 * 获取关键词
 * */
export const getKeyWordsApi = obj => {
  return get('/goods/keywords/list', obj, false)
}
/*
 * 添加关键词
 * */
export const setKeyWordsApi = obj => {
  return get('/goods/keywords', obj, false)
}
/*
 * 删除关键词
 * */
export const deleteKeyWordApi = id => {
  return del(`/goods/keywords/${id}`, {}, false)
}

export const SELECT_SYS_PARAM = obj => {
  return get('/systemParam/selectSysParam', {code: 'WITHDRAW_DESC'})
}

export const UPDATE_BY_ID = obj => {
  return post('/systemParam/updateById', obj)
}

export const GET_PARAM_INFO = obj => {
  return get('/systemConfig/getByParam', obj)
}

export const SET_PARAM_INFO = obj => {
  return post('/systemConfig/update', obj)
}
