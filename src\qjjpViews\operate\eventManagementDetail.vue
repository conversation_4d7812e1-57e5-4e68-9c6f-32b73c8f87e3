<template>
    <div id="eventManagementDetail">
        <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="120px" class="demo-ruleForm">
            <div class="form_view">
                <div class="form_view_title">
                    <div class="title_line" /><span>基础信息</span>
                </div>
            </div>
            <el-form-item label="活动类型" prop="activityType" :rules="addRules.common"
                :style="{ 'display': 'inline-block', 'margin-right': '20px' }">
                <el-select v-model="addForm.activityType" clearable placeholder="请选择活动类型">
                    <el-option v-for="item in activityTypeList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="活动名称" prop="activityName" :rules="addRules.common"
                :style="{ 'display': 'inline-block', 'margin-right': '20px' }">
                <div :style="{ width: '200px', 'display': 'inline-block', 'margin-right': '20px' }">
                    <el-input v-model="addForm.activityName" placeholder="请输入活动名称" maxlength="50" />
                </div>
                <span>{{ addForm.activityName ? addForm.activityName.length : 0 }}/50</span>
            </el-form-item>
            <el-form-item label="权益商品" prop="activityName" :rules="addRules.common" :style="{ 'margin-right': '20px' }">
                <el-button type="primary" plain size="small" class="search-btn"
                    @click="checkSPShow = true">选择商品</el-button>
                <el-table :data="addForm.thirdGoodsEquityReqList" border ref="table" style="width: 100%"
                    v-loading="categoryIsSort" v-if="!categoryIsSort" :row-style="hideRow" row-key="ID"
                    id="categoryList">
                    <el-table-column prop="date" width="180">
                        <template slot-scope="scope">
                            <i class="el-icon-rank" @click="handleClick(scope.row)"></i>
                        </template>
                    </el-table-column>
                    <el-table-column prop="thirdGoodsId" label="商品库ID" width="180">
                    </el-table-column>
                    <el-table-column prop="equityType" label="权益分类" width="180">
                        <template slot-scope="scope">
                            <span>{{ scope.row.equityType == 0 ? '影音会员' : scope.row.equityType == 1 ? '话费券' : '-'
                                }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="officialPrice" label="原价">
                    </el-table-column>
                    <el-table-column prop="salePrice" label="售价">
                    </el-table-column>
                    <el-table-column prop="drawDownNum" label="单期参与数量">
                    </el-table-column>
                    <el-table-column prop="showName" label="显示名称">
                    </el-table-column>
                    <el-table-column prop="showUrl" label="封面图">
                        <template slot-scope="scope">
                            <el-image style="width: 100px; height: 100px" :src="scope.row.showUrl"
                                :preview-src-list="[scope.row.showUrl]">
                            </el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="statusType" label="上架状态">
                        <template slot-scope="scope">
                            <span>{{ scope.row.statusType == 0 ? '下架' : scope.row.statusType == 1 ? '在售' :
                                scope.row.statusType == 2 ?
                                    '售馨' :
                                    scope.row.statusType == 3 ?
                                        '删除' : '-' }}</span>
                        </template>

                    </el-table-column>
                    <el-table-column prop="address" label="操作">
                        <template slot-scope="scope">
                            <el-button @click="uptSp(scope.row)" type="primary" plain>编辑</el-button>
                            <el-button type="danger" plain @click="delSp(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
            <div class="form_view">
                <div class="form_view_title">
                    <div class="title_line" /><span>显示设置</span>
                </div>
            </div>
            <el-form-item label="活动场景" prop="activityScene" :style="{ 'margin-right': '20px', width: '500px' }">
                <tableSelect :table-list="activitySceneList" :defaultSelect="defaultSelectPackIds" :key-list="keyList"
                    @emitSelect="addForm.activityScene = $event" @checkEl="checkEl" />
            </el-form-item>
            <div class="form_view">
                <div class="form_view_title">
                    <div class="title_line" /><span>活动时间</span>
                </div>
            </div>
            <el-form-item label="单期活动时间" prop="activityDay" :rules="addRules.common"
                :style="{ 'margin-right': '20px', width: '500px' }">
                <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                    单期活动时间
                    <el-tooltip content="单期活动群益商品的发放时间，即用户支付成功权益订单号后每T+29day 零点发放一次权益商品，T=权益订单支付日期"
                        placement="top-start" :style="{ color: '#409eff' }">
                        <i class="el-icon-question" style="font-size: 14px" />
                    </el-tooltip>
                </span>
                <el-select v-model="addForm.activityDay" clearable placeholder="请选择单期活动时间">
                    <el-option v-for="item in activityDayList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <div class="form_view">
                <div class="form_view_title">
                    <div class="title_line" /><span>启用状态</span>
                </div>
            </div>
            <el-form-item label="状态" prop="activityStatus" :rules="addRules.common">
                <el-switch v-model="addForm.activityStatus" :active-value="1" :inactive-value="0" />
            </el-form-item>
            <div :style="{ 'text-align': 'right', width: '100%' }">
                <el-button @click="qx">取消</el-button>
                <el-button type="primary" @click="submit('addForm')">确认</el-button>
            </div>
        </el-form>
        <el-dialog v-if="checkSPShow" title="选择商品" :visible.sync="checkSPShow" :close-on-click-modal="false"
            @close="closeCheckSPShow">
            <div style="display: flex; align-items: center; margin-bottom: 10px">
                <page :request="request2" :list="list2" class="complaint-list" table-type="selection"
                    @selectionChange="selectionChange" :table-select-methods="tableSelectMethods" />
            </div>
            <div :style="{ 'text-align': 'right', width: '100%' }">
                <el-button @click="closeCheckSPShow">取消</el-button>
                <el-button type="primary" @click="CheckSPList(1)">确认</el-button>
            </div>
        </el-dialog>


        <el-drawer v-if="drawer" :visible.sync="drawer" direction="rtl" size="50%" :with-header="false"
            :wrapper-closable="false">
            <div class="close_button">
                <i class="el-icon-close" @click="drawer = false" />
            </div>
            <div class="drawer_package">
                <div class="drawer_title">
                    <span>商品编辑/新增</span>
                </div>
                <div class="addForm_package">
                    <el-form ref="addForm1" :model="addForm1" :rules="addRules" label-width="180px"
                        class="demo-ruleForm">
                        <div class="form_view">
                            <div class="form_view_title">
                                <div class="title_line" /><span>基础信息</span>
                            </div>
                            <el-form-item label="商品名称" prop="name" :rules="addRules.common"
                                :style="{ display: 'inline-block' }">
                                <div :style="{ width: '200px', display: 'inline-block' }">
                                    <el-input v-model="addForm1.name" placeholder="请输入商品名称" maxlength="30" disabled />
                                </div>
                            </el-form-item>
                            <el-form-item label="权益类型" prop="equityType" :rules="addRules.common"
                                :style="{ display: 'inline-block' }">
                                <div :style="{ width: '200px', display: 'inline-block ' }">
                                    <el-select v-model="addForm1.equityType" placeholder="请选择权益类型" disabled>
                                        <el-option v-for="item in typeList" :key="item.id" :label="item.name"
                                            :value="item.id" :disabled="Number(item.id) == 0" />
                                    </el-select>
                                </div>
                            </el-form-item>
                            <el-form-item label="供应商" prop="goodsSupplier" :rules="addRules.common"
                                :style="{ display: 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-select v-model="addForm1.goodsSupplier" placeholder="请选择供应商" disabled>
                                        <el-option v-for="item in supplierListData" :key="item.value"
                                            :label="item.label" :value="item.value"
                                            :disabled="Number(item.type) == 0" />
                                        <!-- <el-option v-for="item in supplierListData" :key="item.value">{{ item.type
                                            }}</el-option> -->
                                    </el-select>
                                </div>
                            </el-form-item>
                            <el-form-item label="话费面额" prop="billFaceAmount" :rules="addRules.common"
                                :style="{ display: 'inline-block' }" v-if="addForm1.type == 1">
                                <div :style="{ width: '200px' }">
                                    <el-select v-model="addForm1.billFaceAmount" placeholder="请选择话费面额" disabled>
                                        <el-option v-for="item in hfList" :key="item.id" :label="item.name"
                                            :value="item.id" />
                                    </el-select>
                                </div>
                            </el-form-item>
                            <el-form-item label="成本价" prop="price" :rules="addRules.common"
                                :style="{ display: 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-input v-model="addForm1.price" placeholder="请输入成本价" maxlength="30" disabled />
                                </div>
                            </el-form-item>
                            <el-form-item label="原价" prop="officialPrice" :rules="addRules.common"
                                :style="{ display: 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-input v-model="addForm1.officialPrice" placeholder="请输入原价" maxlength="30"
                                        disabled />
                                </div>
                            </el-form-item>
                            <el-form-item label="售价" prop="salePrice" :rules="addRules.common"
                                :style="{ display: 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-input v-model="addForm1.salePrice" placeholder="请输入售价" maxlength="30"
                                        disabled />
                                </div>
                            </el-form-item>
                            <el-form-item label="单期发放数量" prop="drawDownNum" :rules="addRules.common">
                                <div :style="{ width: '200px' }">
                                    <el-input v-model="addForm1.drawDownNum" placeholder="请输入单期发放数量"
                                        oninput="value=value.replace(/^\D*([0-9]\d*)?.*$/,'$1')"
                                        @blur="addForm1.drawDownNum = $event.target.value" />
                                    <span class="tips_txt">(单个权益会员每期可以领取的权益商品数量)</span>
                                </div>
                            </el-form-item>
                            <el-form-item label="显示商品名称" prop="showName" :rules="addRules.common"
                                :style="{ display: 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-input v-model="addForm1.showName" placeholder="请输入显示商品名称" maxlength="20" />
                                </div>
                            </el-form-item>
                            <el-form-item label="封面图" prop="showUrl" :rules="addForm1.common">
                                <uploadFile v-model="addForm1.showUrl" :width="180" :height="120" :multiple="false"
                                    :size="imgSize" :limit="1">
                                    <div class="addDiv">
                                        <i class="el-icon-plus" />
                                        <span class="txt">上传图标(.jpg/.png/.gif) <br>图片大小{{ filterSize(imgSize) }}</span>
                                    </div>
                                </uploadFile>
                            </el-form-item>
                            <el-form-item label="充值方式" prop="topUpType" :rules="addRules.common"
                                :style="{ display: 'inline-block' }">
                                <div :style="{ width: '200px' }">
                                    <el-checkbox-group v-model="addForm1.topUpType">
                                        <el-checkbox label="1">手机号</el-checkbox>
                                        <el-checkbox label="2">qq</el-checkbox>
                                    </el-checkbox-group>
                                </div>
                            </el-form-item>
                        </div>

                        <div class="form_view">
                            <div class="form_view_title">
                                <div class="title_line" /><span>上架状态</span>
                            </div>
                            <el-form-item label="状态" prop="statusType" :rules="addRules.common">
                                <el-radio-group v-model="addForm1.statusType" size="mini">
                                    <el-radio-button label="0">下架</el-radio-button>
                                    <el-radio-button label="1">在售</el-radio-button>
                                    <el-radio-button label="2">售馨</el-radio-button>
                                    <!-- <el-radio-button label="3">删除</el-radio-button> -->
                                </el-radio-group>
                            </el-form-item>
                        </div>
                        <div :style="{ 'text-align': 'right', width: '100%' }" class="view_button">
                            <el-button @click="drawer = false">取消</el-button>
                            <el-button type="primary" @click="handMessageStyleListAdd('addForm1')">确认</el-button>
                        </div>
                    </el-form>
                </div>
            </div>
        </el-drawer>

        <el-drawer v-if="elDrawer" :visible.sync="elDrawer" direction="rtl" size="50%" :with-header="false"
            :wrapper-closable="false">
            <div class="close_button">
                <i class="el-icon-close" @click="elDrawer = false" />
            </div>
            <div class="drawer_package">
                <div class="drawer_title">
                    <span>场景说明</span>
                </div>

                <div class="addForm_package">
                    <el-form ref="addForm1" :model="addForm1" :rules="addRules" label-width="180px"
                        class="demo-ruleForm">
                        <div class="form_view">
                            <div class="form_view_title">
                                <div class="title_line" /><span>场景UI</span>
                            </div>
                            <div class="el_img_view">
                                <el-image style="width: 187px;" :src="img1_pop" :preview-src-list="[img1_pop]" class="">
                                </el-image>
                            </div>
                            <div class="form_view_title">
                                <div class="title_line" /><span>场景显示逻辑</span>
                            </div>
                            <div class="form_txt1">
                                <div class="form_txt1_title">
                                    一、全局说明：
                                </div>
                                <div class="form_txt1_main">
                                    1、所有触达场景均根据后台活动配置决定<br />
                                    2、引导使用页支付抽屉、AI识图支付抽屉支付成功后显示支付成功弹窗；其他地方支付成功均进入权益领取页<br />
                                    3、只对普通用户显示<br />
                                    4、已购买常规会员用户，在常规会员生效期内，无法再购买会员，会员过期后可再次购买联合会员<br />
                                    5、已退款用户、设备不支持再次购买（不管任何会员，白名单用户&设备除外）
                                </div>
                            </div>
                            <div class="form_txt2">
                                <div class="form_txt2_title">
                                    <span class="form_txt2_title_txt1">二、场景显示逻辑：</span>
                                    <span class="form_txt2_title_txt2" @click="elAllShow">查看全部场景</span>
                                </div>
                                <div class="form_txt2_main" :style="{ 'margin-Top': '20px' }">
                                    <el-table :data="elCheckList" border style="width: 100%">
                                        <el-table-column prop="name" label="名称" width="180">
                                        </el-table-column>
                                        <el-table-column prop="txt" label="概述" align="left">
                                            <template slot-scope="scope">
                                                <div v-html="scope.row.txt" :style="{ 'text-align': 'left' }"></div>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>

                            </div>
                        </div>
                    </el-form>
                </div>
            </div>
        </el-drawer>

        <el-dialog title="全部场景" :visible.sync="elAll">
            <el-table :data="elList" border>
                <el-table-column property="sort" label="序号" width="150"></el-table-column>
                <el-table-column property="name" label="名称" width="200"></el-table-column>
                <el-table-column property="txt" label="概述">
                    <template slot-scope="scope">
                        <div v-html="scope.row.txt" :style="{ 'text-align': 'left' }"></div>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<script>
import {
    getQuestionsList
} from '@/qjjpApi/memberRefund'
import Sortable from 'sortablejs'
import page from '@/components/restructure/page'
import tableSelect from './components/tableSelect'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { thirdGoodsPage, syncThirdGoods, thirdGoodsCreate, thirdGoodsUpdate, supplierList, equityActivitiesCreate, equityActivitiesUpdate, equityActivitiesDetail } from '@/qjjpApi/operate'
import Json from '@/components/restructure/upload/json.vue'
import img1 from '@/assets/img/cj_img/img_1.png'
import img2 from '@/assets/img/cj_img/img_2.png'
import img3 from '@/assets/img/cj_img/img_3.png'
import img4 from '@/assets/img/cj_img/img_4.png'
import img5 from '@/assets/img/cj_img/img_5.png'
import img6 from '@/assets/img/cj_img/img_6.png'
import img7 from '@/assets/img/cj_img/img_7.jpg'
import img8 from '@/assets/img/cj_img/img_8.jpg'
import img9 from '@/assets/img/cj_img/img_9.png'
export default {
    components: {
        page,
        tableSelect,
        uploadFile: () => import('@/components/yc-upload/handleUploadImage')
    },
    watch: {
        categoryIsSort: {
            handler(val) {
                if (!val) { this.int() }
            },
        },
        elId: {
            handler(vals) {
                let arr = {}
                let val = vals
                // arr = this.elList.find(item => item.id == val)
                console.info(this.elCheckList, '我执行了')
                switch (val) {
                    case '0':
                        arr = this.elList[3]
                        break;
                    case '26':
                        arr = this.elList[3]
                        break;
                    case '14':
                        arr = this.elList[2]
                        break;
                    case '23,24,25':
                        arr = this.elList[1]
                        break;
                    case '1,2,8':
                        arr = this.elList[4]
                        break;
                    case '6,7,9':
                        arr = this.elList[5]
                        break;
                    case '27,28,29':
                        arr = this.elList[6]
                        break;
                    case '10':
                        arr = this.elList[7]
                        break;
                    case '35,36,37':
                        arr = this.elList[8]
                        break;
                    default:
                        break;
                }
                this.elCheckList = [{ ...arr }]

            },
            deep: true,
            immediate: true
        }
    },
    async mounted() {
        const { data: supplierListData } = await supplierList()
        this.supplierListData = supplierListData.map(n => ({ label: n.name, value: n.code, type: n.type }))
        if (this.$route.query.type == 'add') {
            this.addForm.activityScene = ['35,36,37']
            this.addForm = { ...this.addForm, activityScene: ['35,36,37'] }
            this.defaultSelectPackIds.push('35,36,37')
        }
        console.info(this.addForm, 'this.addForm')

        if (this.$route.query.type == 'edit') {
            equityActivitiesDetail(this.$route.query.id).then(res => {
                let str = res.data.activityScene.split(',')
                let arr = []
                for (let i = 0; i < this.activitySceneList.length; i++) {
                    let arrs = this.activitySceneList[i].id.split(',')
                    let commonCount = this.countCommonValues(str, arrs);
                    console.info(arrs, commonCount)
                    if (commonCount != 0) {
                        arr.push(this.activitySceneList[i].id)
                        this.defaultSelectPackIds.push(this.activitySceneList[i].id)
                    }
                }
                // return
                this.addForm = { ...res.data, thirdGoodsEquityReqList: res.data.thirdGoodsEquityVos, activityScene: arr }
                this.$nextTick(() => {
                    for (let i = 0; i < this.addForm.thirdGoodsEquityReqList.length; i++) {
                        if (this.addForm.thirdGoodsEquityReqList[i].topUpType) {
                            this.addForm.thirdGoodsEquityReqList[i].topUpType = this.addForm.thirdGoodsEquityReqList[i].topUpType.split(",")

                        }
                        console.info(this.addForm.thirdGoodsEquityReqList[i], 'this.addForm')
                    }

                })


            })
        }
        this.int()
    },
    data() {
        return {
            img1_pop: '',
            elId: '',
            elCheckList: [],
            goodsTypeList: [
                {
                    id: -1,
                    name: '客服直冲'
                },
                {
                    id: 6,
                    name: 'api充值'
                },
            ],
            elAll: false,
            elList: [
                {
                    id: '',
                    sotr: 0,
                    name: '全局说明',
                    txt: `1、所有触达场景均根据后台活动配置决定<br/>
2、引导使用页支付抽屉、AI识图支付抽屉支付成功后显示支付成功弹窗；其他地方支付成功均进入权益领取页<br/>
3、只对普通用户显示<br/>
4、已购买常规会员用户，在常规会员生效期内，无法再购买会员，会员过期后可再次购买联合会员<br/>
5、已退款用户、设备不支持再次购买（不管任何会员，白名单用户&设备除外）`
                },
                {
                    id: '',
                    sotr: 1,
                    name: '复登',
                    txt: `触发复登场景时判断：<br/>
1、对应渠道存在联合会员，显示联合会员活动页<br/>
2、对应渠道不存在联合会员，显示原复登页<br/>
去掉原有触发条件：<br/>
1、仅对已达标用户触达（去掉）`,
                },
                {
                    id: '',
                    sotr: 2,
                    name: '待支付',
                    txt: `触发待支付场景时判断：<br/>
1、对应待支付订单是联合会员，显示联合会员待支付页<br/>
2、对应待支付订单不是联合会员，显示原待支付页<br/>
新增待支付订单显示频次<br/>
1、同一笔打开2次过后（含首次未支付返回）销毁待支付订单页面，进入其他二次打开app场景(常规会员和联合会员都遵循此逻辑)`
                },
                {
                    id: '',
                    sotr: 3,
                    name: '首页弹窗',
                    txt: `主推场景（弹窗）新增条件：<br/>
1、只存在联合会员时，显示联合会员推荐弹窗（新增），不再显示次推弹窗<br/>
2、只存在非联合会员时，弹出条件不变<br/>
3、联合会员与普通会员都存在时，取的sku是联合会员时显示联合会员主推推荐弹窗（新），取的sku非联合会员时显示原弹窗（老）<br/>
次推场景（弹窗）新增条件：<br/>
1、只存在联合会员时，不再显示次推弹窗<br/>
2、只存在非联合会员时，弹出条件不变<br/>
3、联合会员与普通会员都存在时，取的sku是联合会员时显示联合会员次推推荐弹窗（新），取的sku非联合会员时显示原弹窗（老）<br/>
<span style="color:#409eff">注：主推与次推sku取值一样时，按支付金额从高到底向下取，主推支付金额最高，次推支付金额第二，只有一个sku只显示主推</span> `
                },
                {
                    id: '',
                    sotr: 4,
                    name: '会员中心',
                    txt: `进入会员中心：<br/>
1、支付sku存在联合会员时，新增联合会员sku显示`
                },
                {
                    id: '',
                    sotr: 5,
                    name: '引导使用页',
                    txt: `支付抽屉：<br/>
1、支付sku存在联合会员时，新增联合会员sku显示<br/>
引导使用页返回新增条件<br/>
1、未达标，存在联合会员进入联合会员支付页；不存在联合会员，进入原会员中心<br/>
2、已达标，存在打卡会员进入打卡会员页，不存在打卡会员直接退出<br/>
<span style="color:#409eff">新增系统参数控制返回是否进入对应活动页</span>`
                },
                {
                    id: '',
                    sotr: 6,
                    name: 'AI识图',
                    txt: `支付抽屉：<span style="color:#409eff">（与引导使用页一套）</span><br/>
1、支付sku存在联合会员时，新增联合会员sku显示`
                },
                {
                    id: '',
                    sotr: 7,
                    name: '0元打卡页（包含底导）',
                    txt: `底导：<br/>
1、对应支付仅存在联合会员时，底导显示联合会员，页面显示联合会员活动页<br/>
2、对应支付不存在联合会员或联合会员+普通会员同步存在时，底导显示打卡会员，页面显示打卡会员页，新增联合会员sku显示<br/>
优先级：<br/>
1、会员用户：购买联合会员或打卡联合会员（显示联合会员领取页）——购买打卡会员（显示打卡页）<br/>
2、普通用户：系统参数是否显示——是否关联打卡场景——是否存在联合会员sku`
                },
                {
                    id: '',
                    sotr: 8,
                    name: '权益会员场景',
                    txt: `权益会员活动页<br/>
1、复登：设备对应渠道存在联合会员，显示联合会员活动页<br/>
2、活动底导：对应支付仅存在联合会员时，底导显示联合会员，页面显示联合会员活动页<br/>
权益会员待支付页<br/>
1、对应待支付订单是联合会员，显示联合会员待支付页<br/>
首页权益会员推荐弹窗：见首页弹窗说明`
                }
            ],
            elDrawer: false,
            delArray: [],
            categoryIsSort: false,
            tableSelectMethods: {
                selectable: data => {
                    return !this.addForm.thirdGoodsEquityReqList.find(item => item.thirdGoodsId == data.id)
                }
            },
            addForm1: {
                topUpType: []
            },
            imgSize: 500 * 1024,
            hfList: [
                {
                    name: '10',
                    id: 10
                },
                {
                    name: '20',
                    id: 20
                },
                {
                    name: '30',
                    id: 30
                },
            ],
            gysList: [
                {
                    name: '蓝色兄弟',
                    id: 0
                },
            ],
            typeList: [
                {
                    name: '话费券',
                    id: 1
                },
                {
                    name: '影音会员',
                    id: 0
                }
            ],
            drawer: false,
            listQuery2: {},
            checkList: [],
            stautsList: [
                {
                    label: '启用',
                    value: 1
                },
                {
                    label: '禁用',
                    value: 0
                }
            ],
            supplierListData: [],
            typeListData: [
                {
                    label: '话费卷',
                    value: 1
                },
                {
                    label: '音影会员',
                    value: 0
                }
            ],
            request: {
                getListUrl: async data => {
                    const list = await getQuestionsList({
                        planId: this.planId,
                        scene: 1,
                        ...data
                    })
                    const { records, total } = list.data
                    let dataList = []
                    if (records && records.length) {
                        dataList = [...records]
                    }
                    const result = {
                        data: dataList
                    }
                    return result
                }
            },
            request2: {
                getListUrl: async data => {
                    this.listQuery2 = { ...this.listQuery2, ...data }
                    // if (this.supplierListData.length == 0) {
                    const { data: supplierListData } = await supplierList()
                    this.supplierListData = supplierListData.map(n => ({ label: n.name, value: n.code, type: n.type }))
                    // }
                    const list = await thirdGoodsPage({ ...this.listQuery2, sellStatus: 1 })
                    const { records, total } = list.data
                    let dataList = []
                    if (records && records.length) {
                        dataList = {
                            total: total,
                            rows: records
                        }
                    }
                    const result = {
                        data: dataList
                    }
                    return result
                }
            },
            listQuery: {},
            activityDayList: [
                {
                    id: 29,
                    name: '一个月(T+29day)'
                }
            ],
            defaultSelectPackIds: [],
            activitySceneList: [
                {
                    id: '35,36,37',
                    name: '权益会员',
                    img: img1
                },
                {
                    id: '4,11,12',
                    name: '站内承接',
                    img: img9
                },
                {
                    id: '0',
                    name: '主推弹窗',
                    img: img2
                },
                {
                    id: '26',
                    name: '次推弹窗',
                    img: img3
                },
                {
                    id: '14',
                    name: '待支付页',
                    img: img4
                },
                {
                    id: '23,24,25',
                    name: '复登',
                    img: img5
                },
                {
                    id: '1,2,8',
                    name: '会员中心',
                    img: img6
                },
                {
                    id: '6,7,9',
                    name: '引导体验页',
                    img: img7
                },
                {
                    id: '27,28,29',
                    name: 'AI识图',
                    img: img8
                },
                {
                    id: '10',
                    name: '0元打卡页（活动底导）',
                    img: img9
                },

            ],
            checkSPShow: false,
            activityTypeList: [
                {
                    id: 0,
                    name: '会员权益活动'
                }
            ],
            addForm: {
                activityType: '',
                activityName: '',
                equityTypes: '',
                activityScene: '',
                activityStatus: '',
                activityDay: '',
                updateName: '',
                thirdGoodsEquityReqList: [],
                // thirdGoodsEquityReqList: [
                //     {
                //         id: '',
                //         goodsSupplier: '',
                //         thirdGoodsId: '',
                //         officialPrice: '',
                //         salePrice: '',
                //         drawDownNum: '',
                //         showName: '',
                //         showUrl: '',
                //         topUpType: '',
                //         sort: '',
                //         statusType: '',
                //         equityType: ''
                //     }
                // ]
            },
            addRules: {
                common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
            }
        }
    },
    computed: {
        list2() {
            return [
                {
                    title: '商品库名称',
                    key: 'name',
                    type: formItemType.input,
                    search: true,
                    clearable: true,
                    tableHidden: true,
                },
                {
                    title: '商品库ID',
                    key: 'id'
                },
                {
                    title: '供应商',
                    key: 'goodsSupplier',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.supplierListData,
                    clearable: true
                },
                {
                    title: '权益类型',
                    key: 'type',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.typeListData,
                    clearable: true
                },
                {
                    title: '商品名称',
                    key: 'name',
                    type: formItemType.input
                },
                {
                    title: '商品封面图',
                    key: 'url',
                    type: formItemType.upload,
                    tableView: tableItemType.tableView.picture
                },
                {
                    title: '充值类型',
                    key: 'goodsType',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.goodsTypeList,
                    listFormat: {
                        label: 'name',
                        value: 'id'
                    }
                },
                {
                    title: '话费面额',
                    key: 'billFaceAmount'
                },
                {
                    title: '成本价',
                    key: 'price'
                },
                {
                    title: '原价',
                    key: 'officialPrice'
                },
                {
                    title: '售价',
                    key: 'salePrice'
                }
                // {
                //   key: 'dateSearch',
                //   title: '日期',
                //   type: formItemType.datePickerDaterangeGai,
                //   options: {
                //     format: 'YYYY-MM-DD',
                //     valueFormat: 'yyyy-MM-dd'
                //   },
                //   childKey: ['startDate', 'endDate'],
                //   formHidden: true,
                //   search: true,
                //   val: [currentDate, currentDate]
                // },
                // {
                //   title: '商户平台',
                //   key: 'way',
                //   type: formItemType.select,
                //   tableView: tableItemType.tableView.text,
                //   list: this.list1,
                //   listFormat: {
                //     label: 'name',
                //     value: 'id'
                //   },
                //   val: this.listQuery.siteId,
                //   reg: ['required'],
                //   search: true,
                //   clearable: true
                // },
            ]
        },
        keyList() {
            const list = [{ label: '场景名称', keyVal: 'name' }]
            return list
        },
        list() {
            return [
                {
                    title: 'ID',
                    key: 'id',
                    width: 200
                },
                {
                    title: '问题',
                    key: 'name',
                    width: 200
                },
                {
                    title: '答案',
                    width: 200,
                    key: 'totalFee',
                    render: (h, params) => {
                        const refundEntrance = params.data.row.type
                        console.info(params.data.row, 'paramsparamsparamsparamsparamsparams')
                        if (refundEntrance == 1) {
                            return h('span', {}, !params.data.row.url || params.data.row.url == '' ? '-' : params.data.row.url)
                        } else {
                            return h('span', {}, '富文本类型请打开编辑查看')
                        }
                    }
                }
            ]
        }
    },
    methods: {
        countCommonValues(arr1, arr2) {
            return arr1.filter(item => arr2.includes(item)).length;
        },
        elAllShow() {
            console.info(123)
            this.elAll = true
        },
        checkEl(id) {
            const a = this.activitySceneList.find(item => item.id == id)
            this.img1_pop = a.img
            this.elId = id
            console.info(id, 'this.elId')
            this.elDrawer = true
        },
        int() {
            this.$nextTick(() => {
                const tbody = document.querySelector('#categoryList')
                const elTbody = document.querySelector('#categoryList');
                console.info(elTbody, 'elTbody')
                const that = this
                let el = this.$refs.table.$el.querySelectorAll(
                    ".el-table__body-wrapper > table > tbody"
                )[0];
                console.info(el)
                this.categoryListsort = new Sortable(el, {
                    animation: 150,
                    sort: true,
                    draggable: '.el-table__row', // 设置可拖拽行的类名(el-table自带的类名)
                    forceFallback: true,
                    ghostClass: 'blue-background-class',
                    async onEnd(evt) {
                        that.categoryIsSort = true
                        // const arr = JSON.parse(JSON.stringify(that.categoryList))
                        // const curr = arr.splice(evt.oldIndex, 1)[0]
                        // arr.splice(evt.newIndex, 0, curr) // 把被拖拽的元素添加到checkQaCategoryList中
                        // const ids = arr.map(item => item.id)
                        // categorySort({
                        //     planId: that.planId,
                        //     categoryIds: ids
                        // })
                        //     .then(res => {
                        //         that.categoryIsSort = false
                        //         if (res.code == 200) {
                        //         }
                        //     })
                        //     .catch(res => {
                        //         that.categoryIsSort = false
                        //     })


                        // that.categoryIsSort = true
                        const arr = JSON.parse(JSON.stringify(that.addForm.thirdGoodsEquityReqList))
                        const curr = arr.splice(evt.oldIndex, 1)[0]
                        arr.splice(evt.newIndex, 0, curr) // 把被拖拽的元素添加到checkQaCategoryList中
                        const ids = arr.map(item => item.id)

                        that.addForm.thirdGoodsEquityReqList = arr
                        that.$nextTick(() => {
                            that.categoryIsSort = false
                        })
                        console.info(arr, 'ids')
                        that.$forceUpdate()
                    }
                })
            })
        },
        hideRow({ row, rowIndex }) {
            if (row.statusType == '3') { // 假设我们想隐藏第二行
                console.info(row, 'hideRowhideRowhideRow')
                return {
                    "display": "none",
                    "color": "green"
                }
            }
        },
        filterSize(size) {
            const pow1024 = (num) => {
                return Math.pow(1024, num)
            }
            if (!size) return ''
            if (size < pow1024(1)) return size + ' B'
            if (size < pow1024(2)) return (size / pow1024(1)).toFixed(0) + ' KB'
            if (size < pow1024(3)) return (size / pow1024(2)).toFixed(0) + ' MB'
            if (size < pow1024(4)) return (size / pow1024(3)).toFixed(0) + ' GB'
            return (size / pow1024(4)).toFixed(2) + ' TB'
        },
        uptSp(addForm1) {
            this.addForm1 = JSON.parse(JSON.stringify(addForm1))
            console.info(this.addForm1, 'this.addForm1')
            this.drawer = true
        },
        delSp(addForm1) {
            for (let i = 0; i < this.addForm.thirdGoodsEquityReqList.length; i++) {
                if (addForm1.thirdGoodsId == this.addForm.thirdGoodsEquityReqList[i].thirdGoodsId) {
                    this.addForm.thirdGoodsEquityReqList[i].statusType = 3
                    if (addForm1.id) {
                        this.delArray.push(this.addForm.thirdGoodsEquityReqList[i])
                    }
                    this.addForm.thirdGoodsEquityReqList.splice(i, 1)
                }
            }

        },
        handMessageStyleListAdd(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    for (let i = 0; i < this.addForm.thirdGoodsEquityReqList.length; i++) {
                        let item = this.addForm.thirdGoodsEquityReqList[i]
                        if (item.thirdGoodsId == this.addForm1.thirdGoodsId) {

                            // this.addForm.thirdGoodsEquityReqList[i] = this.addForm1
                            this.$set(this.addForm.thirdGoodsEquityReqList[i], 'showName', this.addForm1.showName)
                            this.$set(this.addForm.thirdGoodsEquityReqList[i], 'drawDownNum', this.addForm1.drawDownNum)
                            this.$set(this.addForm.thirdGoodsEquityReqList[i], 'showUrl', this.addForm1.showUrl)
                            this.$set(this.addForm.thirdGoodsEquityReqList[i], 'topUpType', this.addForm1.topUpType)
                            this.$set(this.addForm.thirdGoodsEquityReqList[i], 'statusType', this.addForm1.statusType)

                        }

                    }
                    this.$forceUpdate()
                    this.$nextTick(() => {
                        this.drawer = false
                    })

                    // const arr = this.addForm.thirdGoodsEquityReqList.find(item => item.thirdGoodsId == this.addForm1.thirdGoodsId)
                    // arr = this.addForm1
                }
            })
        },
        qx() {
            this.$router.push({
                path: '/qjjp/operate/eventManagement'
            })
        },
        submit(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    console.info(this.addForm, 'this.addForm')
                    let arr = JSON.parse(JSON.stringify(this.addForm))
                    if (arr.thirdGoodsEquityReqList.length < 1) {
                        this.$message.error('请选择商品')
                        return
                    }
                    for (let i = 0; i < arr.thirdGoodsEquityReqList.length; i++) {
                        if (!arr.thirdGoodsEquityReqList[i].drawDownNum || arr.thirdGoodsEquityReqList[i].drawDownNum == '') {
                            this.$message.error('请编辑选择商品单期领取数量')
                            return
                        }
                        if (!arr.thirdGoodsEquityReqList[i].showName || arr.thirdGoodsEquityReqList[i].showName == '') {
                            this.$message.error('请编辑选择商品显示名字')
                            return
                        }
                        if (!arr.thirdGoodsEquityReqList[i].topUpType || arr.thirdGoodsEquityReqList[i].topUpType == '') {
                            this.$message.error('请编辑选择商品充值方式')
                            return
                        }
                        if (!arr.thirdGoodsEquityReqList[i].showUrl || arr.thirdGoodsEquityReqList[i].showUrl == '') {
                            this.$message.error('请编辑选择商品封面')
                            return
                        }
                    }
                    if (arr.activityScene.length < 1) {
                        this.$message.error('活动场景不能为空')
                        return
                    }
                    let delArray = JSON.parse(JSON.stringify(this.delArray))
                    arr.thirdGoodsEquityReqList = [...arr.thirdGoodsEquityReqList, ...delArray]
                    arr.activityScene = arr.activityScene.join(',');
                    for (let i = 0; i < arr.thirdGoodsEquityReqList.length; i++) {

                        if (arr.thirdGoodsEquityReqList[i].topUpType) {
                            arr.thirdGoodsEquityReqList[i].topUpType = arr.thirdGoodsEquityReqList[i].topUpType.join(',');
                        }
                    }


                    if (this.$route.query.type == 'edit') {
                        equityActivitiesUpdate({ ...arr }).then(res => {
                            if (res.code == 200) {
                                this.$message.success('操作成功')
                                this.$router.push({
                                    path: '/qjjp/operate/eventManagement'
                                })
                            }
                        })
                    } else {
                        equityActivitiesCreate({ ...arr }).then(res => {
                            if (res.code == 200) {
                                this.$message.success('操作成功')
                                this.$router.push({
                                    path: '/qjjp/operate/eventManagement'
                                })
                            }
                        })
                    }

                }
            })

        },
        closeCheckSPShow() {
            this.checkSPShow = false
        },
        CheckSPList() {
            let selection = JSON.parse(JSON.stringify(this.checkList))
            let arr = []
            console.info(this.addForm.thirdGoodsEquityReqList, 'this.addForm.thirdGoodsEquityReqList')
            for (let i = 0; i < selection.length; i++) {
                let obj = selection[i]
                obj.statusType = 1
                // obj.drawDownNum = 1
                // obj.showName = '测试'
                // obj.sort = i
                obj.thirdGoodsId = selection[i].id
                obj.showUrl = selection[i].url
                obj.equityType = selection[i].type
                if (!obj.showName || obj.showName == '') {
                    obj.showName = obj.name
                }
                if (!obj.topUpType) {
                    obj.topUpType = []
                }

                // obj.topUpType = []
                delete obj.id
                delete obj.url
                if (!(this.addForm.thirdGoodsEquityReqList.find(item => item.thirdGoodsId == obj.thirdGoodsId))) {
                    arr.push(obj)
                } else {

                }

                this.$forceUpdate()
                // this.defaultSelectPackIds.push(obj.thirdGoodsId)
            }
            this.addForm.thirdGoodsEquityReqList = [...this.addForm.thirdGoodsEquityReqList, ...arr]
            this.checkSPShow = false
        },
        selectionChange(selection) {
            // if (this.addForm.thirdGoodsEquityReqList) {
            //     this.addForm.thirdGoodsEquityReqList = []
            // }
            // this.checkList = selection
            this.checkList = []
            for (let i = 0; i < selection.length; i++) {
                this.checkList.push(selection[i])
            }
            // const a = []
            // a.push(selection)
            // this.addIconList = selection
        },
    }
}
</script>

<style lang="scss" scoped>
#eventManagementDetail {
    padding: 30px;

    .form_view {
        width: 100%;
        padding: 15px;
        border-radius: 5px;

        .form_view_package {
            width: 80%;
            margin: 10px 0;
            padding: 10px 10px;
            background-color: rgba(242, 242, 242, 0.5);
            display: flex;
            flex-wrap: wrap;


            span:first-child {
                width: 100%;
                margin-bottom: 5px
            }
        }

        .form_view_title {
            margin-bottom: 20px;

            .title_line {
                width: 2px;
                height: 10px;
                background-color: #66b1ff;
                display: inline-block;
                vertical-align: middle;
            }

            span {
                padding-left: 5px;
                vertical-align: middle;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 20px;
                color: #333333;
                line-height: 30px;
                text-align: left;
                font-style: normal;
            }
        }
    }
}

::v-deep .el-drawer__body {
    overflow: scroll;
    // padding-bottom: 20px;
    padding: 0 30px 20px;
    position: relative;
    /* overflow-x: auto; */
}

::v-deep .el-drawer__header {
    span {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        line-height: 30px;
        text-align: left;
        font-style: normal;
    }

}

::v-deep .el-drawer__body {}

.close_button {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    background-color: rgb(0, 0, 0, 1);
    text-align: center;
    cursor: pointer;

    i {
        color: white;
        line-height: 40px;
    }
}

.drawer_package {
    height: 100%;
    position: relative;

    .drawer_title {
        padding: 10px 20px 5px;
        vertical-align: middle;

        span {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 20px;
            color: #333333;
            line-height: 30px;
            text-align: left;
            font-style: normal;
        }

    }
}

.addForm_package {
    background-color: rgb(189, 184, 184, 0.1);
    padding: 15px;
    height: 100%;

    .demo-ruleForm {
        background-color: #ffffff;
        width: 100%;
        height: 100%;
        position: relative;

        .view_button {
            position: absolute;
            bottom: 0;
        }
    }
}

.view_button {
    background-color: #ffffff;
    padding: 20px;
    border-top: 1px dashed #000000;

    ::v-deep .el-button {
        margin: 0 10px;
    }
}

.form_view {
    // margin: 0 0px 20px;
    background-color: #ffffff;
    // border: 1px solid rgba(0,0,0,0.2);
    width: 100%;
    padding: 15px;
    border-radius: 5px;

    .form_view_title {
        margin-bottom: 20px;

        .title_line {
            width: 2px;
            height: 10px;
            background-color: #66b1ff;
            display: inline-block;
            vertical-align: middle;
        }

        span {
            padding-left: 5px;
            vertical-align: middle;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 20px;
            color: #333333;
            line-height: 30px;
            text-align: left;
            font-style: normal;
        }
    }
}

.excel-upload {
    text-align: center;

    &::v-deep.el-upload-dragger {
        width: 225px;

    }
}

.fail_list {
    margin-top: 10px;

    .tip {
        width: 100%;
        height: 20px;
        background: #e4e7ed;
        text-align: center;
        line-height: 20px;
        margin: 10px 0;
    }
}

.copy-btn {
    cursor: pointer;
}

::v-deep .activeButton {
    .el-button {
        margin-right: 5px;
        padding: 7px 6px;
    }
}

.addDiv {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .el-icon-plus {
        font-size: 16px;
        color: #409EFF;
    }

    .txt {
        width: 100%;
        height: 20px;
        font-size: 12px;
        color: #409EFF;
        text-align: center;
        line-height: 1.5;
        transform: scale(0.8);
    }
}

.el_img_view {
    margin: 50px;
    height: 308px;
    overflow: hidden
}

.form_txt1 {
    padding-left: 20px;

    .form_txt1_title {
        font-size: 15px;
        font-weight: 600;
        padding-bottom: 10px;
    }

    .form_txt1_main {
        padding-left: 20px;
        line-height: 20px;
    }
}

.form_txt2 {
    padding-left: 20px;
    margin-top: 20px;

    .form_txt2_title {
        display: flex;
        justify-content: space-between;
        padding-bottom: 10px;
        padding-right: 200px;

        span {
            font-size: 15px;
            font-weight: 600;
        }

        .form_txt2_title_txt2 {
            cursor: pointer;
            color: #409eff;
        }
    }

    .form_txt2_main {
        width: 610px;
    }
}

::v-deep .el-icon-circle-close {
    width: 50px;
    height: 50px;
    font-size: 50px;
}

.tips_txt {
    font-size: 10px;
}
</style>