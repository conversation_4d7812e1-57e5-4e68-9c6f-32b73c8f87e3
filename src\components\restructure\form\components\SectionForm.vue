<template>
  <div class="section-form">
    <div v-for="(section, sectionIndex) in groupedFormItems" :key="sectionIndex" class="form-section">
      <!-- 分组标题 -->
      <div v-if="section.title" class="section-title">
        <div class="title-line"></div>
        <span class="title-text">{{ section.title }}</span>
      </div>

      <!-- 分组内容 -->
      <div class="section-content">
        <el-form
          ref="ruleForm"
          :model="resData"
          :label-width="labelWidth"
          class="section-form-content"
          :label-position="labelPosition"
          :size="size"
        >
          <el-row :gutter="20">
            <el-col
              v-for="(item, index) in section.items"
              :key="index"
              :span="getColSpan(item)"
              class="form-item-col"
            >
              <el-form-item
                :label="item.titleHidden ? '' : item.options && item.options.title ? item.options.title : item.title"
                :rules="getReg(item, index)"
                :prop="item.key"
                :class="getFormItemClass(item)"
                :style="item.itemStyleFn ? item.itemStyleFn(item, resData) : {}"
              >
                <SRender :results="results" :item="item" :val.sync="resData[item.key]">
                  <div v-if="item.slot" :slot="item.slot">
                    <slot :name="item.slot" />
                  </div>
                </SRender>
                <!-- 字符计数显示 -->
                <div v-if="item.showCount && item.maxLength" class="char-count">
                  {{ (resData[item.key] || '').length }}/{{ item.maxLength }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <slot name="formContainer" />
  </div>
</template>

<script>
import {regular} from '@/libs/validate'
import {keyWord} from '@/config/sysConfig'
import SRender from './render/index'
import {copy} from '@/config/basicsMethods'

export default {
  name: 'SectionForm',
  components: {
    SRender
  },
  props: {
    formItemList: {
      type: Array,
      default: () => []
    },
    results: {
      type: Object,
      default: () => ({})
    },
    labelWidth: {
      type: String,
      default: '120px'
    },
    labelPosition: {
      type: String,
      default: 'right'
    },
    size: {
      type: String,
      default: 'small'
    },
    regState: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      copy: copy,
      trigger: ['blur'],
      resData: {}
    }
  },
  computed: {
    groupedFormItems() {
      const groups = {}
      const result = []

      this.formItemList.forEach(item => {
        const groupTitle = item.groupTitle || '基本信息'

        if (!groups[groupTitle]) {
          groups[groupTitle] = {
            title: groupTitle,
            items: []
          }
        }

        groups[groupTitle].items.push(item)
      })

      // 转换为数组并保持顺序
      Object.keys(groups).forEach(title => {
        result.push(groups[title])
      })

      return result
    }
  },
  watch: {
    formItemList: {
      handler(to) {
        this.ruleForm()
      },
      deep: true
    },
    resData: {
      handler(to) {
        this.$emit('changeForm', to)
      },
      deep: true
    }
  },
  created() {
    this.ruleForm()
    this.$emit('formMount')
  },
  methods: {
    ruleForm() {
      const tempObj = {}
      this.formItemList.forEach((item, index) => {
        if (!item.slot && !this.basics.isNull(item.key)) {
          const getKey = item.key
          item.val = this.basics.isNull(item.val) ? this.setItemVal(item) : this.getItemVal(item, item.val)
          item.list = item.list ? item.list : this.$set(item, 'list', [])
          const getVal = item.val
          if (!this.basics.isNull(getKey)) {
            if (
              this.basics.isNull(this.resData[getKey]) ||
              (this.basics.isArray(this.resData[getKey]) && this.basics.isArrNull(this.resData[getKey]))
            ) {
              tempObj[getKey] = this.basics.isNull(getVal) ? '' : getVal
            } else {
              tempObj[getKey] = this.resData[getKey]
            }
          }
        }
      })
      this.resData = tempObj
    },

    getColSpan(item) {
      // 根据字段类型和配置决定列宽
      if (item.colSpan) return item.colSpan
      if (item.type === 'textarea' || item.fullWidth) return 24
      return item.halfWidth ? 12 : 12 // 默认一行两列
    },

    getFormItemClass(item) {
      const classes = []
      if (item.type === 'textarea') classes.push('textarea-item')
      if (item.required) classes.push('required-item')
      return classes.join(' ')
    },

    setItemVal(item) {
      if (item.type && item.type.indexOf(keyWord.multiple) >= 0) {
        return []
      } else {
        return ''
      }
    },

    getItemVal(item, val) {
      if (item.type && item.type.indexOf(keyWord.multiple) >= 0 && this.basics.isString(val)) {
        return val.split(keyWord.relatedWords)
      } else {
        return val
      }
    },

    getReg(item, index) {
      if (!this.regState) return []
      const itemReg = item.reg
      let tempRegObj = []
      if (this.basics.isNull(itemReg)) {
        return tempRegObj
      } else if (this.basics.isString(itemReg)) {
        tempRegObj = this.setReg([itemReg])
      } else if (this.basics.isArray(itemReg)) {
        tempRegObj = this.setReg(itemReg)
      } else {
        throw new Error('不支持当前验证类型')
      }
      return tempRegObj
    },

    setReg(regStrArr) {
      return regStrArr.map(item => {
        if (this.basics.isObj(item)) {
          return item
        } else {
          if (item === 'required') return {required: true, message: '此项不能为空', trigger: this.trigger}
          return Object.assign({}, {trigger: this.trigger, validator: regular(item)})
        }
      })
    },

    submitForm() {
      return new Promise((resolve, reject) => {
        // 获取所有表单实例
        const formRefs = this.$refs.ruleForm
        if (!formRefs || formRefs.length === 0) {
          resolve(this.resData)
          return
        }

        // 验证所有表单
        const validatePromises = formRefs.map(form => {
          return new Promise((formResolve, formReject) => {
            form.validate(valid => {
              if (valid) {
                formResolve(true)
              } else {
                formReject(false)
              }
            })
          })
        })

        Promise.all(validatePromises)
          .then(() => {
            resolve(this.resData)
          })
          .catch(() => {
            reject('表单验证失败')
          })
      })
    },

    resetForm() {
      const formRefs = this.$refs.ruleForm
      if (formRefs && formRefs.length > 0) {
        formRefs.forEach(form => {
          form.resetFields()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.section-form {
  .form-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .title-line {
      width: 3px;
      height: 16px;
      background: #409eff;
      margin-right: 8px;
    }

    .title-text {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .section-content {
    background: #fafafa;
    border-radius: 6px;
    padding: 20px 20px 0;
    border: 1px solid #e4e7ed;
  }

  .section-form-content {
    .form-item-col {
      margin-bottom: 0;
    }
  }
}

::v-deep .el-form-item {
  margin-bottom: 18px;

  &.textarea-item {
    .el-form-item__content {
      position: relative;
    }
  }

  &.required-item {
    .el-form-item__label::before {
      content: '*';
      color: #f56c6c;
      margin-right: 4px;
    }
  }
}

::v-deep .el-textarea {
  .el-textarea__inner {
    min-height: 80px !important;
    resize: vertical;
  }
}

.char-count {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 12px;
  color: #909399;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 4px;
  border-radius: 2px;
}
</style>
