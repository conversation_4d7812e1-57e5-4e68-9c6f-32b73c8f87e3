<template>
  <el-form class="equip-form" label-width="100px">
    <el-form-item label="设备">
      <el-radio-group v-if="equimentInfo.length>0 && isPost" v-model="formObj.deviceName">
        <el-radio v-for="item in equimentInfo" :key="item" :label="item">{{ item }}</el-radio>
      </el-radio-group>
      <div v-if="equimentInfo.length==0 && isPost">暂无设备</div>
    </el-form-item>
    <el-form-item>
      <el-button size="middle" :disabled="equimentInfo.length==0 " type="primary" @click="submitEquipmentInit">初始化</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { white_list_device, white_list_clear } from '@/qjjpApi/whitelistInvestigation'
export default {
  name: '',
  components: {},
  data() {
    return {
      equimentInfo: [],
      formObj: {
        deviceName: 1
      },
      isPost: false,
      initing: false
    }
  },
  created() {
    white_list_device().then(res => {
      if (res.data && res.data.length > 0) {
        this.equimentInfo = res.data
        this.formObj.deviceName = this.equimentInfo[0]
      }
    }).finally(() => {
      this.isPost = true
    })
  },
  methods: {
    changeType(e) {
      this.formObj.equiment = e == 1 ? 1 : 3
    },
    submitEquipmentInit() {
      if (this.initing) {
        this.$message.warning('设备正在初始化,请稍后')
        return
      }
      this.initing = true
      white_list_clear(this.formObj).then(res => {
        if (res.code == 200) {
          this.$message.success('初始换成功！')
        }
      }).finally(() => {
        this.initing = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.equip-form {
  margin: 50px;
}
</style>
