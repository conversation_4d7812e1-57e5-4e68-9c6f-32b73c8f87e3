/* author: lints
 * date:2018-05-04
 * axios请求封装
 * 主要封装方法get,post,put,delete方法
 */
import Route from '@/router'
import Store from '@/store'
import axios from 'axios'
import {
  Message
} from 'element-ui'
import Cookies from 'js-cookie'
import qs from 'qs'

axios.defaults.baseURL = process.env.VUE_APP_BASE_API
// axios.defaults.timeout = 8000;

// post方法：
const post = (url, data = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.post(url, data, defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// post方法：
const postZt = (url, data = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_requestURL
  return new Promise((resolve, reject) => {
    axios.post(url, data, defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}
// get方法：
const get = (url, params = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.get(url, {
      params: params,
      defaultObj
    })
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// get方法：
const getJjjp = (url, params = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.get(url, {
      params: params,
      defaultObj
    })
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

const postJjjp = (url, data = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.post(url, data, defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}
const putJjjp = (url, data = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.put(url, data, defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 月记管家请求
// get方法：
const getYjgj = (url, params = {}, defaultObj = { from: 'yjjp' }) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.get(url, {
      params: params,
      defaultObj
    })
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

const postYjgj = (url, data = {}, defaultObj = { from: 'yjjp' }) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.post(url, data, defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

const delYjgj = (url, data = {}, defaultObj = { from: 'yjjp' }) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.delete(url, qs.stringify(data), defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

const putYjgj = (url, data = {}, defaultObj = { from: 'yjjp' }) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.put(url, data, defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// get中台方法：
const getZt = (url, params = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_requestURL
  return new Promise((resolve, reject) => {
    axios.get(url, {
      params: params,
      defaultObj
    })
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}
const del = (url, data = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.delete(url, qs.stringify(data), defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}
const delWithQuery = (url, data = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.delete(url + '?' + qs.stringify(data), null, defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}
const deleteWithBody = (url, data = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.delete(url, {
      headers: {
        'Content-Type': 'application/json'
      },
      data: data
    })
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}
const put = (url, data = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.put(url, data, defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}
const patch = (url, data = {}, defaultObj = {}) => {
  axios.defaults.baseURL = process.env.VUE_APP_BASE_API
  return new Promise((resolve, reject) => {
    axios.patch(url, data, defaultObj)
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 请求拦截器
axios.interceptors.request.use(function(config) {
  if (Store.state.user.authorization) {
    config['headers'].Authorization = Store.state.user.authorization
    config['headers'].siteId = 'd6c4a4bbd1f748f89e879c00d60edd8e'
    console.info(config, 'config.url')
    config['headers'].accessCode = '84c8854c-3464-4a42-aa8a-79a414a39c95'
    // if (config.url.includes('advflow')) {
    //   if (config.defaultObj?.from == 'yjjp' || config.from == 'yjjp') {
    //     config['headers'].accessCode = '2dedf1f5-c1ac-4fdb-ad58-0c01f65a8ce9'
    //   } else {
    //     config['headers'].accessCode = '84c8854c-3464-4a42-aa8a-79a414a39c95'
    //   }
    // }

    console.info(config, 'config')
  }
  return config
}, err => {
  return Promise.reject(err)
})

// 响应拦截器
axios.interceptors.response.use(function(res) {
  if (res.data.code === 401) {
    Message.error('登录失效,请重新登录')
    Store.commit('user/authorization', '')
    Cookies.remove('token')
    Route.push({
      path: '/login'
    })

    return res
  }
  console.info(res, 'resresres')
  // res.data.code == 10000 特殊处理，弹二次弹窗的
  if ((res.data.code !== 0 && res.data.code !== 200 && res.data.code !== 10000) && (!res.data?.status || res.data?.status !== 200) && (!res.config.defaultObj || (res.config.defaultObj && !res.config.defaultObj.noError))) {
    // 成功返回但出错提示
    res.data.message ? Message.error(res.data.message) : Message.error(res.data.msg)
  }
  return res
}, err => {
  // 服务器出错
  return Promise.reject(err)
})

export {
  getYjgj,
  postYjgj,
  putYjgj,
  getJjjp,
  postJjjp,
  post,
  get,
  getZt,
  postZt,
  del,
  delWithQuery,
  deleteWithBody,
  put,
  patch,
  putJjjp,
  delYjgj
}

