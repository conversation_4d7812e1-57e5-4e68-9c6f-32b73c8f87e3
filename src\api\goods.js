import {
  put,
  get,
  post,
  del
} from '@/libs/axios.package'

/*
* 商品库列表
*
*/
export const getStoreList = obj => {
  return get('goods/chengquan/list', obj, false)
}

/*
* 同步商品库
*
*/
export const synThirdGoods = obj => {
  return get('goods/chengquan/insertOrUpdate', obj, false)
}

/*
* 直充商品列表
*
*/
export const rechargeList = obj => {
  return get('goods/recharge/list', obj, false)
}

/*
* 直充商品列表-分类
*/
export const selectGoodsTypeName = obj => {
  return get('goods/recharge/selectGoodsTypeName', obj, false)
}

/*
* 直充商品统计
*
*/
export const rechargeStatistic = obj => {
  return get('goods/recharge/statistic', obj, false)
}

/*
* 直充商品添加
*
*/
export const addRechargeGodos = obj => {
  return post('goods/recharge', obj, false)
}

/*
* 直充商品修改
*
*/
export const updateRechargeGodos = obj => {
  return put('goods/recharge', obj, false)
}

/*
* 直充商品删除
*
*/
export const delRecharge = id => {
  return del('goods/recharge/' + id, null, false)
}

/*
* 直充商品详情
*
*/
export const rechargeDetail = id => {
  return get('goods/recharge/' + id, null, false)
}

/*
* 外链商品添加
*
*/
export const extendAdd = obj => {
  return post('goods/extend', obj, false)
}

/*
* 外链商品统计
*
*/
export const extendStatistic = () => {
  return get('goods/extend/statistic', null, false)
}

/*
* 外链商品列表
*
*/
export const extendList = (obj) => {
  return get('goods/extend/list', obj, false)
}

/*
* 外链商品删除
*
*/
export const extendDel = (id) => {
  return del('goods/extend/' + id, null, false)
}

/*
* 外链商品更新
*
*/
export const extendUpdate = (obj) => {
  return put('goods/extend', obj, false)
}

/*
* 外链商品详情
*
*/
export const extendDetail = (id) => {
  return get('goods/extend/' + id, null, false)
}
/*
* 获取扩展商品分类
*
*/
export const getPromotionCommoditiesDataApi = (obj) => {
  return get('/goods/category/list', obj, false)
}
/*
* 新增扩展商品分类
*
*/
export const setPromotionCommoditiesDataApi = (obj) => {
  return post('/goods/category', obj, false)
}
/*
* 编辑扩展商品分类
*
*/
export const editPromotionCommoditiesDataApi = (obj) => {
  return put('/goods/category', obj, false)
}
/*
* 删除扩展商品分类
*
*/
export const deletPromotionCommoditiesDataApi = (id) => {
  return del('/goods/category/' + id, null, false)
}
/*
* 获取扩展商品库
*
*/
export const getPromotionStoreroomApi = (obj) => {
  return get('/goods/category/list', obj, false)
}
/*
* 获取扩展商品库列表数据
*
*/
export const getPromotionStoreroomListApi = (obj) => {
  return get('/goods/extension/list', obj, false)
}

/*
* 获取扩展商品库列表数据
*
*/
export const getTopicProductListApi = (obj) => {
  return get('/goods/extension/list', obj)
}
/*
/*
* 获取选品库列表
*
*/
export const getAllFavoritesApi = (obj) => {
  return get('/goods/getAllFavorites', obj, false)
}
/*
* 推广商品统计（全部/上架/下架）
*
*/
export const getPromotExtendStatisticApi = (obj) => {
  return get('/goods/extension/statistic', obj, false)
}
/*
* 添加商品库
*
*/
export const setPromotionGoodApi = (obj) => {
  return post('/goods/extension', obj, false)
}
/*
* 删除商品库商品
*
*/
export const deletPromotionGoodApi = (id) => {
  return del('/goods/extension/' + id, null, false)
}
/*
* 获取商品库商品详情
*
*/
export const getEditCommonListDetailApi = (id) => {
  return get('/goods/extension/' + id, null, false)
}
/*
* 修改商品库商品
*
*/
export const editEditCommonListDetailApi = (obj) => {
  return put('/goods/extension', obj, false)
}
/*
* 获取商品位置列表
*
*/
export const getGoodsLocationApi = (obj) => {
  return get('/goods/extension/location', obj, false)
}
/*
* 查询淘宝商品库列表
*
*/
export const getGoodsPackageApi = (obj) => {
  return get('/goods/extension/goodspackage', obj, false)
}
/*
* 获取商品列表
*
*/
export const getGoodsTableArrayApi = (obj) => {
  return get('goods/extension/goodsstore', obj, false)
}

export const getTaobaoCommodityBankApi = (obj) => {
  return get('/goods/getAllFavorites', obj, false)
}
/*
* 查询京东商品库列表
*
*/
export const getJingDongCommodityBankApi = (obj) => {
  return post('/jdGoods/getPackageList', obj, false)
}

/*
* 同步京东商品库数据
*/
export const getGoodsSynchronousDataApi = (obj) => {
  return post('/jdGoods/getGoodsItemList', obj, false)
}

/*
* 同步淘宝商品信息
*/
export const getGoodsTbGoodsItem = (obj) => {
  return post('/goods/goodsItem', obj, false)
}

/** *********  获取返利比例  ***********/
export const getRatioApi = (obj) => {
  return get('/goods/extension/ratio', obj, false)
}
/** *********  同步数据***********/
export const GET_SYSC = source => {
  return get(`goods/extension/sync/${source}`)
}

/**
 * 推广分类 -- 除淘宝之外的平台
 */

export const getGoodsCategoryListByMallId = (obj) => {
  return get(`/goods/category/list/${obj.mallId}`, obj)
}

export const getGoodsCategoryThreepartyListByMallId = (mallId) => {
  return get(`/goods/category/threeparty/list/${mallId}`)
}
