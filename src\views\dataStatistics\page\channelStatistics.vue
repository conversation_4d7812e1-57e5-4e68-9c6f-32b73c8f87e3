<template>
  <div>
    <!-- 会员统计表格 -->
    <!-- <div class="tab-head" style="margin-top:30px;align-items: center;">
      <span class="title">总体统计（统计数据非实时，只统计今日00:00之前的数据）</span>
    </div> -->
    <!--    <el-row type="flex" class="row-bg" justify="end">-->
    <!--      <el-button type="success" plain icon="el-icon-refresh" @click="refresh()">刷新</el-button>-->
    <!--    </el-row>-->

    <!--  查询条件  -->
    <el-row class="search-row" type="flex" style="margin-bottom: 30px;">
      <el-col class="search-col" style="display: flex;flex-wrap: wrap">
        <span class="col">
          <label class="name">日期：</label>
          <el-date-picker
            v-model="searchObj.timeArr"
            :picker-options="basics.pickerOptions()"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            style="width: 260px;margin-right: 10px;"
          />
        </span>
        <span class="col">
          <label class="name">渠道名称：</label>
          <el-input
            v-model="searchObj.channelName"
            class="search-maxInput"
            clearable
            placeholder="请输入渠道名"
            style="width: 160px;"
          />
        </span>
        <span class="col">
          <label class="name">渠道类型：</label>
          <el-select
            v-model="searchObj.channelType"
            clearable
            placeholder="全部"
            style="margin-right: 10px;width: 130px;"
          >
            <el-option
              v-for="(type, index) in ChannelTypes"
              :key="index"
              :label="type.name"
              :value="type.name"
            />
          </el-select>
        </span>
        <span class="col">
          <label class="name">设备：</label>
          <el-select
            v-model="searchObj.os"
            class="search-maxInput"
            clearable
            placeholder="全部"
            style="width: 130px;"
          >
            <el-option label="android" value="android" />
            <el-option label="ios" value="ios" />
            <!--          <el-option label="其他" value="other"></el-option>-->
          </el-select>
        </span>
        <span class="col">
          <label class="name">流程：</label>
          <el-select
            v-model="searchObj.pid"
            class="search-maxInput"
            clearable
            placeholder="全部"
            style="width: 130px;"
          >
            <el-option
              v-for="item in channelProcessList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
            <!--          <el-option label="其他" value="other"></el-option>-->
          </el-select>
          <el-button
            type="primary"
            class="search-btn"
            icon="el-icon-search"
            @click="search"
          >查询</el-button>
        </span>
      </el-col>
    </el-row>

    <!--  数据列表  -->
    <section>
      <div class="tab-head">
        <span class="title">数据统计(默认展示当天实时数据)</span>
        <el-button
          type="warning"
          size="small"
          icon="el-icon-download"
          @click="handExport"
        >导出数据</el-button>
      </div>
      <el-table
        v-loading="DataLoading"
        :data="tableData"
        border
        style="width: 100%;margin-bottom: 30px;"
      >
        <el-table-column prop="channelName" label="渠道名称" />
        <el-table-column prop="progressName" label="渠道流程" />
        <el-table-column prop="countChannelUv" label="访问PV/UV">
          <template slot-scope="scope">
            {{ scope.row.countChannelPv }} / {{ scope.row.countChannelUv }}
          </template>
        </el-table-column>
        <el-table-column prop="clickCardPv" label="点击办卡PV/UV">
          <template slot-scope="scope">
            {{ scope.row.clickCardPv }} / {{ scope.row.clickCardUv }}
          </template>
        </el-table-column>
        <el-table-column
          prop="joinPageRate"
          label="访问转化率"
        />
        <el-table-column
          prop="h5Register"
          label="H5留资用户数"
        />
        <el-table-column prop="drawPv" label="提交申请PV/UV">
          <template slot-scope="scope">
            {{ scope.row.drawPv }} / {{ scope.row.drawUv }}
          </template>
        </el-table-column>
        <el-table-column prop="confirmPayPv" label="确认支付PV/UV">
          <template slot-scope="scope">
            {{ scope.row.confirmPayPv }} / {{ scope.row.confirmPayUv }}
          </template>
        </el-table-column>
        <el-table-column
          prop="visitLoginRate"
          label="访问留资率"
        />
        <el-table-column
          prop="h5LoginRate"
          label="办卡-H5留资转化"
        />
        <el-table-column
          prop="secondPageRate"
          label="留资购卡率"
        />
        <el-table-column
          prop="successBuyCard"
          label="成功购卡数"
        />
        <el-table-column
          prop="wechatUserNum"
          label="微信支付购卡数/支付宝支付购卡数"
        >
          <template slot-scope="scope">
            {{ scope.row.wechatUserNum }} / {{ scope.row.alipayUserCount }}
          </template>
        </el-table-column>
        <el-table-column
          prop="wechatTotalFee"
          label="微信购买金额"
        />
        <el-table-column
          prop="alipayTotalFee"
          label="支付宝购买金额"
        />
        <el-table-column
          prop="confirmPayRate"
          label="确认支付—购卡转化"
        />
        <el-table-column prop="totalFee" label="购卡总金额" />
        <el-table-column
          prop="vipCardRate"
          label="购卡成功率"
        />
        <!--        <el-table-column prop="vipAppLoginNew" label="购卡会员登录访问首页老/新">-->
        <!--          <template slot-scope="scope">{{scope.row.vipAppLoginOld}} / {{scope.row.vipAppLoginNew}}</template>-->
        <!--        </el-table-column>-->
        <el-table-column
          prop="activeRate"
          label="购卡-APP登录转化"
        />
        <el-table-column prop="p2H5LoginNum" label="登录数" />
        <el-table-column prop="p2InputMobilePv" label="输入手机号PV/UV">
          <template
            slot-scope="scope"
          >{{ scope.row.p2InputMobilePv }} /
            {{ scope.row.p2InputMobileUv }}</template>
        </el-table-column>
        <el-table-column prop="p2InputVCodePv" label="输入验证码PV/UV">
          <template
            slot-scope="scope"
          >{{ scope.row.p2InputVCodePv }} /
            {{ scope.row.p2InputVCodeUv }}</template>
        </el-table-column>
        <el-table-column prop="p2WechatCopyPv" label="复制微信公众号PV/UV">
          <template
            slot-scope="scope"
          >{{ scope.row.p2WechatCopyPv }} /
            {{ scope.row.p2WechatCopyUv }}</template>
        </el-table-column>
        <el-table-column
          prop="p2LoginRate"
          label="访问登录率"
        />
        <el-table-column prop="p2PaidRate" label="P1购卡率" />
        <el-table-column prop="appTotalLogin" label="APP登录(总/新)">
          <template
            slot-scope="scope"
          >{{ scope.row.appTotalLogin }} /
            {{ scope.row.appLoginNew }}</template>
        </el-table-column>
        <el-table-column prop="buyTotalNum" label="下单用户数(总/新)">
          <template
            slot-scope="scope"
          >{{ scope.row.buyTotalNum }} / {{ scope.row.buyNewNum }}</template>
        </el-table-column>
        <el-table-column
          prop="loginBuyRate"
          label="登录购买比"
        />
        <el-table-column prop="orderNum" label="订单数" />
        <el-table-column
          prop="orderTotalPrice"
          label="订单总额"
        />
        <el-table-column
          prop="commissionPrice"
          label="平台佣金金额"
        />
        <el-table-column
          prop="userRebatePrice"
          label="用户返利金额"
        />
        <el-table-column
          prop="platformRevenue"
          label="平台收益"
        />
        <el-table-column prop="firstBuyRate" label="首购率" />
        <el-table-column prop="secondBuyRate" label="复购率" />
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              plain
              @click="linkDetails(scope.row)"
            >查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 70, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </section>
  </div>
</template>

<script>
import moment from 'moment'
import { getChannelStatics } from '@/api/dataStatistics'
import { get_channelType_list } from '@/api/operate'
import { get_channel_process_list_no_page } from '@/api/landingPage'

export default {
  data() {
    return {
      channelProcessList: [],
      searchObj: {
        timeArr: [],
        channelName: '',
        status: '',
        os: '',
        channelType: '',
        pid: ''
      },
      total: 0,
      pageSize: 10,
      currentPage: 1,
      tableData: [],
      DataLoading: false,
      ChannelTypes: []
    }
  },
  created() {
    this.searchObj.timeArr[0] = this.$utils.getTimer(moment(new Date()))
    this.searchObj.timeArr[1] = this.$utils.getTimer(moment(new Date()))
    this.getChannelType()
    this.getChannelProcess()
    this.getData()
  },
  methods: {
    // 获取渠道数据
    search() {
      this.currentPage = 1
      this.getData()
    },
    getChannelProcess() {
      // 获取渠道类型
      get_channel_process_list_no_page().then(res => {
        if (res.code === 0) {
          this.channelProcessList = res.data
        }
      })
    },
    getData() {
      this.DataLoading = true
      getChannelStatics({
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
        startDate: this.searchObj.timeArr
          ? this.$utils.getTimer(this.searchObj.timeArr[0])
          : '',
        endDate: this.searchObj.timeArr
          ? this.$utils.getTimer(this.searchObj.timeArr[1])
          : '',
        channelName: this.searchObj.channelName,
        channelType: this.searchObj.channelType,
        pid: this.searchObj.pid,
        os: this.searchObj.os
      }).then(res => {
        this.DataLoading = false
        if (res.code === 0) {
          this.tableData = res.data
          this.total = res.totalCount
        }
      })
    },
    getChannelType() {
      // 获取渠道类型
      get_channelType_list().then(res => {
        if (res.code === 0) {
          this.ChannelTypes = res.data
        }
      })
    },
    linkDetails(item) {
      this.$router.push({
        path: '/dataStatistics/channelStatistics/details',
        query: {
          channelCode: item.channelCode
        }
      })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },
    // 导出
    handExport() {
      const startDate = this.searchObj.timeArr
        ? this.$utils.getTimer(this.searchObj.timeArr[0])
        : ''
      const endDate = this.searchObj.timeArr
        ? this.$utils.getTimer(this.searchObj.timeArr[1])
        : ''
      const search =
        'token=' +
        this.$utils.getToken() +
        '&channelName=' +
        this.searchObj.channelName +
        '&channelType=' +
        this.searchObj.channelType +
        '&startDate=' +
        startDate +
        '&endDate=' +
        endDate +
        '&pid=' +
        this.searchObj.pid +
        '&os=' +
        this.searchObj.os +
        '&pageNumber=' +
        this.currentPage +
        '&pageSize=' +
        this.pageSize
      window.location.href =
        this.$CONSTANT.publicPath + '/channelStatics/exploreList?' + search
    }
  }
}
</script>

<style scoped></style>
