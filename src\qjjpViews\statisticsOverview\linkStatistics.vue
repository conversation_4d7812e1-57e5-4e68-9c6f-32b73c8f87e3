<template>
  <div>
    <page :request="request" :list="list" table-title="投放链路统计">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page/v8'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getmerchantcount, downOrUp, getlink, linkexport } from '@/qjjpApi/statisticsOverview'
import { checkChannelLook } from '@/qjjpApi/system'
import {
  get_admin_list
} from '@/api/system'
import moment from 'moment'
import linkStatisticsAll from './linkStatisticsAll'

const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpLinkStatistics',
  components: {
    page
  },
  mixins: [linkStatisticsAll],
  props: {},
  data() {
    return {
      haveQX: false,
      adminList: [],
      curStatusParams: {
        id: 0,
        status: 0
      },

      siteIds: [],
      listQuery: {
        iosServiceChargeType: 1,
        fixedDate: false,
        startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await getlink(this.listQuery)
          await checkChannelLook().then(async res => {
            if (res.code === 200) {
              this.haveQX = res.data
              if (res.data) {
                await get_admin_list({ pageSize: 1000 }).then(res => {
                  if (res.code === 0) {
                    this.adminList = res.data
                  }
                })
              }

            }
          })
          const { records, total } = list.data
          console.info(list, 'list')

          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    curDate() {
      return [this.listQuery.startDate, this.listQuery.endDate]
    },
    list() {
      return [
        {
          title: '日期',
          key: 'eventDate',
          fixed: 'left',
          render: (h, params) => {
            const eventDate = params.data.row.eventDate
            return (
              <div>
                {!eventDate || eventDate == '' || eventDate == '汇总' ? <div>汇总 <el-tooltip
                  content='按照筛选条件进行去重汇总计算'
                  placement='top'
                  style='color:#409eff'
                >
                  <i class='el-icon-question' style='font-size: 14px' />
                </el-tooltip>
                </div> : eventDate}
              </div>

            )
          }
        },
        ...this.getListArray({ isShowChannelCode: false, from: 'list' }),

        {
          title: '操作栏',
          width: '100px',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          fixed: 'right',
          activeType: [
            {
              text: '渠道详情',
              key: 'edit1',
              type: tableItemType.activeType.event,
              theme: 'warning',
              hidden: params => {
                return params.eventDate == '汇总' || params.eventDate == '' || !params.eventDate
              },
              click: ($index, item, params) => {
                const paramsData = { ...this.listQuery }
                paramsData['startDate'] = params.eventDate
                paramsData['endDate'] = params.eventDate
                delete paramsData.pageSize
                delete paramsData.pageNumber
                console.info(paramsData.adminId, 'paramsData.adminId')
                this.$router.push({
                  path: '/qjjp/statisticsOverview/linkStatisticsDetailChannel',
                  query: { ...paramsData, adminId: params.adminId }
                })
              }
            },
            {
              text: '时段详情',
              key: 'edit2',
              type: tableItemType.activeType.event,
              theme: 'success',
              hidden: params => {
                return params.eventDate == '汇总' || params.eventDate == '' || !params.eventDate
              },
              click: ($index, item, params) => {

                const paramsData = { ...this.listQuery }
                paramsData['startDate'] = params.eventDate
                paramsData['endDate'] = params.eventDate
                delete paramsData.pageSize
                delete paramsData.pageNumber
                this.$router.push({
                  path: '/qjjp/statisticsOverview/linkStatisticsDetailTimer',
                  query: { ...paramsData, adminId: params.adminId }
                })
              }
            }
          ]
        }
      ]
    }
  },
  async created() {

  },
  methods: {
    fixedDateChange() {
      this.$store.dispatch('tableRefresh', this)
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = linkexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    changeStatus() {
      const params = this.curStatusParams
      downOrUp({ ...params }).then(res => {
        if (res.code == 200) {
          this.$message.success('更改状态成功')
          this.$store.dispatch('tableRefresh', this)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.checkFixedDate {
  margin-left: 20px;
}

.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  display: block;
  margin-bottom: 5px;

  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
