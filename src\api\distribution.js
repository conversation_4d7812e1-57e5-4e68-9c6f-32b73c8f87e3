import CONSTANT from '@/config/constant.conf'
import qs from 'qs'
import { del, get, post, put } from '@/libs/axios.package'

// 获取分发策略列表
export function getTriageStrategyList(params) {
  return get('/api/triage-strategy/pageList', params)
}

// 获取分发策略列表 需要替换为真实的，并且修改使用的地方
export function getDistributionPolicyList(params) {
  return get('/distribution/policy/list', params)
}

// 获取节点列表
export function getNodeList(params) {
  return get('/triageNoConfig/list', params)
}

// 获取信息筛选项
export function getInfoCategories(params) {
  return get('/info-category/value', params)
}

// 获取所有节点代码
export function getAllNodeCodes() {
  return get('/deliveryLinkConfig/allNodeCode')
}

// 新增节点
export function addNode(data) {
  return post('/triageNoConfig/add', data)
}

// 编辑节点
export function updateNode(data) {
  return put('/triageNoConfig/update', data)
}

// 获取分发记录列表
export function getTriageRecordList(params) {
  return get('/triage-record/list', params)
}

// 导出分发记录
export const exportTriageRecordList = data => CONSTANT.publicPath + '/triage-record/list/export?' + qs.stringify(data)

// 获取投放链路节点
export function getDeliveryLinkConfig(params) {
  return get('/deliveryLinkConfig/getByType', params)
}

// 获取机构列表
export function getAdvertiserList(params) {
  return get('/advertiser/list', params)
}

// 新增机构
export function addAdvertiser(data) {
  return post('/advertiser/add', data)
}

// 编辑机构
export function updateAdvertiser(data) {
  return put('/advertiser/update', data)
}

// 获取信息分类列表
export function getInfoCategoryList(params) {
  return get('/info-category/list', params)
}

// 新增信息分类
export function addInfoCategory(data) {
  return post('/info-category/add', data)
}

// 编辑信息分类
export function updateInfoCategory(data) {
  return put('/info-category/update', data)
}

// 获取策略集列表
export function getStrategyCollectList(params) {
  return get('/triageStrategyCollect/page', params)
}

// 新增或编辑策略集
export function addOrUpdateStrategyCollect(data) {
  return post('/triageStrategyCollect/addOrUpdate', data)
}

// 获取策略集详情
export function getStrategyCollectDetail(id) {
  return get(`/triageStrategyCollect/detail?id=${id}`)
}

// 检查默认策略集
export function checkDefaultStrategyCollect(id) {
  const baseUrl = '/triageStrategyCollect/checkDefault'
  return get(id ? `${baseUrl}?id=${id}` : baseUrl)
}

// 获取分发策略关联列表
export function getTriageLinkChannelStrategyPage(data) {
  return get('/triageLinkChannelStrategy/page', data)
}

// 添加渠道分发策略关联
export function addTriageLinkChannelStrategy(data) {
  return post('/triageLinkChannelStrategy/add', data)
}

// 删除渠道分发策略关联
export function deleteTriageLinkChannelStrategy(id) {
  return del(`/triageLinkChannelStrategy/${id}`)
}

// 获取机构配置列表
export function getInstitutionConfig(params) {
  // 字段映射
  const mappedParams = {
    downloadType: params.linkType,
    landingPage: params.routeId,
    node: params.nodeCode
  }
  return get('/triageNoConfig/getConfigByParam', mappedParams)
}

// 新增分发策略
export function createTriageStrategy(data) {
  return post('/api/triage-strategy/create', data)
}

// 获取分发策略详情
export function getTriageStrategyDetail(id) {
  return get(`/api/triage-strategy/detail/${id}`)
}

// 编辑分发策略
export function editTriageStrategy(data) {
  return post('/api/triage-strategy/edit', data)
}

// 获取策略选项
export function getTrialStrategyByParam(params) {
  return get('/api/triage-strategy/getTrialStrategyByParam', params)
}

// 获取所有已关联的渠道代码
export function getAllChannelCode() {
  return get('/triageLinkChannelStrategy/getAllChannelCode')
}

// 根据类型获取承接列表
export function getUndertakeByType(params) {
  return get('/channel/undertake/selector', params)
} 