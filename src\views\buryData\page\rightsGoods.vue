<template>
  <div class="role">
    <page :request="request" :list="list" table-title="权益分类统计">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出数据</el-button>
      </div>
    </page>
    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="dialogOps">
      <rights-goods-detail :detail-id="id" @success="success" @close="close" />
    </SDialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import SDialog from '@/components/restructure/dialog'
import { tableItemType, formItemType } from '@/config/sysConfig'
import RightsGoodsDetail from '@/views/buryData/modules/rightsGoodsDetail'
import { EXPORT_RIGHTS_GOODS, GET_RIGHTS_GOODS } from '@/api/buryData'
import { count_channel_application_list } from '@/api/NewChannel'
import moment from 'moment'
export default {
  components: {
    RightsGoodsDetail,
    page,
    SDialog
  },
  data() {
    return {
      request: {
        getListUrl: async(data) => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = [{ siteId: '', siteName: '综合' }, ...res.data]
                this.listQuery.siteId = ''
              }
            })
          }
          const { startTime, endTime, siteId } = this.listQuery
          return Promise.all([GET_RIGHTS_GOODS({ startTime: Number(startTime), endTime: Number(endTime), siteId, ...data })]).then(
            (res) => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      listQuery: {
        startTime: moment()
          .subtract(7, 'days')
          .format('YYYYMMDD'),
        endTime: moment().format('YYYYMMDD'),
        siteId: ''
      },
      type: '',
      id: '',
      dialogOps: {
        title: '',
        width: '1200px'
      },
      siteIds: [],
      dialogFormVisible: false
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startTime', 'endTime'],
          options: {
            format: 'YYYYMMDD',
            valueFormat: 'yyyyMMdd'
          },
          val: [
            moment()
              .subtract(7, 'days')
              .format('YYYYMMDD'),
            moment().format('YYYYMMDD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: this.listQuery.siteId,
          clearable: false,
          reg: ['required'],
          search: true,
          tableHidden: true
        },
        {
          title: '平台',
          key: 'platform',
          type: formItemType.select,
          list: [
            {
              label: 'IOS',
              value: 'ios'
            },
            {
              label: 'ANDROID',
              value: 'android'
            }
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '橱窗分类',
          key: 'title'
        },
        {
          title: '橱窗PV/UV',
          key: 'category'
        },
        {
          title: '创建时间',
          key: 'createTime'
        },
        {
          title: '修改时间',
          key: 'updateTime'
        },
        {
          title: '状态',
          key: 'status'
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          list: [
            {
              label: '上架',
              value: '0'
            },
            {
              label: '下架',
              value: '1'
            }
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          type: tableItemType.active,
          width: 180,
          headerContainer: false,
          activeType: [
            {
              text: '明细',
              key: 'detail',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: ($index, item, params) => {
                this.id = params.id
                this.dialogOps.title = '明细'
                this.dialogFormVisible = true
              }
            }
          ]
        }
      ]
    }
  },
  mounted() {

  },
  methods: {
    close() {
      this.dialogFormVisible = false
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_RIGHTS_GOODS({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    success() {
      this.$store.dispatch('tableRefresh', this)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
