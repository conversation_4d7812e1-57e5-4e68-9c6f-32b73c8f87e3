<!--
 * @Author: 陈小豆
 * @Date: 2024-11-13 09:58:35
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-11-15 13:58:40
-->
<template>
    <div>
        <page ref="eventManagement" :request="request" :list="list" table-title="活动管理">
            <div slot="searchContainer" style="display: inline-block">
                <el-button type="primary" plain size="small" class="search-btn"
                    @click="addDetail('add')">新增活动</el-button>
                <!-- <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button> -->
            </div>
        </page>
    </div>
</template>

<script>
import { thirdGoodsPage, equityActivitiesPage, syncThirdGoods, thirdGoodsCreate, thirdGoodsUpdate, supplierList } from '@/qjjpApi/operate'
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
    name: 'equityGoodsWarehouse',
    components: {
        page
    },
    props: {
        params: {
            type: Object,
            default: () => {
                return {
                    pageType: 'page'
                }
            }
        }
    },
    computed: {
        list() {
            return [
                {
                    title: '活动类型',
                    key: 'activityType',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.activityTypeList,
                    listFormat: {
                        label: 'name',
                        value: 'id'
                    },
                    search: true,
                    tableHidden: true,
                    clearable: true
                },
                {
                    title: '活动名称',
                    key: 'activityName',
                    type: formItemType.input,
                    search: true,
                    clearable: true,
                    tableHidden: true,
                },
                {
                    title: '状态',
                    key: 'activityStatus',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.stautsList,
                    listFormat: {
                        label: 'label',
                        value: 'value'
                    },
                    search: true,
                    tableHidden: true,
                    clearable: true
                },
                {
                    title: 'ID',
                    key: 'id'
                },
                {
                    title: '活动类型',
                    key: 'activityType',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.activityTypeList,
                    listFormat: {
                        label: 'name',
                        value: 'id'
                    },
                    clearable: true
                },
                {
                    title: '活动名称',
                    key: 'activityName'
                },
                {
                    title: '供应商',
                    key: 'goodsSupplierString',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px;margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.goodsSupplierString}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '权益类型',
                    key: 'equityType',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px;margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.equityType == 0 ? '权益商品' : '话费'}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '商品名称',
                    key: 'thirdGoodsAllName',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px;margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.thirdGoodsAllName}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '充值类型',
                    key: 'goodsType',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px;margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.goodsType}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '成本价',
                    key: 'price',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px;margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.price}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '原价',
                    key: 'officialPrice',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px;margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.officialPrice}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '售价',
                    key: 'salePrice',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px;margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.salePrice}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '单期参与数量',
                    key: 'drawDownNum',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px;margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.drawDownNum}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '显示名称',
                    key: 'showName',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px; margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.showName}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '商品封面图',
                    key: 'showUrl',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`height:30px;margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > <el-image
                                        style="width: 30px; height: 30px" src={item.showUrl} preview-src-list={[item.showUrl]}>
                                    </el-image> </div>
                                })
                                }
                            </div >
                        )
                    }
                },
                {
                    title: '上架状态',
                    key: 'statusType',
                    render: (h, { data }) => {
                        return (
                            <div>
                                {data.row.thirdGoodsEquityVos.map((item, index) => {
                                    return <div style={`margin: 0 -10px; padding: 12px 0; background: ${index % 2 === 1 ? 'rgba(64, 158, 255, 0.1)' : ''};min-height:50px`} > {item.statusType == 0 ? '下架' : item.statusType == 1 ? '在售' : item.statusType == 2 ? '售馨' : item.statusType == 3 ? '删除' : '-'}</div>
                                })}
                            </div >
                        )
                    }
                },
                {
                    title: '创建时间',
                    key: 'createTime',
                    render: (h, params) => {
                        if (!params.data.row.createTime) {
                            return h('span', '--')
                        }
                        return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
                    }
                },
                {
                    title: '更新时间',
                    key: 'updateTime',
                    render: (h, params) => {
                        if (!params.data.row.updateTime) {
                            return h('span', '--')
                        }
                        return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
                    }
                },
                {
                    title: '更新人',
                    key: 'updateName'
                },
                {
                    title: '状态',
                    key: 'activityStatus',
                    type: formItemType.select,
                    tableView: tableItemType.tableView.text,
                    list: this.stautsList,
                    listFormat: {
                        label: 'label',
                        value: 'value'
                    },
                },
                {
                    title: '操作',
                    type: tableItemType.active,
                    headerContainer: false,
                    key: 'operation',
                    activeType: [
                        {
                            text: '编辑',
                            key: 'edit1',
                            // type: tableItemType.activeType.detailsDialog
                            type: tableItemType.activeType.event,
                            click: ($index, item, params) => {
                                console.log(params)
                                this.addDetail('edit', params)
                            }
                        }
                    ]
                }
                // {
                //   key: 'dateSearch',
                //   title: '日期',
                //   type: formItemType.datePickerDaterangeGai,
                //   options: {
                //     format: 'YYYY-MM-DD',
                //     valueFormat: 'yyyy-MM-dd'
                //   },
                //   childKey: ['startDate', 'endDate'],
                //   formHidden: true,
                //   search: true,
                //   val: [currentDate, currentDate]
                // },
                // {
                //   title: '商户平台',
                //   key: 'way',
                //   type: formItemType.select,
                //   tableView: tableItemType.tableView.text,
                //   list: this.list1,
                //   listFormat: {
                //     label: 'name',
                //     value: 'id'
                //   },
                //   val: this.listQuery.siteId,
                //   reg: ['required'],
                //   search: true,
                //   clearable: true
                // },
            ]
        }
    },
    data() {
        return {
            listQuery: {},
            stautsList: [
                {
                    label: '启用',
                    value: 1
                },
                {
                    label: '禁用',
                    value: 0
                }
            ],
            activityTypeList: [
                {
                    id: 0,
                    name: '会员权益活动'
                }
            ],
            request: {
                getListUrl: async data => {
                    this.listQuery = { ...this.listQuery, ...data }
                    const { data: supplierListData } = await supplierList()
                    this.supplierListData = supplierListData.map(n => ({ label: n.name, value: n.code }))

                    const list = await equityActivitiesPage(this.listQuery)
                    const { records, total } = list.data
                    let dataList = []
                    if (records && records.length) {
                        dataList = {
                            total: total,
                            rows: records
                        }
                    }
                    const result = {
                        data: dataList
                    }
                    return result
                }
            }
        }
    },
    methods: {
        addDetail(type, params = {}) {
            this.$router.push({
                path: '/qjjp/customScript/eventManagementDetail',
                query: {
                    type: type,
                    id: params.id
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-icon-circle-close {
    width: 50px;
    height: 50px;
    font-size: 50px;
}
</style>