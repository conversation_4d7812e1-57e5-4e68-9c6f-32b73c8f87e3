<template>
  <page :request="request" :list="list" table-title="领取详情" />
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { GET_PRESENT_COUPON_RECORD } from '@/api/cashBackCard'

export default {
  components: {
    page
  },
  data() {
    return {
      request: {
        getListUrl: GET_PRESENT_COUPON_RECORD
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '用户id',
          key: 'userId',
          type: formItemType.input,
          search: true
        },
        {
          title: '电话号码',
          key: 'userPhone',
          type: formItemType.input,
          search: true
        },
        {
          title: '返现卡名称',
          key: 'couponName',
          type: formItemType.input,
          search: true
        },
        {
          title: '领取时间',
          key: 'receiveTime',
          type: formItemType.input
        },
        {
          title: '券有效时间',
          key: 'effectiveTime',
          type: formItemType.input
        },
        {
          title: '券状态',
          key: 'status',
          list: [
            { label: '未使用', value: 0 },
            { label: '已使用', value: 1 },
            { label: '已过期', value: 2 }
          ],
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          options: {
            valueType: 'Number'
          },
          search: true
        },
        {
          title: '使用时间',
          key: 'finishTime',
          type: formItemType.input
        },
        {
          title: '返利金额',
          key: 'couponAmount',
          type: formItemType.input
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
</style>
