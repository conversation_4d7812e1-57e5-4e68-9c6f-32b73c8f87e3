<template>
  <div>
    <el-row type="flex" class="row-bg" justify="end" style="margin: 0 0 10px;">
      <el-button
        type="primary"
        plain
        icon="el-icon-circle-plus-outline"
        @click="linkDetails('add')"
      >添加分类</el-button>
      <el-button type="success" plain icon="el-icon-refresh" @click="refresh()">刷新</el-button>
    </el-row>
    <el-row class="search-row" type="flex" style="margin-bottom:20px;">
      <el-col class="search-col" style="display: flex;flex-wrap: wrap">
        <span class="col">
          <label class="name">分类名称：</label>
          <el-input v-model="parameterObj.name" class="search-maxInput" clearable placeholder="请输入分类名称" />
          <el-button type="primary" class="search-btn" icon="el-icon-search" @click="getData">查询</el-button>
        </span>
      </el-col>
    </el-row>
    <section>
      <div class="tab-head">
        <span class="title">数据列表</span>
      </div>
      <el-table
        v-loading="DataLoading"
        :data="tableData"
        border
        style="width: 100%;margin-bottom: 30px;"
      >
        <el-table-column label="序列" type="index" />
        <el-table-column prop="id" label="编号" />
        <el-table-column prop="name" label="分类名称" />
        <el-table-column label="级别">
          <template slot-scope="scope">
            <span v-if="scope.row.level==1">一级</span>
            <span v-if="scope.row.level==2">二级</span>
            <span v-if="scope.row.level==3">三级</span>
            <span v-if="scope.row.level==4">四级</span>
          </template>
        </el-table-column>
        <el-table-column label="是否显示">
          <template slot-scope="scope">
            <span v-if="scope.row.isDisplay=='DISPLAY'" style="color:#1ABC9C">是</span>
            <span v-if="scope.row.isDisplay=='HIDE'" style="color:#FF7F50">否</span>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" />
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button type="warning" size="mini" plain @click="linkDetails('edit',scope.row.id)">编辑</el-button>
            <el-button type="danger" size="mini" plain @click="del(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 70, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </section>
  </div>
</template>

<script>
import { getList, delGoodsType } from '@/api/goodsType'
export default {
  data() {
    return {
      parameterObj: {
        name: ''
      },
      DataLoading: false,
      tableData: [1],
      total: 0,
      pageSize: 10,
      currentPage: 1
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    /**
     * 删除数据
     */
    del(id) {
      this.$confirm('是否确认删除？','操作提示')
        .then(() => {
          delGoodsType(id).then(res => {
            if (res.code === 0) {
              this.$message({
                message: '删除商品类型成功',
                type: 'success'
              })
              this.getData()
            }
          })
        })
        .catch(() => {
          // 取消
        })
    },
    /**
     * 获取数据
     */
    getData() {
      this.DataLoading = true
      getList({
        name: this.parameterObj.name,
        pageNumber: this.currentPage,
        pageSize: this.pageSize
      }).then(res => {
        this.DataLoading = false
        if (res.code === 0) {
          this.tableData = res.data
          this.total = res.totalCount
        }
      })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },
    linkDetails(type, id) {
      this.$router.push({
        path: '/commodity/classify_detail',
        query: {
          type,
          id
        }
      })
    },
    refresh() {
      this.getData()
    }
  }
}
</script>

<style scoped>
</style>
