import CONSTANT from '@/config/constant.conf'
import {del, get, post, put, delWithQuery} from '@/libs/axios.package'
import qs from 'qs'

// 贷款记录列表
export const getLoanRecordList = obj => get('/user/carloans/page', obj)

// 贷款记录导出
export const exportLoanRecord = data => CONSTANT.publicPath + '/user/carloans/export?' + qs.stringify(data)

// 投放链路统计按天统计
export const countPageByDate = obj => get('/count/pageByData', obj)

// 投放链路统计按天统计导出
export const countPageByDateExport = data => CONSTANT.publicPath + '/count/pageByDate/export?' + qs.stringify(data)

// 投放链路统计按渠道统计
export const countPageByChannelCode = obj => get('/count/pageByChannelCode', obj)

// 投放链路统计按渠道统计导出
export const countPageByChannelCodeExport = data =>
  CONSTANT.publicPath + '/count/pageByChannelCode/export?' + qs.stringify(data)

// 机构管理列表
export const getAdvertiserInfo = obj => get('/advertiserInfo/page', obj)

// 机构管理新增
export const addAdvertiserInfo = obj => post('/advertiserInfo/insert', obj)

// 机构管理刪除
export const delAdvertiserInfo = obj => delWithQuery('/advertiserInfo/delete', obj)

// 机构管理修改
export const updateAdvertiserInfo = obj => post('/advertiserInfo/update', obj)

// 上传pdf文件
export const uploadPdf = () => CONSTANT.publicPath + '/upload/pdf'
