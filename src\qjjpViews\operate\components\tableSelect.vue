<template>
  <div class="page-container">
    <div class="btn-groups">
      <button @click="selectAll">全选</button>/
      <button @click="selectNotAll">全不选</button>/
      <button @click="selectNotSelect">反选</button>
    </div>
    <el-table :data="tableList" style="width: 100%" max-height="300" border class="channelDetail-table"
      @selection-change="selectionChange">
      <el-table-column type="selection" width="155" />
      <el-table-column v-for="(item, index) in keyList" :key="index" :prop="item.keyVal" :label="item.label">
        <template slot-scope="scope">
          <div><span>{{ scope.row[item.keyVal] || '-' }}</span><i class="el-icon-zoom-in lan"
              @click="checkEl(scope.row)" v-if="scope.row.id != 4"></i></div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  name: 'tableSelect',
  props: {
    tableList: {
      type: Array,
      default: () => []
    },
    defaultSelect: {
      type: Array,
      default: () => []
    },
    keyList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      checkList: []
    }
  },
  watch: {
    tableList: {
      handler(val) {
        if (val && val.length > 0) {

          this.$nextTick(() => {
            this.checkList = this.tableList.filter(item => this.defaultSelect.includes(item.id))
            console.info(this.checkList, 'this.checkList')
            this.tableList.forEach(item => {
              this?.$children?.[0].toggleRowSelection(item, this.defaultSelect.includes(item.id))
            })
          })
        }
      },
      deep: true,
      immediate: true
    },
    defaultSelect: {
      handler(val) {
        if (val && val.length > 0) {
          this.$nextTick(() => {
            this.checkList = this.tableList.filter(item => this.defaultSelect.includes(item.id))
            console.info(this.checkList, 'this.checkList')
            this.tableList.forEach(item => {
              this?.$children?.[0].toggleRowSelection(item, this.defaultSelect.includes(item.id))
            })
          })
        }
      },
      deep: true,
      immediate: true
    }

  },
  methods: {
    checkEl(data) {
      this.$emit('checkEl', data.id)
    },
    selectionChange(selection) {
      this.checkList = selection
      const ids = []
      this.checkList.forEach(item => ids.push(item.id))
      this.$emit('emitSelect', ids)
    },
    selectAll() {
      this.tableList.forEach(row => {
        this?.$children?.[0].toggleRowSelection(row, true)
      })
    },
    selectNotAll() {
      this?.$children?.[0].clearSelection()
    },
    selectNotSelect() {
      const ids = []
      this.checkList.forEach(item => ids.push(item.id))
      this.tableList.forEach(row => {
        if (ids.includes(row.id)) {
          this?.$children?.[0].toggleRowSelection(row, false)
        } else {
          this?.$children?.[0].toggleRowSelection(row, true)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.lan {
  color: #409eff;
  padding-left: 10px;
  cursor: pointer;
}

.page-container {
  position: relative;

  .btn-groups {
    height: 65px;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    z-index: 999;
    justify-content: center;
    width: 150px;
    text-align: center;

    button {
      cursor: pointer;
      margin: 0 5px;
      border: none;
      background: transparent;
      color: #02A7F0;
    }
  }

}

.channelDetail-table {
  &::v-deep {
    thead {
      .el-checkbox {
        display: none;
      }
    }
  }
}
</style>
