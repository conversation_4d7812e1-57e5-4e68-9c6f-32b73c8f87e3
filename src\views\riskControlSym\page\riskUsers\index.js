export const tableColumn = [
  {
    title: '用户属性标记',
    key: 'userAttrTag',
    children: [
      {
        title: '高风险用户数及占比',
        key: 'highRiskUvAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比:高风险用户/购卡用户数')
        }
      },
      {
        title: '中风险用户数及占比',
        key: 'middleRiskUvAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比:中风险用户/购卡用户数')
        }
      },
      {
        title: '低风险用户数及占比',
        key: 'lowRiskUvAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比:低风险用户/购卡用户数')
        }
      }
    ]
  },
  {
    title: '投诉数及占比分布',
    key: 'highRiskComplain',
    children: [
      {
        title: '高风险用户二次投诉数及占比',
        key: 'highRiskComplaintRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：高风险用户第二次投诉的用户/投诉总用户数')
        }
      },
      {
        title: '中风险用户投诉数及占比',
        key: 'middleRiskComplaintRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：中风险用户二次购买并投诉的用户/投诉总用户数')
        }
      },
      {
        title: '低风险用户投诉数及占比',
        key: 'lowRiskComplaintRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：低风险用户二次购买并投诉的用户数/投诉总用户数')
        }
      }
    ]
  },
  {
    title: '退款数及占比分布',
    key: 'highRisk',
    children: [
      {
        title: '高风险用户退款数及占比',
        key: 'highRiskRefundRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：高风险用户第二次退款的用户/退款总用户数')
        }
      },
      {
        title: '中风险用户退款数及占比',
        key: 'middleRiskRefundRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：中风险用户第二次退款的用户/退款总用户数')
        }
      },
      {
        title: '低风险用户退款数及占比',
        key: 'lowRiskRefundRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：低风险用户第二次退款的用户/退款总用户数')
        }
      }
    ]
  },
  {
    title: '风险用户二次行为及占比分布',
    key: 'highRiskUserAction',
    children: [
      {
        title: '高风险再次支付及占比',
        key: 'highRiskPayAgainAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：高风险用户中再次支付成功用户/购卡用户数')
        }
      },
      {
        title: '高风险再次投诉及占比',
        key: 'highRiskComplaintAgainAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：高风险用户二次投诉用户/投诉用户数')
        }
      },
      {
        title: '中风险再次支付及占比',
        key: 'middleRiskPayAgainAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：中风险用户中再次支付成功用户/购卡用户数')
        }
      },
      {
        title: '中风险再次退款及占比',
        key: 'middleRiskRefundAgainAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：中风险用户中再次退款用户/退款用户数')
        }
      },
      {
        title: '低风险再次支付及占比',
        key: 'lowRiskPayAgainAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：低风险用户中再次支付成功用户/购卡用户数')
        }
      },
      {
        title: '低风险再次退款及占比',
        key: 'lowRiskRefundAgainAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：低风险用户中再次退款用户/退款用户数')
        }
      },
      {
        title: '低风险再次投诉及占比',
        key: 'lowRiskComplaintAgainAndRate',
        renderHeader: (h, { column }) => {
          return renderHeaders(h, column, '占比：低风险用户中再次投诉用户/投诉用户数')
        }
      }
    ]
  }
]
function renderHeaders(h, column, text) {
  return h('div', [
    h('span', column.label),
    h(
      'el-tooltip',
      {
        props: {
          content: text
        }
      },
      [
        h('i', {
          class: 'el-icon-question',
          style: 'color:#409eff;margin-left:5px;font-size: 16px;line-height:23px'
        })
      ]
    )
  ])
}
