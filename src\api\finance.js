import {get, post} from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'
/**
 * 财务管理模块api
 */

// //提现审核列表
// export const get_finance_list = obj => {
//   return get("withdraw/finance/list", obj, false)
// };

// //提现审核统计
// export const get_finance_statistic = () => {
//   return get("withdraw/statistic/finance/vo", null, false)
// };

// //提现审核同意申请
// export const finance_approval = (id,content) => {
//   return post(`withdraw/${id}/finance/approval`, {content}, false)
// };

// //拒绝提现
// export const finance_rejected = (id,content) => {
//   return post(`withdraw/${id}/finance/rejected`, {content}, false)
// };

export const get_financeOperate_list = obj => {
  return get('/withdraw/operate/list', obj, false)
}
// 运营审批通过
export const post_operate_approval = withdrawApplyId => {
  return post(`/withdraw/operate/approval/${withdrawApplyId}`, null, false)
}
export const post_operate_approval_batch = params => {
  return post(`/withdraw/operate/approval/batch`, params, false)
}

export const post_operate_approval_all = params => {
  return post(`/withdraw/operate/approval/all`, params, false)
}
// 运营审批拒绝

export const post_operate_rejected = params => {
  return post(`/withdraw/operate/rejected/${params.withdrawApplyId}`, params, false)
}
export const post_operate_rejected_batch = params => {
  return post(`/withdraw/operate/rejected/batch`, params, false)
}

export const post_operate_rejected_all = params => {
  return post(`/withdraw/operate/rejected/all`, params, false)
}
// 财务审批通过
export const post_finance_approval = withdrawApplyId => {
  return post(`/withdraw/finance/approval/${withdrawApplyId}`, null, false)
}
export const post_finance_approval_batch = params => {
  return post(`/withdraw/finance/approval/batch`, params, false)
}
export const get_finance_approval_batch = params => {
  return get(`/withdraw/select/approval/batch`, params)
}
export const post_finance_approval_all = params => {
  return post(`/withdraw/finance/approval/all`, params, false)
}
export const get_finance_approval_all = params => {
  return get(`/withdraw/select/approval/all`, params, false)
}
// 订单统计
export const get_withdraw_statis = params => {
  return get(`/withdraw/statis`, params, false)
}
export const EXPORT_Withdraw_Finance = data => CONSTANT.publicPath + '/withdraw/export?' + qs.stringify(data)

export const GET_WITHDRAW_FIND_LIST = params => {
  return get(`/activity/withdraw/record/findList`, params, false)
}
export const EXPORT_WITHDRAW_RECORD = data =>
  CONSTANT.publicPath + '/activity/withdraw/record/export?' + qs.stringify(data)

export const WITHDRAW_RECORD_AUDIT = params => {
  return get(`/activity/withdraw/record/audit`, params, false)
}

// 线下会员退款订单查询
export const GET_VIP_REFUND_ORDER_LIST = obj => {
  return get('/vip/order/refund/offline/page', obj, false)
}
// 线下会员退款审核
export const REFUND_OFFLINE_AUDIT = obj => {
  return post('/vip/order/refund/offline/audit', obj)
}
// 线下会员退款审核统计
export const REFUND_OFFLINE_STATISTICS = obj => {
  return get('/vip/order/refund/offline/statistics', obj, false)
}
// 线下会员退款订单统计
export const GET_VIP_REFUND_STATISICS = obj => {
  return get('/vip/order/refund/offline/statistics', obj, false)
}

// 线下会员退款订单 备注更新
export const REFUND_UPDATE_REMARKE = obj => {
  return post('/vip/order/refund/offline/update/remark', obj)
}

// 线下会员退款订单 信息更新
export const REFUND_UPDATE_ACCOUNT = obj => {
  return post('/vip/order/refund/offline/update/account', obj)
}

// 线下会员退款订单 待退款订单导出
export const EXPORT_INREFUND_EXCEL = data =>
  CONSTANT.publicPath + '/vip/order/refund/offline/export/unrefund?' + qs.stringify(data)

// 线下会员退款订单 待退款订单导出
export const EXPORT_OFFLINE_EXPORT = data =>
  CONSTANT.publicPath + '/vip/order/refund/offline/export?' + qs.stringify(data)

// 线下会员退款订单 待退款订单导入
export const IMPORT_INREFUND_EXCEL = obj => {
  return post('/vip/order/refund/offline/import/refund', obj)
}
