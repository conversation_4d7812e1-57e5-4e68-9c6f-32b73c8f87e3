<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 14:33:57
 * @LastEditors: 蒋雪
 * @LastEditTime: 2024-07-03 14:56:38
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="变声器管理" />
    <SDialog top="5vh" :dialog-form-visible.sync="dialogFormVisible" :data="dialogOps">
      <voiceSetDetail :cur-form-data="curFormData" @closeDialog="dialogFormVisible=false" @updateList="tableRefresh" />
    </SDialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { voiceChangerPage } from '@/qjjpApi/operate'
import SDialog from '@/components/restructure/dialog'
import voiceSetDetail from './voiceSetDetail'
export default {
  name: 'qjjpVoiceManage',
  components: {
    page,
    SDialog,
    voiceSetDetail
  },
  data() {
    return {
      listQuery: {
      },
      dialogFormVisible: false,
      dialogOps: {
        title: '编辑变声器',
        width: '60%'
      },
      curFormData: {},
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await voiceChangerPage(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      },
      list1: [{ id: 0, name: '免费' }, { id: 1, name: '付费' }],
      list2: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ]
    }
  },
  computed: {
    list() {
      return [
        {
          title: 'id',
          key: 'id'
        },
        {
          title: '三方名称',
          key: 'tripartiteName'
        },
        {
          title: 'voice_type',
          key: 'voiceType'
        },
        {
          title: '变声名称',
          key: 'name',
          type: formItemType.input,
          search: true,
          clearable: true
        },
        {
          title: '变声类型',
          key: 'type1',
          searchKey: 'type',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list1,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '启用状态',
          key: 'enabled',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          search: true,
          clearable: true,
          tableHidden: true
        },

        {
          title: '头像',
          key: 'avatar',
          tableView: tableItemType.tableView.picture
        },
        {
          title: '示例语音',
          key: 'demoText',
          render: (h, params) => {
            if (!params.data.row.demoUrl) {
              return h('span', params.data.row.demoText)
            }
            return <a href={params.data.row.demoUrl} target='_blank'>{params.data.row.demoText}</a>
          }
        },
        {
          title: '排序',
          key: 'sort'
        },
        {
          title: '变声类型',
          key: 'type',
          tableView: tableItemType.tableView.text,
          list: this.list1,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'enabled',
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.curFormData = params
                this.dialogFormVisible = true
              }
            }
          ]
        }
      ]
    }
  },
  created() {},
  methods: {
    tableRefresh() {
      this.$store.dispatch('tableRefresh', this)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
