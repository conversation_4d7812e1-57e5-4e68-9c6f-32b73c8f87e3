<template>
  <page :request="request" :list="list" table-title="底导页面统计">
    <div slot="searchContainer" style="display: inline-block">
      <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { GET_Bury_BottomGuide, EXPORT_Bury_BottomGuide } from '@/api/buryData'
import moment from 'moment'
import { count_channel_application_list } from '@/api/NewChannel'

export default {
  components: {
    page
  },
  props: {},
  data() {
    const that = this
    return {
      listQuery: {
        startDate: moment()
          .subtract(7, 'days')
          .format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        siteId: '',
      },
      request: {
        getListUrl: async (data) => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = res.data
                this.listQuery.siteId = res.data[0].siteId
              }
            })
          }
          return Promise.all([GET_Bury_BottomGuide(this.listQuery)]).then(
            res => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      siteIds: [],
    }
  },
  computed:{
    list(){
      return[
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [
            moment()
              .subtract(7, 'days')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId',
          },
          val:this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable:false,
          tableHidden: true,
        },
        {
          title: '日期',
          key: 'date',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '首页访问PV/UV',
          key: 'homePage'
        },
        {
          title: '尊享特权访问PV/UV',
          key: 'privileges'
        },
        {
          title: '返利商城访问PV/UV',
          key: 'rebateShopping'
        },
        {
          title: '赚现金访问PV/UV',
          key: 'makeMoney'
        },
        {
          title: '个人中心访问PV/UV',
          key: 'userCenter'
        }
      ]
    }
  },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_Bury_BottomGuide({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
