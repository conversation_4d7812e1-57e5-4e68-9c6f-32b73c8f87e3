<template>
  <div>
    <div class="title">非会员关联券</div>
    <el-checkbox-group v-model="activeCheckList" @change="listChange">
      <div v-if="checkList.length > 0">
        <el-checkbox
          v-for="(item, index) in checkList"
          :key="index"
          :label="item.couponId"
          class="checkbox"
        >{{ item.name
        }}<el-input
          v-if="showInput(item.couponId)"
          v-model="item.plansCouponSort"
          style="width: 80px; margin-left: 10px;"
          placeholder="排序"
        /></el-checkbox>
      </div>
      <div v-else style="margin-top:10px">暂无关联券~</div>
    </el-checkbox-group>

    <div class="title" style="margin-top:20px">会员关联券</div>
    <el-checkbox-group v-model="activeCheckList" @change="listChange">
      <div v-if="checkList2.length > 0">
        <el-checkbox
          v-for="(item, index) in checkList2"
          :key="index"
          :label="item.couponId"
          class="checkbox"
        >{{ item.name
        }}<el-input
          v-if="showInput(item.couponId)"
          v-model="item.plansCouponSort"
          style="width: 80px; margin-left: 10px;"
          placeholder="排序"
        /></el-checkbox>
      </div>
      <div v-else style="margin-top:10px">暂无关联券~</div>
    </el-checkbox-group>

    <div class="title" style="margin-top:20px">会员续费关联</div>
    <el-checkbox-group v-model="activeCheckList" @change="listChange">
      <div v-if="checkList3.length > 0">
        <el-checkbox
          v-for="(item, index) in checkList3"
          :key="index"
          :label="item.couponId"
          class="checkbox"
        >{{ item.name
        }}<el-input
          v-if="showInput(item.couponId)"
          v-model="item.plansCouponSort"
          style="width: 80px; margin-left: 10px;"
          placeholder="排序"
        /></el-checkbox>
      </div>
      <div v-else style="margin-top:10px">暂无关联券~</div>
    </el-checkbox-group>
    <p style="text-align: right; margin-top: 10px">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="handleSure">确定</el-button>
    </p>
  </div>
</template>

<script>
import {
  prepaidPhonePlans_updateAssociatedCoupon,
  prepaidPhonePlans_getAssociatedCoupon,
  prepaidPhoneCoupon_list
} from '@/api/costManagement'
export default {
  props: {
    id: ''
  },
  data() {
    return {
      checkList: [],
      checkList2: [],
      checkList3: [],
      activeCheckList: [],
      couponList: [],
      memberList: [],
      renewMemberList: []
    }
  },
  async created() {
    await prepaidPhoneCoupon_list({ id: this.id }).then(res => {
      if (res.data) {
        this.couponList = res.data.commonList
        this.memberList = res.data.memberList
        this.renewMemberList = res.data.renewMemberList
      }
    })
    await prepaidPhonePlans_getAssociatedCoupon({ id: this.id }).then((res) => {
      if (res.data) {
        console.log(this.couponList, 123456)
        this.checkList = this.couponList.map((item) => {
          for (let i = 0; i < res.data.length; i++) {
            const element = res.data[i]
            if (element.couponId == item.id) {
              item.plansCouponSort = element.plansCouponSort
              break
            } else {
              item.plansCouponSort = ''
            }
          }
          return {
            plansId: this.id,
            couponId: item.id,
            name: item.name,
            plansCouponSort: item.plansCouponSort
          }
        })

        this.checkList2 = this.memberList.map((item) => {
          for (let i = 0; i < res.data.length; i++) {
            const element = res.data[i]
            if (element.couponId == item.id) {
              item.plansCouponSort = element.plansCouponSort
              break
            } else {
              item.plansCouponSort = ''
            }
          }
          return {
            plansId: this.id,
            couponId: item.id,
            name: item.name,
            plansCouponSort: item.plansCouponSort
          }
        })

        this.checkList3 = this.renewMemberList.map((item) => {
          for (let i = 0; i < res.data.length; i++) {
            const element = res.data[i]
            if (element.couponId == item.id) {
              item.plansCouponSort = element.plansCouponSort
              break
            } else {
              item.plansCouponSort = ''
            }
          }
          return {
            plansId: this.id,
            couponId: item.id,
            name: item.name,
            plansCouponSort: item.plansCouponSort
          }
        })
        this.activeCheckList = res.data.map((item) => item.couponId)
      }
    })
  },
  destroyed() {
    this.checkList = []
    this.checkList2 = []
    this.activeCheckList = []
  },
  methods: {
    showInput(id) {
      return this.activeCheckList.includes(id)
    },
    listChange(e) {
    },
    handleSure() {
      const params = {
        id: this.id,
        plansCoupons: []
      }
      for (let i = 0; i < this.checkList.length; i++) {
        const item = this.checkList[i]
        if (this.activeCheckList.includes(item.couponId)) {
          if (!item.plansCouponSort) {
            this.$message.error('请输入对应的排序')
            return
          }
          params.plansCoupons.push({
            plansId: item.plansId,
            couponId: item.couponId,
            plansCouponSort: item.plansCouponSort
          })
        }
      }
      for (let i = 0; i < this.checkList2.length; i++) {
        const item = this.checkList2[i]
        if (this.activeCheckList.includes(item.couponId)) {
          if (!item.plansCouponSort) {
            this.$message.error('请输入对应的排序')
            return
          }
          params.plansCoupons.push({
            plansId: item.plansId,
            couponId: item.couponId,
            plansCouponSort: item.plansCouponSort
          })
        }
      }
      for (let i = 0; i < this.checkList3.length; i++) {
        const item = this.checkList3[i]
        if (this.activeCheckList.includes(item.couponId)) {
          if (!item.plansCouponSort) {
            this.$message.error('请输入对应的排序')
            return
          }
          params.plansCoupons.push({
            plansId: item.plansId,
            couponId: item.couponId,
            plansCouponSort: item.plansCouponSort
          })
        }
      }
      prepaidPhonePlans_updateAssociatedCoupon(params).then((res) => {
        if (res.status === 200) {
          this.$message.success('操作成功')
          this.$emit('success')
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style scoped>
.checkbox {
  margin: 0 20px 20px 0;
  width: 182px;
  height: 40px;
}
.title{
  font-size:16px;
  font-weight: bold;
}
</style>
>
