<template>
  <div>
    <page :request="request" :list="list" table-title="商户号管理">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getmerchant, downOrUp, merchantexport, downOrUpwarn } from '@/qjjpApi/statisticsOverview'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      curStatusParams: {
        id: 0,
        status: 0
      },
      list1: [
        {
          id: 1,
          name: '微信'
        },
        {
          id: 2,
          name: '支付宝'
        },
        {
          id: 0,
          name: 'ios支付'
        }
      ],
      list2: [
        {
          id: 1,
          name: 'APP支付'
        },
        {
          id: 2,
          name: 'H5支付'
        },
        {
          id: 3,
          name: '周期扣款-支付并签约'
        },
        {
          id: 4, 
          name: '周期扣款-签约后扣款'
        },
        {
          id: 5,
          name: '小程序支付'
        }
      ],
      list3: [
        {
          id: 1,
          name: '站内支付'
        },
        {
          id: 2,
          name: '站内落地页支付'
        },
        {
          id: 3,
          name: '悬浮窗支付'
        }
      ],
      list7: [
        {
          id: 0,
          name: '普通属性'
        },
        {
          id: 1,
          name: '权益会员活动'
        },
        {
          id: 2,
          name: '权益卡类'
        }
      ],
      list4: [
        {
          id: 0,
          name: '启用'
        },
        {
          id: 1,
          name: '禁用'
        }
      ],
      siteIds: [],
      listQuery: {
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await getmerchant(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        // {
        //   key: 'dateSearch',
        //   title: '日期',
        //   type: formItemType.datePickerDaterangeGai,
        //   options: {
        //     format: 'YYYY-MM-DD',
        //     valueFormat: 'yyyy-MM-dd'
        //   },
        //   childKey: ['startDate', 'endDate'],
        //   formHidden: true,
        //   search: true,
        //   val: [currentDate, currentDate]
        // },
        {
          title: '商户平台',
          key: 'way',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list1,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true
        },
        {
          title: '商户类型',
          key: 'type',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true
        },
        {
          title: '支付场景',
          key: 'scene',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          clearable: true,
          search: true
        },
        {
          title: '活动属性',
          key: 'rightSupport',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list7,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true,
          search: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          clearable: true,
          search: true,
          tableHidden: true
        },
        {
          title: '商户名称',
          key: 'merchantName'
        },
        {
          title: '商户id',
          key: 'merchant'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          tableView: tableItemType.tableView.date,
          options: {
            format: 'YYYY-MM-DD HH:mm:ss'
          }
        },
        {
          title: '更新人员',
          key: 'adminName'
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          clearable: false
        },
        {
          title: '操作',
          key: 'status1',
          render: (h, params) => {
            const data = params.data.row
            const changeStatus = data.status == 0 ? 1 : 0
            return h('el-switch', {
              props: {
                value: data.status == 0
              },
              on: {
                change: (e) => {
                  const changeFn = () => {
                    this.curStatusParams = {
                      id: data.id,
                      status: changeStatus
                    }
                    this.changeStatus()
                  }
                  if (changeStatus === 1) {
                    downOrUpwarn({ id: data.id, type: changeStatus + 1 }).then(res => {
                      if (res.code == 200 && res.data == 1) {
                        this.$message.error('操作限制：该支付场景仅存在一个商户号，不可禁用')
                      } else {
                        changeFn()
                      }
                    })
                  } else {
                    downOrUpwarn({ id: data.id, type: changeStatus + 1 }).then(res => {
                      if (res.code == 200 && res.data == 1) {
                        this.$confirm('该支付场景已存在启用商户，启用新的商户将自动禁用已启用商户，请确认是否启用', '提示', {
                          confirmButtonText: '确定',
                          cancelButtonText: '取消',
                          type: 'warning'
                        }).then(() => {
                          changeFn()
                        }).catch(() => {
                        })
                      } else {
                        changeFn()
                      }
                    })
                  }
                }
              }
            })
          }
        }
      ]
    }
  },
  created() { },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = merchantexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    changeStatus() {
      const params = this.curStatusParams
      downOrUp({ ...params }).then(res => {
        if (res.code == 200) {
          this.$message.success('更改状态成功')
          this.$store.dispatch('tableRefresh', this)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
