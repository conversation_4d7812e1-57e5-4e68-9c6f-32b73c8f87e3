<!--
 * @Author: 陈小豆
 * @Date: 2024-04-28 19:46:33
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-04-28 19:49:19
-->
<template>
  <div class="tinymce">
    <Editor :id="textId" v-model="value" :init="init" />
  </div>
</template>

<script>
import lang from '@/tinymce/langs/zh_CN'
import tinymce from 'tinymce/tinymce'
import 'tinymce/themes/silver/theme'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/plugins/image'
import 'tinymce/plugins/link'
import 'tinymce/plugins/code'
import 'tinymce/plugins/table'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/contextmenu'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/colorpicker'
import 'tinymce/plugins/textcolor'
import 'tinymce/plugins/imagetools'
import 'tinymce/icons/default/icons'
tinymce.addI18n('zh_CN', lang)

export default {
  name: 'Tinymce',
  components: { Editor },
  props: {
    textId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      init: {
        language_url: lang,
        language: 'zh_CN',
        height: 300,
        plugins: 'link lists image code table imagetools wordcount',
        toolbar:
          'bold italic underline strikethrough | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent blockquote | undo redo | link unlink code | removeformat',
        imagetools_toolbar:
          'rotateleft rotateright | flipv fliph | editimage imageoptions',
        skin_url: '/tinymce/skins/ui/oxide'
      },
      value: ''
    }
  },
  mounted() {
    // tinymce.init({});
  }
}
</script>

<style>
@import '../../tinymce/skins/ui/oxide/skin.min.css';
</style>

<style lang="scss">
.tox,
.tox-tinymce {
  /*height: 200px;*/
  width: 700px;
}
</style>
