import { put, get, post, del } from '@/libs/axios.package'

/**
 * 商城类型列表
 * */
export const GET_MALL_TYPE_LIST = obj => {
  return get('/shopmalls/all', obj, null)
}

/**
 * 商城类型列表
 * */
export const EDIT_MALL_TYPE = obj => {
  return put(`/shopmalls/${obj.id}`, obj, null)
}

/**
 * 商城类型列表
 * */
export const GET_MALL_GOODS_LIST = obj => {
  return get(`/goods/extension/list`, obj, null)
}

export const GET_MALL_GOODS_DETAIL_BY_ID = id => {
  return get(`/goods/extension/${id}`, null, false)
}

export const EDIT_MALL_GOODS = (obj) => {
  return put('/goods/extension', obj, false)
}

export const ADD_MALL_GOODS = (obj) => {
  return post('/goods/extension', obj, false)
}

export const GET_SHOW_WINDOW_LIST = obj => {
  return get(`/banner/list`, obj, null)
}

export const GET_LOCATION_LIST = obj => {
  return get('/banner/locationlist', obj)
}

export const ADD_BANNER = obj => {
  return post(`/banner`, obj)
}

export const PUT_BANNER = obj => {
  return put(`/banner`, obj)
}

export const GET_BANNER_DETAIL = id => {
  return get(`/banner/${id}`)
}

export const DELETE_BANNER = id => {
  return del(`/banner/${id}`)
}

// 商城统计 banner 统计列表
export const GET_BANNER_dataShopList = obj => {
  return get(`/banner/shop/list`, obj)
}

export const GET_BANNER_dataSelectStatisByBannerId = obj => {
  return get(`/bury/banner/selectStatisByBannerId/${obj.bannerId}`, obj)
}

export const GET_MALL_FIND_LIST = obj => {
  return get(`/shop/type/manage/findList`, obj)
}

export const GET_MALL_FIND_BY_ID = obj => {
  return get(`/shop/type/manage/${obj.id}`, null)
}

export const SAVE_MALL_FIND = obj => {
  return post(`/shop/type/manage`, obj)
}

export const GET_TYPE_CHANNEL_LIST = obj => {
  return get(`/shop/type/channel`, obj)
}
