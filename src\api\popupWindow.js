import { get, post } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

export const GET_POPUP_NEW = obj => {
  return get('/popupnew/list', obj)
}

export const GET_POPUP_DETAIL = obj => {
  return get('/popupnew/detail', obj)
}

export const ADD_POPUP = obj => {
  return post('/popupnew/add', obj)
}

export const GET_POPUP_NEW_ROUTE = obj => {
  return get('/popup/new/route/list', obj)
}

export const ADD_POPUP_NEW_ROUTE = obj => {
  return post('/popup/new/route/addorupdate', obj)
}

export const GET_POPUP_NEW_ROUTE_BY_ID = obj => {
  return get('/popup/new/route/detail', obj)
}

export const GET_CHANNEL_CATEGORY = obj => {
  return get('/channelcategory/selector', obj)
}

export const GET_CHANNEL_POSITION_LIST = obj => {
  return get('/popup/new/position/list', obj)
}

export const ADD_CHANNEL_POSITION = obj => {
  return post('/popup/new/position/add', obj)
}

export const popupnew_count = obj => {
  return get('/popupnew/count', obj)
}

export const popupnew_count_export = data => CONSTANT.publicPath + '/popupnew/count/export?' + qs.stringify(data)

