<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="186px"
      class="demo-ruleForm"
    >
      <el-form-item label="名称：" prop="name">
        <el-input v-model="ruleForm.name" placeholder="请输入任务名称" />
      </el-form-item>
      <el-form-item label="广告类型：" prop="adType">
        <el-select v-model="ruleForm.adType" placeholder="请选择广告类型">
          <el-option
            v-for="(item, i) in advertisementTypeList"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="展示策略：" prop="strategy">
        <el-select v-model="ruleForm.strategy" placeholder="请选择展示策略">
          <el-option
            v-for="(item, i) in strategyList"
            :key="i"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="规则">
        <el-row
          v-for="(item, itemIndex) in ruleForm.rule"
          :key="itemIndex"
          style="margin-bottom:10px"
        >
          <el-form-item :label="`${item.adName}`" label-width="80px">
            <el-input
              v-model="ruleForm.rule[itemIndex].proportion"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-row>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-radio-group v-model="ruleForm.status">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
        >确定</el-button>
        <el-button @click="resetForm('ruleForm')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  post_rule,
  put_rule,
  get_strategy_list,
  get_ad_list,
  get_type_list
} from '@/api/advertisement'
export default {
  props: ['taskType', 'id', 'params'],
  data() {
    return {
      projectList: [],
      strategyList: [],
      ruleForm: {
        name: '',
        adType: '',
        strategy: '',
        status: 0,
        rule: []
      },
      rules: {
        name: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        adType: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        strategy: [
          { required: true, message: '此项不能为空', trigger: 'blur' }
        ],
        rule: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '此项不能为空', trigger: 'blur' }]
      },
      advertisementProviderList: [],
      advertisementTypeList: [],
      osList: this.$utils.getOsList(),
      paramsTaskLisk: []
    }
  },
  mounted() {
    if (this.taskType == 'edit') {
      this.getDataDetail()
    }
    this.initData()
    this.initRule()
  },
  methods: {
    initData() {
      get_strategy_list().then(res => {
        if (res.code == 200) {
          this.strategyList = res.data
        }
      })
      get_type_list().then(res => {
        this.advertisementTypeList = res.data.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.id
          }
        })
      })
    },
    initRule() {
      get_ad_list().then(res => {
        if (res.code == 200) {
          if (this.taskType == 'add') {
            const arr = []
            res.data.forEach(item => {
              arr.push({
                adId: item.id,
                adName: item.name,
                proportion: ''
              })
            })
            this.ruleForm.rule = arr
          } else {
            const arr = [...this.ruleForm.rule]
            const ruleIdList = this.ruleForm.rule.map(item => item.adId)
            res.data.forEach(item => {
              if (ruleIdList.indexOf(item.id) < 0) {
                arr.push({
                  adId: item.id,
                  adName: item.name,
                  proportion: ''
                })
              }
            })
            this.ruleForm.rule = arr
          }
        }
      })
    },
    getDataDetail() {
      this.ruleForm = {
        ...this.params,
        rule: JSON.parse(this.params.rule)
      }
    },
    handChange() {
      this.ruleForm.linkType = ''
      if (this.ruleForm.ruleId == '104' && this.ruleForm.totalTimes) {
        this.handleChangeTotalTimes(this.ruleForm.totalTimes)
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let validRule = true
          this.ruleForm.rule.forEach(item => {
            if (
              item.proportion === '' ||
              !/^[0-9]*$/.test(Number(item.proportion))
            ) {
              validRule = false
            }
          })
          if (!validRule) {
            this.$message.error('输入正确的规格内容')
            return
          }
          if (this.taskType == 'add') {
            post_rule(this.ruleForm).then(res => {
              if (res.code == 200) {
                this.$refs[formName].resetFields()
                this.$emit('close', 'add')
                this.$message.success('添加成功！')
              }
            })
          } else {
            put_rule(this.ruleForm).then(res => {
              if (res.code == 200) {
                this.$refs[formName].resetFields()
                this.$emit('close', 'add')
                this.$message.success('编辑成功！')
              }
            })
          }
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$emit('close', 'close')
    },
    handleChangeTotalTimes(val) {
      val = Number(val)
      if (val > 0 && this.ruleForm.ruleId == '104') {
        const len = this.paramsTaskLisk.length
        if (val > this.paramsTaskLisk.length) {
          for (let i = 0; i < val - len; i++) {
            this.paramsTaskLisk.push({
              code: '',
              value: ''
            })
          }
        } else {
          for (let i = 0; i < len - val; i++) {
            this.paramsTaskLisk.pop()
          }
        }
      } else {
        this.paramsTaskLisk = []
      }
    }
  }
}
</script>

<style scoped></style>
