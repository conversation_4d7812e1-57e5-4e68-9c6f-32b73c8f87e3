import {
  get,
  post
} from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/**
 * 微信小程序列表管理
 */
export const get_miniProgram_list = obj => {
  return get('/v1/wx/mini/shares', obj)
}

export const export_miniProgram_list = data => CONSTANT.publicPath + '/v1/wx/mini/shares/export?' + qs.stringify(data)

/**
 * 微信小程序数据统计
 */
export const get_miniProgram_statistic = obj => {
  return get('/v1/wx/mini/statistic', obj)
}

export const export_miniProgram_statistic = data => CONSTANT.publicPath + '/v1/wx/mini/statistic/export?' + qs.stringify(data)

// 权益列表
export const GET_EQUITY_LIST = data => {
  return get('/channel/interests/list', data)
}

// 编辑权益
export const UPDATE_EQUITY_DETAIL = data => {
  return post('/channel/interests', data)
}
// 权益详情
export const GET_EQUITY_DETAIL = data => {
  return get(`/channel/interests/${data.id}`)
}

// 添加权益
export const ADD_EQUITY_DETAIL = data => {
  return post('/channel/interests/edit', data)
}

// 小程序配置列表
export const GET_MINIPROGRAM_LIST = data => {
  return get('/channel/interests/mini/list', data)
}

// 添加小程序配置
export const ADD_MINIPROGRAM_LIST = data => {
  return post('/channel/interests/mini', data)
}

// 小程序配置列表编辑
export const UPDATE_MINIPROGRAM_LIST = data => {
  return post('/channel/interests/mini/edit', data)
}

// 获取详情
export const GET_MINIPROGRAM_DETAIL = data => {
  return get(`/channel/interests/mini/${data.id}`, data)
}

// 权益列表
export const GET_EQUITY_CONDITION_LIST = data => {
  return get('/channel/interests/select', data)
}

// 获取小程序下拉列表
export const GET_MINIPROGRAM_CONDITION_LIST = data => {
  return get('/channel/mini', data)
}


// 获取公众号文章链接
export const GET_ARTICLE_LINKURL = data => {
  return get('/mini/config/one',data)
}

// 修改公众号文章链接
export const UPDATE_ARTICLE_LINKURL = data => {
  return post('/mini/config/update',data)
}

