/*
 * 版本管理子路由
 * */

const version = [
  {
    path: '/versionManagement/versionList',
    name: 'versionManagement_versionList',
    meta: {
      title: '版本列表'
    },
    // component: () => import("@/views/version/page/versionList"),
    component: () => import('@/views/version/page/versionList')
  },
  {
    path: '/versionManagement/versionDetail',
    name: 'versionManagement_versionDetail',
    meta: {
      title: '详情',
      parentTitle: '版本列表',
      activeMenu: '/versionManagement/versionList'
    },
    // component: () => import("@/views/version/page/versionDetail"),
    component: () => import('@/views/version/page/versionDetail')
  },
  {
    path: '/versionManagement/IOSversion',
    name: 'versionManagement_IOSversion',
    meta: {
      title: 'IOS AB面控制',
      parentTitle: 'IOS AB面控制',
      activeMenu: '/versionManagement/versionList'
    },
    component: () => import('@/views/version/page/IOSversion')
  }
]

export default version
