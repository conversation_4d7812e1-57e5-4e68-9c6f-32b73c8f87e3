import { get, post } from '@/libs/axios.package'

/*
* 申请列表
* */
export const get_list = obj => {
  return get('withdraw/operate/list', obj)
}

/*
* 申请列表
* */
export const get_appliying_list = obj => {
  return get('withdraw/inapproval', obj)
}

/*
* 审批列表
* */
export const get_handle_list = obj => {
  return get('withdraw/process', obj)
}

/*
* 完成列表
* */
export const get_finish_list = obj => {
  return get('withdraw/complete', obj)
}

/*
* 统计
* */
export const get_statistic = () => {
  return get('withdraw/statistic/operate/vo', null)
}

/*
* 同意
* */
export const handle_approval = (id, content) => {
  return post('withdraw/' + id + '/operate/approval', { content })
}

/*
* 拒绝
* */
export const handle_rejected = (id, content) => {
  return post('withdraw/' + id + '/operate/rejected', { content })
}
