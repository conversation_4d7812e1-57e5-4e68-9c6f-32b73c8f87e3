<template>
  <div>
    <el-row type="flex" class="row-bg" justify="end">
      <el-button
        type="primary"
        plain
        icon="el-icon-circle-plus-outline"
        @click="linkDetails('add')"
      >添加推广商品</el-button>
      <el-button type="success" plain icon="el-icon-refresh" @click="refresh()">刷新</el-button>
    </el-row>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item,index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.status"
      >
        <div slot="label">
          {{ item.label }}(
          <span style="color:#FF7F50">{{ item.count }}</span>
          )
        </div>
        <ycCommonList ref="ycCommonList" :status="item.status" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ycCommonList from '../module/yc-common-list'
import { getPromotExtendStatisticApi } from '@/api/goods'
export default {
  components: {
    ycCommonList
  },
  data() {
    return {
      tabs: [],
      activeName: '0',
      casCadeData: []// 商品库分类
    }
  },
  created() {
    this.initStatisticData()
  },
  methods: {
    // 初始化 设置组件对应点击的字段
    initStatisticData() {
      const thar = this
      getPromotExtendStatisticApi().then(res => {
        if (res.code === 0) {
          const arr = [
            { label: '全部商品', status: '0' },
            { label: '已上架', status: '1' },
            { label: '未上架', status: '2' }
          ]
          arr[0].count = res.data.total
          arr[1].count = res.data.up
          arr[2].count = res.data.down
          this.tabs = arr
          this.$nextTick(() => {
            thar.handleClick()
          })
        }
      })
    },
    // 调用组件里面的初始化方法  避免组件里面初始化时多次调用接口
    handleClick(name) {
      this.$refs.ycCommonList[this.activeName].getData()
      this.$refs.ycCommonList[this.activeName].getPromotionCommoditiesData()
      this.$refs.ycCommonList[this.activeName].getGoodsLocation()
    },
    // 点击添加推广商品
    linkDetails(type) {
      this.$router.push({
        path: '/commodity/commonList_detail',
        query: {
          type: type
        }
      })
    },
    refresh() {
      this.handleClick()
    }
  }
}
</script>

<style scoped>
</style>
