<template>
  <div class="app-info-manage">
    <div v-if="listClass.search.length > 0" class="filter-container">
      <span class="title">筛选条件</span>
      <div class="filter-box">
        <SSearch :seach-form-data="listClass.search" :search-button-text="searchButtonText"
          @handleSearch="handleSearch">
          H5正向链路统计筛选情况修改 新增三行-->
          <template slot="searchContainerBefor">
            <slot name="searchContainerBefor" />
          </template>
          <!-- H5正向链路统计筛选情况修改 -->
          <template slot="searchContainer">
            <!-- H5正向链路统计筛选情况修改 添加注释 -->
            <!-- <slot name="searchContainerBefor" /> -->
            <!-- H5正向链路统计筛选情况修改 -->
            <el-form-item v-if="listClass.isDetailsDrawer">
              <el-button class="filter-item" size="small" type="primary" plain icon="el-icon-circle-plus-outline"
                @click="insertDrawer">{{ listClass.headerContainerText }}</el-button>
            </el-form-item>
            <el-form-item v-else-if="listClass.isDetailsDialog">
              <el-button class="filter-item" size="small" type="primary" plain icon="el-icon-circle-plus-outline"
                @click="insert">{{ listClass.headerContainerText }}</el-button>
            </el-form-item>
            <el-form-item v-if="!basics.isNull(exportFileUrl)">
              <exportFile :url="exportFileUrl" :data="Object.assign(exportFileData, query)" />
            </el-form-item>
            <slot name="searchContainer" />
          </template>
          <!-- 新增扩展插槽 表头展开，多选列，table栏展示和隐藏 -->
          <template slot="searchLine">
            <slot name="searchLine" />
          </template>
          <template slot="othersSearchLine">
            <slot name="othersSearchLine" />
          </template>
        </SSearch>
      </div>
    </div>
    <div v-if="listClass.search.length <= 0" class="filter-container-less">
      <slot name="searchContainer">
        <el-form-item v-if="listClass.isDetailsDrawer">
          <el-button class="filter-item" size="small" type="primary" plain icon="el-icon-circle-plus-outline"
            @click="insertDrawer">{{ listClass.headerContainerText }}</el-button>
        </el-form-item>
        <div v-else-if="listClass.isDetailsDialog"
          style="display: flex; justify-content: flex-end; border: 1px solid #ebeef5; margin-bottom: 5px; padding: 5px">
          <el-button class="filter-item" size="small" type="primary" plain icon="el-icon-circle-plus-outline"
            @click="insert">{{ listClass.headerContainerText }}</el-button>
        </div>
      </slot>
    </div>
    <div class="tab-title">
      <div class="title-container">
        <slot name="titleContainer" />
      </div>
    </div>
    <STable v-if="basics.isNull(tableRefreshState) ? $store.state.ref.table : tableRefreshState" :ref="sTableName"
      :fy="fy" :default-total="defaultTotal" :current-tab="currentTab" :change-tab="changeTab" :request="request"
      :query="query" :type="tableType" :column="listClass.column" :row-data="rowData" :table-title="tableTitle"
      :pagination-state="tablePaginationState" :max-height="maxHeight" :select-methods="tableSelectMethods"
      :table-style="tableStyle" :tree-props="treeProps" :row-key="rowKey" :row-style="rowStyle"
      :span-method="spanMethod" :load="load" :lazy="lazy" @currentChange="currentChange"
      @selectionChange="selectionChange" @dialog="dialog" @load="tableLoad" @getByIdCallback="getByIdCallback" @getByIdDrawerCallback="getByIdDrawerCallback" @rowClick="$emit('rowClick',$event)"  />
    <SFormDialog :dialog-footer-state="dialogFooterState" :urls="request" :label-width="labelWidth"
      :click-type="clickType" :before-update="beforeUpdate" :form-item-list="listClass.formItemList" :data="data"
      :title="title" :show.sync="show" @getSubSuccess="getSubSuccess" />
    <SFormDrawer :dialog-footer-state="dialogFooterState" :urls="request" :label-width="labelWidth"
      :click-type="clickType" :before-update="beforeUpdate" :form-item-list="listClass.formItemList" :data="drawerData"
      :title="title" :show.sync="showDrawer" @getSubSuccess="getSubSuccess" />
    <div>
      <SDialog v-for="(i, key) in listClass.dialogList" v-if="dialogStatus[key]" :key="key" :data="i"
        :dialog-form-visible.sync="dialogStatus[key]" @open="$emit(key + 'DialogOpen')"
        @close="$emit(key + 'DialogClose')">
        <template>
          <slot :name="key" :params="dialogParams" :state="dialogStatus" />
        </template>
      </SDialog>
    </div>
  </div>
</template>

<script>
import STabs from '../tabs'
import STable from '../table'
import SFormDialog from '../form/components/dialog/init'
import SFormDrawer from '../form/components/drawer/init'
import SDialog from '../dialog'
import { dialogFooterState, tableItemType } from '@/config/sysConfig'
import { caseAndCase, copy } from '@/config/basicsMethods'
import SSearch from '../search'
import exportFile from '@/components/restructure/button/exportFile'

export default {
  name: 'Index',
  components: {
    STabs,
    STable,
    SFormDialog,
    SFormDrawer,
    SDialog,
    SSearch,
    exportFile
  },
  props: {
    fy: {
      type: Array,
      default: null
    },
    defaultTotal: {
      type: Number,
      default: 0
    },
    rowStyle: {
      type: Function,
      default: () => ({})
    },
    tableTitle: {
      type: String,
      default: ''
    },
    currentTab: {
      type: String,
      default: ''
    },
    changeTab: {
      type: Boolean,
      default: false
    },
    /* 导出文件链接*/
    exportFileUrl: {
      type: String,
      default: ''
    },
    /* 导出文件额外参数*/
    exportFileData: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    request: {
      type: Object,
      default: () => { }
    },
    column: {
      type: Array,
      default: () => []
    },
    rowData: {
      type: Array,
      default: () => []
    },
    formItemList: {
      type: Array,
      default: () => []
    },
    list: {
      type: Array,
      default: () => []
    },
    beforeUpdate: {
      type: Function,
      default: () => {
        return {}
      }
    },
    clickType: {
      type: String
    },
    labelWidth: {
      type: String
    },
    tablePaginationState: {
      type: Boolean,
      default: true
    },
    maxHeight: {
      type: String
    },
    tableSelectMethods: {
      type: Object
    },
    tableRefreshState: {
      type: Boolean,
      default: undefined
    },
    tableType: {
      type: String
    },
    tableStyle: {
      type: Object,
      default: () => ({})
    },
    searchButtonText: {
      type: String,
      default: '搜索'
    },
    dialogFooterState: {
      type: String,
      default: dialogFooterState.common
    },
    treeProps: {
      type: Object,
      default: () => ({})
    },
    spanMethod: {
      type: Function,
      default: () => {
        return [1, 1]
      }
    },
    rowKey: {
      type: String,
      default: ''
    },
    lazy: {
      type: Boolean,
      default: false
    },
    load: {
      type: Function,
      default: () => { }
    }
  },
  data() {
    return {
      sTableName: `${new Date().getTime()}_${parseInt(Math.random() * 1000)}_sTableName`,
      show: false,
      showDrawer: false,
      data: {},
      drawerData: {},
      dialogParams: {},
      dialogStatus: {},
      query: {}
    }
  },
  computed: {
    listClass: function () {
      const column = []
      const formItemList = []
      let isDetailsDialog = false
      let isDetailsDrawer = false
      const dialogList = {}
      const search = []
      let headerContainerText = '添加'
      this.list.forEach(item => {
        if (item.key) item.key = caseAndCase(item.key)
        if (item.childKey) {
          item.childKey.forEach((childItem, index) => {
            item.childKey[index] = caseAndCase(childItem)
          })
        }
        if (item.type && item.type === tableItemType.active) {
          if (
            this.basics.isArray(item.activeType) &&
            !this.basics.isArrNull(item.activeType) &&
            (this.basics.isNull(item.tableHidden) || item.tableHidden === false)
          ) {
            column.push(copy(item))
          }
          if (this.basics.isNull(item.headerContainer) || item.headerContainer) {
            isDetailsDialog = true
          }
          if (item.headerContainerText) {
            headerContainerText = item.headerContainerText
          }
          if (this.basics.isNull(item.activeType)) {
            isDetailsDialog = true
          } else if (this.basics.isArray(item.activeType)) {
            item.activeType.forEach((filterItem, filterIndex) => {
              if (
                (filterItem === tableItemType.activeType.detailsDialog ||
                  (this.basics.isObj(filterItem) && filterItem.type === tableItemType.activeType.detailsDialog)) &&
                (this.basics.isNull(item.headerContainer) || item.headerContainer)
              ) {
                isDetailsDialog = true
              } else if (
                (filterItem === tableItemType.activeType.detailsDrawer ||
                  (this.basics.isObj(filterItem) && filterItem.type === tableItemType.activeType.detailsDrawer)) &&
                (this.basics.isNull(item.headerContainer) || item.headerContainer)
              ) {
                isDetailsDialog = true
                isDetailsDrawer = true
              } else if (
                filterItem === tableItemType.activeType.dialog ||
                (this.basics.isObj(filterItem) && filterItem.type === tableItemType.activeType.dialog)
              ) {
                const state = filterItem.state ? filterItem.state : false
                this.$set(this.dialogStatus, filterItem.key, state)
                dialogList[filterItem.key] = copy(filterItem)
                try {
                  delete column[column.length - 1].activeType[filterIndex].click
                } catch (e) { }
              }
            })
          }
        } else {
          const copyItem = item
          if (this.basics.isNull(copyItem.formHidden) || copyItem.formHidden === false) {
            formItemList.push(this.filterKey('form', copyItem, ['key', 'render', 'title', 'options', 'type', 'val']))
          }
          if (this.basics.isNull(copyItem.tableHidden) || copyItem.tableHidden === false) {
            column.push(
              this.filterKey('table', copyItem, ['key', 'render', 'title', 'options', 'type', 'val', 'tagState'])
            )
          }
        }
        if (item.search) {
          const data = { ...item }
          if (data['searchReg']) data['reg'] = data['searchReg']
          else data['reg'] = []
          if (data['searchKey']) data['key'] = caseAndCase(data['searchKey'])
          if (data['searchTitle']) data['title'] = caseAndCase(data['searchTitle'])
          if (data['searchType']) data['type'] = data['searchType']
          if (data['searchVal']) data['val'] = data['searchVal']
          if (data['searchList']) data['list'] = data['searchList']
          if (data['searchChildKey']) data['childKey'] = data['searchChildKey']
          if (data['searchRequestList']) data['requestList'] = data['searchRequestList']
          search.push(data)
        }
      })
      return {
        column,
        formItemList,
        isDetailsDialog,
        isDetailsDrawer,
        dialogList,
        search,
        headerContainerText
      }
    }
  },
  watch: {
    show() {
      this.sFormDialogChange()
      if (!this.show) {
        this.$store.dispatch('addSubmission')
      }
    },
    showDrawer() {
      this.sFormDrawerChange()
      if (!this.showDrawer) {
        this.$store.dispatch('addSubmission')
      }
    },
    defaultTotal: {
      handler(val) {
        console.info(val, 'defaultTotal')
      }, deep: true
    }
  },
  methods: {
    getByIdCallback(getPromise) {
      this.data = {}
      this.$store.dispatch('editSubmission')
      this.show = true
      getPromise().then(msg => {
        this.data = msg
      })
    },
    getByIdDrawerCallback(getPromise) {
      this.drawerData = {}
      this.$store.dispatch('editSubmission')
      this.showDrawer = true
      getPromise().then(msg => {
        this.drawerData = msg
      })
    },
    /* 筛选关键字*/
    filterKey(key, item, keys) {
      const data = { ...item }
      for (const item of keys) {
        const getKey = caseAndCase(key + '_' + item)
        if (data[getKey]) {
          if (item === 'key') data[getKey] = caseAndCase(data[getKey])
          data[item] = data[getKey]
          delete data[getKey]
        }
      }
      return data
    },
    insert() {
      this.data = {}
      this.$store.dispatch('addSubmission')
      this.show = true
    },
    insertDrawer() {
      this.drawerData = {}
      this.$store.dispatch('addSubmission')
      this.showDrawer = true
    },
    dialog(data) {
      const { key, params } = data
      if (this.listClass.dialogList[key] && this.listClass.dialogList[key].click) {
        this.listClass.dialogList[key].click(data)
      }
      this.dialogStatus[key] = true
      this.dialogParams = params
    },
    handleSearch(data) {
      this.query = data
      this.$emit('handleSearch', data)
    },
    tableLoad(data) {
      this.$emit('tableLoad', data)
    },
    selectionChange(selection) {
      this.$emit('selectionChange', selection)
    },
    currentChange(selection) {
      this.$emit('currentChange', selection)
    },
    sFormDialogChange() {
      this.$emit('sFormDialogChange', this.show)
    },
    sFormDrawerChange() {
      this.$emit('sFormDrawerChange', this.showDrawer)
    },
    // 刷新列表
    getSubSuccess({ isCurrent = false } = {}) {
      if (isCurrent) {
        this.$refs[this.sTableName].getTableList()
      } else {
        if (this.basics.isNull(this.tableRefreshState)) {
          this.$store.dispatch('tableRefresh', this)
        } else {
          this.$emit('update:tableRefreshState', false)
          this.$nextTick(() => {
            this.$emit('update:tableRefreshState', true)
          })
        }
      }
      this.$emit('getSubSuccess')
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
/*.app-info-manage {*/
/*padding: 20px;*/
/*}*/
</style>
