<template>
  <div v-if="dialogVisible">
    <el-dialog
      title="管理分类"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="男生" name="first" />
        <el-tab-pane label="女生" name="second" />
      </el-tabs>
      <div class="classifyMain" :class="[typeList == 'opening' && 'classifyMain1']">
        <el-button
          class="button"
          plain
          icon="el-icon-circle-plus-outline"
          type="primary"
          size="small"
          @click="add"
        >添加{{ typeList == 'style'?'风格':'开场白' }}分类(tab栏)</el-button>
        <page v-if="isRefresh" ref="classifyListPage" :request="requestObj" :list="list" table-title="" />
      </div>
    </el-dialog>

    <el-dialog
      :title="dialogTxt"
      :visible.sync="addVisible"
      width="500px"
    >
      <div class="addForm">
        <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="应用名称" prop="siteIds" :rules="addRules.common">
            <el-select v-model="addForm.siteIds"  multiple  placeholder="请选择应用">
              <el-option
                v-for="item in siteIds"
                :key="item.siteId"
                :label="item.name"
                :value="item.siteId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="名称" prop="name" :rules="addRules.common">
            <div :style="{width: '200px'}">
              <el-input v-model="addForm.name" placeholder="请输入名称" />
            </div>
          </el-form-item>
          <el-form-item label="排序" prop="sort" :rules="addRules.common">
            <div :style="{width: '200px'}">
              <el-input
                v-model="addForm.sort"
                class="noArrowInput"
                type="number"
                placeholder="请输入排序"
              />
            </div>
          </el-form-item>
          <el-form-item label="状态" prop="status" :rules="addRules.common">
            <el-radio-group v-model="addForm.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addVisible=false">取 消</el-button>
        <el-button @click="submit()">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>
<script>
import page from '@/components/restructure/page'
import { messageStyleList, categoryPage, categoryadd } from '@/qjjpApi/operate'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
export default {
  name: 'ClassifyList',
  components: {
    page
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    typeList: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      addForm: {
        siteIds: [],
        name: '',
        sort: '',
        status: 1
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      addVisible: false,
      dialogTxt: '添加',
      dialogVisible: false,
      isRefresh: true,
      activeName: 'first',
      listQuery: {

      },
      siteIds: []
    }
  },
  async created() {
    await count_channel_application_list().then(res => {
      if (res.code === 200) {
        if (res.data && res.data.length) {
          this.siteIds = res.data
        }
      }
    })
  },
  computed: {

    requestObj() {
      const params = { sex: this.activeName == 'first' ? 1 : 0, type: this.typeList == 'style' ? 1 : 2 }
      return {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data, ...params }
          const list = await categoryPage(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    },
    list() {
      return [
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          tableHidden: true,
          search: true,
          clearable: true
        },
        {
          title: '序号',
          key: 'id'
        },
        {
          title: '名称',
          key: 'name'
        },
        {
          title: '排序',
          key: 'sort'
        },
        {
          title: '应用名称',
          key: 'siteName'
        },
        {
          title: '更新时间',
          key: 'updateTime'
        },
        {
          title: '更新人员',
          key: 'updateUser'
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [{
            id: 1,
            name: '启用'
          },
          {
            id: 0,
            name: '禁用'
          }],
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                const params1 = JSON.parse(JSON.stringify(params))
                params1.siteIds = params1.siteIds.split(',')
                this.addForm = Object.assign({}, params1)
                this.dialogTxt = '编辑'
                this.addVisible = true
              }
            }
            // {
            //   text: '删除',
            //   key: 'edit2',
            //   theme: 'warning',
            //   type: tableItemType.activeType.event,
            //   click: ($index, item, params) => {
            //     console.log(params.id)
            //     this.isRefreshFn()
            //   }
            // }
          ]
        }
      ]
    }
  },
  watch: {
    dialogVisible(value) {
      this.addForm = this.$options.data.call(this).addForm
      this.listQuery = this.$options.data.call(this).listQuery
      this.$emit('update:visible', value)
    },
    visible: {
      handler(value) {
        this.dialogVisible = value
      },
      deep: true,
      immediate: true
    },
    requestObj: {
      handler() {
        this.isRefreshFn()
      },
      deep: true

    }
  },
  methods: {
    submit(skipStep = 0) {
      const h = this.$createElement
      this.$refs['addForm'].validate(valid => {
        if (valid) {
          const params = {
            type: this.typeList == 'style' ? 1 : 2,
            sex: this.activeName == 'first' ? 1 : 0,
            skipStep
          }
          const params1 = JSON.parse(JSON.stringify(this.addForm))
          params1.siteIds = params1.siteIds.join(',')
          categoryadd({
            ...params, ...params1
          }).then(res => {
            console.log(res)
            if (res.code == 200) {
              this.addForm = this.$options.data.call(this).addForm
              this.addVisible = false
              this.$message.success(`${this.dialogTxt}成功`)
              this.$emit('updateClassify')
              this.isRefreshFn()
            } else if (res.code == 10000) {
              this.$msgbox({
                title: '温馨提示',
                message: h('div', null, [
                  h('div', { style: 'color: red;font-size:16px' }, '是否确认禁用该分类?'),
                  h('div', { style: 'color: teal;line-height: 2;margin-top:  10px' }, res.msg)
                ]),
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消'
              }).then(() => {
                this.submit(1)
              })
            }
          })
        }
      })
    },
    add() {
      this.dialogTxt = '添加'
      this.addVisible = true
      this.addForm = this.$options.data.call(this).addForm
      this.$nextTick(() => {
        this.$refs['addForm'].clearValidate()
      })
    },

    isRefreshFn() {
      this.isRefresh = false
      setTimeout(() => {
        this.isRefresh = true
      }, 0)
    }
  }
}
</script>
<style lang="scss" scoped>
.classifyMain{
  border: 1px solid #EBEEF5;
  border-top:none ;
  &.classifyMain1{
    border:none ;
  }
  .button{
    margin: 10px 0 10px 10px;
  }
}
::v-deep .el-dialog__body{
  padding:10px 20px;
}
.dialog-footer{
  display: flex;
  justify-content: center;
}

</style>
