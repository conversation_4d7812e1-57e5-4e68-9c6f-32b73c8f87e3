/*
 * 用户管理子路由
 * */

const user = [
  {
    path: '/user/list',
    name: 'userList',
    meta: {
      title: '用户列表'
    },
    // component: () => import("@/views/user/page/userList"),
    component: () => import('@/views/user/page/userList')
  },
  {
    path: '/user/detail',
    name: 'userList_detail',
    meta: {
      title: '用户详情',
      parentTitle: '用户列表',
      activeMenu: '/user/list'
    },
    // component: () => import("@/views/user/page/userDetail")
    component: () => import('@/views/user/page/userDetail')
  },
  {
    path: '/user/statistics',
    name: 'user-statistics',
    meta: {
      title: '用户统计'
    },
    // component: () => import("@/views/user/page/statistics"),
    component: () => import('@/views/user/page/statistics')
  },
  {
    path: '/user/userOrder',
    name: 'user-userOrder',
    meta: {
      title: '用户订单'
    },
    component: () => import('@/views/user/page/userOrder')
  },
  {
    path: '/user/visitorLog',
    name: 'userVisitorLog',
    meta: {
      title: '来访记录'
    },
    component: () => import('@/views/user/page/UserVisitorLog')
  },
]

export default user
