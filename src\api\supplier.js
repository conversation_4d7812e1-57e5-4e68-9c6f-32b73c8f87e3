import {get, post} from '@/libs/axios.package'

// 获取供应商列表
export function getSupplierList(params) {
  return get('/service-provider/page', params)
}

// 获取供应商列表ALL
export function getSupplierAllList(params) {
  return get('/service-provider/all', params)
}

// 获取供应商详情
export function getSupplierDetail(id) {
  return get(`/service-provider/load?id=${id}`)
}

// 新增供应商
export function addSupplier(data) {
  return post('/service-provider/insert', data)
}

// 更新供应商
export function updateSupplier(data) {
  return post('/service-provider/update', data)
}

// 获取轮换策略列表
export function getStrategyList(params) {
  return get('/provider-strategy/page', params)
}

// 获取轮换策略详情
export function getStrategyDetail(id) {
  return get(`/provider-strategy/load?id=${id}`)
}

// 新增或更新轮换策略
export function addOrUpdateStrategy(data) {
  return post('/service-provider/update', data)
}

// 新增轮换策略
export function addStrategy(data) {
  return post('/provider-strategy/insert', data)
}

// 更新轮换策略
export function updateStrategy(data) {
  return post('/provider-strategy/update', data)
}

/**
 * 处理和格式化枚举数据
 * @param {Array} data - 需要处理的原始数据数组
 * @param {string} uniqueKey - 用于去重的键名
 * @param {string} labelKey - 用于显示标签的键名
 * @param {string} valueKey - 用于值的键名
 * @returns {Array<{label: string, value: any}>} 处理后的枚举数据数组
 */
function _processAndFormatEnum(data, uniqueKey, labelKey, valueKey) {
  if (!Array.isArray(data)) return []
  const uniqueData = [...new Map(data.map(item => [item[uniqueKey], item])).values()]
  return uniqueData.map(item => {
    return {
      label: item[labelKey],
      value: item[valueKey]
    }
  })
}

/**
 * 获取并处理供应商名称和使用场景枚举列表
 * @returns {Promise<{supplierNames: Array, scenes: Array}>}
 */
export async function getSupplierNameAndScene() {
  try {
    const res = await get('/service-provider/enumList')
    if (res.code === 200) {
      const rawEnumList = res.data || []
      const supplierNames = _processAndFormatEnum(rawEnumList, 'providerCode', 'providerName', 'providerCode')
      const scenes = _processAndFormatEnum(rawEnumList, 'useType', 'useTypeName', 'useType')
      return {supplierNames, scenes, rawEnumList}
    }
    return {supplierNames: [], scenes: [], rawEnumList: []}
  } catch (error) {
    console.error('获取供应商名称和使用场景列表失败', error)
    return {supplierNames: [], scenes: [], rawEnumList: []}
  }
}

/**
 * 获取策略节点类型枚举列表
 * @returns {Promise<Array<{label: string, value: any}>>}
 */
export async function getStrategyNode() {
  try {
    const res = await get('/provider-strategy/enumList')
    if (res.code === 200) {
      if (!Array.isArray(res.data)) return []
      return res.data.map(item => ({
        label: item.desc,
        value: item.code
      }))
    }
    return []
  } catch (error) {
    console.error('获取策略节点类型列表失败', error)
    return []
  }
}

// 查询所有的使用场景
export function getUsageScenarios(params) {
  return get('/service-provider/all/usage/scenarios', params)
}

// 根据使用场景查询所有的供应商
export function getSuppliersByUsageScenario(params) {
  return get('/service-provider/selector', params)
}

// 策略关联通讯供应商和模板
export function bindCommunication(data) {
  return post('/provider-strategy/bind/communication', data)
}

// 解除通讯供应商和模板
export function unbindCommunication(data) {
  return post('/provider-strategy/unbind', data)
}

// 测试发送短信
export function testSendSms(data) {
  return post('/provider-strategy/testSend', data)
}

// 根据场景码查询短信模板
export function getSmsTemplateByCode(params) {
  return get('/smsTemplate/list/bycode', params)
}

// 根据id 回显模板信息
export function getSmsTemplateById(params) {
  return get(`smsTemplate/byId/${params.id}`, params)
}
