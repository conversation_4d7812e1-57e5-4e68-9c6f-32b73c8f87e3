<!--
 * @Author: 陈小豆
 * @Date: 2024-05-16 19:00:31
 * @LastEditors: 蒋雪 <EMAIL>
 * @LastEditTime: 2024-07-18 17:10:30
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="广告场景列表">
      <div slot="searchContainer" style="display: inline-block">
        <!-- <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button> -->
        <el-button
          plain
          icon="el-icon-circle-plus-outline"
          type="primary"
          size="small"
          @click="handleAdd"
        >新增场景</el-button>
      </div>
    </page>
    <el-drawer
      v-if="drawer"
      title="投放承接编辑/新增"
      :visible.sync="drawer"
      direction="rtl"
      size="50%"
    >
      <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="100px" class="demo-ruleForm">
        <div class="form_view">
          <div class="form_view_title">
            <span>基础信息</span>
          </div>
          <div>
            <el-form-item label="应用类型" prop="os" :rules="addRules.common">
              <el-select v-model="addForm.os" placeholder="请选择应用类型" :disabled="type=='edit'" @change="getChangeOsAPP">
                <el-option
                  v-for="item in osList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="应用名称" prop="adId" :rules="addRules.common">
              <el-select v-model="addForm.adId" placeholder="请选择应用名称" :disabled="type=='edit' ">
                <el-option
                  v-for="item in siteIdsForm"
                  :key="item.id"
                  :label="item.applicationName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="广告类型" prop="adType" :rules="addRules.common">
              <el-select v-model="addForm.adType" placeholder="请选择广告类型" :disabled="type=='edit' ">
                <el-option
                  v-for="item in adTypes"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="场景名称" prop="sceneName" :rules="addRules.common" :style="{display: 'inline-block'}">
              <div :style="{width: '200px'}">
                <el-input v-model="addForm.sceneName" placeholder="请输入场景名称" maxlength="30" />
              </div>
            </el-form-item>
            <span :style="{padding: '0 0 0 10px'}">{{ addForm.sceneName.length }}/30</span>
          </div>
        </div>

        <div class="form_view">
          <div class="form_view_title">
            <span>状态信息</span>
          </div>
          <el-form-item label="状态" prop="status" :rules="addRules.common">
            <el-switch
              v-model="addForm.status"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>
        </div>
        <div :style="{'text-align': 'right', width: '100%'}">
          <el-button @click="drawer=false">取消</el-button>
          <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { undertakeListexport } from '@/qjjpApi/operate'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import { applicationList, addOrEdit, adType, scenelist } from '@/qjjpApi/adver'

import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
import { osList } from '@/qjjpViews/appVersion/basicParams'
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      type: 'add',
      osList,
      addForm: {
        os: '',
        adId: '',
        adType: '',
        sceneName: '',
        status: 1
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      drawer: false,
      list4: [
        {
          id: 1,
          name: '会员用户'
        },
        {
          id: 0,
          name: '非会员用户'
        }
      ],
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      list2: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ],
      list3: [
        {
          id: 1,
          name: '落地页'
        }
      ],
      siteIds: [],
      adTypes: [],
      listQuery: {
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          await applicationList({ status: 1, pageSize: 999 }).then(res => {
            if (res.code === 200) {
              if (res.data.records && res.data.records.length) {
                this.siteIds = res.data.records
              }
            }
          })
          await adType().then(res => {
            if (res.code === 200) {
              if (res.data && res.data.length) {
                this.adTypes = res.data
              }
            }
          })
          const list = await scenelist(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      },
      siteIdsForm: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '应用类型',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.osList,
          search: true,
          clearable: true
        },
        {
          title: '应用名称',
          key: 'adId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'applicationName',
            value: 'id'
          },
          // val:'d6c4a4bbd1f748f89e879c00d60edd8e',
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '广告场景',
          key: 'adSceneId',
          type: formItemType.input,
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '广告类型',
          key: 'adType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.adTypes,
          listFormat: {
            label: 'value',
            value: 'key'
          },
          // val:'d6c4a4bbd1f748f89e879c00d60edd8e',
          reg: ['required'],
          search: true,
          tableHidden: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '场景ID',
          key: 'id'
        },
        {
          title: '应用名称',
          key: 'applicationName'
        },
        {
          title: '广告类型',
          key: 'adType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.adTypes,
          listFormat: {
            label: 'value',
            value: 'key'
          },
          // val:'d6c4a4bbd1f748f89e879c00d60edd8e',
          reg: ['required']
        },
        {
          title: '广告场景',
          key: 'sceneName'
        },
        {
          title: '创建时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required']
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',

              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.addForm = JSON.parse(JSON.stringify(params))
                this.drawer = true
                this.type = 'edit'
              }
            }
          ]
        }
      ]
    }
  },
  async mounted() {
    // const list1 = await messageStyleList()
    // const { records, total } = list1.data
    // let dataList = []
    // console.info(records, 'records')
    // if (records && records.length) {
    //   dataList = [total, ...records]
    // }
    // const result = {
    //   data: dataList
    // }
    // console.info(result, 'result')
  },
  created() {},
  methods: {
    handMessageStyleListAdd(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          addOrEdit({ ...this.addForm }).then(res => {
            if (res.code == 200) {
              this.drawer = false
              this.$message.success('操作成功')
              this.$store.dispatch('tableRefresh', this)
            }
          })
        }
      })
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = undertakeListexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    reloadAddform() {
      this.addForm = {
        adId: '',
        adType: '',
        sceneName: '',
        status: 1
      }
    },
    handleAdd() {
      this.reloadAddform()
      this.type = 'add'
      this.drawer = true
    },
    getChangeOsAPP() {
      applicationList({ status: 1, os: this.addForm.os, pageSize: 999 }).then(res => {
        if (res.code === 200) {
          if (res.data.records && res.data.records.length) {
            this.siteIdsForm = res.data.records
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
    overflow: scroll;
    // padding-bottom: 20px;
    padding: 0 30px 20px;
    /* overflow-x: auto; */
}
::v-deep .el-drawer__header{
  span{
    font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
  }

}
.form_view{
        margin: 0 0px 20px;
        background-color: rgb(189, 184, 184,0.2);
        border: 1px solid rgba(0,0,0,0.2);
        width: 100%;
        padding: 15px;
        border-radius: 5px;
        .form_view_title{
            margin-bottom: 20px;
            span{
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
    }
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
