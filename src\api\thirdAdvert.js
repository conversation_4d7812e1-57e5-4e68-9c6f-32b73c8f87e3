import { put, get, post, del } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

// 广告供应商
export const get_advertisement_provider = obj => {
  return get('/advertisement/provider', obj)
}

export const post_advertisement_provider = obj => {
  return post('/advertisement/provider', obj)
}

export const put_advertisement_provider = obj => {
  return put('/advertisement/provider', obj)
}

export const del_advertisement_provider = obj => {
  return del('/advertisement/provider', obj)
}
// 代码位
export const get_code_seat = obj => {
  return get('/code/seat', obj)
}

export const post_code_seat = obj => {
  return post('/code/seat', obj)
}

export const put_code_seat = obj => {
  return put('/code/seat', obj)
}

export const del_code_seat = obj => {
  return del('/code/seat', obj)
}

export const GET_Count_CodeSeat = obj => {
  return get(`/count/codeSeat`, obj)
}

export const EXPORT_Count_CodeSeat = data => CONSTANT.publicPath + '/count/codeSeat/export?' + qs.stringify(data)

export const GET_Count_AdProvider = obj => {
  return get(`/count/adProvider`, obj)
}
// 广告规则

export const Post_Advertisement_Rule = obj => {
  return post(`/advertisement/rule`, obj)
}

export const Get_Advertisement_Rule_List = obj => {
  return get(`/advertisement/rule/list`, obj)
}

export const Get_Advertisement_Rule_ById = obj => {
  return get(`/advertisement/rule/${obj.id}`, obj)
}

// 广告展示管理
export const Post_Code_Seat_Manage = obj => {
  return post(`/code/seat/manage`, obj)
}

export const Get_Code_Seat_Manage_FindList = obj => {
  return get(`/code/seat/manage/findList`, obj)
}
export const Get_Code_Seat_Manage_Scenee = obj => {
  return get(`code/seat/manage/scene`, obj)
}

export const Get_Code_Seat_Manage_ById = obj => {
  return get(`/code/seat/manage/${obj.id}`, obj)
}

export const get_code_seat_FindFilter = obj => {
  return get('/code/seat/findFilter', obj)
}

export const EXPORT_Count_AdProvider = data => CONSTANT.publicPath + '/count/adProvider/export?' + qs.stringify(data)

