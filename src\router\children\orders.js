/*
 * 系统管理子路由
 * */

const orders = [
  {
    path: '/orders/list',
    name: 'orders_list',
    meta: {
      title: '自营订单',
      keepAlive: true,
      keepAliveName: ['orders_list']
    },
    // component: () => import("@/views/orders/page/list")
    component: () => import('@/views/orders/page/list')
  },
  {
    path: '/orders/order_detail',
    name: 'order_detail',
    meta: {
      title: '权益订单详情',
      parentTitle: '权益订单',
      activeMenu: '/orders/rightsAndInterests',
      keepAliveName: ['rightsAndInterests']
    },
    // component: () => import("@/views/orders/page/order_detail")
    component: () => import('@/views/orders/page/order_detail')
  },
  {
    path: '/orders/tbList',
    name: 'orders_tbList',
    meta: {
      title: '淘宝订单',
      keepAlive: true,
      keepAliveName: ['orders_tbList']
    },
    // component: () => import("@/views/orders/page/tb_list")
    component: () => import('@/views/orders/page/tb_list')
  },
  {
    path: '/orders/tbDetail',
    name: 'orders-tbDetail',
    meta: {
      title: '淘宝订单详情',
      parentTitle: '淘宝订单',
      activeMenu: '/orders/rebateOrder'
    },
    // component: () => import("@/views/orders/page/tb_detail")
    component: () => import('@/views/orders/page/tb_detail')
  },
  {
    path: '/orders/jdList',
    name: 'orders_jdList',
    meta: {
      title: '京东订单',
      keepAlive: true,
      keepAliveName: ['orders_jdList']
    },
    // component: () => import("@/views/orders/page/jd_list")
    component: () => import('@/views/orders/page/jd_list')
  },
  {
    path: '/orders/jdDetail',
    name: 'orders-jdDetail',
    meta: {
      title: '京东订单详情',
      parentTitle: '京东订单',
      activeMenu: '/orders/rebateOrder'
    },
    // component: () => import("@/views/orders/page/jd_detail")
    component: () => import('@/views/orders/page/jd_detail')
  },
  {
    path: '/orders/eleDetail',
    name: 'orders-eleDetail',
    meta: {
      title: '饿了么订单详情',
      parentTitle: '返利订单',
      activeMenu: '/orders/rebateOrder'
    },
    // component: () => import("@/views/orders/page/jd_detail")
    component: () => import('@/views/orders/page/jd_detail')
  },
  {
    path: '/orders/meituanDetail',
    name: 'orders-meituanDetail',
    meta: {
      title: '美团订单详情',
      parentTitle: '返利订单',
      activeMenu: '/orders/rebateOrder'
    },
    // component: () => import("@/views/orders/page/jd_detail")
    component: () => import('@/views/orders/page/jd_detail')
  },
  {
    path: '/orders/pddDetail',
    name: 'orders-pddDetail',
    meta: {
      title: '拼多多订单详情',
      parentTitle: '返利订单',
      activeMenu: '/orders/rebateOrder'
    },
    // component: () => import("@/views/orders/page/jd_detail")
    component: () => import('@/views/orders/page/jd_detail')
  },
  {
    path: '/orders/statistics',
    name: 'orders_statistics',
    meta: {
      title: '返利订单统计'
    },
    // component: () => import("@/views/orders/page/statistics")
    component: () => import('@/views/orders/page/statistics')
  },
  {
    path: '/orders/commonOrderList',
    name: 'orders_common_order_list',
    meta: {
      title: '淘口令订单'
    },
    // component: () => import("@/views/orders/page/statistics")
    component: () => import('@/views/orders/page/commonOrderList')
  },
  {
    path: '/orders/vipOrderList',
    name: 'orders_vipOrderList',
    meta: {
      title: '会员订单'
    },
    // component: () => import("@/views/orders/page/statistics")
    component: () => import('@/views/orders/page/vipOrderList')
  },
  {
    path: '/orders/vipOrderDetail/:vipOrderId',
    name: 'orders_vipOrderDetail',
    meta: {
      parentTitle: '会员订单',
      activeMenu: '/orders/vipOrderList',
      title: '会员订单详情'
    },
    // component: () => import("@/views/orders/page/statistics")
    component: () => import('@/views/orders/page/vipOrderDetail')
  },
  {
    path: '/orders/eleOrderList',
    name: 'orders_eleOrderList',
    meta: {
      title: '饿了么订单'
    },
    // component: () => import("@/views/orders/page/statistics")
    component: () => import('@/views/orders/page/eleOrderList')
  },
  {
    path: '/orders/orderStatistics',
    name: 'orders_orderStatistics',
    meta: {
      title: '订单统计'
    },
    component: () => import('@/views/orders/page/orderStatistics')
  },
  {
    path: '/orders/orderStatisticsByChannel',
    name: 'orders_orderStatisticsByChannel',
    meta: {
      title: '渠道订单统计',
      parentTitle: '订单统计',
      activeMenu: '/orders/orderStatistics'
    },
    component: () => import('@/views/orders/page/orderStatisticsByChannel')
  },
  {
    path: '/orders/rightsAndInterests',
    name: 'orders_rightsAndInterests',
    meta: {
      title: '权益订单'
    },
    component: () => import('@/views/orders/page/rightsAndInterests')
  },
  {
    path: '/orders/rebateOrder',
    name: 'orders_rebateOrder',
    meta: {
      title: '返利订单'
    },
    component: () => import('@/views/orders/page/rebateOrder')
  },
  {
    path: '/orders/oilOrder',
    name: 'orders_oilOrder',
    meta: {
      title: '加油订单'
    },
    component: () => import('@/views/orders/page/oilOrder')
  },
  {
    path: '/orders/shareOrderList',
    name: 'shareOrderList',
    meta: {
      title: '分享订单'
    },
    component: () => import('@/views/orders/page/shareOrderList')
  }
]

export default orders
