<template>
  <div class="comes-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="custom-tabs">
      <el-tab-pane label="站内用户初始化" name="internal">
        <el-form class="comes-form" label-width="100px">
          <el-form-item label="手机号码">
            <el-input
              v-model="internal.phone"
              maxlength="11"
              disabled
              class="custom-input"
              placeholder="请获取手机号"
            />
          </el-form-item>
          <el-form-item label="验证码">
            <el-input 
              v-model="internal.verifyCode" 
              autosize 
              disabled 
              class="custom-textarea"
              placeholder="验证码将显示在这里" 
            />
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="internal.phone"
              size="middle"
              type="primary"
              @click="searchVerificationCode('internal')"
              icon="el-icon-view"
            >查看验证码</el-button>
            <el-button
              v-else
              size="middle"
              type="primary"
              @click="getPhoneNumber('internal')"
              icon="el-icon-mobile-phone"
            >获取手机号</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="站外用户初始化" name="external">
        <el-form class="comes-form" label-width="100px">
          <el-form-item label="手机号码">
            <el-input
              v-model="external.phone"
              maxlength="11"
              disabled
              class="custom-input"
              placeholder="请获取手机号"
            />
          </el-form-item>
          <el-form-item label="验证码">
            <el-input 
              v-model="external.verifyCode" 
              autosize 
              disabled 
              class="custom-textarea"
              placeholder="验证码将显示在这里" 
            />
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="external.phone"
              size="middle"
              type="primary"
              @click="searchVerificationCode('external')"
              icon="el-icon-view"
            >查看验证码</el-button>
            <el-button
              v-else
              size="middle"
              type="primary"
              @click="getPhoneNumber('external')"
              icon="el-icon-mobile-phone"
            >获取手机号</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { white_verification_code, white_verification_code_h5 } from '@/qjjpApi/whitelistInvestigation'
export default {
  name: '',
  components: {},
  data() {
    return {
      activeTab: 'internal',
      internal: {
        phone: '',
        verifyCode: ''
      },
      external: {
        phone: '',
        verifyCode: ''
      }
    }
  },
  created() {},
  methods: {
    handleTabClick() {
      // 重置
      const tab = this.activeTab
      this[tab].phone = ''
      this[tab].verifyCode = ''
    },
    
    // 获取接口
    getApiFunction(type) {
      return type === 'internal' ? white_verification_code : white_verification_code_h5
    },
    
    // 获取验证码
    searchVerificationCode(type) {
      const data = this[type]
      if (!/^1[3456789]\d{9}$/.test(data.phone)) {
        this.$message.warning('手机号码不合法,请刷新页面')
        return
      }
      
      const apiFunction = this.getApiFunction(type)
      apiFunction({
        phone: data.phone
      }).then(res => {
        if (res.code == 200) {
          data.verifyCode = res.data.validationCode
          this.$message.success('验证码已发送')
        }
      })
    },
    
    getPhoneNumber(type) {
      const apiFunction = this.getApiFunction(type)
      const data = this[type]
      
      // 获取手机号码
      apiFunction().then(res => {
        if (res.code == 200) {
          data.phone = res.data.phone
          this.$message.success('手机号获取成功')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.comes-container {
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  max-width: 700px;
  margin: 20px auto;
}

.custom-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 25px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-tabs__item) {
    font-size: 16px;
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    transition: all 0.3s;
    
    &.is-active {
      color: #409EFF;
      font-weight: bold;
    }
    
    &:hover {
      color: #79bbff;
    }
  }
}

.comes-form {
  width: 100%;
  max-width: 450px;
  margin: 20px auto;
  
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }
}

.custom-input, .custom-textarea {
  :deep(.el-input__inner), :deep(.el-textarea__inner) {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    padding: 12px 15px;
    transition: all 0.3s;
    background-color: #f5f7fa;
    
    &:focus {
      border-color: #409EFF;
      box-shadow: 0 0 5px rgba(64, 158, 255, 0.2);
    }
    
    &:disabled {
      color: #606266;
      cursor: not-allowed;
    }
  }
}
</style>
