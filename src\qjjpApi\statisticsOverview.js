/*
 * @Author: 陈小豆
 * @Date: 2024-04-25 21:08:56
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-05-09 11:25:28
 */
import CONSTANT from '@/config/constant.conf'
import { getJjjp, postJjjp } from '@/libs/axios.package'
import qs from 'qs'

// 消息风格列表
export const getmerchant = obj => {
  return getJjjp(`/cms/merchant/page`, obj)
}
// 编辑或新增商品分类接口
export const crowdProductCategoryEdit = obj => {
  return postJjjp('/crowd-product-category/edit', obj)
}

export const downOrUp = obj => {
  return getJjjp(`/cms/merchant/downOrUp`, obj)
}

export const merchantexport = data => CONSTANT.qjjpPath + '/cms/merchant/page/export?' + qs.stringify(data)

export const downOrUpwarn = obj => {
  return getJjjp(`/cms/merchant/downOrUp/warn`, obj)
}

export const getmerchantcount = obj => {
  return getJjjp(`/cms/merchant/count`, obj)
}

export const getlink = obj => {
  return getJjjp(`/cms/count/link`, obj)
}
// 投放链路统计渠道详情
export const getlinkChannelDetail = obj => {
  return getJjjp(`/cms/count/link/channelDetail`, obj)
}

// 投放链路统计小时详情
export const getlinkHourDetail = obj => {
  return getJjjp(`/cms/count/link/hourDetail`, obj)
}

// 广告收益统计
export const getCountrAdvertising = obj => {
  return getJjjp(`/cms/count/advertising`, obj)
}
// 广告收益统计-渠道详情
export const getCountrAdvertisingChannelDetail = obj => {
  return getJjjp(`/cms/count/advertising/channelDetail`, obj)
}

export const countexport = data => CONSTANT.qjjpPath + '/cms/merchant/count/export?' + qs.stringify(data)

export const linkexport = data => CONSTANT.qjjpPath + '/cms/count/link/export?' + qs.stringify(data)

export const linkChannelDetailExport = data => CONSTANT.qjjpPath + '/cms/count/link/channelDetail/export?' + qs.stringify(data)

export const linkHourDetailExport = data => CONSTANT.qjjpPath + '/cms/count/link/hourDetail/export?' + qs.stringify(data)

export const countrAdvertisingExport = data => CONSTANT.qjjpPath + '/cms/count/advertising/export?' + qs.stringify(data)

export const countrAdvertisingChannelDetailExport = data => CONSTANT.qjjpPath + '/cms/count/advertising/channelDetail/export?' + qs.stringify(data)

// 人设效果监控表
export const countStyle = obj => {
  return getJjjp(`/cms/count/style`, obj)
}
// 人设效果监控表导出
export const countStyleExport = data => CONSTANT.qjjpPath + '/cms/count/style/export?' + qs.stringify(data)

// 人设详情监控表
export const countStyleDetail = obj => {
  return getJjjp(`/cms/count/style/detail`, obj)
}
// 人设详情监控表导出
export const countStyleDetailExport = data => CONSTANT.qjjpPath + '/cms/count/style/detail/export?' + qs.stringify(data)

// 分页查询支付场景监控
export const getCountPayPage = obj => {
  return getJjjp(`/cms/countPay/page`, obj)
}

export const getCountPayPageexport = data => CONSTANT.qjjpPath + '/cms/countPay/page/export?' + qs.stringify(data)

// 分页查询支付场景监控-日期
export const getCountPayDayListe = obj => {
  return getJjjp(`/cms/countPay/dayList`, obj)
}

export const getCountPayDayListExport = data => CONSTANT.qjjpPath + '/cms/countPay/dayList/export?' + qs.stringify(data)

// 分页查询支付场景监控-渠道
export const getCountPayChannelList = obj => {
  return getJjjp(`/cms/countPay/channelList`, obj)
}

export const getCountPayChannelListExport = data => CONSTANT.qjjpPath + '/cms/countPay/channelList/export?' + qs.stringify(data)

export const getCountPaySceneDetail = obj => {
  return getJjjp(`/cms/countPay/sceneDetail`, obj)
}
export const materialReportReport = obj => {
  return getJjjp(`/cms/materialReport/report`, obj)
}
