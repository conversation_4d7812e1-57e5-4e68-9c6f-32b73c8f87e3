<template>
  <div>
    <page
      :request="request"
      :list="list"
      table-title="数据统计"
      :table-pagination-state="false"
    />
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { formItemType } from '@/config/sysConfig'
import moment from 'moment'
import { GET_RECHARGE_SELECTSTATIS } from '@/api/business'

export default {
  components: {
    page
  },
  props: {},
  data() {
    return {
      listQuery: {
        startDate: moment()
          .subtract(6, 'days')
          .format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: data => {
          return this.getData(data)
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [
            moment()
              .subtract(6, 'days')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date'
        },
        {
          title: '充值页访问用户(总/新)',
          key: 'rechargePageVisit'
        },
        {
          title: '点击立即充值用户数(总/新)',
          key: 'clickImmediatelyRecharge'
        },
        {
          title: '订单确认访问用户数(总/新)',
          key: 'orderConfirmAccess'
        },
        {
          title: '立即支付按钮用户数(总/新)',
          key: 'immediatelyPayButton'
        },
        {
          title: '支付成功用户数(总/新)',
          key: 'paySuccess'
        },
        {
          title: '使用抵扣券用户数',
          key: 'useDiscount'
        },
        {
          title: '升级用户数',
          key: 'upgrade'
        },
        {
          title: '充值页访问-充值按钮点击转化',
          key: 'visitToClick'
        },
        {
          title: '订单确认页-支付成功转化',
          key: 'confirmToPaySuccess'
        },
        {
          title: '充值页访问-支付成功转化',
          key: 'visitToPaySuccess'
        }
      ]
    }
  },
  watch: {},
  mounted() {},
  methods: {
    getData(data) {
      this.listQuery = { ...this.listQuery, ...data }
      delete this.listQuery.pageSize
      delete this.listQuery.pageNumber
      return Promise.all([GET_RECHARGE_SELECTSTATIS(this.listQuery)]).then(
        res => {
          return Promise.resolve(res[0])
        }
      )
    }
  }
}
</script>

<style lang="" scoped>
</style>
