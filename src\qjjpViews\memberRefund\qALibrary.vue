<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 10:30:33
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-04-28 19:47:23
-->
<template>
  <div>
    <page v-show="!isShow" :request="request" :list="list" class="complaint-list">
      <div slot="searchContainer" style="display: inline-block">
        <el-button type="primary" size="small" icon="el-icon-plus" :style="{'margin-bottom': '10px'}" @click="add">
          添加
        </el-button>
      </div>
    </page>
    <!-- 提交投诉凭证 -->
    <!-- <el-dialog title="问答库" :visible.sync="isShow" width="1200px" :close-on-click-modal="false" @close="resetForm">

    </el-dialog> -->
    <div v-show="isShow">
      <h1>问答库</h1>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="场景" prop="scene">
          <el-select v-model="ruleForm.scene" placeholder="请选择" prop="scene">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="ruleForm.scene === 3" label="投诉对象" prop="targetOfComplaint">
          <el-select v-model="ruleForm.targetOfComplaint" placeholder="请选择" prop="targetOfComplaint">
            <el-option v-for="item in targetOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="ruleForm.scene === 3" label="投诉次数" prop="complaintNum">
          <el-select v-model="ruleForm.complaintNum" placeholder="请选择" prop="complaintNum">
            <el-option v-for="item in complaintOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="问题内容" prop="name">
          <div :style="{width: '700px'}">
            <el-input
              v-model="ruleForm.name"
              placeholder=""
              maxlength="30"
              :style="{display: 'inline-block', 'vertical-align': 'middle', width: '500px'}"
            />
            <span :style="{'padding-left': '20px', 'vertical-align': 'middle'}">{{ ruleForm.name.length }}/30</span>
          </div>
        </el-form-item>
        <el-form-item label="回答类型" prop="type">
          <el-radio-group v-model="ruleForm.type">
            <el-radio :label="2">常规富文本</el-radio>
            <el-radio :label="1" :disabled="ruleForm.scene === 3">跳转链接</el-radio>
            <el-radio :label="3" :disabled="ruleForm.scene === 3">内部跳转</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.type == 1 || ruleForm.type == 3" label="跳转链接" prop="url">
          <div :style="{width: '500px'}">
            <el-input v-model="ruleForm.url" placeholder="" />
          </div>
        </el-form-item>
        <el-form-item v-if="ruleForm.type == 2" label="回答" prop="content" :style="{display: 'inline-block'}">
          <span :style="{position: 'absolute', left: '-50px', color: '#F56C6C'}">*</span>
          <qjjpTinymce ref="content" text-id="content" :set="tinymceSet" :menubar-is-show="isShow" />
        </el-form-item>
        <el-form-item v-if="ruleForm.scene == 1" label="icon" prop="icon">
          <el-input ref="productUrl" v-model="ruleForm.icon" style="display: none" />
          <qjjpUpload
            v-if="isShow"
            ele-name="productUrl"
            :pic-url="ruleForm.icon"
            @uploadSuccess="
              url => {
                this.ruleForm.icon = url
                $refs.productUrl.focus()
              }
            "
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="ruleForm.status">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="0">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm('ruleForm')">取消</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import constant from '@/config/constant.conf'
import qjjpUpload from '@/components/qjjp-upload/upload'
import {
  aliComplaintRefundList,
  complaintContentParams,
  refundOne,
  refundMore,
  complaintRefundListExport
} from '@/api/memberRefund'
import {
  getQaQuestionList,
  QaQuestionAddOrUpdate,
  getQaQuestionDetail,
  ownUploadImg,
  aliUploadImg
} from '@/qjjpApi/memberRefund'
import moment from 'moment'
import { count_channel_application_list } from '@/api/NewChannel'
export default {
  components: {
    page,
    qjjpUpload,
    ycTitle: () => import('@/components/yc-title/title'),
    qjjpTinymce: () => import('@/components/qjjp-tinymce/newTinymce') // 富文本编辑
  },
  props: {},
  data() {
    return {
      tinymceSet: {
        height: 600
      },
      uploadUrl: '',
      headers: {},
      popType: 'add',
      fileList: [],
      options: [
        {
          value: 1,
          label: 'icon位'
        },
        {
          value: 2,
          label: '列表位'
        },
        {
          value: 3,
          label: '官方投诉'
        }
      ],
      targetOptions: [
        {
          value: 1,
          label: '订单'
        },
        {
          value: 2,
          label: '商家/服务'
        },
        {
          value: 3,
          label: '商家/多次退款页'
        }
      ],
      complaintOptions: [
        {
          value: 1,
          label: '首次投诉'
        },
        {
          value: 2,
          label: '二次投诉'
        }
      ],
      ruleForm: {
        id: '',
        scene: 2,
        type: 2,
        name: '',
        content: '',
        url: '',
        icon: '',
        status: 1,
        targetOfComplaint: null,
        complaintNum: null
      },
      isShow: false,
      rules: {
        scene: { required: true, message: '请选择场景', trigger: 'blur' },
        type: { required: true, message: '请选择回答类型', trigger: 'blur' },
        name: { required: true, message: '请输入回答类型', trigger: 'blur' },
        // content: { required: true, message: '请输入回答', trigger: 'blur' },
        url: { required: true, message: '请输入跳转链接', trigger: 'blur' },
        icon: { required: true, message: '请选择icon', trigger: 'blur' },
        status: { required: true, message: '请选择状态', trigger: 'blur' },
        targetOfComplaint: { required: true, message: '请选择投诉对象', trigger: 'blur' },
        complaintNum: { required: true, message: '请选择投诉次数', trigger: 'blur' }
      },
      typeList: [
        {
          label: '跳转连接',
          value: 1
        },
        {
          label: '常规富文本',
          value: 2
        },
        {
          label: '内部跳转',
          value: 3
        }
      ],
      QAType: [
        {
          label: '开启',
          value: 1
        },
        {
          label: '关闭',
          value: 0
        }
      ],
      reFresh: true,
      listQuery: { siteId: '' },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          //   if (!this.siteIds.length) {
          //     await count_channel_application_list().then(res => {
          //       if (res.code === 200) {
          //         this.siteIds = res.data
          //       }
          //     })
          //   }
          return this.getData(this.listQuery)
        }
      },
      siteIds: [],
      complaintDialogVisible: false,
      currentOrder: {},
      tableSelectAble: {
        selectable: function(row, index) {
          if (row.localStatus === 0 || row.refundStatus === 2) {
            return false
          } else return true
        }
      },
      refundList: [], // 批量退款数据
      paramsOption: [] // 投诉文案选择
    }
  },
  computed: {
    list() {
      return [
        {
          title: '搜索关键词',
          key: 'keyWord',
          width: 100,
          search: true,
          type: 'input',
          titleHidden: true,
          tableHidden: true
        },
        {
          title: 'ID搜索',
          key: 'id',
          width: 100,
          search: true,
          type: 'input',
          titleHidden: true,
          tableHidden: true
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.QAType,
          titleHidden: true,
          tableHidden: true
        },
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '使用场景',
          key: 'scene',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.options,
          titleHidden: true
        },
        {
          title: '回答类型',
          key: 'type',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.typeList,
          titleHidden: true
        },
        {
          title: '问题',
          key: 'name'
        },
        {
          title: '答案',
          key: 'totalFee',
          render: (h, params) => {
            const refundEntrance = params.data.row.type
            if (refundEntrance == 1 || refundEntrance == 3) {
              return h('span', {}, !params.data.row.url || params.data.row.url == '' ? '-' : params.data.row.url)
            } else {
              return h('span', {}, '富文本类型请打开编辑查看')
            }
          }
        },

        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.QAType,
          titleHidden: true
        },
        {
          type: tableItemType.active,
          width: 160,
          headerContainer: false,
          tooltip: false,
          fixed: 'right',
          activeType: [
            {
              text: '编辑',
              type: tableItemType.activeType.event,
              theme: 'primary',
              click: ($index, item, params) => {
                getQaQuestionDetail(params.id).then(res => {
                  if (res.code == 200) {
                    this.popType = 'edit'
                    this.ruleForm = res.data
                    this.isShow = true

                    setTimeout(() => {
                      window.tinyMCE.activeEditor.setContent(this.ruleForm.content)
                    }, 300)
                  }
                })
              }
            }
          ]
        }
      ]
    }
  },
  watch: {},
  created() {
    this.uploadUrl = constant.publicPath + '/upload/image'
    // 请求头
    this.headers = { Authorization: `${this.$store.getters.authorization}` }
  },
  mounted() {},
  methods: {
    uploadFile(param) {
      // 手动上传
      const thar = this
      const formData = new FormData()
      formData.append('file', param.file)
      ownUploadImg(formData).then(ownRes => {
        if (ownres.code === 200) {
          formData.append('appId', this.currentOrder.appId)
          aliUploadImg(formData).then(aliRes => {
            if (alires.code === 200) {
              thar.fileList.push(ownRes.data)
              thar.aliFileList.push(aliRes.data)
            } else {
              thar.$message.error('图片上传失败')
            }
          })
        } else {
          this.$message.error('图片上传失败')
        }
      })
    },
    add() {
      this.popType = 'add'
      this.initData()
      this.isShow = true
      setTimeout(() => {
        window.tinyMCE.activeEditor.setContent(this.ruleForm.content)
      }, 300)
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        // 没选择官方时投诉对象跟投诉次数置空
        if (this.ruleForm.scene !== 3) {
          this.ruleForm.targetOfComplaint = null
          this.ruleForm.complaintNum = null
        }
        if (valid) {
          if (this.ruleForm.type == 2) {
            this.ruleForm.content = window.tinyMCE.activeEditor.getContent() // 获取温馨提示内容
            if (this.ruleForm.content == '') {
              this.$message.error('回答不能为空')
              return
            }
          }

          QaQuestionAddOrUpdate(this.ruleForm).then(res => {
            if (res.code === 200) {
              this.$message.success('操作成功')
              this.resetForm('ruleForm')
              this.$store.dispatch('tableRefresh', this)
            } else {
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.initData()
      this.$emit('close')
      this.isShow = false
    },
    initData() {
      this.ruleForm = {
        id: '',
        scene: 2,
        type: 2,
        name: '',
        content: '',
        url: '',
        icon: '',
        status: 1,
        targetOfComplaint: null,
        complaintNum: null
      }
    },
    // 刷新表格数据
    refresh() {
      this.$store.dispatch('tableRefresh', this)
    },
    // 退款弹出框
    refundApprovedSubmit(id) {
      this.$confirm('此操作将通过退款申请, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          const res = await refundOne({ id })
          if (res.code == 200) {
            this.refresh()
            this.$message({
              message: '处理退款成功',
              type: 'success'
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: '处理退款失败'
          })
        })
    },
    handleRefund() {
      // 批量退款
      if (this.refundList && this.refundList.length) {
        // 批量退款操作
        console.log(this.refundList, '批量退款')
        this.$confirm('此操作将批量通过退款申请, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async() => {
            const ids = this.refundList.map(item => {
              return item.aliComplaintRecordId
            })
            const res = await refundMore({ ids })
            if (res.code == 200) {
              this.refresh()
              this.$message({
                message: '处理退款成功',
                type: 'success'
              })
            }
          })
          .catch(() => {
            this.$message({
              type: 'error',
              message: '处理退款失败'
            })
          })
      } else {
        this.$message.error('请勾选退款的数据')
        return
      }
    },
    async getData(data) {
      this.listQuery = { ...this.listQuery, ...data }
      const list = await getQaQuestionList(this.listQuery)
      const { records, total } = list.data
      let dataList = []
      if (records && records.length) {
        dataList = {
          total: total,
          rows: records
        }
      }
      const result = {
        data: dataList
      }
      return result
    },
    handleExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = complaintRefundListExport({
        ...data,
        token: this.$store.getters.authorization
      })
    },

    // 点击编辑 获取凭证参数
    async getParamsOption(data) {
      this.paramsOption = []
      await complaintContentParams({
        outTradeNo: data.outTradeNo,
        code: 'ALI_COMPLAINT_RIGHTS_CONTENT'
      }).then(res => {
        if (res.code === 200) {
          this.paramsOption.push({
            code: 'ALI_COMPLAINT_RIGHTS_CONTENT',
            label: res.data,
            typeVal: '权益使用凭证说明'
          })
        }
      })
      this.currentOrder = { ...data }
      this.complaintDialogVisible = true
    },
    // 提交凭证
    confirm() {
      this.complaintDialogVisible = false
      this.refresh()
    },
    close() {
      this.complaintDialogVisible = false
    },
    getCurSelect(val) {
      // 获取当前的选项
      if (val && val.length) {
        this.refundList = val
      } else this.refundList = []
    }
  }
}
</script>

<style lang="scss" scoped>
.complaint-list {
  ::v-deep .complaint-img-list {
    .el-image-viewer__close {
      color: #fff !important;
      [class^='el-icon-'] {
        font-size: 40px !important;
      }
    }
  }
}
</style>
