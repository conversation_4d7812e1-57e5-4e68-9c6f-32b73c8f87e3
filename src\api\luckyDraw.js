import { put, get, post, del } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

// 抽抽乐活动统计数据
export const GET_COUNT_TURNTABLE_LIST = (obj) => {
  return get(`/count/turntable/list`, obj)
}

// 抽抽乐付费埋点
export const GET_COUNT_LOTTERY_BUY_LIST = (obj) => {
  return get(`/count/lottery/buy/list`, obj)
}

// 转盘活动商品埋点查询
export const GET_COUNT_TURNTABLE_GOODS = (obj) => {
  return get(`/count/turntable/goods`, obj)
}

/**
 * 抽抽乐付费记录
 * */
export const order_lottery_page = (obj) => {
  return get('/order/lottery/page', obj)
}
/**
 * 抽抽乐订单申请退款
 * */
export const order_lottery_order_refund = (obj) => {
  return post('/order/lottery/order/refund', obj)
}
/**
 * 抽抽付费订单状态下拉选
 * */
export const order_lottery_status = (obj) => {
  return get('/order/lottery/status', obj)
}
/**
 * 抽抽乐用户信息列表
 * */
export const turntableAwardAddress_getPage = (obj) => {
  return get('/turntableAwardAddress/getPage', obj)
}
/**
 * 抽抽乐用户信息更新
 * */
export const turntableAwardAddress_updateAddress = (obj) => {
  return post('/turntableAwardAddress/updateAddress', obj)
}
/**
 * 抽抽乐拦截数据查询
 * */
export const GET_INTERCEPT_DATA_DRAW_LIST = (obj) => {
  return get('/count/countTurntableIntercept', obj)
}
