/*
 * @Author: 陈小豆
 * @Date: 2024-05-16 16:55:52
 * @LastEditors: 陈小豆
 * @LastEditTime: 2025-03-07 16:43:16
 */
import CONSTANT from '@/config/constant.conf'
import {
    getJjjp,
    postJjjp
} from '@/libs/axios.package'
import qs from 'qs'

/**
 * APP渠道市场
 */
export const applicationList = obj => {
  return getJjjp('/application/info', obj)
}

export const applicationaddOrEdit = obj => {
  return postJjjp('/ad/application/addOrEdit', obj)
}

export const dockinglist = obj => {
  return getJjjp('/ad-docking/list', obj)
}

export const adType = obj => {
  return getJjjp('/ad-docking/adType', obj)
}

export const scenelist = obj => {
  return getJjjp('/ad-scene/list', obj)
}

export const addOrEdit = obj => {
  return postJjjp('/ad-scene/addOrEdit', obj)
}

export const dockingaddOrEdit = obj => {
  return postJjjp('/ad-docking/addOrEdit', obj)
}

export const EXPORT_COUNTAPPUSER_EXPORTBYCODE = data => CONSTANT.publicPath + '/countappuser/exportByCode?' + qs.stringify(data)
