import CONSTANT from '@/config/constant.conf'
import { get, post } from '@/libs/axios.package'
import qs from 'qs'

// 编辑或新增商品分类接口
export const crowdProductCategoryEdit = obj => {
  return post('/crowd-product-category/edit', obj)
}
// 商品类型查询接口
export const crowdProductCategoryList = obj => {
  return get('/crowd-product-category/list', obj)
}
// 商品库管理-分页查询
export const crowdProductsStorePage = obj => {
  return get('/crowdProductsStore/page', obj)
}
// 商品库管理-添加
export const crowdProductsStoreAdd = obj => {
  return post('/crowdProductsStore/add', obj)
}
// 商品库管理-添加
export const crowdProductsStoreEdit = obj => {
  return post('/crowdProductsStore/update', obj)
}
export const seckill_goodsstorages_store = obj => {
  return get('/seckill/goodsstorages/store', obj)
}
export const goods_extension_goodspackage = obj => {
  return get('/goods/extension/goodspackage', obj)
}
// 商品分页查询
export const crowdProductsPageList = obj => {
  return get('/crowdProducts/pageList', obj)
}
// 商品类型下拉选
export const crowdProductCategorySelector = obj => {
  return get('/crowd-product-category/selector', obj)
}
// 商品更新
export const crowdProductsUpdate = obj => {
  return post('/crowdProducts/update', obj)
}
// 商品新增
export const crowdProductsBatchAdd = obj => {
  return post('/crowdProducts/batchAdd', obj)
}
// 商品关联承接查询
export const productCategoryList = obj => {
  return get('/crowd-product-channel-category/list', obj)
}
// 新增承接关联商品
export const productCategoryAdd = obj => {
  return post('/crowd-product-channel-category/add', obj)
}
// 编辑承接关联商品
export const productCategoryEdit = obj => {
  return post('/crowd-product-channel-category/edit', obj)
}
// 拼返现订单：分页查询
export const crowdGoodsOrderPage = obj => {
  return get('/crowdGoodsOrder/page', obj)
}
// 拼返现订单-列表 导出
export const crowdGoodsOrderExport = data => CONSTANT.publicPath + '/crowdGoodsOrder/export?' + qs.stringify(data)

// 拼返现订单：分页查询
export const countCashbackPage = obj => {
  return post('/count/cashback/page', obj)
}
// 会员补贴金额统计接口
export const countCrowdSubsidyList = obj => {
  return get('/count/crowd/subsidy/list', obj)
}
// 拼返现订单：处理退款
export const crowdGoodsOrderRefund = obj => {
  return get('/crowdGoodsOrder/handleRefund', obj)
}
// 拼返现订单：更新物流信息
export const crowdGoodsOrderUpdateTrackInfo = obj => {
  return get('/crowdGoodsOrder/updateTrackInfo', obj)
}
// 拼返现订单：分页查询导出
export const countCashbackExport = data => CONSTANT.publicPath + '/count/cashback/export?' + qs.stringify(data)

// 分页查询商品库存信息
export const crowdProductsStockList = obj => {
  return get('/crowdProducts/stock/list', obj)
}

// 更新商品库存信息
export const crowdProductsStockUpdate = obj => {
  return post('/crowdProducts/stock/update', obj)
}
// 承接页统计查询
export const cashbackPageV2 = obj => {
  return post('/count/cashback/page/v2', obj)
}
// 承接页统计导出
export const cashbackExportV2 = data => CONSTANT.publicPath + '/count/cashback/export/v2?' + qs.stringify(data)

// 商品关联承接详情
export const crowdProductChannelCategoryIssueList = params => {
  return get(`/crowd-product-channel-category/issue/list`, params)
}
// 商品关联承接详情导出
export const categoryIssueExport = data => CONSTANT.publicPath + '/crowd-product-channel-category/issue/export?' + qs.stringify(data)
// 分发页商品列表
export const pageDispositionList = obj => {
  return get('/crowd/distribute/goods/list', obj)
}

// 分发页商品新增、更新
export const pageDispositionEdit = obj => {
  return post('/crowd/distribute/goods/addOrUpdate', obj)
}

// 分发页商品删除
export const pageDispositionDel = params => {
  return get(`/crowd/distribute/goods/del`, params)
}

// 查询天天特卖管理配置
export const crowdTicketGet = params => {
  return get(`/crowdTicketConfig/get`, params)
}
// 修改天天特卖管理配置
export const crowdTicketUpdate = params => {
  return post(`/crowdTicketConfig/update`, params)
}
// 天天特卖统计查询
export const countCrowdSpecialPage = params => {
  return get(`/count/crowdSpecial/page`, params)
}
// 天天特卖统计导出
export const countCrowdSpecialPageExport = data =>
  CONSTANT.publicPath + '/count/crowdSpecial/page/export?' + qs.stringify(data)

// 天天特卖统计导出
export const orderGoodsorderCrowdTicketExport = data =>
  CONSTANT.publicPath + '/order/goodsorder/crowdTicket/export?' + qs.stringify(data)

// 万人拼-优惠券订单
export const orderGoodsorderCrowdTicket = params => {
  return get(`/order/goodsorder/crowdTicket`, params)
}
// 万人拼-优惠券订单
export const orderDorefund = params => {
  return get(`/order/dorefund`, params)
}
// 万人拼下单链路数据：分页查询
export const countLinkDataPage = obj => {
  return get('count/cashback/linkData', obj)
}
// 万人拼下单链路数据-列表 导出
export const linkDataExport = data => CONSTANT.publicPath + '/count/cashback/linkData/export?' + qs.stringify(data)

//  天天特卖承接统计查询
export const crowdSpecialContinueList = params => {
  return get(`/count/crowdSpecial/continue/page`, params)
}
// 天天特卖承接统计导出
export const crowdSpecialContinueExport = data => {
  return CONSTANT.publicPath + '/count/crowdSpecial/continue/page/export?' + qs.stringify(data)
}

//  万人拼商品综合留存
export const crowdRetainedList = params => {
  return get(`/crowd/goods/retained/list`, params)
}
// 万人拼商品综合留存导出
export const crowdRetainedExport = data => {
  return CONSTANT.publicPath + '/crowd/goods/retained/list/export?' + qs.stringify(data)
}
//  商品综合收益
export const goodsEarningsStatisticsList = params => {
  return get(`/goodsEarningsStatistics/page`, params)
}
// 商品综合收益导出
export const goodsEarningsStatisticsExport = data => {
  return CONSTANT.publicPath + '/goodsEarningsStatistics/page/export?' + qs.stringify(data)
}

// 添加邮费地址
export const crowedShippingAddressAdd = params => {
  return post(`/crowedShippingAddress/add`, params)
}

// 查询邮费地址
export const crowedShippingAddressGetData = params => {
  return get(`/crowedShippingAddress/getData`, params)
}

// 更新邮费地址
export const crowedShippingAddressUpdate = params => {
  return post(`/crowedShippingAddress/update`, params)
}

// 添加邮费地址
export const crowedShippingAddressDelete = params => {
  return post(`/crowedShippingAddress/delete`, params)
}

// 快递物流公司下拉选
export const crowdGoodsOrderTrackcompany = params => {
  return get(`/crowdGoodsOrder/trackcompany`, params)
}

// 快递物流公司下拉选
export const crowedAddressTemplateCheckDelete = params => {
  return get(`/crowedAddressTemplate/checkDelete`, params)
}

// 查询邮费地址
export const crowedAddressTemplateGetData = params => {
  return get(`/crowedAddressTemplate/getData`, params)
}

// 免费兑换统计
export const getFreeList = params => {
  return get(`/count/crowd/free/list`, params)
}
// 售后申请列表分页查询
export const getCrowdGoodsOrderRefund = params => {
  return get(`/crowdGoodsOrderRefund/page`, params)
}
// 处理售后
export const handleOrderRefund = params => {
  return post(`/crowdGoodsOrderRefund/handle`, params)
}
// 确认收货
export const confirmReceiptOrderRefund = params => {
  return post(`/crowdGoodsOrderRefund/confirmReceipt`, params)
}
// 关闭售后
export const closeSaleOrderRefund = params => {
  return post(`/crowdGoodsOrderRefund/closeSale`, params)
}
// 万人拼订单-确认发货
export const getCrowdGoodsUpdateToOutbound = params => {
  return get(`/crowdGoodsOrder/updateToOutbound`, params)
}
// 商家对账表分页查询
export const getReconciliationPage = params => {
  return get(`/crowdGoodsOrder/reconciliation/page`, params)
}
// 商家对账表分页导出
export const getReconciliationExport = data =>
  CONSTANT.publicPath + '/crowdGoodsOrder/reconciliation/export?' + qs.stringify(data)
// 供应商商家对账表分页查询
export const supplierReconciliationPage = params => {
  return get(`/crowdGoodsOrder/supplier/reconciliation/page`, params)
}
// 供应商商家对账表分页导出
export const supplierReconciliationExport = data =>
  CONSTANT.publicPath + '/crowdGoodsOrder/supplier/reconciliation/export?' + qs.stringify(data)
// 保存备注
export const saleAfterRemarkSave = obj => {
  return post('/saleAfterRemark/save', obj)
}
// 获取对应备注
export const saleAfterRemarkList = params => {
  return get(`/saleAfterRemark/getList`, params)
}

// 拼返现订单：发起售后申请按钮
export const crowdGoodsOrderCrowdRefund = obj => {
  return get('/crowdGoodsOrder/crowdRefund', obj)
}
// 新商品库分页导出
export const crowdProductsStoreExport = data =>
  CONSTANT.publicPath + '/crowdProductsStore/page/export?' + qs.stringify(data)

// 新商品库新增前校验
export const crowdProductsCheck = obj => {
  return post('/crowdProductsStore/checkUpdate', obj)
}

export const crowdProductsDetail = obj => {
  return get('/crowdProducts/detail', obj)
}

// 商品管理导出
export const crowdProductsPageListStoreExport = data =>
  CONSTANT.publicPath + `/crowdProducts/pageList/export?` + qs.stringify(data)

// 1688商品列表
export const getGoodsListFor1688 = obj => {
  return get('/1688/product/page', obj)
}

// 1688商品类目
export const getCategoryFor1688 = obj => {
  return get('/1688/category', obj)
}
// 商品打标列表查询
export const getCrowdLabelList = obj => {
  return get('/crowdLabel/list', obj)
}
// 商品打标新增
export const CrowdLabelAdd = obj => {
  return post('/crowdLabel/add', obj)
}

// 1688商品批量添加
export const crowdProductsStoreAddBatch = obj => {
  return post('/crowdProductsStore/addBatch', obj)
}

// 采购订单
export const crowdListBy1688 = obj => {
  return get('/crowd/1688/list', obj)
}

export const crowdFreeExport = data => CONSTANT.publicPath + '/count/crowd/free/export?' + qs.stringify(data)

// 售后订单通过和拒绝按钮处理
export const passAndRefuse = obj => {
  return post('/crowdGoodsOrderRefund/passAndRefuse', obj)
}

// 承接商品详情页编辑回显
export const crowdProductChannelCategoryEditEcho = obj => {
  return get('/crowd-product-channel-category/editEcho', obj)
}

// 单个商品禁用
export const crowdProductChannelCategoryForbidden = obj => {
  return get('/crowd-product-channel-category/forbidden', obj)
}

// 获取渠道列表
export const crowdProductChannelCategoryGetChannelList = obj => {
  return get('/crowd-product-channel-category/getChannelList', obj)
}

// 新增承接组商品
export const crowdProductChannelCategoryBatchAdd = obj => {
  return post('/crowd-product-channel-category/batch/add', obj)
}

// 编辑承接组商品
export const crowdProductChannelCategoryBatchEdit = obj => {
  return post('/crowd-product-channel-category/batch/edit', obj)
}

// 预退款接口
export const vipOrdersPreApply = obj => {
  return get('/vipOrders/pre/apply', obj)
}

// 上传凭证
export const vipOrdersUploadVoucher = obj => {
  return post('/crowdGoodsOrderRefund/upload/voucher', obj)
}
// 负价商品订单
export const crowdGoodsOrderMinus = obj => {
  return get('/crowdGoodsOrder/minus', obj)
}
// 商品转化效果监控表分页查询
export const countCrowdTransformation = obj => {
  return get('/countCrowdTransformation/dateList', obj)
}
// 商品转化效果监控表分页导出
export const countCrowdTransformationExport = data =>
  CONSTANT.publicPath + `/countCrowdTransformation/dateList/export?` + qs.stringify(data)
  // 手动投递
export const crowdGoodsApplyRetry = obj => {
  return post('/crowdGoodsOrderRefund/apply/retry', obj)
}
// 修改购物钱包配置
export const crowdWalletConfigUpdate = obj => {
  return post('/crowdWalletConfig/update', obj)
}
// 查询购物钱包配置
export const crowdWalletConfigGet = obj => {
  return get('/crowdWalletConfig/get', obj)
}
// 1688商品配置-新增和修改
export const product1688ConfigSaveOrUpdate = obj => {
  return post('/product/1688/config/saveOrUpdate', obj)
}
// 查询购物钱包配置
export const product1688ConfigList = obj => {
  return get('/product/1688/config/list', obj)
}
// 查询购物钱包配置
export const product1688ConfigDel = obj => {
  return post('/product/1688/config/del', obj)
}

// 标签池保存和更新
export const crowdLabelPoolConfigAddOrUpdate = obj => {
  return post('/crowdLabelPoolConfig/addOrUpdate', obj)
}

// 标签池 分页查询
export const crowdLabelPoolConfigList = obj => {
  return get('/crowdLabelPoolConfig/list', obj)
}

// 标签池 场景
export const crowdLabelPoolConfigListScene = obj => {
  return get('/crowdLabelPoolConfig/listScene', obj)
}
// 快速修改商品库状态接口
export const quick_productStatus = obj => {
  return post('/crowdProductsStore/quick/productStatus', obj)
}
// 商品分类详情
export const crowdProductCategoryDtail = obj => {
  return get('/crowd-product-category/detail', obj)
}
// 查询关键词配置接口
export const keywordConfigList = obj => {
  return get('/crowd1688-keyword-config/list', obj)
}
// 新增关键词配置接口
export const keywordConfigAdd = obj => {
  return post('/crowd1688-keyword-config/add', obj)
}
// 修改关键词配置接口
export const keywordConfigUpdate = obj => {
  return post('/crowd1688-keyword-config/update', obj)
}
// 关键词删除
export const keywordConfigDel = params => {
  return get(`/crowd1688-keyword-config/delete`, params)
}
// 查询关键词在哪个分类使用
export const keywordRelevanceCategory = params => {
  return get(`/crowd1688-keyword-config/relevanceCategory`, params)
}
