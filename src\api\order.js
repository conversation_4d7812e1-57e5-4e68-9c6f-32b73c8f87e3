import CONSTANT from '@/config/constant.conf'
import {
  get,
  post, put
} from '@/libs/axios.package'
import qs from 'qs'

/*
* 订单统计
*/
export const GET_ORDER_COUNT = obj => { return get('order/selectStatusCount/', obj) }

/*
* 订单列表
*/
export const GET_ORDER_LIST = obj => { return get('/order/listAll', obj) }

/*
* 订单详情
*/
export const GET_ORDER_EDIT = obj => { return get(`order/getOrderById/${obj.id}`) }

/*
* 订单处理
*/
export const PUT_ORDER = obj => { return post(`order/processOrder`, obj) }

/*
* 重新发货订单
*/
export const sendGoods = obj => { return post(`/order/sendGoodsById`, obj) }

/*
* 根据用户id查询订单
*/
export const GET_USER_ORDER = obj => { return get(`order/getAllOrdersByUserId`, obj) }
/*
* 根据用户id查询返利订单
*/
export const GET_USER_ORDER_Back = obj => { return get(`/rebateOrder/getListByUserId`, obj) }

/*
* 返利订单列表 （淘宝、京东返利订单）
* */
export const get_rebateOrder_list = obj => { return get(`/rebateOrder/list`, obj) }
/*
* 返利订单收益统计
* */
export const get_rebateList = obj => { return get(`/rebateOrder/rebateList`, obj) }
/*
* 返利订单收益统计
* */
export const export_rebateList = data => CONSTANT.publicPath + '/rebateOrder/exportRebateList?' + qs.stringify(data)
/*
* 淘口令订单收益统计
* */
export const export_TaoKouLinList = data => CONSTANT.publicPath + '/rebateOrder/exportTaoKouLinList?' + qs.stringify(data)

/*
* 淘口令返利订单
* */
export const get_taoKouLinList = obj => { return get(`/rebateOrder/taoKouLinList`, obj) }
/*
* 会员订单列表
* */
export const get_vipOrderList = obj => { return get(`/vipOrders`, obj) }

/*
* 会员订单状态数量查询
* */
export const get_vipOrderStatusNum = obj => { return get(`/vipOrders/status/number`, obj) }

/*
* 会员订单批量发货
* */
export const vipOrderSendBatch = obj => { return post(`/vipOrders/send/batch`, obj) }

/*
* 淘口令订单收益统计
* */
export const export_vipOrderList = data => CONSTANT.publicPath + '/vipOrders/export?' + qs.stringify(data)
/*
* 会员订单详情
* */
export const get_vipOrderDetail = obj => { return get(`/vipOrders/${obj}`, {}) }

/*
* 会员订单详情
* */
export const GET_REBATE_ORDER_DETAIL = obj => get(`/rebateOrder/detail`, obj)
/*
* 会员订单所在区域
* */
export const get_areaTree = obj => { return get(`/area/tree`, obj) }
/*
* 重新发货订单
*/
export const updateConsigneeInfo = obj => { return post(`/order/updateConsigneeInfo`, obj) }
/*
* 重新发货订单
*/
export const updateLogisticsInfo = obj => { return post(`/order/updateLogisticsInfo`, obj) }
/*
* 订单统计列表
*/
export const GET_ORDER_STATISTIC = obj => { return get(`/orderStatistic/allordercount`, obj) }
/*
* 饿了么订单列表导出
*/
export const EXPORT_ELE_ORDER_STATICS = data => CONSTANT.publicPath + '/rebateOrder/export?' + qs.stringify(data)
/*
* 订单统计列表导出
*/
export const EXPORT_ORDER_STATICS = data => CONSTANT.publicPath + '/orderStatistic/exportallordercount?' + qs.stringify(data)

/*
* 权益订单列表
*/
export const GET_ORDER_LIST_ALL = obj => { return get(`/order/listAll`, obj) }

export const GET_SYSTEM_PARAM_CODE = code => { return get(`/systemParam/selectByCode/${code}`, null) }

export const APPLY_VIP_ORDERS = id => { return put(`/vipOrders/${id}`, null) }

/** 加油订单 */

export const get_oil_order_detail_list = obj => get(`/oil/order/detail/list`, obj)

export const export_oil_order_detail_list = data => CONSTANT.publicPath + '/oil/order/detail/list/port?' + qs.stringify(data)

// 渠道订单统计
export const GET_ORDER_LIST_BY_CHANNEL = obj => {
  return get('/orderStatistic/allordercount/channel', obj)
}
// 渠道订单统计导出
export const EXPORT_GET_ORDER_LIST_BY_CHANNEL = data => CONSTANT.publicPath + '/orderStatistic/allordercount/channel/export?' + qs.stringify(data)

// 权益订单 - 蓝色兄弟补单
export const Post_Order_SendBlueBrother = obj => { return post(`/order/sendBlueBrother?id=${obj.id}&userPhone=${obj.userPhone}`, obj) }

// 美团本地统计
export const count_meituan_local_list = obj => { return get(`/count/meituan/local/list`, obj) }

// 是否退款二次确认
export const get_isReceive = obj => {
  return get('/prepaid/phone/coupon/isReceive', obj)
}
// 退款前置校验
export const refundApplyInfo = obj => {
  return get('/order/refund/admin/apply/info', obj)
}

// 退款前置校验
export const refundApplyInfoNew = obj => {
  return get('/vipOrder/VipOrderRefundQuery/getUseRightsList', obj)
}

/*
* 会员购卡订单列表
* */
export const get_user_vipOrders = obj => { return get(`/vipOrders/user/center/${obj.userId}`, obj) }
