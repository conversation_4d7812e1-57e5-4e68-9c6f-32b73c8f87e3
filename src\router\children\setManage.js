/*
* 平台设置子路由
* */

const setManage = [
  {
    path: '/setManage/withdrawal',
    name: 'setManage_withdrawal',
    meta: {
      title: '提现设置'
    },
    // component: () => import("@/views/setManage/page/withdrawal"),
    component: () => import('@/views/setManage/page/withdrawal')
  },
  {
    path: '/setManage/redEnvelopes',
    name: 'setManage_redEnvelopes',
    meta: {
      title: '红包设置'
    },
    // component: () => import("@/views/setManage/page/redEnvelopes"),
    component: () => import('@/views/setManage/page/redEnvelopes')
  },
  {
    path: '/setManage/goldenBeans',
    name: 'setManage_goldenBeans',
    meta: {
      title: '金豆设置'
    },
    // component: () => import("@/views/setManage/page/goldenBeans"),
    component: () => import('@/views/setManage/page/goldenBeans')
  },
  {
    path: '/setManage/rebate',
    name: 'setManage_rebate',
    meta: {
      title: '京东返利比例设置'
    },
    // component: () => import("@/views/setManage/page/rebate"),
    component: () => import('@/views/setManage/page/rebate')
  },
  {
    path: '/setManage/search',
    name: 'setManage_search',
    meta: {
      title: '搜索设置'
    },
    // component: () => import("@/views/setManage/page/search"),
    component: () => import('@/views/setManage/page/search')
  },
  {
    path: '/setManage/withdrawDepositDesc',
    name: 'setManage_withdrawDepositDesc',
    meta: {
      title: '极速提现说明'
    },
    component: () => import('@/views/setManage/page/withdrawDepositDesc')
  }
]

export default setManage
