<template>
  <div>
    <page :request="request" :list="list" table-title="商户号统计">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getmerchantcount, downOrUp, countexport } from '@/qjjpApi/statisticsOverview'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      curStatusParams: {
        id: 0,
        status: 0
      },
      list1: [
        {
          id: 1,
          name: '微信'
        },
        {
          id: 2,
          name: '支付宝'
        },
        {
          id: 0,
          name: 'iOS支付'
        }
      ],
      list2: [
        {
          id: 1,
          name: 'APP支付'
        },
        {
          id: 2,
          name: 'H5支付'
        },
        {
          id: 3,
          name: '周期扣款'
        }
      ],
      list3: [
        {
          id: 1,
          name: '站内支付'
        },
        {
          id: 2,
          name: '站内落地页支付'
        },
        {
          id: 3,
          name: '悬浮窗支付'
        }
      ],
      list4: [
        {
          id: 0,
          name: '启用'
        },
        {
          id: 1,
          name: '禁用'
        }
      ],
      siteIdsList: [],
      siteIds: [],
      listQuery: {
        startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await getmerchantcount(this.listQuery)
          await count_channel_application_list().then(res => {
            if (res.code === 200) {
              this.siteIdsList = res.data
            }
          })
          const { records, total } = list.data
          console.info(list, 'list')
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          key: 'dateSearch',
          title: '日期',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          pickerDay: 30,
          search: true,
          tableHidden: true,
          val: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        },
        {
          title: '商户平台',
          key: 'payType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list1,
          listFormat: {
            label: 'name',
            value: 'id'
          },

          reg: ['required'],
          search: true,
          tableHidden: true,
          clearable: true
        },
        {
          title: '商户类型',
          key: 'merchantType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },

          reg: ['required'],
          search: true,
          tableHidden: true,
          clearable: true
        },
        {
          title: '支付场景',
          key: 'scene',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          listFormat: {
            label: 'name',
            value: 'id'
          },

          reg: ['required'],
          clearable: true,
          tableHidden: true,
          search: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        // {
        //   title: '日期',
        //   key: 'eventDate'
        // },
        // {
        //   title: '应用名称',
        //   key: 'siteId',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.siteIdsList,
        //   listFormat: {
        //     label: 'name',
        //     value: 'siteId'
        //   },
        //   multiple: true,
        //   reg: ['required'],
        //   clearable: true,
        //   options: {
        //     on: () => {
        //       return {
        //         change: e => {
        //           const a = this.siteIdsList.filter(item => item.siteId == e)
        //           this.packageName = a[0].packageName
        //         }
        //       }
        //     }
        //   }
        // },
        {
          title: '商户平台',
          key: 'payTypeStr'
        },
        {
          title: '商户类型',
          key: 'merchantTypeStr'
        },
        {
          title: '支付场景',
          key: 'sceneStr'
        },
        {
          title: '商户名称',
          key: 'merchantNam'
        },

        {
          title: '商户id',
          key: 'merchant'
        },
        {
          title: '收款笔数',
          key: 'payCount'
        },
        {
          title: '收款金额',
          key: 'payAmount',
          renderHeader: (...args) => this.renderHeader(...args, ['收款金额，单位元'])
        },
        {
          title: '退款订单数（新/总）',
          key: 'refundCountAndNew',
          renderHeader: (...args) => this.renderHeader(...args, ['退款订单数，计算公式：总=当日所有的退款成功订单数；新=当日购卡当日退款成功的订单数'])
        },
        {
          title: '退款金额（新/总）',
          key: 'refundAmountAndNew',
          renderHeader: (...args) => this.renderHeader(...args, ['退款金额，计算公式：总=当日所有的退款成功金额;新=当日购卡当日退款成功金额'])
        },
        {
          title: '退款订单占比（新/总）',
          key: 'refundRationAndNew',
          renderHeader: (...args) => this.renderHeader(...args, ['退款订单占比，计算公式：退款订单数（新/总）/收款笔数'])
        },
        {
          title: '退款金额占比（新/总）',
          key: 'refundAmountRationAndNew',
          renderHeader: (...args) => this.renderHeader(...args, ['退款金额占比，计算公式：退款金额（新/总）/收款金额'])
        },
        {
          title: '投诉订单数（新/总）',
          key: 'complaintCountAndNew',
          renderHeader: (...args) => this.renderHeader(...args, ['投诉订单数，计算公式：总=当日所有的投诉订单数;新=当日购卡当日投诉订单数'])
        },
        {
          title: '投诉率（新/总）',
          key: 'complaintRation',
          renderHeader: (...args) => this.renderHeader(...args, ['投诉率，计算公式：投诉订单数（新/总）/收款笔数'])
        }
      ]
    }
  },
  created() { },
  methods: {
    renderHeader(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h(
            'div',
            {
              slot: 'content'
            },
            [textArr.map(item => h('div', null, item))]
          ),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    },

    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = countexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    changeStatus() {
      const params = this.curStatusParams
      downOrUp({ ...params }).then(res => {
        if (res.code == 200) {
          this.$message.success('更改状态成功')
          this.$store.dispatch('tableRefresh', this)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
