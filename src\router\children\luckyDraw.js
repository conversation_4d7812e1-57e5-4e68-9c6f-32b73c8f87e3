const luckyDraw = [
  {
    path: '/luckyDraw/paymentRecords',
    name: 'paymentRecords',
    meta: {
      title: '支付记录'
    },
    component: () => import('@/views/luckyDraw/page/paymentRecords')
  },
  {
    path: '/luckyDraw/refundLog',
    name: 'refundLog',
    meta: {
      title: '退款记录'
    },
    component: () => import('@/views/luckyDraw/page/refundLog')
  },
  {
    path: '/luckyDraw/activityData',
    name: 'activityData',
    meta: {
      title: '活动数据'
    },
    component: () => import('@/views/luckyDraw/page/activityData')
  },
  {
    path: '/luckyDraw/interceptData',
    name: 'interceptData',
    meta: {
      title: '拦截数据'
    },
    component: () => import('@/views/luckyDraw/page/interceptData')
  },
  {
    path: '/luckyDraw/goodsBuriedpoint',
    name: 'goodsBuriedpoint',
    meta: {
      title: '商品埋点'
    },
    component: () => import('@/views/luckyDraw/page/goodsBuriedpoint')
  },
  {
    path: '/luckyDraw/payBuriedpoint',
    name: 'payBuriedpoint',
    meta: {
      title: '付费埋点'
    },
    component: () => import('@/views/luckyDraw/page/payBuriedpoint')
  },
  {
    path: '/luckyDraw/receivingInformation',
    name: 'receivingInformation',
    meta: {
      title: '收货信息'
    },
    component: () => import('@/views/luckyDraw/page/receivingInformation')
  }
]
export default luckyDraw
