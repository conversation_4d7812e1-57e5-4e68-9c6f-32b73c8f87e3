import { get, post } from '@/libs/axios.package'

export const GET_PRESENT_QUERY_LIST = obj => {
  return get('/activity/present/queryList', obj)
}

export const GET_PRESENT_FERRULE_LIST = obj => {
  return get('/activity/present/ferrule/queryList', obj)
}

export const GET_PRESENT_CYCLE_LIST = obj => {
  return get('/activity/present/cycle/relation/queryList', obj)
}

export const EDIT_PRESENT = obj => {
  return post('/activity/present', obj)
}

export const EDIT_PRESENT_FERRULE = obj => {
  return post('/activity/present/ferrule', obj)
}

export const EDIT_CYCLE_RELATION = obj => {
  return post('/activity/present/cycle/relation', obj)
}
