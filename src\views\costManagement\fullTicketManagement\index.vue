<template>
  <div>
    <page :request="request" :list="list" table-title="满减券列表">
      <div slot="searchContainer" style="display: inline-block">
        <el-button
          type="primary"
          size="small"
          plain
          icon="el-icon-plus"
          @click="showDialogVisible(1)"
        >
          新增
        </el-button>
      </div>
    </page>

    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="满减券名称:" prop="name">
          <el-input v-model="form.name" :disabled="!!form.id" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="套餐金额:" prop="planAmount">
          <el-input v-model="form.planAmount" :disabled="!!form.id" placeholder="请输入" type="number" />
        </el-form-item>
        <el-form-item label="优惠金额:" prop="discountAmount">
          <el-input v-model="form.discountAmount" :disabled="!!form.id" placeholder="请输入" type="number" />
        </el-form-item>
        <el-form-item label="使用限制:" prop="useLimit">
          <el-select v-model="form.useLimit" placeholder="请选择使用限制">
            <el-option
              v-for="(item, index) in category_list"
              :key="index"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数量:" prop="number">
          <el-input v-model="form.number" placeholder="请输入" type="number" />
        </el-form-item>
        <el-form-item v-if="form.placement!==3" label="渠道关联" prop="channelMethod">
          <el-radio-group v-model="form.channelMethod">
            <el-radio label="0">全渠道</el-radio>
            <el-radio label="1">投放渠道</el-radio>
            <el-radio label="2">无</el-radio>
          </el-radio-group>
          <el-button
            v-if="form.channelMethod == 1"
            style="margin-left: 8px"
            type="primary"
            size="mini"
            @click="showDialog(), queryList()"
          >选择渠道</el-button>
          <div v-if="form.channelMethod == 1" ref="codeList">
            <span
              v-for="(item, index) in tempChannelList"
              :key="index"
              style="margin-right: 20px; display: inline-block; min-width: 70px"
            >
              {{ item }}

              <i class="el-icon-close" @click="deleteList(3, index)" />
            </span>
          </div>
        </el-form-item>
        <el-form-item v-if="form.placement!==3" label="投放商品类型关联:" prop="number">
          <el-cascader
            v-model="form.channelCategoryIds"
            :options="categoryRanges"
            :props="{value: 'id', label: 'channelUndertakeTypeName', children: 'channelCategories', emitPath: false,multiple :true,'show-all-levels':false}"
          />
        </el-form-item>

        <el-dialog
          v-if="dialogChannelVisible"

          :append-to-body="true"
          :before-close="(done) => ((inputVisible = false), done())"
          :visible.sync="dialogChannelVisible"
          title="添加渠道"
          width="800px"
        >
          <el-tabs v-model="activeName2">
            <el-tab-pane label="渠道选择" name="tab1">
              <h1>搜索</h1>

              <el-input
                v-model="queryPra.keyword"
                style="width: 300px"
                placeholder="关键词"
                size="small"
              />&nbsp;

              <el-button
                type="primary"
                @click=";(queryPra.pageNumber = 1), queryList()"
              >搜索</el-button>

              <el-table
                ref="multipleTable"
                :data="list3"
                tooltip-effect="dark"
                row-key="channelCode"
                style="width: 100%"
                @select="selectionChange"
                @select-all="selcetionAllChange"
              >
                <el-table-column type="selection" width="55" />

                <el-table-column
                  prop="channelCode"
                  label="渠道"
                  width="120"
                  show-overflow-tooltip
                />

                <el-table-column
                  prop="channelName"
                  label="渠道名称"
                  show-overflow-tooltip
                />
              </el-table>

              <br>

              <el-pagination
                style="text-align: right"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="queryPra.total"
                @size-change="handleSizeChange"
                @current-change="paginChange"
              />
            </el-tab-pane>

            <el-tab-pane label="分组选择" name="tab2">
              <el-tag
                v-for="item in dynamicTags2"
                :key="item.id"
                closable
                :disable-transitions="false"
                @close="handleClose2(item)"
                @click="tagClick2(item)"
              >
                {{ item.groupName }}

                <!-- <a
              @click.stop="handleAddTag2(item)"
              :style="{color: idslist2.filter(dd => dd.id == item.id).length > 0 ? '#666' : 'red'}"
              >{{ idslist2.filter(dd => dd.id == item.id).length > 0 ? '取消' : '选择' }}</a
            > -->

                <a
                  :style="{ color: item.isActivted ? '#666' : 'red' }"
                  @click.stop="handleAddTag2(item)"
                >{{ item.isActivted ? '取消' : '选择' }}</a>
              </el-tag>

              <el-button
                v-if="!inputVisible"
                class="button-new-tag"
                size="small"
                @click="showInput2"
              >+ 新增</el-button>

              <div v-if="inputVisible">
                <el-input
                  ref="saveTagInput"
                  v-model="addArea2.groupName"
                  style="width: 300px"
                  class="input-new-tag"
                  placeholder="请输入分组名称"
                  size="small"
                />

                <h1>搜索</h1>

                <el-input
                  v-model="queryPra2.keyword"
                  style="width: 300px"
                  placeholder="关键词"
                  size="small"
                />&nbsp;

                <el-button
                  type="primary"
                  @click=";(queryPra2.pageNumber = 1), queryList2()"
                >搜索</el-button>

                <el-table
                  ref="multipleTable2"
                  :data="list2"
                  tooltip-effect="dark"
                  row-key="channelCode"
                  style="width: 100%"
                  @select="selectionChange2"
                  @select-all="selcetionAllChange2"
                >
                  <el-table-column type="selection" width="55" />

                  <el-table-column
                    prop="channelCode"
                    label="渠道"
                    width="120"
                    show-overflow-tooltip
                  />

                  <el-table-column
                    prop="channelName"
                    label="渠道名称"
                    show-overflow-tooltip
                  />
                </el-table>

                <br>

                <el-pagination
                  style="text-align: right"
                  background
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="queryPra2.total"
                  @size-change="handleSizeChange2"
                  @current-change="paginChange2"
                />

                <el-button
                  @click=";(inputVisible = false), (addArea2 = {})"
                >取 消</el-button>

                <el-button
                  type="primary"
                  @click="handleInputConfirm2"
                >确 定</el-button>
              </div>
            </el-tab-pane>
          </el-tabs>

          <div v-if="!inputVisible" slot="footer" class="dialog-footer">
            <el-button @click="closeDialog(3)">取 消</el-button>
            <el-button type="primary" @click="saveChannel">确 定</el-button>
          </div>
        </el-dialog>
        <el-form-item label="关联位置:" prop="placement">
          <el-select
            v-model="form.placement"
            :disabled="!!form.id"
            placeholder="请选择关联位置"
            @change="placementChange"
          >
            <el-option
              label="话费活动"
              :value="1"
            />
            <el-option
              label="会员退款"
              :value="2"
            />
            <el-option
              label="会员续费"
              :value="3"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.placement === 3" label="关联套餐:" prop="renewPackageType">
          <el-select v-model="form.renewPackageType" placeholder="请选择关联套餐">
            <el-option
              label="1个月"
              :value="1"
            />
            <el-option
              label="3个月"
              :value="2"
            />
            <el-option
              label="12个月"
              :value="3"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.placement === 2" label-width="130px" label="关联购卡金额:" prop="memberAmount">
          <el-input v-model="form.memberAmount" type="number" />
        </el-form-item>
        <el-form-item label="状态:" prop="couponStatus">
          <el-radio-group v-model="form.couponStatus">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleColse()">取 消 </el-button>
        <el-button v-if="!form.id" type="primary" @click="submit">确 定</el-button>
        <el-button v-else type="primary" @click="submit">修改</el-button>
      </span>

    </el-dialog>

  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import {
  GET_CHANNEL_LIST_ALL,
  channelAdGroupType,
  channelAdGroupCodes,
  channelAdGroupDelete, channelcategoryGroupSelector
} from '@/api/NewChannel.js'
import {
  prepaidPhoneCoupon_page,
  prepaidPhoneCoupon_update,
  prepaidPhoneCoupon_add,
  prepaidPhoneCoupon_delete
} from '@/api/costManagement'
import moment from 'moment'

export default {
  components: {
    page
  },
  data() {
    return {
      categoryRanges: [],
      dialogFormVisible: true,
      dialogOps: {
        title: '关联券'
      },
      formCopy: {},
      form: {
        name: '',
        planAmount: '',
        useLimit: '',
        discountAmount: '',
        number: '',
        couponStatus: '',
        channelMethod: '',
        channels: [],
        id: '',
        flag: false,
        memberAmount: '',
        placement: '',
        renewPackageType: ''
      },
      category_list: [
        {
          title: '普通用户',
          value: 0,
          id: 0
        },
        {
          title: '会员用户',
          value: 1,
          id: 1
        }
      ],
      searchForm: {},
      dialogVisible: false,
      listQuery: {
        useLimit: 0,
        couponStatus: 1
      },
      request: {
        getListUrl: (data) => {
          this.listQuery = { ...this.listQuery, ...data }
          return prepaidPhoneCoupon_page(this.listQuery)
        }
        // insertHttp: (data) => {
        //   return prepaidPhoneCoupon_add(data)
        // },
        // updateHttp: (data) => {
        //   return prepaidPhoneCoupon_update(data)
        // }
      },
      // 渠道相关
      dialogChannelVisible: false,
      queryPra: { pageNumber: 1, pageSize: 10, total: 0 },
      list3: [],
      flag: false,
      tempChannelListCopy: [],
      activeName2: 'tab1',
      queryPra2: { pageNumber: 1, pageSize: 10, total: 0 },
      inputVisible: false,
      dynamicTags2: [],
      onlySetVal2: [], // 每次选择的数据渠道
      list2: [],
      addArea2: { groupName: '', codeGroupIds: [] },
      tempChannelList: [],
      idslist2: [], // 每次选择id集合渠道
      rules: {
        name: [
          { required: true, message: '此项为必填', trigger: 'blur' }
        ],
        planAmount: [
          { required: true, message: '此项为必填', trigger: 'blur' }
        ],
        // api
        discountAmount: [
          { required: true, message: '此项为必填', trigger: 'blur' }
        ],
        useLimit: [
          { required: true, message: '此项为必填', trigger: 'blur' }
        ],
        number: [
          { required: true, message: '此项为必填', trigger: 'blur' }
        ],
        channelMethod: [
          { required: true, message: '此项为必填', trigger: 'blur' }
        ],
        couponStatus: [
          { required: true, message: '此项为必填', trigger: 'blur' }
        ],
        placement: [
          { required: true, message: '此项为必填', trigger: 'blur' }
        ],
        memberAmount: [
          { required: true, message: '此项为必填', trigger: 'blur' }
        ],
        renewPackageType: [
          { required: true, message: '此项为必填', trigger: 'change' }
        ]

      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '券ID',
          key: 'id',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '满减券名称',
          key: 'name',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '套餐金额',
          key: 'planAmount',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '优惠金额',
          key: 'discountAmount',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '使用限制',
          key: 'useLimit',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          list: [
            {
              label: '普通用户',
              value: 0
            },
            {
              label: '会员用户',
              value: 1
            }
          ],
          search: true,
          val: 0
        },
        {
          title: '数量',
          key: 'number',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '关联位置',
          key: 'placement',
          list: [
            { value: 1, label: '话费活动' },
            { value: 2, label: '会员退款' },
            { value: 3, label: '会员续费' }
          ],
          tableView: tableItemType.tableView.text,
          type: formItemType.select,
          search: true
        },
        {
          title: '关联购卡金额',
          key: 'memberAmount'
        },
        {
          key: 'createTime',
          title: '创建日期',
          type: formItemType.datePickerDaterangeGai,

          render: (h, params) => {
            const data = params.data.row
            if (data.createTime) {
              return h('span', {}, moment(data.createTime).format('YYYY-MM-DD'))
            } else {
              return h('span', {}, '--')
            }
          },
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          // search: true,
          val: [moment().subtract(6, 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        },
        // {
        //   key: 'createTime',
        //   title: '创建日期',
        //   type: formItemType.datePickerDaterangeGai,
        //   options: {
        //     format: 'YYYY-MM-DD',
        //     valueFormat: 'yyyy-MM-dd'
        //   },
        //   childKey: ['startDate', 'endDate'],
        //   search: true,
        //   tableHidden: true,
        //   val: [moment().subtract(6, 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        // },
        {
          title: '关联渠道',
          key: 'channelMethod',
          render: (h, params) => {
            const data = params.data.row
            if (data.channelMethod == 1) {
              return h('span', null, data.channels)
            } else if (data.channelMethod == 0) {
              return h('span', null, '全渠道')
            } else {
              return h('span', null, '--')
            }
          }
        },
        {
          title: '投放商品类型关联',
          key: 'categoryNames',
          render: (h, params) => {
            return h('span', null, params.data.row.categoryNames?.join('，') || '-')
          }
        },
        {
          title: '状态',
          key: 'couponStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          list: [
            {
              label: '启用',
              value: 1
            },
            {
              label: '禁用',
              value: 0
            }
          ],
          search: true,
          val: 1
        },
        {
          type: tableItemType.active,
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              // type: tableItemType.activeType.detailsDialog,
              theme: 'warning',
              click: ($index, item, params) => {
                params.couponStatus = params.couponStatus.toString()
                params.channelMethod = params.channelMethod.toString()

                this.form = JSON.parse(JSON.stringify(params))
                this.tempChannelList = params.channelMethod === '1' ? this.form.channels.split(',') : []
                this.form.channels = params.channelMethod === '1' ? this.form.channels.split(',') : []
                this.form.channelCategoryIds = params.channelCategoryIds?.split(',').map(id => id * 1)
                this.flag = true
                this.showDialogVisible(2)
              }
            },
            {
              text: '删除',
              key: 'edit',
              theme: 'danger',
              click: ($index, item, params) => {
                this.$confirm('是否删除该商品, 是否继续?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                })
                  .then(async() => {
                    this.submitLoading = true
                    const res = await prepaidPhoneCoupon_delete({ id: params.id })
                    if (res.status == 200) {
                      this.$store.dispatch('tableRefresh', this)
                      this.$message({
                        type: 'success',
                        message: '删除成功'
                      })
                      this.dialogVisible = false
                    } else {
                      this.$message({
                        type: 'warn',
                        message: res.msg
                      })
                    }
                    this.submitLoading = false
                  })
                  .catch(() => {
                    this.$message({
                      type: 'info',
                      message: '已取消删除'
                    })
                  })
              }
            }
          ]
        }
      ]
    }
  },
  mounted() {
    this.getCategorySelector()
  },
  methods: {
    placementChange() {
      this.$set(this.form, 'renewPackageType', '')
    },
    getCategorySelector() {
      channelcategoryGroupSelector({ pageNumber: 1, pageSize: 1000 }).then(
        (res) => {
          // if (res.code === 0) {
          //   this.categoryRanges = res.data
          // }
          if (res.code === 0) {
            this.categoryRanges = res.data.map((item, index) => {
              return {
                ...item,
                id: '' + item.id + index + new Date().getTime(),
                channelCategories: item.channelCategories.map(s => {
                  return {
                    ...s,
                    channelUndertakeTypeName: s.channelCategoryName
                  }
                })
              }
            })
          }
        }
      )
    },
    success() {
      this.$store.dispatch('tableRefresh', this)
      this.dialogFormVisible = false
    },
    close() {
      this.dialogFormVisible = false
    },
    showDialogVisible(val) {
      if (val == 1) {
        this.form = this.formCopy
        this.tempChannelList = this.tempChannelListCopy
      }
      this.dialogVisible = true
    },
    showDialog() {
      this.dialogChannelVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    dialogVisibleColse() {
      if (!this.form.id) {
        this.formCopy = this.form
        this.tempChannelListCopy = this.tempChannelList
      } else {
        this.tempChannelList = []
      }
      this.dialogVisible = false
      // this.flag = false
    },
    submit() {
      let flag = true
      this.$refs['form'].validate(valid => {
        // 验证类型
        if (!valid) {
          flag = false
        }
      })
      if (flag) {
        this.form.channelMethod = Number(this.form.channelMethod)
        this.form.useLimit = Number(this.form.useLimit)
        this.form.number = Number(this.form.number)
        this.form.channelMethod === 0 ? this.form.channels = [] : 1
        if (Array.isArray(this.form.channelCategoryIds)) {
          this.form.channelCategoryIds = this.form.channelCategoryIds.join(',')
        }
        if (this.form.id) { // 修改
          prepaidPhoneCoupon_update(this.form).then(res => {
            if (res.status === 200) {
              this.$message({
                message: '修改成功',
                type: 'success'
              })
              this.$store.dispatch('tableRefresh', this)
              for (const key in this.form) {
                this.form[key] = ''
              }
              this.form.channels = []
              this.dialogVisible = false
            }
          })
        } else {
          prepaidPhoneCoupon_add(this.form).then(res => { // 新增
            if (res.status === 200) {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.$store.dispatch('tableRefresh', this)
              for (const key in this.form) {
                this.form[key] = ''
              }
              this.form.channels = []
              this.dialogVisible = false
            }
          })
        }
      }
    },
    queryList() {
      GET_CHANNEL_LIST_ALL(this.queryPra).then((res) => {
        this.list3 = res.data.rows

        this.queryPra.total = res.data.total
      })
    },
    selectionChange(e, row) {
      if (this.onlySetVal2.includes(row.channelCode)) {
        this.onlySetVal2.splice(this.onlySetVal2.indexOf(row.channelCode, 1))
      } else {
        this.onlySetVal2.push(row.channelCode)
      }
    },
    selectionChange2(e, row) {
      if (this.addArea2.codeGroupIds.includes(row.channelCode)) {
        this.addArea2.codeGroupIds.splice(
          this.addArea2.codeGroupIds.indexOf(row.channelCode),
          1
        )
      } else {
        this.addArea2.codeGroupIds.push(row.channelCode)
      }
      console.log('this.addArea2.codeGroupIds', this.addArea2.codeGroupIds)
    },
    selcetionAllChange(rows) {
      rows.forEach((row) => {
        if (this.onlySetVal2.includes(row.channelCode)) {
          this.onlySetVal2.splice(this.onlySetVal2.indexOf(row.channelCode, 1))
        } else {
          this.onlySetVal2.push(row.channelCode)
        }
      })
    },
    selcetionAllChange2(rows) {
      rows.forEach((row) => {
        if (this.addArea2.codeGroupIds.includes(row.channelCode)) {
          this.addArea2.codeGroupIds.splice(
            this.addArea2.codeGroupIds.indexOf(row.channelCode, 1)
          )
        } else {
          this.addArea2.codeGroupIds.push(row.channelCode)
        }
      })
    },
    handleSizeChange(e) {
      this.queryPra.pageSize = e

      this.queryList()
    },

    paginChange(e) {
      this.queryPra.pageNumber = e

      this.queryList()
    },
    handleClose2(tag) {
      this.$confirm('此操作将永久删除该分组, 是否继续?', '提示', {
        confirmButtonText: '确定',

        cancelButtonText: '取消',

        type: 'warning'
      })

        .then(() => {
          channelAdGroupDelete({ id: tag.id }).then((res) => {
            if (res.status == 200) {
              this.dynamicTags2 = this.dynamicTags2.filter(
                (item) => item.id != tag.id
              )

              this.$message({
                type: 'success',

                message: '删除成功!'
              })
            } else {
              this.$message({
                type: 'error',

                message: res.msg
              })
            }
          })
        })

        .catch(() => {
          this.$message({
            type: 'info',

            message: '已取消删除'
          })
        })
    },

    tagClick2(item) {
      channelAdGroupCode({ id: item.id }).then((res) => {
        this.inputVisible = true

        this.addArea2 = {
          groupName: item.groupName,

          id: item.id,

          codeGroupIds: res.data
        }

        this.queryList2()
      })
    },
    handleAddTag2(item) {
      // 需求更改=> 需要添加分组的区域直接到this.tempChannelList 数组中，格式是[]

      // 后端将区域数据直接放在分组对象里面，这里直接concat item.key 然后回显到表格里面（再次打开的时候）

      this.$set(item, 'isActivted', !item.isActivted)
      if (item.isActivted) {
        if (this.idslist2.filter((dd) => dd.id == item.id).length > 0) {
          this.idslist2 = this.idslist2.filter((dd) => dd.id != item.id)
        } else {
          this.idslist2.push(item)
        }
        this.idslist2.forEach((dts) => {
          this.onlySetVal2.push(...dts.codeList)
        })
      } else {
        item.codeList.forEach((item) => {
          const index = this.onlySetVal2.findIndex((o) => o == item)
          this.onlySetVal2.splice(index, 1)
        })
      }
    },
    showInput2() {
      this.inputVisible = true

      this.queryPra2.pageNumber = 1

      this.addArea2 = {
        groupName: '',

        id: '',

        codeGroupIds: []
      }

      this.queryList2()
    },
    queryList2() {
      GET_CHANNEL_LIST_ALL(this.queryPra2).then((res) => {
        this.list2 = res.data.rows

        this.queryPra2.total = res.data.total

        if (
          this.addArea2.codeGroupIds &&
          this.addArea2.codeGroupIds.length > 0
        ) {
          this.$nextTick((_) => {
            this.addArea2.codeGroupIds.forEach((item) => {
              this.list2

                .filter((dt) => item == dt.channelCode)

                .forEach((vals) => {
                  this.$refs.multipleTable2.toggleRowSelection(vals, true)
                })
            })
          })
        }
      })
    },
    handleInputConfirm2() {
      const inputValue = this.addArea2.groupName

      if (inputValue) {
        channelAdGroupCodes({
          ...this.addArea2,
          groupName: inputValue,
          codes: this.addArea2.codeGroupIds
        }).then((res) => {
          if (res.status == 200) {
            this.$message.success(res.msg)

            this.getDYtag2()

            this.inputVisible = false

            this.addArea2.groupName = ''

            this.addArea2.codeGroupIds = []
          }
        })
      }
    },
    handleSizeChange2(e) {
      this.queryPra2.pageSize = e

      this.queryList2()
    },

    paginChange2(e) {
      this.queryPra2.pageNumber = e

      this.queryList2()
    },
    closeDialog() {
      this.dialogChannelVisible = false

      this.onlySetVal2 = []

      this.idslist2 = []
      this.queryPra = { pageNumber: 1, pageSize: 10, total: 0 }

      this.queryPra2 = { pageNumber: 1, pageSize: 10, total: 0 }

      this.inputVisible = false
    },
    saveChannel() {
      this.tempChannelList = Array.from(
        new Set(this.tempChannelList.concat(this.onlySetVal2))
      )
      this.form.channels = this.tempChannelList
      this.closeDialog()
    },
    getDYtag2() {
      channelAdGroupType({ type: 1 }).then((res) => {
        this.dynamicTags2 = res.data
      })
    },
    deleteList(i, j) {
      this.tempChannelList.splice(j, 1)
      this.form.channels = this.tempChannelList
    }
    // 渠道相关结束
  }
}
</script>

<style scoped lang="scss">
  ::deep .el-form-item__content{
    margin-left:0px !important;
  }
    //处理input type = number的上下箭头::v-deep input::-webkit-outer-spin-button,
  ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  ::v-deep input[type='number'] {
    -moz-appearance: textfield !important;
  }
</style>
