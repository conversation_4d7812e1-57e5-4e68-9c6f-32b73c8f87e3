/*
 * @Author: 陈小豆
 * @Date: 2024-04-25 15:18:09
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-11-13 21:48:56
 */
import CONSTANT from '@/config/constant.conf'
import { getJjjp, postJjjp, putJjjp } from '@/libs/axios.package'
import qs from 'qs'

// 消息风格列表
export const messageStyleList = obj => {
  return getJjjp(`/cms/message-style/list`, obj)
}
// 编辑或新增商品分类接口
export const crowdProductCategoryEdit = obj => {
  return postJjjp('/crowd-product-category/edit', obj)
}

export const get_vipCard_list = obj => {
  return getJjjp('/vipCard/get_page_list', obj)
}

export const messageStyleListAdd = obj => {
  return postJjjp('/cms/message-style/add', obj)
}

export const undertakeList = obj => {
  return getJjjp('/cms/channel/undertake/list', obj)
}

export const addOrUpdate = obj => {
  return postJjjp('/cms/channel/undertake/addOrUpdate', obj)
}

export const undertakeListexport = data => CONSTANT.qjjpPath + '/cms/channel/undertake/list/export?' + qs.stringify(data)

export const messageStyleListexport = data => CONSTANT.qjjpPath + '/cms/message-style/list/export?' + qs.stringify(data)
// 查询自定义话术详情
export const replyBankDetail = obj => {
  return getJjjp('/cms/message-reply-bank/detail', obj)
}
// 查询自定义话术列表
export const replyBankList = obj => {
  return getJjjp('/cms/message-reply-bank/list', obj)
}

// 编辑自定义话术
export const replyBankEdit = obj => {
  return postJjjp('/cms/message-reply-bank/edit', obj)
}
// 获取自定义话术编辑风格
export const bankMessageStyle = obj => {
  return getJjjp('/cms/message-reply-bank/messageStyle', obj)
}
// 添加默认文案
export const defaultQuestionPost = obj => {
  return postJjjp('/cms/message-reply-bank/default/question', obj)
}
// 获取默认文案
export const defaultQuestionGet = obj => {
  return getJjjp('/cms/message-reply-bank/default/question', obj)
}
// 模型仓库分页查询
export const modelRepositorypage = obj => {
  return getJjjp('/cms/modelRepository/page', obj)
}
export const modelRepositoryplatform = obj => {
  return getJjjp('/cms/modelRepository/platform', obj)
}

export const modelRepositoryexit = obj => {
  return postJjjp('/cms/modelRepository/exit', obj)
}

export const modelRepositoryselect = id => {
  return getJjjp(`/cms/modelRepository/select/${id}`)
}

export const categoryPage = (obj) => {
  return getJjjp(`/cms/message-style-category/page`, obj)
}
export const categoryList = (obj) => {
  return getJjjp(`/cms/message-style-category/list`, obj)
}
export const categoryadd = (obj) => {
  return postJjjp(`/cms/message-style-category/add`, obj)
}
export const voiceChangerPage = (obj) => {
  return getJjjp(`/cms/chat/voiceChanger/page`, obj)
}
export const voiceChangerEdit = (obj) => {
  return putJjjp(`/cms/chat/voiceChanger`, obj)
}
export const voiceChangerAudition = (obj) => {
  return postJjjp(`/cms/chat/voiceChanger/audition`, obj)
}

export const knowImageConfigPage = (obj) => {
  return getJjjp(`/cms/knowImageConfig/page`, obj)
}
// 获取智能体id
export const knowImageConfigGetBots = (obj) => {
  return getJjjp(`/cms/knowImageConfig/getBots`, obj)
}
// 获取所属分类下拉
export const getKnowCategoryTypeList = (obj) => {
  return getJjjp(`/cms/knowImageConfig/getKnowCategoryTypeList`, obj)
}
// 新增
export const knowImageConfigCreate = (obj) => {
  return postJjjp(`/cms/knowImageConfig/create`, obj)
}

// 更新
export const knowImageConfigUpdate = (obj) => {
  return postJjjp(`/cms/knowImageConfig/update`, obj)
}
// 获取实例图片配置
export const knowImageConfigExampleImageConfig = (obj) => {
  return getJjjp(`/cms/knowImageConfig/example/image/config`, obj)
}
// 更新实例图片
export const knowImageConfigUpdateExampleImage = (obj) => {
  return postJjjp(`/cms/knowImageConfig/update/example/image`, obj)
}

// 情感教师列表
export const emotionalMentorPage = (obj) => {
  return getJjjp(`/cms/emotionalMentor/page`, obj)
}
// 情感教师列表更新
export const emotionalMentorUpdate = (obj) => {
  return postJjjp(`/cms/emotionalMentor/update`, obj)
}
// 情感教师列表新增
export const emotionalMentorCreate = (obj) => {
  return postJjjp(`/cms/emotionalMentor/create`, obj)
}
// 检查当前的其他二维码状态是否是启用
export const emotionalMentorCheckStatus = (obj) => {
  return postJjjp(`/cms/emotionalMentor/checkStatus`, obj)
}

// 获取权益列表
export const thirdGoodsPage = (obj) => {
  return getJjjp(`/cms/thirdGoods/page`, obj)
}

// 同步蓝色兄弟商品库
export const syncThirdGoods = (obj) => {
  return getJjjp(`/cms/thirdGoods/syncThirdGoods`, obj)
}

// 新增 话费套餐
export const thirdGoodsCreate = (obj) => {
  return postJjjp(`/cms/thirdGoods/create`, obj)
}

// 更新 话费套餐
export const thirdGoodsUpdate = (obj) => {
  return postJjjp(`/cms/thirdGoods/update`, obj)
}

// 获取供应商
export const supplierList = (obj) => {
  return postJjjp(`/cms/equityActivities/supplierList`, obj)
}


export const equityActivitiesCreate = (obj) => {
  return postJjjp(`/cms/equityActivities/create`, obj)
}

export const equityActivitiesPage = (obj) => {
  return getJjjp(`/cms/equityActivities/page`, obj)
}

export const equityActivitiesDetail = (id) => {
  return getJjjp(`/cms/equityActivities/detail/${id}`)
}

export const equityActivitiesUpdate = (obj) => {
  return postJjjp(`/cms/equityActivities/update`, obj)
}