<template>
  <page :request="request" :list="list" table-title="商品分享统计" :table-pagination-state="false">
    <div slot="titleContainer" class="title-container-box">
      <el-button plain type="warning" size="small" icon="el-icon-upload" @click="handUpload">导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import {
  get_count_goods_share_page,
  get_count_goods_share_page_exports
} from '@/api/buryData'
import { formItemType } from '@/config/sysConfig'
import moment from 'moment'

const date = {
  startTime: moment().subtract(6, 'days').format('YYYY-MM-DD'),
  endTime: moment().format('YYYY-MM-DD')
}

export default {
  components: {
    page
  },
  props: {},
  data() {
    return {
      listQuery: {},
      request: {
        getListUrl: (data) => {
          this.listQuery = { ...date, ...data }
          return Promise.all([get_count_goods_share_page(this.listQuery)]).then(
            (res) => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      list: [
        {
          key: 'time',
          title: '日期',
          search: true,
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startTime', 'endTime'],
          val: [
            moment().subtract(6, 'd').format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          tableHidden: true
        },
        {
          key: 'goodsSource',
          title: '平台',
          type: formItemType.select,
          tableHidden: true,
          search: true,
          list: [
            {
              label: '淘宝',
              value: '0'
            },
            {
              label: '京东',
              value: '2'
            },
            {
              label: '拼多多',
              value: '3'
            }
          ]
        },
        {
          title: '日期',
          key: 'date'
        },

        {
          title: '详情pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.detailPv} / ${data.detailUv}`)
          }
        },
        {
          title: '分享按钮pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.buttonPv} / ${data.buttonUv}`)
          }
        },
        {
          title: '复制文案pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.copywritingPv} / ${data.copywritingUv}`)
          }
        },
        {
          title: '点击打开微信pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.openWechatPv} / ${data.openWechatUv}`)
          }
        },
        {
          title: '分享图文pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.shareImgPv} / ${data.shareImgUv}`)
          }
        },
        {
          title: '保存图片pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.saveImgPv} / ${data.saveImgUv}`)
          }
        },
        {
          title: '分享微信pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.shareWechatPv} / ${data.shareWechatUv}`)
          }
        },
        {
          title: '分享朋友圈pv/uv',
          render: (h, params) => {
            const data = params.data.row
            return h('span', `${data.shareFriendPv} / ${data.shareFriendUv}`)
          }
        },
        {
          title: '分享订单',
          key: 'order'
        }
      ]
    }
  },
  watch: {},
  mounted() {},
  methods: {
    handUpload() {
      window.location.href = get_count_goods_share_page_exports({
        ...this.listQuery,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="" scoped>
</style>
