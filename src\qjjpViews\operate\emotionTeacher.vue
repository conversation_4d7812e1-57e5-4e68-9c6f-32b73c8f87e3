
<template>
  <Page :request="request" :list="list" table-title="情感教师管理" />
</template>

<script>
import Page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { emotionalMentorPage, emotionalMentorUpdate, emotionalMentorCreate, emotionalMentorCheckStatus } from '@/qjjpApi/operate'

const STATUS = [
  {
    label: '启用',
    value: 1
  },
  {
    label: '禁用',
    value: 0
  }
]
export default {
  name: 'emotionTeacher',
  components: {
    Page
  },
  data() {
    return {
      request: {
        getListUrl: (data) => {
          return emotionalMentorPage(data).then(res => {
            const { records, total } = res.data
            let dataList = []
            if (records && records.length) {
              dataList = {
                total: total,
                rows: records
              }
            }
            const result = {
              data: dataList
            }
            return result
          })
        },
        insertHttp: (data) => {
          return emotionalMentorCheckStatus(data).then(res => {
            if (res.data) {
              return this.$confirm(res.data + '，是否继续添加', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
            }
            return
          }).then(() => {
            return emotionalMentorCreate(data)
          })
        },
        updateHttp: (data) => {
          return emotionalMentorCheckStatus(data).then(res => {
            if (res.data) {
              return this.$confirm(res.data + '，是否继续修改', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
            }
            return
          }).then(() => {
            return emotionalMentorUpdate(data)
          })
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: 'ID',
          key: 'id',
          formHidden: true
        },
        {
          title: '二维码名称',
          key: 'qrCodeName',
          type: formItemType.input,
          reg: ['required'],
          search: true
        },
        {
          title: '二维码',
          key: 'qrCodeUrl',
          type: formItemType.upload,
          tableView: tableItemType.tableView.picture,
          reg: ['required']
        },
        {
          title: '状态',
          key: 'codeStatus1',
          search: true,
          searchKey: 'codeStatus',
          type: formItemType.select,
          tableHidden: true,
          formHidden: true,
          list: STATUS,
          val: '',
          clearable: true
        },
        {
          title: '状态',
          key: 'codeStatus',
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          list: STATUS,
          val: '',
          options: {
            valueType: 'Number'
          },
          reg: ['required']
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: true,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.detailsDialog
            }
          ]
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>

.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}

</style>

<style lang="scss">
.temperatures{
    color:#409eff;
    cursor: pointer;
  }
.styleManagePop{
  width: 340px;
  .styleManagePopTooltip{
    color:#409eff;
  }
  .popTitle{
    font-weight: 600;
  }
  .toolView{
    width: 170px;
    display: inline-block;
  }
  .popView{
    color: #409eff;
    margin: 0 20px 0 3px;
    padding: 5px 0;
    display: inline-block;
    cursor: pointer;
  }
}

</style>
