<template>
  <div class="distribution-node">
    <page :request="request" :list="list" table-title="节点管理列表">
      <div slot="searchContainer" style="display: inline-block; margin-bottom: 15px;">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small"
          @click="handleAdd">添加</el-button>
      </div>
    </page>

    <!-- 新增/编辑节点抽屉 -->
    <Drawer
      :visible.sync="nodeDrawer"
      :title="nodeType === 'add' ? '新增节点' : '编辑节点'"
      @confirm="handleNodeSubmit('nodeForm')"
      @cancel="nodeDrawer = false"
    >
      <el-form ref="nodeForm" :model="nodeForm" :rules="formRules" label-width="120px" label-position="top" class="demo-ruleForm">
        <!-- 链路信息 -->
        <FormSection title="链路信息">
          <div class="link-info-container">
            <el-form-item label="投放链路" prop="deliveryLinkType" :rules="formRules.deliveryLinkType">
              <el-select v-model="nodeForm.deliveryLinkType" placeholder="请选择投放链路" style="width: 240px"
                :disabled="nodeType === 'edit'" @change="handleLinkTypeChange">
                <el-option v-for="item in distributionPathOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="nodeType === 'add'" label="落地页使用场景" prop="pageType" :rules="formRules.pageType">
              <el-select v-model="nodeForm.pageType" placeholder="请选择落地页使用场景" style="width: 240px"
                :disabled="nodeType === 'edit'" @change="($event) => handleLinkTypeChange(nodeForm.deliveryLinkType, $event)">
                <el-option label="投放落地页" :value="1" />
                <el-option label="分发落地页" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="nodeType === 'add' ? nodeForm.deliveryLinkType && nodeForm.pageType : true" label="落地页路由" prop="deliveryRouterId" :rules="formRules.deliveryRouterId">
              <el-select v-model="nodeForm.deliveryRouterId" placeholder="请选择落地页路由" style="width: 240px"
                :disabled="nodeType === 'edit'">
                <el-option v-for="item in distributionRouteOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </div>
        </FormSection>

        <!-- 基础信息 -->
        <FormSection title="基础信息">
          <el-form-item label="节点名称" prop="code" :rules="formRules.code">
            <el-select v-model="nodeForm.code" placeholder="请选择节点名称" style="width: 240px"
              @change="handleNodeCodeChange"
              :disabled="nodeType === 'edit'">
              <el-option v-for="item in nodeCodeOptions" :key="item.nodeCode" :label="item.nodeName" :value="item.nodeCode" />
            </el-select>
          </el-form-item>
          <el-form-item label="节点描述" prop="nodeRemark">
            <el-input type="textarea" v-model="nodeForm.nodeRemark" placeholder="请输入节点描述" :maxlength="100" show-word-limit></el-input>
          </el-form-item>
        </FormSection>

        <!-- 节点信息 -->
        <FormSection title="节点信息">
          <el-form-item label="基础信息（请选择当前节点用户的基础信息）" prop="distributionInfo" :rules="formRules.distributionInfo" required>
            <card-checkbox-groups>
              <card-checkbox-group
                title="个人信息"
                v-model="nodeForm.personInfo"
                :options="personalInfoOptions"
                @change="handlePersonalInfoChange"
                :disabled="nodeType === 'edit'"
              ></card-checkbox-group>

              <card-checkbox-group
                title="车辆信息"
                v-model="nodeForm.carInfo"
                :options="vehicleInfoOptions"
                @change="handleVehicleInfoChange"
                :disabled="nodeType === 'edit'"
              ></card-checkbox-group>
            </card-checkbox-groups>
          </el-form-item>
        </FormSection>

        <!-- 状态 -->
        <FormSection title="状态">
          <el-form-item label="状态" prop="status" :rules="formRules.status">
            <el-radio-group v-model="nodeForm.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </FormSection>
      </el-form>
    </Drawer>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { getNodeList, addNode, updateNode, getNodeDetail, getInfoCategories, getDeliveryLinkConfig, getUndertakeByType } from '@/api/distribution'
import { undertakelist } from '@/api/operate'
import moment from 'moment'
import { CardCheckboxGroup, CardCheckboxGroups } from '@/components/CardCheckboxGroup'
import Drawer from '@/components/Drawer'
import FormSection from '@/components/Drawer/FormSection'

export default {
  name: 'DistributionNode',
  components: {
    page,
    CardCheckboxGroup,
    CardCheckboxGroups,
    Drawer,
    FormSection
  },
  data() {
    // 自定义校验方法
    const validateDistributionInfo = (rule, value, callback) => {
      if (this.nodeForm.personInfo.length === 0 && this.nodeForm.carInfo.length === 0) {
        callback(new Error('请至少选择一项分发信息'))
      } else {
        callback()
      }
    }

    return {
      // 查询参数
      listQuery: {},
      // 节点相关数据
      nodeDrawer: false,
      nodeType: 'add',
      nodeForm: {
        code: '',
        nodeName: '',
        deliveryLinkNodeCode: '',
        nodeRemark: '',
        deliveryLinkType: '',
        pageType: '',
        deliveryRouterId: '',
        personInfo: [],
        carInfo: [],
        status: 1,
        distributionInfo: []
      },
      // 定义选项
      nodeCodeOptions: [],
      personalInfoOptions: [],
      vehicleInfoOptions: [],
      // 选项数据
      distributionPathOptions: [
        {
          id: 1,
          name: 'APK链路'
        },
        {
          id: 2,
          name: 'H5链路'
        }
      ],
      distributionRouteOptions: [],
      statusOptions: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      // 表单验证规则
      formRules: {
        code: [
          { required: true, message: '请选择节点名称', trigger: 'change' }
        ],
        deliveryLinkType: [
          { required: true, message: '请选择投放链路', trigger: 'change' }
        ],
        pageType: [
          { required: true, message: '请选择落地页使用场景', trigger: 'change' }
        ],
        deliveryRouterId: [
          { required: true, message: '请选择落地页路由', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        distributionInfo: [
          { validator: validateDistributionInfo, trigger: 'change' }
        ]
      },
      // API请求配置
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await getNodeList({ ...this.listQuery })
          const { records, total } = list.data
          const result = {
            data: {
              total: total,
              rows: records
            }
          }
          return result
        }
      },
    }
  },
  computed: {
    list() {
      return [
        {
          title: '节点名称',
          key: 'nodeName',
          search: true,
          titleHidden: true,
          type: formItemType.input,
          tableHidden: true,
          options: {
            placeholder: '请输入节点名称'
          }
        },
        {
          title: '投放链路',
          key: 'deliveryLinkType',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableHidden: true,
          list: this.distributionPathOptions,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          options: {
            on: () => {
              return {
                change: e => {
                  // 重新获取落地页路由列表
                  this.fetchDeliveryRoutes(Number(e))

                  // 清空落地页路由的选择
                  setTimeout(() => {
                    const input = document.querySelector('input[placeholder="落地页路由"]');
                    if (input) {
                      input.value = '';
                      input.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                  }, 1000);
                }
              }
            }
          }
        },
        {
          title: '落地页路由',
          key: 'deliveryRouterId',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableHidden: true,
          list: this.distributionRouteOptions,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableHidden: true,
          list: this.statusOptions,
          listFormat: {
            label: 'label',
            value: 'value'
          }
        },
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '节点名称',
          key: 'nodeName'
        },
        {
          title: '节点描述',
          key: 'nodeRemark'
        },
        {
          title: '投放链路',
          key: 'deliveryLinkType',
          render: (h, params) => {
            const path = this.distributionPathOptions.find(item => item.id === params.data.row.deliveryLinkType)
            return h('span', path ? path.name : '--')
          }
        },
        {
          title: '落地页路由',
          key: 'deliveryRouterName'
        },
        {
          title: '节点信息',
          key: 'nodeInfo',
          render: (h, params) => {
            const row = params.data.row
            // 将字符串转换为数组，并确保过滤掉空值
            const personInfo = row.personInfo ? row.personInfo.split(',').filter(Boolean) : []
            const carInfo = row.carInfo ? row.carInfo.split(',').filter(Boolean) : []

            // 获取对应的名称
            const personInfoNames = personInfo.map(code => {
              const option = this.personalInfoOptions.find(item => String(item.value) === String(code))
              return option ? option.label : code
            }).filter(Boolean)

            const carInfoNames = carInfo.map(code => {
              const option = this.vehicleInfoOptions.find(item => String(item.value) === String(code))
              return option ? option.label : code
            }).filter(Boolean)

            // 创建显示内容
            const content = []
            if (personInfoNames.length > 0) {
              content.push(h('div', { style: { marginBottom: '4px' } }, [
                h('span', { style: { color: '#666' } }, '个人信息：'),
                h('span', personInfoNames.join('、'))
              ]))
            }
            if (carInfoNames.length > 0) {
              content.push(h('div', [
                h('span', { style: { color: '#666' } }, '车辆信息：'),
                h('span', carInfoNames.join('、'))
              ]))
            }
            return content.length > 0 ? h('div', content) : h('span', '--')
          }
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            const status = params.data.row.status
            return h('span', status === 1 ? '启用' : '禁用')
          }
        },
        {
          title: '更新人',
          key: 'updateName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.handleEdit(params)
              }
            }
          ]
        }
      ]
    }
  },
  created() {
    // 获取信息分类数据
    this.fetchInfoCategories()
  },
  methods: {
    // 获取信息分类数据
    async fetchInfoCategories() {
      try {
        const res = await getInfoCategories()
        if (res.code === 200) {
          const data = res.data || {}

          // 处理基础信息数据，将 code 和 name 转换为 value 和 label
          if (Array.isArray(data.personList)) {
            this.personalInfoOptions = data.personList.map(item => ({
              value: item.code,
              label: item.name
            }))
          } else {
            this.personalInfoOptions = []
          }

          // 处理车辆信息数据，将 code 和 name 转换为 value 和 label
          if (Array.isArray(data.carList)) {
            this.vehicleInfoOptions = data.carList.map(item => ({
              value: item.code,
              label: item.name
            }))
          } else {
            this.vehicleInfoOptions = []
          }
        }
      } catch (error) {
        this.$message.error(error.message || '获取信息分类失败')
      }
    },
    // 获取节点代码列表
    async fetchNodeCodes(linkType) {
      try {
        const type = linkType || this.nodeForm.deliveryLinkType
        if (!type) {
          this.nodeCodeOptions = []
          return
        }
        const res = await getDeliveryLinkConfig({ type })
        if (res.code === 200) {
          this.nodeCodeOptions = res.data || []
        }
      } catch (error) {
        this.$message.error(error.message || '获取节点代码列表失败')
      }
    },
    // 获取落地页路由选项
    async fetchDeliveryRoutes(type, pageType) {
      try {
        const res = await getUndertakeByType({ type, pageType })
        if (res.code === 200) {
          this.distributionRouteOptions = res.data || []
        }
      } catch (error) {
        console.error('获取落地页路由选项失败:', error)
        this.$message.error('获取落地页路由选项失败')
      }
    },
    // 节点代码选择处理
    handleNodeCodeChange(value) {
      const selectedNode = this.nodeCodeOptions.find(item => item.nodeCode === value)
      if (selectedNode) {
        this.nodeForm.deliveryLinkNodeCode = selectedNode.nodeCode
        this.nodeForm.nodeName = selectedNode.nodeName
      }
    },
    handleAdd() {
      this.nodeType = 'add'
      this.nodeDrawer = true
      this.nodeForm = {
        code: '',
        nodeName: '',
        deliveryLinkNodeCode: '',
        nodeRemark: '',
        deliveryLinkType: '',
        pageType: '',
        deliveryRouterId: '',
        personInfo: [],
        carInfo: [],
        status: 1,
        distributionInfo: []
      }
      this.$nextTick(() => {
        if (this.$refs.nodeForm) {
          this.$refs.nodeForm.resetFields()
        }
      })
    },
    async handleEdit(params) {
      this.nodeType = 'edit'
      try {
        const row = params
        if (!row) {
          this.$message.error('节点信息不完整')
          return
        }

        // 确保已获取选项数据
        if (this.personalInfoOptions.length === 0 || this.vehicleInfoOptions.length === 0) {
          await this.fetchInfoCategories()
        }

        // 先获取落地页路由选项
        await this.fetchDeliveryRoutes(row.deliveryLinkType, row.pageType)

        // 获取节点代码列表
        await this.fetchNodeCodes(row.deliveryLinkType)

        // 处理个人信息和车辆信息，保持字符串格式
        const personInfo = row.personInfo ? row.personInfo.split(',').filter(Boolean) : []
        const carInfo = row.carInfo ? row.carInfo.split(',').filter(Boolean) : []

        // 设置表单数据
        this.nodeForm = {
          ...row,
          code: row.deliveryLinkNodeCode, // 设置选择框的值
          personInfo,
          carInfo,
          distributionInfo: [...personInfo, ...carInfo] // 确保 distributionInfo 也被设置
        }

        this.nodeDrawer = true

        // 等待 DOM 更新后重置校验状态
        this.$nextTick(() => {
          if (this.$refs.nodeForm) {
            this.$refs.nodeForm.clearValidate()
          }
        })
      } catch (error) {
        console.error('编辑节点出错:', error)
        this.$message.error('编辑节点失败')
      }
    },
    resetForm() {
      this.nodeForm = {
        code: '',
        nodeName: '',
        deliveryLinkNodeCode: '',
        nodeRemark: '',
        deliveryLinkType: '',
        deliveryRouterId: '',
        personInfo: [],
        carInfo: [],
        status: 1
      }
    },
    // 个人信息处理
    handlePersonalInfoChange(val) {
      this.updateDistributionInfo()
    },
    // 车辆信息处理
    handleVehicleInfoChange(val) {
      this.updateDistributionInfo()
    },
    // 更新distributionInfo字段
    updateDistributionInfo() {
      this.nodeForm.distributionInfo = [...this.nodeForm.personInfo, ...this.nodeForm.carInfo]
      this.$refs.nodeForm.validateField('distributionInfo')
    },
    handleNodeSubmit(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          try {
            // 构建提交数据
            const { distributionInfo, code, ...otherData } = this.nodeForm
            const submitData = {
              ...otherData,
              // 保持原始字符串格式
              personInfo: this.nodeForm.personInfo,
              carInfo: this.nodeForm.carInfo
            }

            // 根据是新增还是编辑调用不同接口
            const res = this.nodeType === 'add'
              ? await addNode(submitData)
              : await updateNode(submitData)

            if (res.code === 200) {
              this.nodeDrawer = false
              this.$message.success(this.nodeType === 'add' ? '新增成功' : '编辑成功')
              this.$store.dispatch('tableRefresh', this)
            }
          } catch (error) {
            this.$message.error(error.message || '操作失败')
          }
        }
      })
    },
    // 投放链路变更
    handleLinkTypeChange(val, pageType) {
      // 清空落地页路由
      this.nodeForm.deliveryRouterId = ''
      // 清空节点代码
      this.nodeForm.code = ''
      this.nodeForm.nodeName = ''
      this.nodeForm.deliveryLinkNodeCode = ''
      // 获取对应的落地页路由选项
      this.fetchDeliveryRoutes(val, pageType)
      // 获取对应的节点代码列表
      this.fetchNodeCodes(val)
    }
  }
}
</script>

<style lang="scss" scoped>
.distribution-node {
  padding: 20px;

  ::v-deep .activeButton {
    .el-button {
      margin-right: 5px;
      padding: 7px 6px;
    }
  }
}

.link-info-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .el-form-item {
    flex: 1;
    min-width: 240px;
    margin-right: 0;
    margin-bottom: 0;
  }
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 10px;
  width: 100%;
}

// .info-section {
//   width: 100%;
//   border: 1px solid #EBEEF5;
//   border-radius: 4px;
//   padding: 16px;
//   box-sizing: border-box;
//   background-color: #FFFFFF;
//   margin-bottom: 0;

//   &.personal-info, &.vehicle-info {
//     min-width: 100%;
//     max-width: 100%;
//   }

//   .info-header {
//     margin-bottom: 16px;
//     padding-left: 5px;

//     ::v-deep .el-checkbox {
//       .el-checkbox__input {
//         margin-right: 8px;
//       }

//       .el-checkbox__label {
//         font-weight: 500;
//         font-size: 14px;
//         color: #333;
//       }
//     }
//   }

//   .info-content {
//     display: flex;
//     flex-wrap: wrap;
//     gap: 12px;
//     padding-left: 0;

//     ::v-deep .el-checkbox-group {
//       display: flex;
//       flex-wrap: wrap;
//       gap: 12px;
//       width: 100%;
//     }

//     ::v-deep .el-checkbox {
//       margin-right: 0;
//       margin-left: 0;
//       height: 36px;
//       border: 1px solid #DCDFE6;
//       border-radius: 4px;
//       display: inline-flex;
//       align-items: center;
//       margin-bottom: 0;
//       padding: 0 16px 0 32px;
//       box-sizing: border-box;
//       position: relative;
//       background-color: #FFFFFF;
//       cursor: pointer;

//       .el-checkbox__input {
//         position: absolute;
//         left: 10px;
//         top: 50%;
//         transform: translateY(-50%);
//       }

//       &.is-checked {
//         background-color: #F5F7FA;
//         border-color: #409EFF;
//       }

//       &:hover {
//         border-color: #409EFF;
//       }

//       .el-checkbox__label {
//         padding-left: 0;
//         line-height: 36px;
//         font-size: 13px;
//         white-space: nowrap;
//         color: #606266;
//       }
//     }
//   }
// }
</style>
