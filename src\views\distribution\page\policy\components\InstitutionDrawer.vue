<template>
  <el-drawer
    title="机构设置"
    :visible.sync="visible"
    size="80%"
    :destroy-on-close="true"
    :wrapperClosable="false"
    :before-close="handleClose"
    :append-to-body="true"
  >
    <div class="institution-drawer">
      <div class="drawer-content">
        <div class="transfer-container">
          <!-- 左侧表格 -->
          <div class="table-panel">
            <div class="panel-header">
              <span class="title">可选机构列表 ({{ leftTableData.length }}项)</span>
              <div class="search-box">
                <el-input
                  v-model="leftSearchKeyword"
                  placeholder="请输入搜索内容"
                  prefix-icon="el-icon-search"
                  size="small"
                  clearable
                  @input="filterLeftTable"
                />
              </div>
            </div>
            <el-table
              ref="leftTable"
              :data="filteredLeftTableData"
              height="500"
              border
              @selection-change="handleLeftSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="institution" label="机构" min-width="120" />
              <el-table-column prop="infoType" label="信息分类" min-width="120" />
            </el-table>
          </div>
          
          <!-- 中间操作按钮 -->
          <div class="transfer-buttons">
            <el-button 
              type="primary" 
              icon="el-icon-arrow-right" 
              :disabled="!leftSelectedRows.length"
              @click="moveToRight"
            />
            <el-button 
              type="primary" 
              icon="el-icon-arrow-left" 
              :disabled="!rightSelectedRows.length"
              @click="moveToLeft"
            />
          </div>
          
          <!-- 右侧表格 -->
          <div class="table-panel">
            <div class="panel-header">
              <span class="title">已选机构 ({{ rightTableData.length }}项)</span>
              <div class="header-right">
                <el-popconfirm
                  title="确定要删除全部已选项吗？"
                  @onConfirm="handleDeleteAll"
                >
                  <el-button 
                    slot="reference" 
                    type="text" 
                    :disabled="!rightTableData.length"
                    style="margin-right: 10px; padding: 8px 0;"
                    :class="{'delete-btn': rightTableData.length}"
                  >
                    全部删除
                  </el-button>
                </el-popconfirm>
                <div class="search-box">
                  <el-input
                    v-model="rightSearchKeyword"
                    placeholder="请输入搜索内容"
                    prefix-icon="el-icon-search"
                    size="small"
                    clearable
                    @input="filterRightTable"
                  />
                </div>
              </div>
            </div>
            <div class="right-table-wrapper">
              <!-- 自定义表头 -->
              <div class="el-table__header-wrapper">
                <table class="el-table__header" cellspacing="0" cellpadding="0" border="0">
                  <thead>
                    <tr>
                      <th width="55" class="is-center">
                        <div class="cell">
                          <label class="el-checkbox">
                            <span class="el-checkbox__input" :class="{'is-checked': selectAll, 'is-indeterminate': isIndeterminate}">
                              <span class="el-checkbox__inner"></span>
                              <input type="checkbox" 
                                :checked="selectAll" 
                                :disabled="!rightTableData.length"
                                @change="handleSelectAll($event.target.checked)" 
                                class="el-checkbox__original"
                              />
                            </span>
                          </label>
                        </div>
                      </th>
                      <th width="60" class="is-center">
                        <div class="cell">排序</div>
                      </th>
                      <th width="90" class="is-center">
                        <div class="cell">分发优先级</div>
                      </th>
                      <th class="min-width-120">
                        <div class="cell">机构</div>
                      </th>
                      <th class="min-width-120">
                        <div class="cell">信息分类</div>
                      </th>
                      <th width="80" class="is-center">
                        <div class="cell">操作</div>
                      </th>
                    </tr>
                  </thead>
                </table>
              </div>
              
              <!-- 表格内容区域 -->
              <div class="el-table__body-wrapper">
                <table class="el-table__body" cellspacing="0" cellpadding="0" border="0">
                  <draggable 
                    v-model="rightTableData" 
                    tag="tbody" 
                    v-bind="dragOptions"
                    @end="updatePriorities"
                  >
                    <tr v-for="(row, index) in filteredRightTableData" :key="row.id" class="el-table__row">
                      <td width="55" class="is-center">
                        <div class="cell">
                          <label class="el-checkbox">
                            <span class="el-checkbox__input" :class="{'is-checked': rightSelectedRows.some(item => item.id === row.id)}">
                              <span class="el-checkbox__inner"></span>
                              <input 
                                type="checkbox" 
                                :checked="rightSelectedRows.some(item => item.id === row.id)"
                                @change="handleRightRowSelect(row, $event.target.checked)" 
                                class="el-checkbox__original"
                              />
                            </span>
                          </label>
                        </div>
                      </td>
                      <td width="60" class="is-center">
                        <div class="cell">
                          <i class="el-icon-rank drag-handle" />
                        </div>
                      </td>
                      <td width="90" class="is-center">
                        <div class="cell">{{ row.priority }}</div>
                      </td>
                      <td class="min-width-120">
                        <div class="cell">{{ row.institution }}</div>
                      </td>
                      <td class="min-width-120">
                        <div class="cell">{{ row.infoType }}</div>
                      </td>
                      <td width="80" class="is-center">
                        <div class="cell">
                          <el-button 
                            type="text" 
                            @click="handleDeleteRow(row)"
                            class="delete-btn"
                          >
                            删除
                          </el-button>
                        </div>
                      </td>
                    </tr>
                  </draggable>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部操作按钮 -->
      <div class="drawer-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import draggable from 'vuedraggable'
import { getInstitutionConfig } from '@/api/distribution'

export default {
  name: 'InstitutionDrawer',
  components: {
    draggable
  },
  data() {
    return {
      visible: false,
      currentNode: null,
      // 左侧表格
      leftTableData: [],
      leftSearchKeyword: '',
      filteredLeftTableData: [],
      leftSelectedRows: [],
      // 右侧表格
      rightTableData: [],
      rightSearchKeyword: '',
      filteredRightTableData: [],
      rightSelectedRows: [],
      // 全选状态
      selectAll: false,
      loading: false
    }
  },
  computed: {
    dragOptions() {
      return {
        animation: 150,
        handle: '.drag-handle',
        ghostClass: 'sortable-ghost'
      }
    },
    // 添加半选状态计算属性
    isIndeterminate() {
      return this.rightSelectedRows.length > 0 && this.rightSelectedRows.length < this.filteredRightTableData.length;
    }
  },
  watch: {
    leftTableData: {
      handler() {
        this.filterLeftTable();
      },
      immediate: true
    },
    rightTableData: {
      handler() {
        this.filterRightTable();
        this.updatePriorities();
      },
      immediate: true
    }
  },
  methods: {
    // 获取信息分类标签
    getInfoCategoryLabel(value) {
      const map = {
        0: '不限',
        1: '一类',
        2: '二类',
        3: '三类'
      }
      // 确保将 value 转为字符串进行匹配
      return map[String(value)] || value
    },

    // 获取机构配置列表
    async fetchInstitutionConfig(params) {
      try {
        this.loading = true
        const res = await getInstitutionConfig(params)
        if (res.code === 200) {
          // 处理返回的数据
          this.leftTableData = (res.data || []).map(item => ({
            id: item.infoCategoryId,
            institution: item.advertiserName,
            infoType: this.getInfoCategoryLabel(item.infoCategory),
            // 保存原始数据，提交时使用
            triageStrategyNodeId: item.triageStrategyNodeId
          }))
          console.log('处理后的数据:', this.leftTableData)
        }
      } catch (error) {
        console.error('获取机构配置失败:', error)
        this.$message.error('获取机构配置失败')
      } finally {
        this.loading = false
      }
    },

    // 打开抽屉
    async open(node) {
      this.currentNode = node
      this.visible = true
      
      // 重置数据
      this.rightTableData = []
      this.leftTableData = []
      
      // 获取机构配置列表
      await this.fetchInstitutionConfig({
        linkType: node.linkType,
        routeId: node.routeId,
        nodeCode: node.nodeCode
      })
      
      // 如果节点已有数据，加载到表格中
      if (node && node.institutionDisplayData && node.institutionDisplayData.length > 0) {
        // 将已有数据转换为右侧表格格式
        this.rightTableData = node.institutionDisplayData.map(item => ({
          id: item.id,
          institution: item.institution,
          infoType: item.infoType,
          priority: item.priority,
          triageStrategyNodeId: item.triageStrategyNodeId
        }))
        
        // 更新左侧表格，移除已选项目
        this.updateLeftTable()
      }
    },
    
    // 更新优先级
    updatePriorities() {
      this.rightTableData.forEach((item, index) => {
        item.priority = `P${index}`
      })
    },
    
    // 手动选择右侧行
    handleRightRowSelect(row, checked) {
      if (checked) {
        if (!this.rightSelectedRows.some(item => item.id === row.id)) {
          this.rightSelectedRows.push(row);
        }
      } else {
        this.rightSelectedRows = this.rightSelectedRows.filter(item => item.id !== row.id);
      }
      
      // 更新全选状态
      this.selectAll = this.rightSelectedRows.length === this.filteredRightTableData.length;
    },
    
    // 更新左侧表格，移除已在右侧的项目
    updateLeftTable() {
      const selectedIds = this.rightTableData.map(item => item.id)
      this.filteredLeftTableData = this.leftTableData.filter(item => !selectedIds.includes(item.id))
    },
    
    // 筛选左侧表格
    filterLeftTable() {
      const keyword = this.leftSearchKeyword.toLowerCase()
      const selectedIds = this.rightTableData.map(item => item.id)
      
      if (keyword) {
        this.filteredLeftTableData = this.leftTableData.filter(item => 
          !selectedIds.includes(item.id) && 
          (item.institution.toLowerCase().includes(keyword) || 
           item.infoType.toLowerCase().includes(keyword))
        )
      } else {
        this.filteredLeftTableData = this.leftTableData.filter(item => !selectedIds.includes(item.id))
      }
    },
    
    // 筛选右侧表格
    filterRightTable() {
      const keyword = this.rightSearchKeyword.toLowerCase()
      
      if (keyword) {
        this.filteredRightTableData = this.rightTableData.filter(item => 
          item.institution.toLowerCase().includes(keyword) || 
          item.infoType.toLowerCase().includes(keyword) ||
          item.priority.toLowerCase().includes(keyword)
        )
      } else {
        this.filteredRightTableData = [...this.rightTableData]
      }
    },
    
    // 左侧表格选择变化
    handleLeftSelectionChange(rows) {
      this.leftSelectedRows = rows
    },
    
    // 右侧表格选择变化
    handleRightSelectionChange(rows) {
      this.rightSelectedRows = rows
    },
    
    // 移动到右侧
    moveToRight() {
      if (!this.leftSelectedRows.length) return
      
      // 将选中行移到右侧，保持所有数据字段
      const newItems = this.leftSelectedRows.map(item => ({
        ...item,
        priority: `P${this.rightTableData.length}` // 设置初始优先级
      }))
      this.rightTableData = [...this.rightTableData, ...newItems]
      
      // 清除选择
      this.$refs.leftTable.clearSelection()
      this.leftSelectedRows = []
      
      // 更新左侧表格
      this.updateLeftTable()
      
      // 更新优先级
      this.updatePriorities()
    },
    
    // 移动到左侧
    moveToLeft() {
      if (!this.rightSelectedRows.length) return
      
      // 获取要移除的ID
      const removeIds = this.rightSelectedRows.map(item => item.id)
      
      // 移除选中行
      this.rightTableData = this.rightTableData.filter(item => !removeIds.includes(item.id))
      
      // 立即清除选择
      this.rightSelectedRows = []
      
      // 更新左侧表格
      this.updateLeftTable()
      
      // 更新优先级
      this.updatePriorities()

      // 重置全选状态
      this.selectAll = false
    },
    
    // 关闭抽屉
    handleClose() {
      this.visible = false
    },
    
    // 提交表单
    handleSubmit() {
      // 保存设置到节点
      if (this.currentNode) {
        // 保存完整的选择项
        this.currentNode.institutionSettings = {
          selectedItems: [...this.rightTableData]
        }
        
        // 触发更新事件
        this.$emit('update', this.currentNode)
      }
      this.visible = false
    },
    
    // 处理全选
    handleSelectAll(checked) {
      this.selectAll = checked;
      if (checked) {
        this.rightSelectedRows = [...this.filteredRightTableData];
      } else {
        this.rightSelectedRows = [];
      }
    },
    
    // 删除单行
    handleDeleteRow(row) {
      // 从右侧表格移除
      this.rightTableData = this.rightTableData.filter(item => item.id !== row.id)
      // 清除选中状态
      this.rightSelectedRows = this.rightSelectedRows.filter(item => item.id !== row.id)
      // 更新全选状态
      this.selectAll = this.rightSelectedRows.length === this.filteredRightTableData.length
      // 更新左侧表格
      this.updateLeftTable()
      // 更新优先级
      this.updatePriorities()
    },

    // 删除全部
    handleDeleteAll() {
      // 清空右侧表格
      this.rightTableData = []
      // 清空选中状态
      this.rightSelectedRows = []
      // 重置全选状态
      this.selectAll = false
      // 更新左侧表格
      this.updateLeftTable()
    }
  }
}
</script>

<style lang="scss" scoped>
.institution-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;

  .drawer-content {
    flex: 1;
    padding: 20px;
    overflow: hidden;
    
    .transfer-container {
      display: flex;
      gap: 10px;
      height: 100%;
      
      .table-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        border: 1px solid #EBEEF5;
        border-radius: 4px;
        overflow: hidden;
        height: 100%;
        
        .panel-header {
          padding: 12px;
          background-color: #F5F7FA;
          border-bottom: 1px solid #EBEEF5;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }
          
          .search-box {
            width: 200px;
          }

          .header-right {
            display: flex;
            align-items: center;
          }
        }
        
        .right-table-wrapper {
          position: relative;
          flex: 1;
          display: flex;
          flex-direction: column;
          background: #fff;
          height: 500px;
          
          .el-table__header-wrapper {
            background-color: #fff;
            width: 100%;
          }
          
          .el-table__header {
            width: 100%;
            table-layout: fixed;
            border-collapse: separate;
            border-spacing: 0;
            
            th {
              height: 48px;
              padding: 12px 0;
              background-color: #F5F7FA;
              color: #606266;
              font-weight: 500;
              border-bottom: 1px solid #EBEEF5;
              border-right: 1px solid #EBEEF5;
              box-sizing: border-box;
              text-align: left;
              
              &:last-child {
                border-right: none;
              }
              
              &.is-center {
                text-align: center;
              }
              
              &.min-width-120 {
                min-width: 120px;
              }
              
              .cell {
                padding: 0 12px;
                line-height: 23px;
                box-sizing: border-box;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                word-break: break-all;
              }
            }
          }
          
          .el-table__body-wrapper {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            height: calc(100% - 48px);
            
            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }
            
            .el-table__body {
              width: 100%;
              table-layout: fixed;
              border-collapse: separate;
              border-spacing: 0;
              
              tr {
                background-color: #FFFFFF;
                
                &:hover {
                  background-color: #F5F7FA;
                }
                
                td {
                  height: 48px;
                  padding: 12px 0;
                  border-bottom: 1px solid #EBEEF5;
                  border-right: 1px solid #EBEEF5;
                  box-sizing: border-box;
                  
                  &:last-child {
                    border-right: none;
                  }
                  
                  &.is-center {
                    text-align: center;
                  }
                  
                  &.min-width-120 {
                    min-width: 120px;
                  }
                  
                  .cell {
                    padding: 0 12px;
                    line-height: 23px;
                    box-sizing: border-box;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: normal;
                    word-break: break-all;
                    
                    .drag-handle {
                      font-size: 18px;
                      cursor: move;
                      color: #909399;
                    }
                  }
                }
              }
            }
          }
        }
        
        ::v-deep .el-table {
          flex: 1;
          
          .el-table__header th {
            background-color: #F5F7FA;
            color: #606266;
          }
          
          .drag-handle {
            font-size: 18px;
            cursor: move;
            color: #909399;
          }

          // 设置表格容器高度
          .el-table__body-wrapper {
            height: calc(100% - 40px) !important;
          }
        }
      }
      
      .transfer-buttons {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 20px;
        padding: 0 10px;
        
        .el-button {
          padding: 12px;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          i {
            font-size: 16px;
          }
        }

        .el-button+.el-button {
          margin-left: 0;
        }
      }
    }
  }

  .drawer-footer {
    border-top: 1px solid #EBEEF5;
    padding: 20px;
    display: flex;
    justify-content: center;
    gap: 20px;
    
    .el-button {
      margin-left: 8px;
      width: 100px;
    }
  }
}

.sortable-ghost {
  background-color: #f8f8f9 !important;
  opacity: 0.8;
}

// 自定义抽屉样式
::v-deep .el-drawer__header {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #EBEEF5;

  > span{
    font-size: 20px;
  }

  .el-drawer__close-btn {
    i{
      font-size: 30px;
    }
  }
}

::v-deep .el-drawer__body {
  height: calc(100% - 55px);
  padding: 0;
}

.el-checkbox {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  cursor: pointer;
  display: inline-block;
  white-space: nowrap;
  user-select: none;
  
  .el-checkbox__input {
    white-space: nowrap;
    cursor: pointer;
    outline: none;
    display: inline-block;
    line-height: 1;
    position: relative;
    vertical-align: middle;

    &.is-checked {
      .el-checkbox__inner {
        background-color: #409EFF;
        border-color: #409EFF;
        
        &:after {
          transform: rotate(45deg) scaleY(1);
        }
      }
    }
    
    &.is-indeterminate {
      .el-checkbox__inner {
        background-color: #409EFF;
        border-color: #409EFF;
        
        &:before {
          content: "";
          position: absolute;
          display: block;
          background-color: #fff;
          height: 2px;
          transform: scale(.5);
          left: 0;
          right: 0;
          top: 5px;
        }
        
        &:after {
          display: none;
        }
      }
    }
    
    .el-checkbox__inner {
      display: inline-block;
      position: relative;
      border: 1px solid #DCDFE6;
      border-radius: 2px;
      box-sizing: border-box;
      width: 14px;
      height: 14px;
      background-color: #fff;
      z-index: 1;
      transition: border-color .25s cubic-bezier(.71,-.46,.29,1.46),background-color .25s cubic-bezier(.71,-.46,.29,1.46);
      
      &:after {
        box-sizing: content-box;
        content: "";
        border: 1px solid #fff;
        border-left: 0;
        border-top: 0;
        height: 7px;
        left: 4px;
        position: absolute;
        top: 1px;
        transform: rotate(45deg) scaleY(0);
        width: 3px;
        transition: transform .15s ease-in .05s;
        transform-origin: center;
      }
    }
    
    .el-checkbox__original {
      opacity: 0;
      outline: none;
      position: absolute;
      margin: 0;
      width: 0;
      height: 0;
      z-index: -1;
    }
  }
}

.delete-btn {
  color: #F56C6C;
  padding: 0;
  
  &:hover {
    color: #ff7875;
  }
}

.el-checkbox {
  &.is-disabled {
    .el-checkbox__input {
      cursor: not-allowed;
      
      .el-checkbox__inner {
        background-color: #F5F7FA;
        border-color: #E4E7ED;
        cursor: not-allowed;
      }
    }
  }
}
</style> 