<template>
  <div>
    <h1 class="page-title">{{ osFmt }}{{ typeFmt }}版本</h1>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="300px"
      class="demo-ruleForm"
    >
    <el-form-item label="选择应用名称" prop="siteId">
        <el-select v-model="ruleForm.siteId" placeholder="请选择应用名称">
          <el-option
            v-for="(item, index) in siteIds"
            :key="index"
            :label="item.name"
            :value="item.siteId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="版本号" prop="version">
        <el-input
          v-model="ruleForm.version"
          style="width: 300px"
          placeholder="请输入版本号"
          :disabled="type === 'edit'"
        />
      </el-form-item>
      <el-form-item label="版本描述" prop="description">
        <el-input
          v-model="ruleForm.description"
          type="textarea"
          style="width: 300px"
          :rows="6"
          placeholder="请输入版本描述"
        />
      </el-form-item>
      <el-form-item label="选择应用市场/渠道" prop="relationCode">
        <el-select v-model="ruleForm.relationCode" placeholder="请选择应用市场/渠道">
          <el-option
            v-for="(item, index) in appCodeList"
            :key="index"
            :label="item.title"
            :value="item.relationCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否更新" prop="setForce">
        <el-radio-group v-model="ruleForm.setForce">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否强更" prop="setUpdate">
        <el-radio-group v-model="ruleForm.setUpdate">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="ruleForm.status">
          <el-radio :label="0">启用</el-radio>
          <el-radio :label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="!os" label="上传APK文件：" prop="linkUrl">
        <uploader
          ref="uploader"
          :options="options"
          class="uploader-example"
          :file-status-text="statusText"
          @file-success="fileSuccess"
          @file-removed="fileRemoved"
          @file-added="checkFile"
        >
          <uploader-unsupport />
          <uploader-drop>
            <p>拖拽文件上传或者</p>
            <uploader-btn :attrs="attrs">选择文件</uploader-btn>
          </uploader-drop>
          <uploader-list />
        </uploader>
      </el-form-item>
      <el-form-item v-show="os" label="下载地址：" prop="linkUrl">
        <el-input
          v-model="ruleForm.linkUrl"
          @input="($event) => (ruleForm.extendUrl = $event)"
          placeholder="请输入下载地址"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handSubmit('ruleForm')">立即创建</el-button>
        <el-button @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  ADD_ANDROID_SAVE,
  GET_ANDROID_DETAILS_BY_ID,
  GET_CHANNEL_LIST,
  GET_UPLOAD_CALLBACK,
  ADD_IOS_SAVE,
  PUT_IOS_SAVE,
  GET_IOS_DETAILS_BY_ID,
  GET_APP_CHANNEL_LIST
} from '@/api/appVersion'
import { count_channel_application_list } from '@/api/NewChannel'
export default {
  components: {},
  props: {},
  data() {
    return {
      ruleForm: {
        version: '',
        description: '',
        relationCode: '',
        setUpdate: '',
        setForce: '',
        linkUrl: '',
        extendUrl: '',
        name: '',
        isIndex: 1,
        status: '',
        isPersonal: 1,
        siteId:'',
        os: ''
      },
      appCodeList: [],
      rules: {
        siteId: [
          { required: true, message: '应用名称不能为空', trigger: 'change' }
        ],
        version: [
          { required: true, message: '版本号不能为空', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '版本描述不能为空', trigger: 'blur' }
        ],
        relationCode: [
          {
            required: true,
            message: '应用市场/渠道不能为空',
            trigger: 'change'
          }
        ],
        setUpdate: [
          { required: true, message: '请选择是否更新', trigger: 'change' }
        ],
        setForce: [
          { required: true, message: '请选择是否强更', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择是否启用', trigger: 'change' }
        ],
        linkUrl: [
          { required: true, message: '下载地址不能为空', trigger: 'blur' }
        ]
      },
      options: {
        target: `${this.$CONSTANT.publicPath}/upload/apk2`,
        headers: { Authorization: this.$utils.getToken() },
        testChunks: false,
        chunkSize: 1024 * 1024 * 1024,
        simultaneousUploads: 1,
        singleFile: true,
        query: () => {
          return {
            channelCode: this.ruleForm.relationCode
          }
        }
      },
      statusText: {
        success: '上传成功',
        error: '上传失败',
        uploading: '上传中',
        paused: '暂停中',
        waiting: '等待中'
      },
      attrs: {
        accept: 'application/vnd.android.package-archive'
      },
      siteIds:[]
    }
  },
  computed: {
    osFmt() {
      if (this.os == 0) {
        return 'android'
      } else {
        return 'ios'
      }
    },
    typeFmt() {
      if (this.type == 'add') {
        return '新增'
      } else {
        return '编辑'
      }
    },
    os() {
      return Number(this.$route.query.os)
    },
    type() {
      return this.$route.query.type
    },
    id() {
      return this.$route.query.id
    }
  },
  watch: {
    'ruleForm.siteId': {
      handler(val) {
        GET_APP_CHANNEL_LIST({
          siteId: val,
          os: this.os == 1 ? '1' : '2'
        }).then(res => {
          this.appCodeList = Array.isArray(res.data?.records) ? res.data?.records : []
        })
      },
      immediate: true
    }
  },
  mounted() {
    if (this.type == 'edit') {
      GET_ANDROID_DETAILS_BY_ID(this.id).then(res => {
        this.ruleForm = res.data
      })
    }
    if (!this.os) {
      this.$nextTick(() => {
        window.uploader = this.$refs.uploader.uploader
      })
    }
    count_channel_application_list().then(res=>{
      if(res.code === 200){
        this.siteIds = res.data
      }
    })
  },
  methods: {
    checkFile(file) {
      if (this.basics.isNull(this.ruleForm.relationCode)) {
        file.ignored = true
        this.$message.error('请先选择应用市场/渠道！')
      }
    },
    cancel() {
      this.$router.go(-1)
    },
    handSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          ADD_ANDROID_SAVE({ ...this.ruleForm, os: this.os == 1 ? '1' : '2' }).then(res => {
            if (res.code == 200) {
              this.$message.success(`${this.typeFmt}成功！`)
              this.$router.go(-1)
            }
          })
        } else {
          return false
        }
      })
    },
    fileSuccess(rootFile, file, response) {
      // 如果返回的是字符串，需要先解析成 JSON
      let res = response;
      if (typeof response === 'string') {
        try {
          res = JSON.parse(response);
        } catch (e) {
          console.error('解析响应数据失败:', e);
          this.$message.error('服务器返回数据格式错误');
          return;
        }
      }

      console.log('$$$$$$$$$$$$$$$$$', res, res.code)
      if (res.code === 200) {
        this.ruleForm.linkUrl = res.data.sourcePath
        this.ruleForm.extendUrl = res.data.channelPath
      } else {
        this.$message.error(res.msg)
      }
    },
    fileRemoved(file) {
      this.ruleForm.linkUrl = ''
      this.ruleForm.extendUrl = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader-example {
  width: 580px;
  padding: 15px;
  font-size: 14px;
  border: 1px dashed #ddd;
  & ::v-deep .uploader-file[status="success"] .uploader-file-remove {
    display: block;
  }
}

.uploader-example .uploader-btn {
  margin-right: 4px;
}

.uploader-example .uploader-list {
  max-height: 440px;
  overflow: auto;
  overflow-x: hidden;
  overflow-y: auto;
}
.page-title {
  font-size: 20px;
  margin: 0 0 15px 0;
}
</style>
