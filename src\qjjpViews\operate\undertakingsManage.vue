<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 14:33:57
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-05-10 11:36:30
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="渠道管理">
      <div slot="searchContainer" style="display: inline-block">
        <!-- <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button> -->
        <el-button
          plain
          icon="el-icon-circle-plus-outline"
          type="primary"
          size="small"
          @click="handleAdd"
        >新增承接</el-button>
      </div>
    </page>
    <el-drawer
      v-if="drawer"
      title="投放承接编辑/新增"
      :visible.sync="drawer"
      direction="rtl"
      size="50%"
    >
      <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="100px" class="demo-ruleForm">
        <div class="form_view">
          <div class="form_view_title">
            <span>基础信息</span>
          </div>
          <el-form-item label="承接名称" prop="name" :rules="addRules.common" :style="{display: 'inline-block'}">
            <div :style="{width: '200px'}">
              <el-input v-model="addForm.name" placeholder="请输入弹窗名称" maxlength="30" />
            </div>
          </el-form-item>
          <span :style="{padding: '0 0 0 10px'}">{{ addForm.name.length }}/30</span>
          <el-form-item label="承接类型" prop="type" :rules="addRules.common">
            <el-select v-model="addForm.type" placeholder="请选择承接类型" :disabled="!!addForm.id">
              <el-option
                v-for="(typeItem) in list3"
                :key="typeItem.id"
                :label="typeItem.name"
                :value="typeItem.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="承接链接" prop="link" :rules="addRules.common">
            <div :style="{width: '200px'}">
              <el-input v-model="addForm.link" placeholder="请输入承接链接地址，http//:格式" />
            </div>
          </el-form-item>
        </div>

        <div class="form_view">
          <div class="form_view_title">
            <span>人群定向</span>
          </div>
          <el-form-item label="用户类型" prop="userType" :rules="addRules.common">
            <el-radio-group v-model="addForm.userType">
              <el-radio v-for="item in list4" :label="item.id" :disabled="item.id==1">{{ item.name }}</el-radio>
            </el-radio-group>
            <!-- <el-select v-model="addForm.userType" placeholder="请选择用户类型">
              <el-option
                v-for="item in list4"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select> -->
          </el-form-item>
        </div>

        <div class="form_view">
          <div class="form_view_title">
            <span>投放承接状态</span>
          </div>
          <el-form-item label="状态" prop="status" :rules="addRules.common">
            <el-switch
              v-model="addForm.status"
              :active-value="0"
              :inactive-value="1"
            />
          </el-form-item>
        </div>
        <div :style="{'text-align': 'right', width: '100%'}">
          <el-button @click="drawer=false">取消</el-button>
          <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { undertakeList, addOrUpdate, undertakeListexport } from '@/qjjpApi/operate'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      addForm: {
        id: '',
        name: '',
        type: '',
        link: '',
        userType: 0,
        status: 0
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      drawer: false,
      list4: [
        {
          id: 1,
          name: '会员用户'
        },
        {
          id: 0,
          name: '非会员用户'
        }
      ],
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      list2: [
        {
          id: 0,
          name: '启用'
        },
        {
          id: 1,
          name: '禁用'
        }
      ],
      list3: [
        {
          id: 1,
          name: '站内承接'
        },
        {
          id: 2,
          name: '站外承接'
        }
      ],
      siteIds: [],
      listQuery: {
        descs: 'updateTime'
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then(res => {
              if (res.code === 0) {
                if (res.data && res.data.length) {
                  this.siteIds = res.data
                }
              }
            })
          }
          const list = await undertakeList(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '承接名称',
          key: 'styleName',
          type: formItemType.input,
          search: true,
          clearable: true,
          searchKey: 'activityName',
          tableHidden: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '序号',
          key: 'id'
        },
        {
          title: '承接名称',
          key: 'name'
        },
        {
          title: '承接类型',
          key: 'type',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required']
        },
        {
          title: '承接链接',
          key: 'link'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '更新人员',
          key: 'updateAdminName'
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required']
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',

              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.addForm = JSON.parse(JSON.stringify(params))
                this.drawer = true
              }
            }
          ]
        }
      ]
    }
  },
  async mounted() {
    // const list1 = await messageStyleList()
    // const { records, total } = list1.data
    // let dataList = []
    // console.info(records, 'records')
    // if (records && records.length) {
    //   dataList = [total, ...records]
    // }
    // const result = {
    //   data: dataList
    // }
    // console.info(result, 'result')
  },
  created() {},
  methods: {
    handMessageStyleListAdd(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          addOrUpdate({ ...this.addForm }).then(res => {
            if (res.code == 200) {
              this.drawer = false
              this.$message.success('操作成功')
              this.$store.dispatch('tableRefresh', this)
            }
          })
        }
      })
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = undertakeListexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    reloadAddform() {
      this.addForm = {
        id: '',
        name: '',
        type: '',
        link: '',
        userType: 0,
        status: 0
      }
    },
    handleAdd() {
      this.reloadAddform()
      this.drawer = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
    overflow: scroll;
    // padding-bottom: 20px;
    padding: 0 30px 20px;
    /* overflow-x: auto; */
}
::v-deep .el-drawer__header{
  span{
    font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
  }

}
.form_view{
        margin: 0 0px 20px;
        background-color: rgb(189, 184, 184,0.2);
        border: 1px solid rgba(0,0,0,0.2);
        width: 100%;
        padding: 15px;
        border-radius: 5px;
        .form_view_title{
            margin-bottom: 20px;
            span{
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
    }
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
