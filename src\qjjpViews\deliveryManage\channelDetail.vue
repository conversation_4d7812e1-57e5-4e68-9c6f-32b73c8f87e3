<!--
 * @Author: 陈小豆
 * @Date: 2024-06-04 14:16:43
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-11-13 11:03:53
-->
<template>
  <div id="styleDetail">
    <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="120px" class="demo-ruleForm">
      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>基础信息</span>
        </div>
        <el-form-item label="渠道名称" prop="name" :rules="addRules.common"
          :style="{ 'display': 'inline-block', 'margin-right': '20px' }">
          <div :style="{ width: '200px' }">
            <el-input v-model="addForm.name" placeholder="请输入渠道名称" maxlength="30" />
          </div>
        </el-form-item>
        <span>{{ addForm.name.length }}/30</span>
        <el-form-item label="应用名称" prop="siteId" :rules="addRules.common"
          :style="{ 'display': 'inline-block', 'margin-right': '20px' }">
          <el-select v-model="addForm.siteId" placeholder="请选择应用名称" :disabled="type == 'edit'" @change="changeSiteId($event,'change')">
            <el-option v-for="item in siteIds" :key="item.code" :label="item.name" :value="item.siteId" />
          </el-select>
        </el-form-item>
        <el-form-item label="应用类型" prop="os" :rules="addRules.common" :style="{ 'display': 'inline-block' }">
          <div :style="{ width: '200px' }">
            <el-select v-model="addForm.os" placeholder="请选择应用类型" :disabled="type == 'edit'" @change="osChange">
              <el-option v-for="item in osListForm" :key="item.value" :label="item.label" :value="item.value">{{
                item.label }}</el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="键盘默认选中" prop="defaultChoose" :rules="addRules.common">
          <el-select v-model="addForm.defaultChoose" placeholder="请选择默认选中">
            <el-option v-for="item in list5" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </div>
      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /> <span>投放设置</span>
        </div>
        <el-form-item label="投放链路" prop="downloadType" :rules="addRules.common"
          :style="{ 'display': 'inline-block', 'margin-right': '20px' }">
          <el-select v-model="addForm.downloadType" placeholder="请选择投放链路" :disabled="type == 'edit'">
            <el-option v-for="item in list1" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="投放平台" prop="apiCode" :rules="addRules.common"
          :style="{ 'display': 'inline-block', 'margin-right': '20px' }">
          <el-select v-model="addForm.apiCode" placeholder="请选择投放平台" :disabled="type == 'edit'" @change="changeApi($event,'change')">
            <el-option v-for="item in mediaList" :key="item.platformCode" :label="item.platformName"
              :value="item.platformCode" />
          </el-select>
        </el-form-item>
        <!-- :rules="addRules.common" -->
        <el-form-item label="站内承接页面" prop="landingPage" :style="{ 'display': 'inline-block' }">
          <el-select v-model="addForm.landingPage" clearable placeholder="请选择站内承接页面">
            <el-option v-for="item in undertakeList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="addForm.downloadType == 3" label="H5承接页面" prop="p4Page" :rules="addRules.common"
          :style="{ 'display': 'inline-block' }">
          <el-select v-model="addForm.p4Page" clearable placeholder="请选择H5承接页面">
            <el-option v-for="item in undertakeList2" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /><span>回传设置</span>
        </div>
        <el-form-item label="回传节点" prop="deviceReportType" :rules="addRules.common">
          <el-select v-model="addForm.deviceReportType" placeholder="请选择回传节点" :disabled="type == 'edit'"
            @change="changeDeviceReportType">
            <el-option v-for="item in list2" :disabled="item.disabled" :key="item.id" :label="item.name" v-bind="{...(item.disabled?{title:'当前媒体暂不支持多节点回传'}:{})}" :value="item.id" />
          </el-select>
          <div style="color: #f66462;display:inline-flex;align-items: center;margin-left:10px" v-if="addForm.deviceReportType==3">
            <el-tooltip v-model="showeDocument" placement="bottom-start" :style="{ color: '#409eff' }">
              <div slot="content" style="width: 300px;line-height: 2;">
                <i class="el-icon-document" style="font-size: 14px;color:#f66462" ></i> <span  style="color: #f66462">回传打折建议：多节点回传时建议配置一个打折回传系数！</span><br>
                <span style="color: #f66462">1、打折逻辑：</span>由于多节点回传时采用交叉打折逻辑，即一个节点打折后续节点都不回传，转化目标越靠后，转化率越低，最大转化率=1*节点1回传系数*节点2回传系数*节点n回传系数<br>
                <span style="color: #f66462">2、影响范围：</span>所以多节点回传时建议配置一个打折回传系数，转化目标越靠后媒体投放模型命中率越低，可能对媒体投放模型产生负面影响！<br>
              </div>
              <i class="el-icon-document" style="font-size: 14px;color:#f66462"></i>
            </el-tooltip>
          </div>
        </el-form-item>

        <div v-for="(itemList, indexList) in addForm.channelUpLoadInfoList" :key="indexList" class="form_view_package">
          <span>（节点{{ indexList + 1 }}： <el-tooltip
              :content="addForm.deviceReportType == 2 && indexList==1 ? `配置后同一个设备将回传媒体两次` : `配置后同一个设备将回传媒体一次`" placement="top-start"
              :style="{ color: '#409eff' }">
              <i class="el-icon-question" style="font-size: 14px" />
            </el-tooltip>用户行为满足其中一个则回传媒体对应事件）</span>
          <el-form-item :prop="`channelUpLoadInfoList.${indexList}.code`" :rules="addRules.common">
            <span slot="label">
              用户行为
              <el-tooltip placement="top-start" :style="{ color: '#409eff' }">
                <div slot="content">
                  激活：首次创建设备<br>
                  注册：首次创建用户<br>
                  悬浮窗授权成功：用户完成悬浮窗权限授权<br>
                  站内购卡：站内除开落地页场景购卡成功<br>
                  站内落地页购卡：站内落地页场景购卡成功<br>
                  全场景购卡：所有购卡场景购卡成功<br>
                  站内落地页领取人设：站内落地页场景人设生成成功<br>
                  发起支付：所有购卡场景创建待支付订单<br>
                  键盘授权成功：用户完成键盘权限授权<br />
                  重复发起支付：设备发起支付2次及以上<br />
                  arup：设备创建后7个自然日内产生的收益之和，计算公式=（会员支付金额-会员退款金额）+（人设支付金额-人设退款金额）+广告收益<br />
                  留存：设备在首次激活后的7天内（不含当日）访问过app，包括使用端外键盘<br />
                </div>
                <i class="el-icon-question" style="font-size: 14px" />
              </el-tooltip>
            </span>
            <el-select v-model="itemList.code" placeholder="用户行为" multiple :disabled="type == 'edit'">
              <el-option v-for="item in getList3(indexList)" :disabled="item.disabled"  :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="ARPU值" :prop="`channelUpLoadInfoList.${indexList}.extendParamMap.arup.arupValue`"
            :rules="addRules.common" v-if="itemList['code'].includes('arup')">
            <span slot="label">
              ARPU值
              <el-tooltip placement="top-start" :style="{ color: '#409eff' }">
                <div slot="content">
                  设备创建后7个自然日内产生的收益之和，计算公式=（会员支付金额-会员退款金额）+（人设支付金额-人设退款金额）+广告收益
                </div>
                <i class="el-icon-question" style="font-size: 14px" />
              </el-tooltip>
            </span>
            <el-input v-model="itemList.extendParamMap.arup.arupValue" placeholder="ARPU值"
              oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')"
              @blur="itemList.extendParamMap.arup.arupValue = $event.target.value" />
          </el-form-item>
          <el-form-item label="回传事件" :prop="`channelUpLoadInfoList.${indexList}.eventId`" :rules="addRules.common">
            <span slot="label">
              回传事件
              <el-tooltip content="用户触发回传节点后回传给媒体的事件，事件定义请查看媒体官方文档" placement="top-start" :style="{ color: '#409eff' }">
                <i class="el-icon-question" style="font-size: 14px" />
              </el-tooltip>
            </span>
            <el-select v-model="itemList.eventId" placeholder="回传事件" :disabled="type == 'edit'"
              @change="changeEvent($event, indexList)">
              <el-option v-for="items in eventsList" :key="items.id" :label="items.eventName" :value="items.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="回传系数" :prop="`channelUpLoadInfoList.${indexList}.discountCount`"
            :rules="addRules.common">
            <span slot="label">
              回传系数
              <el-tooltip content="用户触发回传节点后将根据回传系数进行满减回传，满减逻辑见对应下拉系数" placement="top-start"
                :style="{ color: '#409eff' }">
                <i class="el-icon-question" style="font-size: 14px" />
              </el-tooltip>
            </span>
            <el-select v-model="itemList.discountCount" placeholder="回传系数" @change="changeEvent($event, indexList)">
              <el-option v-for="items in orderCoefficientList" :key="items.value" :label="items.label"
                :value="items.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="回传订单金额"
            v-if="judegCodeHandler(itemList['code'])">
            <span slot="label">
              回传订单金额
              <el-tooltip
                content="若配置回传订单金额并且用户行为选择全场景购卡/站内落地页购卡/站内购卡/发起支付/重复发起支付时将校验订单金额是否大于等于该配置金额，若满足条件则该笔订单正常回传，若不满足条件则该笔订单不回传"
                placement="top-start" :style="{ color: '#409eff' }">
                <i class="el-icon-question" style="font-size: 14px" />
              </el-tooltip>
            </span>
            <el-input v-model="itemList.orderAmountLimit" placeholder="回传订单金额" :style="{ width: '200px' }"
              oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')"
              @blur="itemList.orderAmountLimit = $event.target.value" />
            <div>（低于该金额的订单不会发生回传，非支付类回传事件不受此影响，未配置则视为全量回传）</div>
          </el-form-item>
          <div class="btn-main" v-if="addForm.deviceReportType == 3 && type!='edit'">
            <el-button type="danger" icon="el-icon-delete" circle @click="changeChannelUpLoadInfoList('del',indexList)"></el-button>
            <el-button type="primary" icon="el-icon-plus" circle @click="changeChannelUpLoadInfoList('add')"></el-button>
          </div>
        </div>
        <div class="lastTip" v-if="addForm.apiCode=='guangDianTongApk' &&addForm.deviceReportType==3"><i class="el-icon-bell" ></i>（请注意！按照腾讯广点通的全链路回传要求，IAP投放方式全链路回传节点需包括<span style="color:#f66462">激活-留存-付费</span>）<a href="https://doc.weixin.qq.com/doc/w3_AL4AtQZ1ACcu3t1h0ikS824ZudrQF?scode=AJEAIQdfAAohz5Axslz4aMhy5aAO8" target="_blank">查看详情</a></div>

      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /> <span>支付设置</span>
        </div>
        <el-form-item label="支付模板" prop="memberTemplateId" :rules="addRules.common">
          <el-select v-model="addForm.memberTemplateId" placeholder="请选择支付模板" @change="checkMembershipTemplate">
            <el-option v-for="item in formMemberTemplate" :key="item.id" :label="item.name + '(id:' + item.id + ')'"
              :value="item.id" />
          </el-select>
          <el-button @click="toFn(formMemberTemplate.find(n=>n.id==addForm.memberTemplateId).name)" v-if="formMemberTemplate.find(n=>n.id==addForm.memberTemplateId)" type="primary" size="mini" style="margin-left:10px">查看模板</el-button>
        </el-form-item>
        <el-form-item label="会员名称" prop="memberPackageIds" :rules="addRules.common" style="width: 50%;min-width:600px">
          <tableSelect :table-list="memberNameList" :default-select="defaultSelectPackIds" :key-list="keyList"
            @emitSelect="addForm.memberPackageIds = $event" />
          <!-- <el-select v-model="addForm.memberPackageIds" placeholder="会员名称" multiple @change="checkmemberName">
            <el-option
              v-for="item in memberNameList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select> -->
        </el-form-item>
        <!-- <el-form-item v-if="price.length>0" label="渠道会员价格">
          <div v-for="(item,index) in price" :key="'price'+index" :style="{'margin-bottom':'20px'}">
            <el-input
              v-model="price[index]"
              placeholder="请输入渠道会员价格"
              disabled
              :style="{width: '200px'}"
            />
          </div>
        </el-form-item> -->
      </div>

      <div class="form_view">
        <div class="form_view_title">
          <div class="title_line" /> <span>状态</span>
        </div>
        <el-form-item label="状态" prop="status" :rules="addRules.common">
          <el-switch v-model="addForm.status" :active-value="0" :inactive-value="1" />
        </el-form-item>
      </div>
      <div :style="{ 'text-align': 'right', width: '100%' }">
        <el-button @click="closeCheckQAShow">取消</el-button>
        <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { count_channel_application_list, undertakeSelector, mediaAll, getAppBindEvents } from '@/qjjpApi/NewChannel'
import { getmembershipTemplate } from '@/qjjpApi/payManage'
import { addChannel, channelDetail, updateChannel } from '@/qjjpApi/deliveryManage'
import { osListForm } from '@/qjjpViews/appVersion/basicParams'
import tableSelect from './components/tableSelect'
import { toFinite } from 'lodash'
export default {
  components: { tableSelect },
  data() {
    return {
      showeDocument: this.$route.query.type == 'add',
      orderAmountLimit: [],
      priceJudegCodeArray: ['buyCardAll', 'buyCardInLandingPage', 'buyCardInApp', 'payClick', 'repeatStartPay'], // 回传订单金额展示
      list5: [
        {
          id: 0,
          name: '文字回复'
        },
        {
          id: 1,
          name: '语音回复'
        }
      ],
      type: 'add',
      currentToal: 0,
      eventsList: [],
      platformCode: null,
      packageName: null,
      mediaList: [],
      price: [],
      memberNameList: [],
      membershipTemplate: [],
      undertakeList: [],
      undertakeList2: [],
      siteIds: [],
      value1: '1',
      osListForm,
      addForm: {
        id: '',
        name: '',
        channelCode: '',
        os: '',
        downloadType: '',
        deviceReportType: '',
        landingPage: '',
        p4Page: '',
        memberTemplateId: '',
        memberPackageIds: [],
        apiCode: '',
        siteId: '',
        adminId: '',
        status: 0,
        channelUpLoadInfoList: [{
          orderAmountLimit: '',
          node: 1, code: [], eventId: '', keyEventName: '', discountCount: '1', extendParamMap: {
            arup: {
              arupValue:
                ''
            }
          }
        }]
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      form: {

      },
      defaultSelectPackIds: [],
      orderCoefficientList: [
        { label: '0.1（满10扣9）', value: '0.1' },
        { label: '0.2（满5扣4）', value: '0.2' },
        { label: '0.3（满10扣7）', value: '0.3' },
        { label: '0.4（满5扣3）', value: '0.4' },
        { label: '0.5（满2扣1）', value: '0.5' },
        { label: '0.6（满5扣2）', value: '0.6' },
        { label: '0.66（满3扣1）', value: '0.66' },
        { label: '0.7（满10扣3）', value: '0.7' },
        { label: '0.75（满4扣1）', value: '0.75' },
        { label: '0.8（满5扣1）', value: '0.8' },
        { label: '0.83（满6扣1）', value: '0.83' },
        { label: '0.88（满8扣1）', value: '0.88' },
        { label: '0.9（满10扣1）', value: '0.9' },
        { label: '1.0（满10扣0）', value: '1' }
      ],
      list1: [
        {
          id: 1,
          name: 'apk链路'
        },
        {
          id: 2,
          name: 'h5链路'
        },
        {
          id: 3,
          name: 'H5下载+APK付费'
        }
      ],
      list2: [
        {
          id: 1,
          name: '普通回传（单节点）'
        },
        {
          id: 2,
          name: '关键行为（双节点）'
        },
        {
          id: 3,
          name: '全链路回传（多节点）',
          disabled: false
        }
      ],
      list3: [
        {
          id: 'active',
          name: '激活'
        },
        {
          id: 'register',
          name: '注册'
        },
        {
          id: 'authorization',
          name: '悬浮窗授权成功'
        },
        {
          id: 'buyCardInApp',
          name: '站内购卡'
        },
        {
          id: 'buyCardInLandingPage',
          name: '站内落地页购卡'
        },
        {
          id: 'buyCardAll',
          name: '全场景购卡'
        },
        {
          id: 'receiveUserMessageStyle',
          name: '领取人设'
        },
        {
          id: 'payClick',
          name: '发起支付'
        },
        {
          id: 'authorizationKeyboard',
          name: '键盘授权成功'
        },
        {
          id: 'repeatStartPay',
          name: '重复发起支付'
        },
        {
          id: 'arup',
          name: 'arup值达标'
        },
        {
          id: 'RETAIN',
          name: '留存'
        }
      ],
      list4: [
        {
          id: 0,
          name: '注册'
        },
        {
          id: 1,
          name: '站内购卡'
        }
      ]
    }
  },
  computed: {
    formMemberTemplate() {
      // android和ios查询ios&Android类型
      const list = this.membershipTemplate || []
      return list?.filter(item => {
        if (['1', '2'].includes(String(this.addForm.os))) {
          return String(item.os) === String(this.addForm.os) || String(item.os) === '0'
        }
        return String(item.os) === String(this.addForm.os)
      }) ?? []
    },
    keyList() {
      const iosAndAndroid = [{ label: 'SKU名称', keyVal: 'name' }, { label: '安卓支付价', keyVal: 'paymentPrice' }, { label: 'ios支付价', keyVal: 'iosPaymentPrice' }]
      const Android = [{ label: 'SKU名称', keyVal: 'name' }, { label: '安卓支付价', keyVal: 'paymentPrice' }]
      const ios = [{ label: 'SKU名称', keyVal: 'name' }, { label: 'ios支付价', keyVal: 'iosPaymentPrice' }]
      const os = String(this.addForm.os)
      if (os === '1') {
        return ios
      } else if (os === '2') {
        return Android
      } else {
        return iosAndAndroid
      }
    }
  },
  watch: {
    // "addForm.apiCode" : {
    //   handler(val) {
    //     this.list2 = this.list2.map(n => {
    //       if (n.id == 3) {
    //         n.disabled = val != 'guangDianTongApk'
    //       }
    //       return n
    //     })
    //     if (this.addForm.deviceReportType == 3 && val != 'guangDianTongApk') {
    //       this.addForm.deviceReportType = 1
    //       this.addForm.channelUpLoadInfoList = this.$options.data.call(this).addForm.channelUpLoadInfoList
    //     }
    //   },
    //   deep: true
    // },
    platformCode: {
      handler(val) {
        console.info(val, 'platformCode')
        if (val && this.packageName && this.addForm.siteId) {
          getAppBindEvents({ platformCode: this.platformCode, packageSignature: this.packageName }).then(res => {
            if (res.code === 200) {
              this.eventsList = res.data || {}
              for (let i = 0; i < this.eventsList.length; i++) {
                this.$set(this.eventsList[i], 'id', Number(this.eventsList[i].id))
              }
            }
          })
        }
      }
    },
    packageName: {
      handler(val) {
        console.info(val, 'packageName')
        if (val && this.platformCode && this.addForm.siteId) {
          getAppBindEvents({ platformCode: this.platformCode, packageSignature: this.packageName }).then(res => {
            if (res.code === 200) {
              this.eventsList = res.data || {}
              for (let i = 0; i < this.eventsList.length; i++) {
                this.$set(this.eventsList[i], 'id', Number(this.eventsList[i].id))
              }
            }
          })
        }
      }
    }

  },
  async mounted() {
    await mediaAll().then(res => {
      if (res.code === 200) {
        this.mediaList = res.data
      }
    })

    await count_channel_application_list().then(res => {
      if (res.code === 200) {
        this.siteIds = res.data
      }
    })
    await Promise.all([
      undertakeSelector({ type: 1 }).then(res => {
        if (res.code === 200) {
          this.undertakeList = res.data
        }
      }),
      undertakeSelector({ type: 2 }).then(res => {
        if (res.code === 200) {
          this.undertakeList2 = res.data
        }
      })
    ])

    const list = await getmembershipTemplate({ status: 1, pageNumber: 1, pageSize: 999 })
    const { records } = list.data
    if (records && records.length) {
      this.membershipTemplate = [...records]
    }
    this.type = this.$route.query.type
    if (this.$route.query.type == 'edit' || this.$route.query.type == 'fz') {
      this.type = this.$route.query.type
      channelDetail({ id: this.$route.query.id }).then(async res => {
        if (res.code == 200) {
          for (let i = 0; i < res.data.channelUpLoadInfoList.length; i++) {
            for (let key in res.data.channelUpLoadInfoList[i].extendParamMap) {
              res.data.channelUpLoadInfoList[i].extendParamMap[key] = JSON.parse(res.data.channelUpLoadInfoList[i].extendParamMap[key])
            }
          }

          for (const key in res.data) {
            this.$set(this.addForm, key, res.data[key])
          }

          if (this.addForm.os === undefined || this.addForm.os === null || this.addForm.os === '') {
            this.addForm.os = 2
          }

          await this.changeApi(this.addForm.apiCode)
          await this.changeSiteId(this.addForm.siteId)
          console.info(res.data.memberTemplateId, 'this.addForm.memberTemplateId')
          this.checkMembershipTemplate(res.data.memberTemplateId)
          const arr = res.data.memberPackageIds.split(',')
          const arr1 = []
          for (let i = 0; i < arr.length; i++) {
            arr1.push(Number(arr[i]))
          }
          this.$set(this.addForm, 'memberPackageIds', arr1)
          this.defaultSelectPackIds = arr1
          // this.changeDeviceReportType(this.addForm.deviceReportType)
          this.checkmemberName(res.data.memberPackageIds.split(','))
          if (this.$route.query.type == 'fz') {
            this.$set(this.addForm, 'id', '')
            this.$set(this.addForm, 'status', 0)
          }

          for (let i = 0; i < this.addForm.channelUpLoadInfoList.length; i++) {
            // this.$set(this.addForm, key, res.data[key])
            if (!this.addForm.channelUpLoadInfoList[i].extendParamMap.arup) {
              this.$set(this.addForm.channelUpLoadInfoList[i].extendParamMap, 'arup', { arupValue: '' })
            }

            if (this.addForm.channelUpLoadInfoList[i]) {
              let orderAmountLimitThis = 0
              this.priceJudegCodeArray.forEach(item => {
                if (this.addForm.channelUpLoadInfoList[i].extendParamMap[item]) {
                  orderAmountLimitThis = this.addForm.channelUpLoadInfoList[i].extendParamMap[item].orderAmountLimit
                }
              })
              // if (this.addForm.channelUpLoadInfoList[i].extendParamMap.buyCardAll) {
              //   orderAmountLimitThis = this.addForm.channelUpLoadInfoList[i].extendParamMap.buyCardAll.orderAmountLimit
              // }
              // if (this.addForm.channelUpLoadInfoList[i].extendParamMap.buyCardInLandingPage) {
              //   orderAmountLimitThis = this.addForm.channelUpLoadInfoList[i].extendParamMap.buyCardInLandingPage.orderAmountLimit
              // }
              // if (this.addForm.channelUpLoadInfoList[i].extendParamMap.buyCardInApp) {
              //   orderAmountLimitThis = this.addForm.channelUpLoadInfoList[i].extendParamMap.buyCardInApp.orderAmountLimit
              // }
              // if (this.addForm.channelUpLoadInfoList[i].extendParamMap.payClick) {
              //   orderAmountLimitThis = this.addForm.channelUpLoadInfoList[i].extendParamMap.payClick.orderAmountLimit
              // }
              // if (this.addForm.channelUpLoadInfoList[i].extendParamMap.repeatStartPay) {
              //   orderAmountLimitThis = this.addForm.channelUpLoadInfoList[i].extendParamMap.repeatStartPay.orderAmountLimit
              // }
              this.$set(this.addForm.channelUpLoadInfoList[i], 'orderAmountLimit', orderAmountLimitThis)
            }
          }

          console.info(this.addForm, 'addfrom')
          this.$forceUpdate()
        }
      })
    }
  },
  methods: {
    toFn(name) {
      const routeUrl = this.$router.resolve({
        path: '/qjjp/payManage/paymentTemplate',
        query: {
          templateName: name
        }
      })
      window.open(routeUrl.href, '_blank')
    },
    judegCodeHandler(codeArray) {
      for (let x = 0; x < this.priceJudegCodeArray.length; x++) {
        const item = this.priceJudegCodeArray[x]
        if (codeArray.includes(item)) {
          return true
        }
      }
    },
    getList3(i) {
      if (this.addForm.deviceReportType == 3) {
        const codeArray = this.addForm.channelUpLoadInfoList.map((item, index) => (index != i ? item.code : [])).flat()
        const codeArrayAll = this.addForm.channelUpLoadInfoList.map((item, index) => item.code).flat()
        // 用户行为不可重复选择
        const list = this.list3.map(n => {
          if (codeArray.includes(n.id)) {
            n.disabled = true
          } else {
            n.disabled = false
          }
          return n
        })
        // 并且不可存在包含关系
        return list.map(n => {
          if (!n.disabled) {
            // 全场景购卡和站内购卡、站内落地页购卡互斥
            if (n.id == 'buyCardInApp' || n.id == 'buyCardInLandingPage' || n.id == 'buyCardAll') {
              if (n.id == 'buyCardInApp' || n.id == 'buyCardInLandingPage') {
                if (codeArrayAll.includes('buyCardAll')) {
                  n.disabled = true
                } else {
                  n.disabled = false
                }
              }
              if (!n.disabled) {
                if (n.id == 'buyCardAll') {
                  if (codeArrayAll.includes('buyCardInApp') || codeArrayAll.includes('buyCardInLandingPage')) {
                    n.disabled = true
                  } else {
                    n.disabled = false
                  }
                }
              }
            }
            // 发起支付和重新发起支付互斥
            if (n.id == 'payClick' || n.id == 'repeatStartPay') {
              if ((n.id == 'payClick' && codeArrayAll.includes('repeatStartPay')) || (n.id == 'repeatStartPay' && codeArrayAll.includes('payClick'))) {
                n.disabled = true
              } else {
                n.disabled = false
              }
            }
          }
          return n
        })
      } else {
        // 重置
        return this.$options.data.call(this).list3
      }
    },
    osChange() {
      this.$set(this.addForm, 'memberTemplateId', '')
      this.$set(this.addForm, 'memberPackageIds', [])
      this.memberNameList = []
    },
    async changeSiteId(e, type = 'auto') {
      if ( type == 'change') {
        this.addForm.deviceReportType = 1
        this.addForm.channelUpLoadInfoList = this.$options.data.call(this).addForm.channelUpLoadInfoList
      }
      const a = this.siteIds.filter(item => item.siteId == e)
      this.$set(this.addForm, 'memberTemplateId', '')
      this.$set(this.addForm, 'memberPackageIds', [])
      this.memberNameList = []
      const list = await getmembershipTemplate({ status: 1, pageNumber: 1, pageSize: 999, siteId: e })
      const { records } = list.data
      if (records && records.length) {
        this.membershipTemplate = [...records]
      }
      console.info(this.siteIds, a, e, 'changeSiteId')
      this.packageName = a[0].packageName
    },
    changeEvent(e, index) {
      const a = this.eventsList.filter(item => item.id == e)
      console.log(e, index, this.eventsList, a)
      if (a.length > 0) {
        this.$set(this.addForm.channelUpLoadInfoList[index], 'eventId', a[0].id)
        this.$set(this.addForm.channelUpLoadInfoList[index], 'keyEventName', a[0].eventName)
      }
    },
    changeChannelUpLoadInfoList(type, index) {
      if (type == 'del') {
        if (this.addForm.channelUpLoadInfoList.length == 1) {
          this.$message.error('至少保留一条')
          return
        }
        this.addForm.channelUpLoadInfoList.splice(index, 1)
        this.addForm.channelUpLoadInfoList = this.addForm.channelUpLoadInfoList.map((n, i) => {
          n.node = i + 1
          return n
        })
      } else {
        if (this.addForm.channelUpLoadInfoList.length >= 10) {
          this.$message.error('已达回传节点添加上限')
          return
        }
        this.addForm.channelUpLoadInfoList.push({
          node: this.addForm.channelUpLoadInfoList.length + 1, orderAmountLimit: '', code: [], eventId: '', keyEventName: '', discountCount: '1', extendParamMap: {
            arup: {
              arupValue:
                ''
            },
            buyCardAll: {
              orderAmountLimit:
                ''
            },
            buyCardInLandingPage: {
              orderAmountLimit:
                ''
            },
            buyCardInApp: {
              orderAmountLimit:
                ''
            },
            payClick: {
              orderAmountLimit:
                ''
            },
            repeatStartPay: {
              orderAmountLimit:
                ''
            }
          }
        })
      }
    },
    changeDeviceReportType(e) {
      if (e == 2) {
        this.addForm.channelUpLoadInfoList = this.$options.data.call(this).addForm.channelUpLoadInfoList
        this.addForm.channelUpLoadInfoList.push({
          node: 2, code: [], orderAmountLimit: '', eventId: '', keyEventName: '', discountCount: '1', extendParamMap: {
            arup: {
              arupValue:
                ''
            },
            buyCardAll: {
              orderAmountLimit:
                ''
            },
            buyCardInLandingPage: {
              orderAmountLimit:
                ''
            },
            buyCardInApp: {
              orderAmountLimit:
                ''
            },
            payClick: {
              orderAmountLimit:
                ''
            },
            repeatStartPay: {
              orderAmountLimit:
                ''
            },
          }
        })
      } else if (e === 1 && this.addForm.channelUpLoadInfoList.length > 1) {
        this.addForm.channelUpLoadInfoList = this.$options.data.call(this).addForm.channelUpLoadInfoList
      } else if (e === 3) {
        this.addForm.channelUpLoadInfoList = this.$options.data.call(this).addForm.channelUpLoadInfoList
      }
    },
    changeApi(e, type) {
      if ( type == 'change') {
        this.addForm.deviceReportType = 1
        this.addForm.channelUpLoadInfoList = this.$options.data.call(this).addForm.channelUpLoadInfoList
      }
      this.platformCode = e
    },
    checkmemberName(e) {
      const a = []
      if (this.memberNameList.length < 1) {
        this.$set(this.addForm, 'memberPackageIds', [])
        return
      }
      console.info(this.memberNameList, 'this.memberNameList')
      for (let i = 0; i < e.length; i++) {
        const num = this.memberNameList?.filter(item => item.id == e[i])

        if (!num[0]) {
          return
        }
        a.push(num[0]?.paymentPrice)
        // if (num[0]?.expiryDay != '-1') {
        //   a.push(num[0]?.paymentPrice)
        // } else {
        //   a.push('永久会员')
        // }
      }
      this.price = a
      console.info(a, e)
    },
    checkMembershipTemplate(e) {
      // this.checkmemberName([])
      const a = this.membershipTemplate?.filter(item => item.id == e)
      this.memberNameList = a[0]?.membershipPackageVos || []
      if (!a[0]) {
        this.$set(this.addForm, 'memberTemplateId', '')
        this.$set(this.addForm, 'memberPackageIds', [])
      } else {
        const ids = []

        this.memberNameList.forEach(item => ids.push(item.id))
        this.$set(this.addForm, 'memberTemplateId', e)
        this.$set(this.addForm, 'memberPackageIds', ids)
      }
      this.defaultSelectPackIds = this.addForm.memberPackageIds
      // console.info(a[0].membershipPackageVos, 'a[0].membershipPackageVos')
    },
    closeCheckQAShow() {
      this.$router.push('/qjjp/deliveryManage/channelManage')
    },
    handMessageStyleListAdd(formName) {
      console.log(this.addForm, 'F')
      this.$refs[formName].validate(valid => {
        if (valid) {
          for (let i = 0; i < this.addForm.channelUpLoadInfoList.length; i++) {
            // this.$set(this.addForm, key, res.data[key])
            this.priceJudegCodeArray.forEach(item => {
              if (this.addForm.channelUpLoadInfoList[i]['code'].includes(item)) {
                this.$set(this.addForm.channelUpLoadInfoList[i].extendParamMap, item, { orderAmountLimit: this.addForm.channelUpLoadInfoList[i].orderAmountLimit })
              }
            })
            // if (this.addForm.channelUpLoadInfoList[i]['code'].includes('buyCardAll')) {
            //   this.$set(this.addForm.channelUpLoadInfoList[i].extendParamMap, 'buyCardAll', { orderAmountLimit: this.addForm.channelUpLoadInfoList[i].orderAmountLimit })
            // }
            // if (this.addForm.channelUpLoadInfoList[i]['code'].includes('buyCardInLandingPage')) {
            //   this.$set(this.addForm.channelUpLoadInfoList[i].extendParamMap, 'buyCardInLandingPage', { orderAmountLimit: this.addForm.channelUpLoadInfoList[i].orderAmountLimit })
            // } if (this.addForm.channelUpLoadInfoList[i]['code'].includes('buyCardInApp')) {
            //   this.$set(this.addForm.channelUpLoadInfoList[i].extendParamMap, 'buyCardInApp', { orderAmountLimit: this.addForm.channelUpLoadInfoList[i].orderAmountLimit })
            // } if (this.addForm.channelUpLoadInfoList[i]['code'].includes('payClick')) {
            //   this.$set(this.addForm.channelUpLoadInfoList[i].extendParamMap, 'payClick', { orderAmountLimit: this.addForm.channelUpLoadInfoList[i].orderAmountLimit})
            // } if (this.addForm.channelUpLoadInfoList[i]['code'].includes('repeatStartPay')) {
            //   this.$set(this.addForm.channelUpLoadInfoList[i].extendParamMap, 'repeatStartPay', { orderAmountLimit: this.addForm.channelUpLoadInfoList[i].orderAmountLimit})
            // }
          }
          console.info(this.addForm, 'this.addForm')
          // 格式化channelUpLoadInfoList里面的数据
          const channelUpLoadInfoList = JSON.parse(JSON.stringify(this.addForm.channelUpLoadInfoList)).map(item => {
            item.extendParamMap = Object.keys(item.extendParamMap).reduce((pre, key) => {
              if (item.extendParamMap[key] && item.extendParamMap[key] != undefined) {
                try {
                  pre[key] = JSON.stringify(item.extendParamMap[key])
                } catch (e) {
                  console.log(e)
                  pre[key] = item.extendParamMap[key]
                }
              } else {
                pre[key] = item.extendParamMap[key]
              }
              return pre
            }, {})
            return item
          })
          if (this.addForm.deviceReportType == 2) {
            if (Number(this.addForm.channelUpLoadInfoList[0]?.discountCount) < Number(this.addForm.channelUpLoadInfoList[1]?.discountCount)) {
              this.$message.error('节点1回传系数不可小于节点2回传系数，请修改后重试')
              return
            }
          }
          console.info(channelUpLoadInfoList, 'channelUpLoadInfoList')
          // this.$set(this.membershipTemplate, 'memberPackageIds', this.membershipTemplate.split(','))
          const fn = (this.type == 'add' || this.type == 'fz') ? addChannel : updateChannel
          fn({ ...this.addForm, memberPackageIds: this.addForm.memberPackageIds.toString(), channelUpLoadInfoList: channelUpLoadInfoList }).then(res => {
            if (res.code == 200) {
              this.$message.success('添加成功')
              this.$router.push('/qjjp/deliveryManage/channelManage')
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#styleDetail {
  padding: 30px;

  .form_view {
    width: 100%;
    padding: 15px;
    border-radius: 5px;

    .form_view_package {
      width: 80%;
      margin: 10px 0;
      padding: 10px 10px;
      background-color: rgba(242, 242, 242, 0.5);
      display: flex;
      flex-wrap: wrap;
      position: relative;

      span:first-child {
        width: 100%;
        margin-bottom: 5px
      }
      .btn-main{
        width: 100px;
        height: 50px;
        position: absolute;
        top:5px;
        right: -10px;
        transform: translateX(100%);
      }
    }

    .form_view_title {
      margin-bottom: 20px;

      .title_line {
        width: 2px;
        height: 10px;
        background-color: #66b1ff;
        display: inline-block;
        vertical-align: middle;
      }

      span {
        padding-left: 5px;
        vertical-align: middle;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        line-height: 30px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
.lastTip{
  a{
    color: blue;
    text-decoration: underline;
  }
}
.toList{
  display: inline-block;
  text-decoration: underline;
  color: blue;
  margin-left: 5px;
  cursor: pointer;
}
</style>
