<template>
  <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
    <el-tab-pane
      v-for="(item, index) in tabs"
      :key="index"
      :label="item.title"
      :name="item.id"
    >
      <page v-if="item.id === activeName" :request="activeName === '1' ? newOsRequest : newLoginRequest" :list="activeName === '1' ? newOsList : newLoginList" table-title="物理返回引导统计" :table-pagination-state="false">
        <div slot="searchContainer" style="display: inline-block;">
          <el-button plain type="warning" size="small" icon="el-icon-upload" @click="handUpload">导出数据</el-button>
        </div>
      </page>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import moment from 'moment'
import {
  EXPORT_COUNT_RETURN_DEVICE,
  EXPORT_COUNT_RETURN_USER,
  GET_COUNT_RETURN_USER,
  GET_RETURN_DEVICE
} from '@/api/buryData'

export default {
  components: {
    page
  },
  data() {
    return {
      tabs: [
        {
          title: '新设备未登录弹窗',
          id: '1'
        },
        {
          title: '新用户登录未下单且未领取极速提现额度',
          id: '2'
        }
      ],
      activeName: '',
      listQuery: {
        startTime: moment()
          .subtract(6, 'days')
          .format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      },
      newOsRequest: {
        getListUrl: data => {
          this.listQuery = { ...this.listQuery, ...data }
          return Promise.all([GET_RETURN_DEVICE(this.listQuery)]).then(
            res => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      newLoginRequest: {
        getListUrl: data => {
          this.listQuery = { ...this.listQuery, ...data }
          return Promise.all([GET_COUNT_RETURN_USER(this.listQuery)]).then(
            res => {
              return Promise.resolve(res[0])
            }
          )
        }
      }
    }
  },
  computed: {
    newOsList() {
      return [
        {
          key: 'time',
          title: '日期',
          search: true,
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startTime', 'endTime'],
          val: [
            moment()
              .subtract(6, 'd')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date'
        },
        {
          title: '退出拦截弹窗PV/UV',
          key: 'exitInterceptPopPvUv'
        },
        {
          title: '看视频按钮点击pv/uv',
          key: 'watchVideoClickPvUv'
        },
        {
          title: '引导登录弹窗Pv/uv',
          key: 'guideLoginPopPvUv'
        },
        {
          title: '登录按钮点击pv/uv',
          key: 'loginButtonClickPvUv'
        },
        {
          title: '登录成功用户数(总/新)',
          key: 'loginSuccessUser'
        },
        {
          title: '提现成功用户数',
          key: 'withdrawSuccessUser'
        },
        {
          title: '访问-看视频转化',
          key: 'watchPopRate'
        },
        {
          title: '访问-成功提现转化',
          key: 'withdrawPopRate'
        }
      ]
    },
    newLoginList() {
      return [
        {
          key: 'time',
          title: '日期',
          search: true,
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startTime', 'endTime'],
          val: [
            moment()
              .subtract(6, 'd')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date'
        },
        {
          title: '退出拦截弹窗Pv/uv',
          key: 'exitInterceptPopPvUv'
        },
        {
          title: '领取按钮点击pv/uv',
          key: 'acquireButtonClickPvUv'
        },
        {
          title: '领取成功用户数',
          key: 'acquireSuccessUser'
        },
        {
          title: '极速提现观看广告用户数',
          key: 'watchAdUser'
        },
        {
          title: '极速提现成功用户数',
          key: 'withdrawSuccessUser'
        },
        {
          title: '拦截弹窗-领取',
          key: 'popAcquireRate'
        },
        {
          title: '领取成功-极速提现成功转化',
          key: 'acquireWithdrawRate'
        }
      ]
    }
  },
  mounted() {
    const localActive = sessionStorage.getItem('physicalReturnListTabActive')
    if (localActive) {
      this.activeName = localActive
    } else {
      this.activeName = '1'
    }
  },
  methods: {
    handUpload() {
      switch (this.activeName) {
        case '1':
          window.location.href = EXPORT_COUNT_RETURN_DEVICE({
            ...this.listQuery,
            token: this.$store.getters.authorization
          })
          break
        case '2':
          window.location.href = EXPORT_COUNT_RETURN_USER({
            ...this.listQuery,
            token: this.$store.getters.authorization
          })
          break
      }
    },
    handleClick(tab) {
      sessionStorage.setItem('physicalReturnListTabActive', tab.name)
      this.listQuery.startTime = moment().subtract(6, 'days').format('YYYY-MM-DD')
      this.listQuery.endTime = moment().format('YYYY-MM-DD')
    }
  }
}
</script>

<style scoped>
</style>
