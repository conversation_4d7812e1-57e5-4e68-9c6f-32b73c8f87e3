import { put, get, post } from '@/libs/axios.package'

/*
 * 获取版本号列表
 * */
export const getVersionListApi = obj => {
  return get('/version/list', obj, null)
}
/*
 * 新增版本
 * */
export const setVersionApi = obj => {
  return post('/version', obj, null)
}
/*
 * 获取版本详情
 * */
export const getVersionDetailApi = obj => {
  return get('/version/' + obj, null)
}
/*
 * 修改版本详情
 * */
export const editVersionDetailApi = obj => {
  return put('/version', obj, null)
}

/*
 * ios AB面控制
 * */
export const GET_APP_CHANNE = obj => {
  return get('appChanne/list', obj)
}
/*
 * ios AB面控制 详情
 * */
export const GET_APP_CHANNE_DETAIL = id => {
  return get(`appChanne/${id}`)
}

/*
 * ios AB面控制 修改
 * */
export const POST_APP_CHANNE_DETAIL = obj => {
  return post(`appChanne/update`, obj)
}

/*
 * ios AB面控制 添加
 * */
export const POST_ADD_APP_CHANNE = obj => {
  return post(`appChanne/add`, obj)
}
