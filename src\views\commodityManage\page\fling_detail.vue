<template>
  <div>
    <ycTitle style="margin: 20px 0;" />
    <div style="margin:50px auto;">
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="300px"
        class="demo-ruleForm"
      >
        <el-form-item label="商品选择：" prop="thirdGoodsId">
          <el-button
            plain
            size="small"
            type="success"
            :disabled="type=='edit'"
            @click="openStore"
          >请选择</el-button>
          <span style="color:#888888;font-size:12px">{{ choiceName }}</span>
        </el-form-item>
        <el-form-item label="选择分类：" prop="goodsTypeIds">
          <el-cascader
            v-model="ruleForm.goodsTypeIds"
            placeholder="请选择"
            :options="casCadeData"
            :props="{ expandTrigger: 'hover' }"
            clearable
          />
        </el-form-item>
        <el-form-item label="商品名称：" prop="name">
          <el-input
            v-model="ruleForm.name"
            style="width:300px;"
            clearable
            placeholder="请输入商品名称"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="销售价格：" prop="price">
          <el-input
            v-model="ruleForm.price"
            autocomplete="off"
            style="width:300px;"
            placeholder="请输入销售价格"
          />
        </el-form-item>
        <el-form-item label="ios内购原价：" prop="iosSourcePrice">
          <el-input
            v-model="ruleForm.iosSourcePrice"
            autocomplete="off"
            style="width:300px;"
            placeholder="请输入ios内购原价"
          />
        </el-form-item>
        <el-form-item label="ios内购现价：" prop="iosPrice">
          <el-input
            v-model="ruleForm.iosPrice"
            autocomplete="off"
            style="width:300px;"
            placeholder="请输入ios内购现价"
          />
        </el-form-item>
        <el-form-item label="内购产品ID：" prop="iosProductId">
          <el-input
            v-model="ruleForm.iosProductId"
            autocomplete="off"
            style="width:300px;"
            placeholder="请输入内购产品ID"
          />
        </el-form-item>
        <!-- <el-form-item label="排序：" prop="sort">
          <el-input
            v-model.number="ruleForm.sort"
            autocomplete="off"
            style="width:300px;"
            placeholder="请输入排序内容,数字越大越靠前"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="是否上架：" prop="status">
          <el-radio v-model="ruleForm.status" label="up">上架</el-radio>
          <el-radio v-model="ruleForm.status" label="down">下架</el-radio>
        </el-form-item>

        <el-form-item style="margin-top: 40px">
          <el-button plain icon="el-icon-arrow-left" @click="onCallback()">返回</el-button>
          <el-button
            style="width:100px;"
            type="primary"
            :disabled="btn_disabled"
            @click="handUpdated('ruleForm')"
          >提交</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-dialog title="商品库" :visible.sync="dialogVisible" width="80%">
      <div style="width:90%;margin:0 auto;">
        <el-form :inline="true">
          <el-form-item label="商品名称">
            <el-input v-model="searchName" placeholder="请输入商品名称" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="multipleTable"
          :data="tableData"
          border
          style="width: 100%;margin-bottom: 30px;"
          highlight-current-row
          @row-click="handleSelectionChange"
        >
          <el-table-column prop="goodsNo" label="编号" />
          <el-table-column prop="name" label="名称" />
          <el-table-column prop="price" label="价格" />
          <el-table-column prop="officialPrice" label="官方价格" />
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 70, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ycTitle from '@/components/yc-title/title'
import { getCascade } from '@/api/goodsType'
import {
  getStoreList,
  addRechargeGodos,
  rechargeDetail,
  updateRechargeGodos
} from '@/api/goods'
import constant from '@/config/constant.conf'
import Store from '@/store'

export default {
  components: {
    ycTitle
  },
  data() {
    return {
      // headers: {},
      editId: '',
      type: '',
      // uploadUrl: "",
      choiceName: '',
      searchName: '',
      pageSize: 10,
      currentPage: 1,
      total: 0,
      tableData: [],
      casCadeData: [],
      dialogVisible: false,
      btn_disabled: false,
      ruleForm: {
        goodsTypeIds: [],
        name: '',
        thirdGoodsId: '',
        price: '', // 价格
        iosSourcePrice: '', // ios原价
        iosPrice: '', // ios现价
        iosProductId: '', // ios产品id
        status: 'down',
        tips: ''
      },
      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        goodsTypeIds: [{ required: true, message: '请选择', trigger: 'blur' }],
        price: [{ required: true, message: '价格不能为空', trigger: 'blur' }],
        iosSourcePrice: [{ required: true, message: 'ios原价不能为空', trigger: 'blur' }],
        iosPrice: [{ required: true, message: 'ios现价不能为空', trigger: 'blur' }],
        iosProductId: [{ required: true, message: 'ios产品id不能为空', trigger: 'blur' }],
        thirdGoodsId: [{ required: true, message: '请选择', trigger: 'blur' }],
        sort: [
          { required: true, message: '排序不能为空', trigger: 'blur' },
          { type: 'number', message: '排序为数字值' }
        ],
        status: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    to.meta.title = to.query.type === 'add' ? '添加直冲商品' : '编辑直冲商品'
    next()
  },
  mounted() {
    this.type = this.$route.query.type
    if (this.type === 'edit') {
      this.editId = this.$route.query.id
      this.getDetail()
    }
    // 上传地址
    // this.uploadUrl = constant.publicPath.api + "/upload/image";
    // 请求头
    // this.headers = {Authorization: `${Store.state.authorization}`};
    this.getCascadeData()
  },
  methods: {
    openStore() {
      this.dialogVisible = true
      this.search()
    },
    /**
     * 选择
     */
    handleSelectionChange(row, column, event) {
      this.ruleForm.thirdGoodsId = row.goodsNo
      this.choiceName =
        '已选择: ' + row.goodsNo + ' ---- ' + row.name + ' ---- ' + row.price
      this.dialogVisible = false
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.search()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.search()
    },
    /**
     * 搜索
     */
    search() {
      getStoreList({
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
        name: this.searchName,
        type: 'up'
      }).then(res => {
        if (res.code === 0) {
          this.tableData = res.data
          this.total = res.totalCount
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /**
     * 获取级联的数据
     */
    getCascadeData() {
      getCascade().then(res => {
        if (res.code === 0) {
          this.casCadeData = res.data
        }
      })
    },
    // beforeUpload(file) {
    //   //上传之前
    //   if (file.size > 2 * 1024 * 1024) {
    //     this.$message.error('上传图片大小不能超过2M!');
    //   }
    //   return file.size;
    // },
    // uploadSuccess: function (res, file, type) {
    //   //图片上传成功
    //   let thar = this;
    //   console.log(type,res)
    //   if (res.code === 0) {
    //     if (type === "coverPicture") {
    //       thar.ruleForm.coverPicture =  res.data;
    //     }
    //     if (type === "detailPicture") {
    //       thar.ruleForm.detailPicture =  res.data;
    //     }
    //   }
    // },
    // uploadError: function (res, file) {
    //   //图片上传失败
    //   this.$message.error('图片上传失败');
    // },
    // // 重新选取图片
    // CikUpload(refName,eleName) {
    //   document.getElementById(eleName).click();
    //   this.$refs[refName].clearFiles();
    // },
    /**
     * 提交表单
     */
    handUpdated(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.btn_disabled = true
          const param = this.ruleForm
          if (this.type === 'add') {
            addRechargeGodos(param).then(res => {
              this.btn_disabled = false
              if (res.code === 0) {
                this.$message.success('新增成功')
                this.onCallback()
              }
            })
          } else {
            param.id = this.editId
            updateRechargeGodos(param).then(res => {
              this.btn_disabled = false
              if (res.code === 0) {
                this.$message.success('编辑成功')
                this.onCallback()
              }
            })
          }
        }
      })
    },
    /**
     * 详情
     */
    getDetail() {
      rechargeDetail(this.editId).then(res => {
        if (res.code === 0) {
          this.ruleForm = res.data
          this.choiceName = res.data.thirdGoods
        }
      })
    },
    onCallback() {
      this.$router.push('/commodity/flingList')
    }
  }
}
</script>

<style scoped>
</style>
