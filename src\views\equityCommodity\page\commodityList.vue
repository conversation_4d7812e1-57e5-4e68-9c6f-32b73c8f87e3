<template>
  <!--  权益商品列表 -->
  <div style="padding-top: 20px;">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="(item,index) in tabs" :key="index" :label="item.label" :status="item.status" :name="item.name">
        <div slot="label">
          {{ item.label }}
          <span style="color:#FF7F50">( {{ item.count || 0 }} )</span>
        </div>
        <commodity_list ref="commodityList" :status="item.status" @refreshInit="init" />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import {
  goods_virtual_count
} from '@/api/equityCommodity'
export default {
  components: {
    commodity_list: () => import('../module/commodity-list.vue')
  },
  data() {
    return {
      tabs: [
        { label: '全部商品', name: '0', status: 'all', count: 0 },
        { label: '上架', name: '1', status: 'down', count: 0 },
        { label: '下架', name: '2', status: 'up', count: 0 }
      ],
      activeName: '0'
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    handleClick(name) {
      this.$refs.commodityList[this.activeName].getData()
    },
    init() {
      // 获取订单统计
      goods_virtual_count().then(res => {
        if (res.code === 0) {
          this.tabs.forEach(tab => {
            switch (tab.status) {
              case 'all': // 全部
                tab.count = res.data.all
                break
              case 'down':
                tab.count = res.data.down
                break
              case 'up':
                tab.count = res.data.up
                break
            }
          })
        }
        this.$refs.commodityList[this.activeName].getData()
      })
    }
  }
}
</script>

<style scoped>

</style>
