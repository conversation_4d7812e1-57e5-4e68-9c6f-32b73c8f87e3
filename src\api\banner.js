import {
  put,
  get,
  post,
  del
} from '@/libs/axios.package'

/*
* 添加banner
*
*/
export const add = obj => { return post('portal', obj) }

/*
* 修改anner
*
*/
export const update = obj => { return put('portal', obj) }

/*
* 详情
*
*/
export const detail = obj => { return get(`portal/${obj.id}`) }

/*
* 删除
*
*/
export const delBanner = id => { return del(`portal/${id}`) }

/*
* 列表
*
*/
export const list = obj => { return get('portal/list', obj) }

/*
* 栏目列表
*/
export const itemList = () => { return get('portal/item/list') }
/*
* 连接列表
*/
export const getLinkListApi = () => { return get('/portal/item/jumpTypeList', null) }
