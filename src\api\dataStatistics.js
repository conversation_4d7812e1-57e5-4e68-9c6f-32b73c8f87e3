import CONSTANT from '@/config/constant.conf'
import { get } from '@/libs/axios.package'
import qs from 'qs'
/*
 *   控制台统计查询接口
 * */

export const overview = () => {
  return get('overview', null)
}

/*
 *   首页图表数据(用户)接口
 * */

export const overview_chat_user = (obj) => {
  return get('overview/chat_user', {
    begin_at: obj.begin_at,
    end_at: obj.end_at,
    type: obj.type
  })
}

/*
 *   首页图表数据(留资)接口
 * */

export const overview_chat_loan = (obj) => {
  return get('overview/chat_loan', {
    begin_at: obj.begin_at,
    end_at: obj.end_at
  })
}

/*
 * 用户统计查询接口
 * */
export const dailyUsers = (obj) => {
  return get('overview/daily_users', {
    begin_at: obj.begin_at,
    end_at: obj.end_at,
    page: obj.page
  })
}
/*
 * 导出用户统计查询
 * */
export const exportUsers = 'overview/csv/daily_users'

/*
 * 充值交易明细接口
 * */
export const transactions = (obj) => {
  return get('overview/transactions', {
    mobile: obj.mobile,
    trade_no: obj.trade_no,
    begin_at: obj.begin_at,
    end_at: obj.end_at,
    status: obj.status,
    promoter_id: obj.promoter_id,
    page: obj.page
  })
}

/*
 * 获取所有推广员信息用于筛选
 * */
export const get_promoter = (obj) => {
  return get('overview/promoter', null)
}

/**
 * 销售统计
 */
export const salesList = (obj) => {
  return get('overview/sales_performances', {
    begin_at: obj.begin_at,
    end_at: obj.end_at,
    page: obj.page || 1
  })
}
/**
 * 渠道统计
 */
export const getChannelStatics = (obj) => {
  return get('/channelStatics/list', obj, false)
}
/**
 * 渠道统计详情
 */
export const getChannelStaticsDetail = (obj) => {
  return get('/channelStatics/listByCode', obj, false)
}

/**
 * 首次购买转换统计
 */
export const statistic_user_trans = (obj) => {
  return get('/statistic/user/trans', obj, false)
}

/*
banner统计
status	否	string	up(上架)down(下架)
title	否	string	例:2019-10-20
pageNumber	否	num	分页参数 number
pageSize
*/
export const banner_total = (obj) => {
  return get('/operation/list', obj)
}

/*
banner统计详情
status	否	string	up(上架)down(下架)
title	否	string	例:2019-10-20
pageNumber	否	num	分页参数 number
pageSize
*/
export const banner_Detail = (obj) => {
  return get('/operation/bannerDetail', obj)
}

/*
橱窗统计
title	否	string	标题
status	否	string	状态
pageNumber	否	num	分页参数 number
pageSize	否	num	分页参数 size
*/
export const windowList = (obj) => {
  return get('/operation/windowList', obj)
}

/*
橱窗统计 详情
windowId	否	string	标题
pageNumber	否	num	分页参数 number
pageSize	否	num	分页参数 size
*/
export const windowDetail = (obj) => {
  return get('/operation/windowDetail', obj)
}

/*
首页运营位统计
startDate	否	string	开始时间
endDate	否	string	结束时间
*/
export const indexOperateStatistic = (obj) => {
  return get('/operation/indexOperateStatistic', obj)
}

/*
大盘数据统计 1.4.1
*/
export const index_tapeData = (obj) => {
  return get('/index/tapeData', obj)
}
/*
数据看板统计 1.4.1
*/
export const GET_DATA_STATIS = (obj) => {
  return get('/index/statisticTable', obj)
}

export const index_tapeData_export = (data) =>
  CONSTANT.publicPath + '/index/tapeData/export?' + qs.stringify(data)
export const EXPORT_DATA_STATIS = (data) =>
  CONSTANT.publicPath + '/index/statisticExport?' + qs.stringify(data)
/**
 * 收益数据统计
 */
export const GET_ORDERSTATISTIC_PROFITCOUNT = (obj) => {
  return get('/orderStatistic/profitCount', obj)
}

export const EXPORT_ORDERSTATISTIC_EXPORTBYCODE = (data) =>
  CONSTANT.publicPath +
  '/orderStatistic/profitCountExport?' +
  qs.stringify(data)

/**
 * 收益数据统计
 */
export const GET_COUNT_LOGIN = (obj) => {
  return get('/index/countLogin', obj)
}

export const EXPORT_COUNT_LOGIN = (data) =>
  CONSTANT.publicPath + '/index/countLoginExport?' + qs.stringify(data)

/**
 * 收益数据统计
 */
export const GET_COUNT_LOGIN_DETAIL = (obj) => {
  return get(`/index/countLogin/detail`, obj)
}

export const EXPORT_COUNT_LOGIN_DETAIL = (data) =>
  CONSTANT.publicPath + `/index/countLogin/detailExport?` + qs.stringify(data)

/**
 * 收益数据统计
 */
export const GET_EARN_CASH = (obj) => {
  return get('/earncash/count', obj)
}

/**
 * 搜索统计
 */
export const GET_Goods_Search_Total = (obj) => {
  return get('goods/search/count/total', obj)
}

export const GET_Goods_Search_page = (obj) => {
  return get('goods/search/count/page', obj)
}

/**
 * 第三方专题统计
 */

export const GET_Special_Count = (obj) => {
  return get('/special/count', obj)
}

/**
 * 淘宝授权统计
 */

export const GET_Count_Authorize_List = (obj) => {
  return get('/count/authorize/list', obj)
}

export const GET_COUNT_GOODS_CATEGORY_LIST = (obj) => {
  return get('/goods/category/countGoodsCategory', obj)
}

export const GET_COUNT_GOODS_CATEGORY_DETAIL = (obj) => {
  return get('/goods/category/countGoodsCategory/detail', obj)
}

export const payConf_list = (obj) => {
  return get('/channel/payConf/list', obj)
}

/**  授权统计   */

export const get_count_authorizationse_page = (obj) => {
  return get(`/count/authorizations/page`, obj)
}

export const get_count_authorizations_exports = (data) =>
  CONSTANT.publicPath + '/count/authorizations/export?' + qs.stringify(data)

export const GET_NEW_USER_GUIDANCE = (obj) => {
  return get('/count/newuser/guidance/page', obj)
}

export const EXPORTS_NEW_USER_GUIDANCE = (data) =>
  CONSTANT.publicPath + '/count/newuser/guidance/export?' + qs.stringify(data)
export const exports_payConf_list = (data) =>
  CONSTANT.publicPath + '/channel/payConf/list/export?' + qs.stringify(data)

// 广告联盟下沉表接口
export const motivational_scene_activity = (obj) => {
  return get('/count/motivational/scene/activity', obj)
}
// 广告联盟下沉表接口导出
export const motivational_scene_activity_export = (data) =>
  CONSTANT.publicPath +
  '/count/motivational/scene/activity/export?' +
  qs.stringify(data)

// 商户号明细统计
export const channel_payConf_detail_list = (obj) => {
  return get('/channel/payConf/detail/list', obj)
}
// 商户号明细统计导出
export const channel_payConf_detail_list_export = (data) =>
  CONSTANT.publicPath +
  '/channel/payConf/detail/list/export?' +
  qs.stringify(data)

// 大盘数据统计
export const count_marketData_page = (obj) => {
  return get('/count/marketData/page', obj)
}
// 大盘数据统计导出
export const count_marketData_export = (data) =>
  CONSTANT.publicPath + '/count/marketData/export?' + qs.stringify(data)

// 增值下沉表
export const count_addvalue_list = (obj) => {
  return get('/count/addvalue/list', obj)
}
// 增值下沉表导出
export const count_addvalue_list_export = (data) =>
  CONSTANT.publicPath + '/count/addvalue/export?' + qs.stringify(data)

// 权益下沉表
export const count_marketData_rightsPage = (obj) => {
  return get('/count/marketData/rightsPage', obj)
}
// 权益下沉表导出
export const count_marketData_rightExport = (data) =>
  CONSTANT.publicPath + '/count/marketData/rightExport?' + qs.stringify(data)

// 自主退款链路监控
export const count_refund_page = (obj) => {
  return get('/count/refundLink/pageList', obj)
}
// 自主退款链路导出
export const count_refund_Export = (data) =>
  CONSTANT.publicPath +
  '/count/refundLink/pageList/export?' +
  qs.stringify(data)

// 投诉举报统计埋点
export const count_complaint_report_pageList = (obj) => {
  return get('/count/complaint/report/pageList', obj)
}
// 投诉举报统计埋点导出
export const count_complaint_report_export = (data) =>
  CONSTANT.publicPath +
  '/count/complaint/report/export?' +
  qs.stringify(data)

// 支付链路统计
export const countPayLinkList = (obj) => {
  return get('/count/payment/link/pageList', obj)
}

// 投诉举报统计埋点导出
export const PayLinkListExport = (data) =>
  CONSTANT.publicPath +
  '/count/payment/link/export?' +
  qs.stringify(data)

// 支付链路统计
export const countPayLinkDetail = (obj) => {
  return get('/count/payment/link/detail/list', obj)
}

// 投诉举报统计埋点导出
export const PayLinkDetailExport = (data) =>
  CONSTANT.publicPath +
  '/count/payment/link/detail/export?' +
  qs.stringify(data)

// 商品搜索统计
export const countCrowdSearchPage = (obj) => {
  return get('/count/crowd/search/page', obj)
}

// 商品搜索统计导出
export const countCrowdSearchExport = (data) =>
  CONSTANT.publicPath +
  '/count/crowd/search/export?' +
  qs.stringify(data)
// 登录数据监控表
export const loginBehaviorList = (obj) => {
  return get('/count/login/behavior/list', obj)
}
// 登录数据监控表导出
export const loginBehaviorExport = (data) =>
  CONSTANT.publicPath +
  '/count/login/behavior/export?' +
  qs.stringify(data)
