<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 14:33:57
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-06-03 16:54:46
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="渠道管理">
      <div slot="searchContainer" style="display: inline-block">
        <el-button
          plain
          icon="el-icon-circle-plus-outline"
          type="primary"
          size="small"
          @click="handleAdd"
        >新增模型</el-button>
      </div>

    </page>
    <el-drawer
      v-if="drawer"
      :visible.sync="drawer"
      direction="rtl"
      size="50%"
      :with-header="false"
      :wrapper-closable="false"
    >
      <div class="close_button">
        <i class="el-icon-close" @click="drawer=false" />
      </div>
      <div class="drawer_package">
        <div class="drawer_title">
          <span>模型编辑/新增</span>
        </div>
        <div class="addForm_package">
          <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="180px" class="demo-ruleForm">
            <div class="form_view">
              <div class="form_view_title">
                <div class="title_line" /><span>基础信息</span>
              </div>
              <el-form-item label="模型所属平台" prop="platform" :rules="addRules.common">
                <el-select v-model="addForm.platform" placeholder="请选择模型所属平台">
                  <el-option
                    v-for="item in siteIds"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="模型名称" prop="name" :rules="addRules.common" :style="{display: 'inline-block'}">
                <div :style="{width: '200px'}">
                  <el-input v-model="addForm.name" placeholder="请输入模型名称" maxlength="30" />
                </div>
              </el-form-item>
              <span :style="{padding: '0 0 0 10px'}">{{ addForm.name.length }}/30</span>
              <el-form-item label="模型调用节点" prop="endPointId" :rules="addRules.common">
                <div :style="{width: '200px'}">
                  <el-input v-model="addForm.endPointId" placeholder="请输入模型调用节点" />
                </div>
              </el-form-item>
              <div>
                <el-form-item label="VOLC_ACCESSKEY" prop="accessKey" :rules="addRules.common" :style="{display: 'inline-block'}">
                  <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                    VOLC_ACCESSKEY
                    <el-tooltip
                      content="调用模型的密钥，对应模型后台获取"
                      placement="top-start"
                      :style="{color:'#409eff'}"
                    >
                      <i class="el-icon-question" style="font-size: 14px" />
                    </el-tooltip>
                  </span>
                  <div :style="{width: '200px'}">
                    <el-input v-model="addForm.accessKey" placeholder="请输入模型后台获取密钥" maxlength="200" type="textarea" />
                  </div>
                </el-form-item>
                <span :style="{padding: '0 0 0 10px'}">{{ addForm.accessKey.length }}/200</span>
              </div>
              <div>
                <el-form-item label="VOLC_SECRETKEY" prop="secretKey" :rules="addRules.common" :style="{display: 'inline-block'}">
                  <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                    VOLC_SECRETKEY
                    <el-tooltip
                      content="调用模型的密钥，对应模型后台获取"
                      placement="top-start"
                      :style="{color:'#409eff'}"
                    >
                      <i class="el-icon-question" style="font-size: 14px" />
                    </el-tooltip>
                  </span>
                  <div :style="{width: '200px'}">
                    <el-input v-model="addForm.secretKey" placeholder="请输入模型后台获取密钥" maxlength="200" type="textarea" />
                  </div>
                </el-form-item>
                <span :style="{padding: '0 0 0 10px'}">{{ addForm.secretKey.length }}/200</span>
              </div>
            </div>

            <div class="form_view">
              <div class="form_view_title">
                <div class="title_line" /><span>模型状态</span>
              </div>
              <el-form-item label="状态" prop="status" :rules="addRules.common">
                <el-switch
                  v-model="addForm.status"
                  :active-value="1"
                  :inactive-value="0"
                />
              </el-form-item>
            </div>
            <div :style="{'text-align': 'right', width: '100%'}" class="view_button">
              <el-button @click="drawer=false">取消</el-button>
              <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { undertakeList, addOrUpdate, undertakeListexport, modelRepositorypage, modelRepositoryplatform, modelRepositoryexit } from '@/qjjpApi/operate'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      addForm: {
        id: '',
        platform: '',
        name: '',
        endPointId: '',
        accessKey: '',
        secretKey: '',
        status: 1
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      drawer: false,
      list4: [
        {
          id: 1,
          name: '会员用户'
        },
        {
          id: 0,
          name: '非会员用户'
        }
      ],
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      list2: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ],
      list3: [
        {
          id: 1,
          name: '落地页'
        }
      ],
      siteIds: [],
      listQuery: {
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await modelRepositoryplatform().then(res => {
              if (res.code === 200) {
                if (res.data && res.data.length) {
                  this.siteIds = res.data
                }
              }
            })
          }

          const list = await modelRepositorypage(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '模型平台',
          key: 'platform',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'code'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '模型名称',
          type: formItemType.input,
          search: true,
          clearable: true,
          searchKey: 'name',
          tableHidden: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '模型所属平台',
          key: 'platformStr'
        },
        {
          title: '模型名称',
          key: 'name'
        },
        {
          title: '模型调用节点',
          key: 'endPointId'
        },
        {
          title: 'VOLC_ACCESSKEY',
          key: 'accessKey',
          renderHeader: (...args) => this.renderHeaders(...args, ['调用模型的密钥，对应模型后台获取'])
        },
        {
          title: 'VOLC_SECRETKEY',
          key: 'secretKey',
          renderHeader: (...args) => this.renderHeaders(...args, ['调用模型的密钥，对应模型后台获取'])
        },
        {
          title: '更新人员',
          key: 'updateUser'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required']
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',

              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.addForm = JSON.parse(JSON.stringify(params))
                this.drawer = true
              }
            }
          ]
        }
      ]
    }
  },
  async mounted() {
    // const list1 = await messageStyleList()
    // const { records, total } = list1.data
    // let dataList = []
    // console.info(records, 'records')
    // if (records && records.length) {
    //   dataList = [total, ...records]
    // }
    // const result = {
    //   data: dataList
    // }
    // console.info(result, 'result')
  },
  created() {},
  methods: {
    renderHeaders(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h('div', { slot: 'content' }, [textArr.map(text => h('div', null, text))]),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    },
    handMessageStyleListAdd(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          modelRepositoryexit({ ...this.addForm }).then(res => {
            if (res.code == 200) {
              this.drawer = false
              this.$message.success('操作成功')
              this.$store.dispatch('tableRefresh', this)
            }
          })
        }
      })
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = undertakeListexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    reloadAddform() {
      this.addForm = {
        id: '',
        platform: '',
        name: '',
        endPointId: '',
        accessKey: '',
        secretKey: '',
        status: 1
      }
    },
    handleAdd() {
      this.reloadAddform()
      this.drawer = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
    overflow: scroll;
    // padding-bottom: 20px;
    padding: 0 30px 20px;
    position: relative;
    /* overflow-x: auto; */
}
::v-deep .el-drawer__header{
  span{
    font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
  }

}
::v-deep .el-drawer__body{

}
.close_button{
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    background-color: rgb(0, 0, 0,1);
    text-align: center;
    cursor: pointer;
i{
  color: white;
  line-height: 40px;
}
  }
.drawer_package{
  height: 100%;
  position: relative;

  .drawer_title{
    padding: 10px 20px 5px;
    vertical-align: middle;
    span{
      font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
    }

}
}

.addForm_package{
  background-color: rgb(189, 184, 184,0.1);
  padding: 15px;
  height: 100%;
  .demo-ruleForm{
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    position: relative;
    .view_button{
      position: absolute;
      bottom: 0;
    }
  }
}
.view_button{
          background-color: #ffffff;
          padding: 20px;
          border-top: 1px dashed #000000;
          ::v-deep .el-button{
            margin: 0 10px;
          }
        }
.form_view{
        // margin: 0 0px 20px;
        background-color: #ffffff;
        // border: 1px solid rgba(0,0,0,0.2);
        width: 100%;
        padding: 15px;
        border-radius: 5px;

        .form_view_title{
            margin-bottom: 20px;
            .title_line{
              width: 2px;
              height: 10px;
              background-color:#66b1ff ;
              display: inline-block;
              vertical-align: middle;
            }
            span{
              padding-left: 5px;
              vertical-align: middle;
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
    }
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
