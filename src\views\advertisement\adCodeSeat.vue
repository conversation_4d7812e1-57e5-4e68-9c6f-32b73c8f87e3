<template>
  <div>
    <page v-if="show" :request="request" :list="list" table-title="广告位管理">
      <div slot="searchContainer" style="display: inline-block">
        <el-button
          plain
          type="primary"
          size="small"
          icon="el-icon-circle-plus"
          @click="handAdd"
        >新增</el-button>
      </div>
    </page>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="40%">
      <addAdvert
        v-if="dialogVisible"
        :id="id"
        :type="listQuery.activityType"
        :params="params"
        :task-type="taskType"
        @close="close"
      />
    </el-dialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import addAdvert from '@/views/advertisement/components/addAdvert'

import { get_code_seat_manager_page, get_ad_list } from '@/api/advertisement'
import { tableItemType, formItemType } from '@/config/sysConfig'
export default {
  name: 'AdCodeSeat',
  components: {
    page,
    addAdvert
  },
  props: {},
  data() {
    return {
      listQuery: {},
      dialogVisible: false,
      dialogVisibleData: false,
      advertisementProviderList: [],
      title: '新增',
      currName: '',
      taskType: '',
      show: true,
      id: '',
      params: {},
      request: {
        getListUrl: get_code_seat_manager_page
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'id',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '名称',
          key: 'name',
          type: formItemType.input,
          search: true
        },
        {
          title: '广告应用',
          key: 'appId',
          type: formItemType.input
        },
        {
          title: '供应商名称',
          key: 'adName',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '广告类型',
          key: 'adTypeName',
          type: formItemType.select,
          tableHidden: true
        },
        {
          title: '供应商',
          key: 'adId',
          type: formItemType.select,
          list: this.advertisementProviderList,
          options: {
            placeholder: '请选择广告供应商'
          },
          search: true,
          tableHidden: true
        },
        {
          title: '设备系统',
          key: 'device',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          search: true,
          list: this.$utils.getOsList(),
          options: {
            placeholder: '请选择设备系统'
          }
        },
        {
          title: '代码位ID(新)',
          key: 'codeSeatNew',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '代码位ID(老)',
          key: 'codeSeatOld',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '用户类型时间',
          key: 'time',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '状态',
          key: 'status',
          list: [
            { label: '启用', value: true },
            { label: '停用', value: false }
          ],
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          options: {
            valueType: 'Number'
          }
        },
        {
          type: tableItemType.active,
          width: '150px',
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: ($index, item, params) => {
                this.title = '编辑'
                this.id = params.id
                this.params = params
                this.taskType = 'edit'
                this.dialogVisible = true
              }
            }
          ]
        }
      ]
    }
  },
  watch: {},
  created() {
    get_ad_list().then(res => {
      if (res.code == 200 && res.data) {
        this.advertisementProviderList = res.data.map(item => {
          return {
            ...item,
            label: item.name,
            value: item.id
          }
        })
      }
    })
  },
  methods: {
    handAdd() {
      this.title = '新增'
      this.taskType = 'add'
      this.dialogVisible = true
    },
    close() {
      this.show = false
      this.dialogVisible = false
      this.$nextTick(() => {
        this.show = true
      })
    }
  }
}
</script>

<style lang="" scoped></style>
