<template>
  <div>
    <page :request="request" :list="list" table-title="用户列表">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import {tableItemType, formItemType} from '@/config/sysConfig'
import {device_list, userlistexport} from '@/qjjpApi/user'
import {count_channel_application_list, mediaAll} from '@/qjjpApi/NewChannel'

// 注册来源 0:app 1：站外落地页
export const registerFromMap = {
  0: 'app',
  1: '落地页'
}

export default {
  name: 'qjjpDeviceList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      siteIds: [],
      mediaList: [],
      listQuery: {},
      list5: [
        {
          name: 'ios',
          id: 1
        },
        {
          name: 'Android',
          id: 2
        },
        {
          name: '小程序',
          id: 3
        }
      ],
      list6: [
        {
          name: '手机号短信注册',
          id: 1
        },
        {
          name: '手机号一键登录注册',
          id: 2
        },
        {
          name: '微信注册',
          id: 3
        }
      ],
      request: {
        getListUrl: async data => {
          await count_channel_application_list().then(res => {
            if (res.code === 200) {
              this.siteIds = res.data
            }
          })
          await mediaAll().then(res => {
            if (res.code === 200) {
              this.mediaList = res.data
            }
          })
          this.listQuery = {...this.listQuery, ...data}
          const list = await device_list(this.listQuery)
          const {records, total} = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteName'
        },
        {
          title: '市场包',
          key: 'platform'
        },
        {
          title: '设备来源',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list5,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '注册方式',
          key: 'accountType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list6,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '注册来源',
          key: 'registerFrom',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: Object.keys(registerFromMap).map(key => {
            return {
              label: registerFromMap[key],
              value: key
            }
          })
        },
        {
          title: '投放平台',
          key: 'apiCode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.mediaList,
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '渠道id',
          key: 'channelCode',
          search: true,
          type: formItemType.input,
          tableHidden: true
        },
        {
          title: '设备Id',
          key: 'deviceId',
          search: true,
          type: formItemType.input
        },
        {
          title: '用户Id',
          key: 'userCode',
          search: true,
          type: formItemType.input
        },
        {
          title: '渠道id',
          key: 'channelCode'
        },
        // {
        //   title: '用户类型',
        //   key: 'userType'
        // },
        // {
        //   title: '会员身份',
        //   key: 'memberIdentity'
        // },
        {
          title: '用户手机号',
          key: 'mobileNo',
          search: true,
          type: formItemType.input
        },
        {
          title: '设备来源',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list5,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          tableHidden: true,
          clearable: true
        },
        {
          title: '注册方式',
          key: 'accountType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list6,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          tableHidden: true,
          clearable: true
        },
        {
          title: '注册来源',
          key: 'registerFrom',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: Object.keys(registerFromMap).map(key => {
            return {
              label: registerFromMap[key],
              value: key
            }
          }),
          search: true,
          tableHidden: true,
          clearable: true
        },
        {
          title: '用户昵称',
          key: 'userName'
        },
        {
          title: '用户头像',
          key: 'headImg',
          type: formItemType.upload,
          render: (h, params) => {
            const data = params.data.row
            return data.headImg
              ? h(
                  'viewer',
                  {
                    class: {
                      'img-wrap': true
                    }
                  },
                  [
                    h('img', {
                      attrs: {
                        src: data.headImg
                      },
                      style: {
                        maxWidth: '40px',
                        maxHeight: '40px'
                      }
                    })
                  ]
                )
              : h('span', '-')
          }
        },
        {
          title: '车牌号',
          key: 'licensePlateNumber'
        },
        {
          title: '车架号',
          key: 'vin'
        },
        {
          title: '车辆残值',
          key: 'residualVehicleValue'
        },
        {
          title: '姓名',
          key: 'realName'
        },
        {
          title: '身份证号',
          key: 'idCardNumber'
        },
        {
          title: '所在城市',
          key: 'cityName'
        },
        {
          title: '房产',
          key: 'houseStr',
          width: 120
        },
        {
          title: '车产',
          key: 'carStr',
          width: 120
        },
        {
          title: '公积金',
          key: 'gjjScopStr',
          width: 120
        },
        {
          title: '社保',
          key: 'sbScopStr',
          width: 120
        },
        {
          title: '性别',
          key: 'sex'
        },
        {
          title: '年龄',
          key: 'age'
        },
        {
          title: '三要素校验',
          key: 'certifyFlagStr'
        },
        {
          title: '车牌校验状态',
          key: 'cardNumberStatusStr'
        },
        {
          title: '人车一致校验状态',
          key: 'carOfPersonStatusStr'
        },

        {
          title: '期望借款金额',
          key: 'wishLoansAmount',
          width: 120
        },
        {
          title: '期望还款周期',
          key: 'wishRepaymentCycle',
          width: 120
        },
        {
          title: '车辆状态',
          key: 'vehicleStatus',
          width: 120
        },
        {
          title: '设备创建时间',
          key: 'deviceCreateTime',
          width: 120
        },
        {
          title: '资方存证状态',
          key: 'moCertificationStr',
          width: 120
        },
        {
          title: '资方存证来源',
          key: 'moCertificationFromStr',
          width: 120
        },
        {
          title: '机构存证状态',
          key: 'inCertificationStr',
          width: 120
        },
        {
          title: '机构存证来源',
          key: 'inCertificationFromStr',
          width: 120
        },
        {
          title: '用户注册时间',
          key: 'registerTime',
          width: 120
        },
        {
          title: '用户登录时间',
          key: 'lastLoginTime',
          width: 120
        },
        {
          title: '用户注销时间',
          key: 'logoutTime',
          width: 120
        }
      ]
    }
  },
  created() {},
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = userlistexport({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
