import { get, post/*, put*/ } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'
/**
 * 开放管理
 */
// 分页查询媒体数据
export const mediaDeliveryAccountPage = obj => {
  return get('/mediaDeveloperAccount/page', obj)
}
// 新增或修改媒体开发者账号
export const addOrUpdate = obj => {
  return post('/mediaDeveloperAccount/addOrUpdate', obj)
}
// 导出
export const mediaDeveloperAccount_export = data =>
  CONSTANT.publicPath + '/mediaDeveloperAccount/export?' + qs.stringify(data)
