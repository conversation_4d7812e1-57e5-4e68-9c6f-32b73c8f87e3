import { getJjjp, postJjjp } from '@/libs/axios.package'

export const getQaQuestionList = obj => {
  return getJjjp('/cms/qa/question/page', obj)
}
export const QaQuestionAddOrUpdate = obj => {
  return postJjjp('/cms/qa/question/addOrUpdate', obj)
}
export const ownUploadImg = obj => {
  return postJjjp('/cms/upload/image', obj)
}
export const aliUploadImg = obj => {
  return postJjjp('/cms/ali/complaint/record/complainImage', obj)
}
// 问答详情
export const getQaQuestionDetail = id => {
  return getJjjp(`/cms/qa/question/detail/${id}`)
}

// 查询方案列表
export const getPlanList = obj => {
  return getJjjp(`/cms/qa/plan/list`, obj)
}
// 新增或修改方案
export const addOrUpdate = obj => {
  return postJjjp(`/cms/qa/plan/addOrUpdate`, obj)
}
// 查询方案下的问答
export const getPlanQuestionList = obj => {
  return getJjjp(`/cms/qa/plan/list/planQuestion`, obj)
}
// 删除方案
export const deletePlan = id => {
  return getJjjp(`/cms/qa/plan/delete/${id}`)
}
// 查询方案未添加的问答
export const getQuestionsList = obj => {
  return getJjjp(`/cms/qa/plan/list/questions`, obj)
}
// 新增方案下的问答
export const addPlanQuestion = obj => {
  return postJjjp(`/cms/qa/plan/addQuestion`, obj)
}
export const getCategoryList = obj => {
  return getJjjp(`/cms/qa/plan/category/list`, obj)
}
// 删除方案下的问答
export const delQuestion = obj => {
  return getJjjp(`/cms/qa/plan/delQuestion`, obj)
}
// 新增或修改分类
export const addCategoryOrUpdate = obj => {
  return postJjjp(`/cms/qa/plan/category/addOrUpdate`, obj)
}
// 删除分类
export const deleteCategory = id => {
  return getJjjp(`/cms/qa/plan/category/del/${id}`)
}
// 分类排序
export const categorySort = obj => {
  return postJjjp(`/cms/qa/plan/category/sort`, obj)
}
export const sortQuestion = obj => {
  return postJjjp(`/cms/qa/plan/sortQuestion`, obj)
}
