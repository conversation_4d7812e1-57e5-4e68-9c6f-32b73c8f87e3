<template>
  <div>
    <el-row class="search-row" type="flex">
      <el-col class="search-col">
        时间：
        <el-date-picker
          v-model="parameterObj.startDate"
          value-format="yyyy-MM-dd"
          format="yyyyMMdd"
          :clearable="false"
          type="date"
          placeholder="选择开始日期"
        />
        <span style="padding: 0px 10px">--</span>
        <el-date-picker
          v-model="parameterObj.endDate"
          style="padding: 0px 10px 0px 0px"
          value-format="yyyy-MM-dd"
          format="yyyyMMdd"
          type="date"
          :clearable="false"
          placeholder="选择截止日期"
        />
        渠道ID：
        <el-input
          v-model="parameterObj.channelCode"
          class="search-maxInput"
          clearable
          placeholder="请输入渠道ID"
          @keyup.enter.native="getAccount('new')"
        />
        设备：
        <el-select
          v-model="parameterObj.os"
          class="search-maxInput"
          clearable
          placeholder="请选择设备"
        >
          <el-option
            v-for="(item, key) in osList"
            :key="key"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button
          type="primary"
          class="search-btn"
          icon="el-icon-search"
          style="margin-left:10px"
          @click="getData('new')"
        >
          查询
        </el-button>
        <el-button
          plain
          type="warning"
          size="small"
          icon="el-icon-download"
          @click="handExport"
          >导出数据</el-button
        >
      </el-col>
    </el-row>
    <section>
      <el-table
        ref="multipleTable"
        v-loading="DataLoading"
        :max-height="tableHeight"
        class="current-table"
        :data="tableData"
        border
        lazy
        row-key="levelId"
        :load="load"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :cell-style="cellStyle"
        :span-method="spanMethod"
        :row-class-name="tableRowClassName"
        @select="handleSelectionChange"
        @expand-change="handleExpandChange"
      >
        <!-- @selection-change="handleSelectionChange"  -->

        <el-table-column type="selection" :selectable="selectableMethods" />

        <el-table-column prop="channelName" label="渠道名称" width="200">
          <template slot-scope="scope">
            <!-- <div
              v-if="scope.row.type == 2 && !scope.row.total"
              style="display:inline-block"
            >
              <el-checkbox v-model="checked"></el-checkbox>
            </div> -->
            <div style="display:inline-block">
              {{ scope.row.channelName }}
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="codeSeatName" label="代码位名称" /> -->
        <!-- <el-table-column prop="codeSeatNum" label="代码位ID" /> -->
        <el-table-column prop="cpm" label="CPM" />
        <el-table-column prop="earnings" label="广告收益" />
        <el-table-column prop="request" label="请求量" />
        <el-table-column prop="response" label="返回量" />
        <el-table-column prop="showPv" label="曝光量" />
        <el-table-column prop="totalShowPv" label="总爆光量">
          <template slot-scope="scope">
            <div v-if="scope.row.type != 3" style="display:inline-block">
              {{ scope.row.totalShowPv }}
            </div>
            <div v-else style="display:inline-block">
              --
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="click" label="点击量" />
        <el-table-column prop="fillRate" label="填充率" />
        <el-table-column prop="showRate" label="展示率" />
        <el-table-column prop="clickRate" label="点击率" />
        <el-table-column prop="date" label="日期" />
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 100, 200, 500, 1000, 5000]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </section>
  </div>
</template>

<script>
import {
  get_count_advertising_channel,
  export_count_advertising_channel
} from '@/api/appChannel'
import moment from 'moment'

export default {
  name: 'ChannelCount',
  data() {
    return {
      tableHeight: 600,
      total: 0,
      pageSize: 10,
      currentPage: 1,
      multipleSelection: [],
      osList: this.$utils.getOsList(),
      parameterObj: {
        startDate: moment()
          .subtract(3, 'days')
          .format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        type: 1,
        channelCode: '',
        os: '',
        scene: '',
        codeSeatNum: ''
      },
      tableData: [],
      loadingFlag: false,
      DataLoading: false,
      nodetree: [],
      columns: [
        {
          id: 'el-table_1_column_1'
        },
        {
          id: 'el-table_1_column_2',
          property: 'channelName',
          label: '渠道名称'
        },
        {
          id: 'el-table_1_column_3',
          property: 'channelCode',
          label: '渠道Code'
        },
        {
          id: 'el-table_1_column_4',
          property: 'codeSeatName',
          label: '代码位名称'
        },
        {
          id: 'el-table_1_column_5',
          property: 'codeSeatNum',
          label: '代码位ID'
        },
        {
          id: 'el-table_1_column_6',
          property: 'cpm'
        },
        {
          id: 'el-table_1_column_7',
          property: 'earnings',
          label: '广告收益'
        },
        {
          id: 'el-table_1_column_8',
          property: 'request',
          label: '请求量'
        },
        {
          id: 'el-table_1_column_9',
          property: 'response',
          label: '返回量'
        },
        {
          id: 'el-table_1_column_10',
          property: 'totalShowPv',
          label: '总曝光量'
        },
        {
          id: 'el-table_1_column_11',
          property: 'showPv',
          label: '曝光量'
        },
        {
          id: 'el-table_1_column_12',
          property: 'click',
          label: '点击量'
        },
        {
          id: 'el-table_1_column_13',
          property: 'fillRate',
          label: '填充率'
        },
        {
          id: 'el-table_1_column_14',
          property: 'showRate',
          label: '展示率'
        },
        {
          id: 'el-table_1_column_15',
          property: 'clickRate',
          label: '点击率'
        },
        {
          id: 'el-table_1_column_16',
          property: 'date',
          label: '日期'
        }
      ]
    }
  },
  created() {
    this.getData()
  },
  mounted() {
    this.$nextTick(() => {
      this.tableHeight = document.body.offsetHeight - 310
    })
  },
  methods: {
    handExport() {
      const data = {
        ...this.parameterObj
      }
      window.location.href = export_count_advertising_channel({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    getUuid() {
      return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = (Math.random() * 16) | 0
        var v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    },
    handleSelectionChange(selection, row) {
      this.multipleSelection = selection
      this.getSummaries(selection, row)
    },
    handleExpandChange(row, expanded) {
      if (expanded) {
        const choose = row.keyLevel
        const data = this.nodetree[choose].slice(
          0,
          this.nodetree[choose].length
        )
        data.forEach(row => {
          let flag = true
          this.multipleSelection.forEach(item => {
            if (item.levelId == row.levelId) {
              flag = false
            }
          })
          if (flag) {
            this.$refs.multipleTable.toggleRowSelection(row)
            this.multipleSelection.push(row)
            this.getSummaries(this.multipleSelection, row)
          }
        })
      }
    },
    getData(type) {
      // 获取菜单列表
      if (type === 'new') {
        this.loadingFlag = true
        this.currentPage = 1
      }
      const isBeforeTime = moment(this.parameterObj.startDate).isBefore(
        moment(this.parameterObj.startDate)
      )
      if (isBeforeTime) {
        this.$message.error('截止时间不能早于开始时间！')
        return
      }
      this.DataLoading = true
      get_count_advertising_channel({
        ...this.parameterObj,
        pageNumber: this.currentPage,
        pageSize: this.pageSize
      }).then(res => {
        this.loadingFlag = false
        this.DataLoading = false
        if (res.code === 0) {
          this.nodetree = []
          this.multipleSelection = []
          this.tableData = res.data.map((item, key) => {
            item.levelId = this.getUuid()
            item.keyLevel = key
            if (this.parameterObj.type == 1) {
              item.hasChildren = true
            }
            return item
          })
          // this.tableData.push({
          //   levelId: this.getUuid(),
          //   channelName: '总计',
          //   cpm: '',
          //   earnings: '',
          //   request: '',
          //   response: '',
          //   showPv: '',
          //   click: '',
          //   fillRate: '',
          //   showRate: '',
          //   clickRate: '',
          //   total: true
          // })
          this.total = res.totalCount
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },
    delParameter() {
      // 清空查询参数内容
      this.parameterObj = this.$utils.removeObj(this.parameterObj)
    },
    selectableMethods(row, index) {
      return row.type == 2 && !row.total
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 指定列号
        return 'text-align:left;font-weight:bold;'
      } else {
        return ''
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.type == 2) {
        return 'success-row'
      } else if (row.type == 3) {
        return 'warn-row'
      }
      return ''
    },
    load(tree, treeNode, resolve) {
      // 默认第二层
      let params = { type: 2, channelCode: tree.channelCode }
      // 如果有代码为codeSeatNum，则表示请求的是第三层
      if (tree.codeSeatNum) {
        params = {
          type: 3,
          codeSeatNum: tree.codeSeatNum,
          channelCode: tree.channelCode
        }
      }
      params.startDate = tree.date
      params.endDate = tree.date
      get_count_advertising_channel(params).then(res => {
        if (res.code == 200) {
          const data = res.data.map(item => {
            item.levelId = this.getUuid()
            // 第二层
            if (params.type == 2) {
              item.hasChildren = true
              item.keyLevel = tree.keyLevel
              item.channelName = `${item.codeSeatName}(${item.codeSeatNum})`
            } else {
              // 第三层
              item.channelName = `场景-${item.scene}`
              // item.codeSeatName =
            }
            return item
          })
          if (params.type == 2) {
            data.push({
              levelId: this.getUuid(),
              channelName: '总计',
              cpm: '',
              earnings: '',
              request: '',
              response: '',
              showPv: '',
              click: '',
              fillRate: '',
              showRate: '',
              clickRate: '',
              type: 2,
              total: true
            })
            this.nodetree[tree.keyLevel] = data
          }
          resolve(data)
          //   const data = res.data.map(item => {
          //     return item
          //   })
          //
        }
      })
    },

    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.type == 3) {
        // if (column.property == 'codeSeatName') {
        //   return {
        //     rowspan: 1,
        //     colspan: 2
        //   }
        // }
        // if (column.property == 'codeSeatNum') {
        //   return {
        //     rowspan: 0,
        //     colspan: 0
        //   }
        // }
      }
    },
    // 计算总数
    getSummaries(data, row) {
      const chooseLevel = row.keyLevel
      const obj = {}
      data = data.filter(item => item.keyLevel == chooseLevel)
      this.columns.forEach((column, index) => {
        if (index === 0) {
          obj[column.property] = '合计'
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          obj[column.property] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          obj[column.property] = 'N/A'
        }
        obj.fillRate =
          obj.request == 0
            ? '0%'
            : ((obj.response / obj.request) * 100).toFixed(2) + '%'
        obj.showRate =
          obj.response == 0
            ? '0%'
            : ((obj.showPv / obj.response) * 100).toFixed(2) + '%'
        obj.clickRate =
          obj.showPv == 0
            ? '0%'
            : ((obj.click / obj.showPv) * 100).toFixed(2) + '%'
        obj.codeSeatNum = 'N/A'
        obj.channelName = '总计'
      })
      obj['levelId'] = this.getUuid()
      if (this.nodetree[chooseLevel]) {
        this.nodetree[chooseLevel][this.nodetree[chooseLevel].length - 1] = obj
        this.$set(
          this.$refs.multipleTable.store.states.lazyTreeNodeMap,
          this.tableData[chooseLevel].levelId,
          this.nodetree[chooseLevel]
        )
      }
    }
  }
}
</script>

<style scoped lang="scss">
.titleName {
  font-weight: bold;
}
.current-table {
  margin: 30px 0;
  position: relative;
  z-index: 10;
}
</style>
<style lang="scss">
.el-table {
  .total-row {
    background-color: #f5f7fa;
  }
  .success-row {
    background-color: #f0f9eb;
  }
  .warn-row {
    background-color: #f8e9cc;
  }
}
</style>
