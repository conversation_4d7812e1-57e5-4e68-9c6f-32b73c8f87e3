/*
* 权益商品管理子路由
* */

const equityCommodity = [
  {
    path: '/equityCommodity/classifyManage',
    name: 'equityCommodity_classifyManage',
    meta: {
      title: '权益商品分类管理'
    },
    component: () => import('@/views/equityCommodity/page/classifyManage')
  },
  {
    path: '/equityCommodity/supplierManage',
    name: 'equityCommodity_supplierManage',
    meta: {
      title: '供货商管理'
    },
    component: () => import('@/views/equityCommodity/page/supplierManage')
  },
  {
    path: '/equityCommodity/commodityBank',
    name: 'equityCommodity_commodityBank',
    meta: {
      title: '权益商品库'
    },
    component: () => import('@/views/equityCommodity/page/commodityBank')
  },
  {
    path: '/equityCommodity/commodityType',
    name: 'equityCommodity_commodityType',
    meta: {
      title: '权益商品订单类型管理'
    },
    component: () => import('@/views/equityCommodity/page/commodityType')
  },
  {
    path: '/equityCommodity/commodityList',
    name: 'equityCommodity_commodityList',
    meta: {
      title: '权益商品列表'
    },
    component: () => import('@/views/equityCommodity/page/commodityList')
  },
  {
    path: '/equityCommodity/add_commodity',
    name: 'equityCommodity_add_commodity',
    meta: {
      title: '新增权益商品',
      parentTitle: '权益商品列表',
      activeMenu: '/equityCommodity/commodityList'
    },
    component: () => import('@/views/equityCommodity/page/add_commodity')
  },
  {
    path: '/equityCommodity/statistics',
    name: 'equityCommodity_statistics',
    meta: {
      title: '权益商品统计'
    },
    component: () => import('@/views/equityCommodity/page/statistics')
  }
]

export default equityCommodity
