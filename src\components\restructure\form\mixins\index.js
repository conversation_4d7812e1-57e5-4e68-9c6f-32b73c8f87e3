import { copy } from '@/config/basicsMethods'
import { keyWord } from '@/config/sysConfig'

export default {
  methods: {
    submitForm() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            const resultForm = copy(this.resData)
            for (const i in resultForm) {
              for (const item in this.formItemList) {
                const current = this.formItemList[item]
                if (i === current.key) {
                  const data = resultForm[i]
                  if (current.childKey && this.basics.isArray(current.childKey)) {
                    current.childKey.forEach((item, index) => {
                      resultForm[item] = data[index] || ''
                    })
                    delete resultForm[i]
                  } else if (this.basics.isArray(data)) {
                    resultForm[i] = data.join(keyWord.relatedWords)
                  }
                }
              }
            }
            resolve(resultForm)
          } else {
            reject(false)
          }
        })
      })
    }
  }
}
