<template>
  <page v-if="isShow" :request="request" :list="list" table-title="激励场景统计">
    <div slot="searchContainer" style="display: inline-block">
      <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import {tableItemType, formItemType} from '@/config/sysConfig'
import {
  get_count_motivational_scene_type,
  get_count_motivational_scene,
  export_count_motivational_scene
} from '@/api/advertisement'
import moment from 'moment'
import {count_channel_application_list} from '@/api/NewChannel'

export default {
  name: 'MotivationalScene',
  components: {
    page
  },
  props: {},
  data() {
    return {
      scenesList: [],
      currentScenes: null,
      isShow: false,
      listQuery: {
        fromDevice: '0',
        startDate: moment().subtract(5, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        siteId: '',
        scenes: 10
      },
      request: {
        getListUrl: async data => {
          this.listQuery = {...this.listQuery, ...data}
          if (!this.siteIds.length) {
            await count_channel_application_list().then(res => {
              if (res.code === 0) {
                if (res.data && res.data.length) {
                  this.siteIds = res.data.concat([
                    {
                      siteId: '',
                      siteName: '全部'
                    }
                  ])
                  this.listQuery.siteId = res.data[0].siteId
                }
              }
            })
          }
          return Promise.all([get_count_motivational_scene(this.listQuery)]).then(res => {
            return Promise.resolve(res[0])
          })
        }
      },
      siteIds: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [moment().subtract(5, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          key: 'os',
          title: '设备',
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: [
            {label: 'android', value: 'android'},
            {label: 'ios', value: 'ios'}
          ],
          type: formItemType.select,
          options: {
            placeholder: '请选择可见设备'
          },
          search: true
        },
        {
          title: '场景',
          key: 'scenes',
          type: formItemType.select,
          tableHidden: true,
          search: true,
          list: this.scenesList,
          multiple: true,
          options: {
            placeholder: '请选择场景'
          }
        },
        // {
        //   title: '申请原因',
        //   key: 'applyReason',
        //   type: formItemType.select,
        //   list: auditReason,
        //   search: true,
        //   multiple: true,
        //   tableHidden: true
        // },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: this.listQuery.siteId,
          search: true,
          // clearable:false,
          tableHidden: true
        },
        {
          title: '查询维度',
          key: 'fromDevice',
          type: formItemType.select,
          tableHidden: true,
          search: true,
          list: [
            {
              label: '用户',
              value: '0'
            }, {
              label: '设备',
              value: '1'
            }
          ],
          val: '0',
          options: {
            placeholder: '请选择查询维度'
          }
        },
        {
          title: '日期',
          key: 'date',
          type: formItemType.input
        },
        {
          title: '激活渗透（总/新）',
          key: 'activatePenetration',
          renderHeader: (h, {column}) => {
            return h('div', [
              h('span', column.label),
              h(
                'el-tooltip',
                {
                  props: {
                    content: h('div', {}, [
                      h('p', {}, `激活渗透（总）=广告观看用户数（总）/设备激活（总）`),
                      h('p', {}, `激活渗透（新）=广告观看用户数（新）/设备激活（新）`)
                    ])
                  }
                },
                [
                  h('i', {
                    class: 'el-icon-question',
                    style: 'color:#409eff;margin-left:5px;font-size: 16px;'
                  })
                ]
              )
            ])
          }
        },
        {
          title: ' 登录渗透（总/新）',
          key: 'loginPenetration',
          renderHeader: (h, {column}) => {
            return h('div', [
              h('span', column.label),
              h(
                'el-tooltip',
                {
                  props: {
                    content: h('div', {}, [
                      h('p', {}, `登录渗透总=广告观看用户总/登录用户总`),
                      h('p', {}, `登录渗透新=广告观看用户新/登录用户新`)
                    ])
                  }
                },
                [
                  h('i', {
                    class: 'el-icon-question',
                    style: 'color:#409eff;margin-left:5px;font-size: 16px;'
                  })
                ]
              )
            ])
          }
        },
        {
          title: '活动访问(总/新)',
          key: 'page'
        },
        {
          title: '广告观看用户(总/新)',
          key: 'adShowUv'
        },
        {
          title: '广告观看次数(总/新)',
          key: 'adShowPv'
        },
        {
          title: '渗透率老',
          key: 'oldPermeability'
        },
        {
          title: '渗透率新',
          key: 'newPermeability'
        },
        {
          title: '点击率老',
          key: 'oldClickRate'
        },
        {
          title: '点击率新',
          key: 'newClickRate'
        },
        {
          title: '人均次数老',
          key: 'oldPerCapita'
        },
        {
          title: '人均次数新',
          key: 'newPerCapita'
        },
        {
          title: '登录人均（总/新）',
          key: 'logInPerCapita'
        },
        {
          title: '广告收益（总/新）',
          key: 'adEarnings'
        },
        {
          title: '广告补贴金额（总/新）',
          key: 'adSubsidyAmountTotalNew',
          renderHeader: (...args) => {
            return this.showTipsHandler(...args, '广告补贴金额=万人拼场景用户购买商品商品订单中看广告减价金额（商品订单中减价金额字段）+红包提现场景用户看视频提现金额+话费场景用户看视频充值减价金额')
          }
        },
        {
          title: '广告净收益（总/新）',
          key: 'adIncomingTotalNew',
          renderHeader: (...args) => {
            return this.showTipsHandler(...args, '广告净收益=广告收益（总/新）-广告补贴金额（总/新）')
          }
        },
        {
          title: 'arpu值（总/新）',
          key: 'arpu'
        },
        {
          title: 'ecpm（总/新）',
          key: 'ecpm'
        }
      ]
    }
  },
  created() {
    get_count_motivational_scene_type()
      .then(res => {
        this.scenesList = res.data.map((item, key) => {
          if (key == 0) {
            this.currentScenes = 0
            this.listQuery.scenes = 0
          }
          return {
            ...item,
            label: item.name,
            value: item.code
          }
        })
        this.isShow = true
      })
      .catch(() => {
        this.isShow = true
      })
  },
  methods: {
    showTipsHandler(h, { column }, content) {
      const arr = []
      if (typeof content === 'string') {
        arr.push(content)
      } else if (Array.isArray(content)) {
        arr.push(...content)
      }
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h(
            'div',
            {
              slot: 'content'
            },
            [arr.map(item => h('div', null, item))]
          ),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = export_count_motivational_scene({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
