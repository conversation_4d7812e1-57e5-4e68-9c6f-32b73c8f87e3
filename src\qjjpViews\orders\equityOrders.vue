<template>
  <div>
    <page ref="equityGoodsWarehouse" :request="request" :list="list" table-title="权益订单">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
        <el-button plain type="success" size="small" icon="el-icon-download"
          @click="$downloadFile('https://pictures.jingximh.net/%E4%BC%9A%E5%91%98%E6%9D%83%E7%9B%8A%E8%AE%A2%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx', '权益订单模板')">下载模板</el-button>
        <el-button plain type="info" size="small" icon="el-icon-upload2" @click="uploadClick">导入</el-button>
      </div>
    </page>
  </div>
</template>
<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
// import { count_channel_application_list } from '@/yjgjApi/NewChannel'
import { thirdGoodsPage, syncThirdGoods, thirdGoodsCreate, thirdGoodsUpdate, supplierList, equityActivitiesCreate } from '@/qjjpApi/operate'
import { thirdGoodsOrderPageList, thirdGoodsOrderExport, thirdGoodsOrderUpdateOrder } from '@/qjjpApi/equityCommodity'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'equityOrders',
  components: {
    page
  },
  data() {
    return {
      transStatusList: [
        {
          label: '待发货',
          value: 'paid'
        },
        {
          label: '充值完成',
          value: 'finished'
        },
        {
          label: '充值失败',
          value: 'fail'
        }
      ],

      listQuery: {
        startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      supplierListData: [],
      siteIds: [],
      request: {
        getListUrl: async data => {
          // if (!this.siteIds.length) {
          //   await count_channel_application_list().then(res => {
          //     if (res.code === 200) {
          //       if (res.data && res.data.length) {
          //         this.siteIds = res.data
          //       }
          //     }
          //   })
          // }
          if (this.supplierListData.length == 0) {
            const { data: supplierListData } = await supplierList()
            this.supplierListData = supplierListData.map(n => ({ label: n.name, value: n.code }))
          }
          this.listQuery = { ...this.listQuery, ...data }
          const list = await thirdGoodsOrderPageList(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'id'
        },
        {
          key: 'createTimeString',
          title: '日期',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          search: true,
          pickerDay: 30,
          val: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        },
        // {
        //   title: '应用名称',
        //   key: 'siteId',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.siteIds,
        //   listFormat: {
        //     label: 'name',
        //     value: 'siteId'
        //   }
        // },
        {
          title: '应用名称',
          key: 'siteName'
        },
        {
          title: '订单编号',
          key: 'orderNo',
          type: formItemType.input,
          // tableView: tableItemType.tableView.text,
          search: true,
          clearable: true
        },
        {
          title: '设备ID',
          key: 'deviceCode',
          type: formItemType.input,
          search: true,
          clearable: true
        },
        {
          title: '设备来源',
          key: 'os',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [
            {
              label: 'ios',
              value: '1'
            },
            {
              label: 'android',
              value: '2'
            }
          ]
        },
        {
          title: '用户账号',
          key: 'userAccount',
          type: formItemType.input,
          search: true,
          clearable: true
        },
        {
          title: '用户ID',
          key: 'userCode',
          type: formItemType.input,
          search: true,
          clearable: true
        },
        {
          title: '渠道ID',
          key: 'channelCode',
          type: formItemType.input,
          search: true,
          clearable: true
        },
        {
          title: '供应商',
          key: 'supplier',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.supplierListData,
          search: true,
          clearable: true
        },
        {
          title: '权益商品名称',
          key: 'goodsName',
          type: formItemType.input,
          search: true,
          clearable: true
        },
        {
          title: '第三方订单',
          key: 'thirdOrderNo'
        },
        {
          title: '官方价',
          key: 'goodsOfficialPrice'
        },
        {
          title: '第三方售价',
          key: 'goodsPrice'
        },
        {
          title: '订单金额',
          key: 'amount'
        },
        {
          title: '充值方式',
          key: 'exchangeTypeString'
        },
        {
          title: '充值账号',
          key: 'exchangeNo'
        },
        {
          title: '状态',
          key: 'transStatusString'
        },
        {
          title: '状态',
          key: 'transStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.transStatusList,
          listFormat: {
            label: 'label',
            value: 'value'
          },
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              hidden(params) {
                return params.transStatus != 'paid'
              },
              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                console.log(params)
                this.editDetailFn(params)
              }
            }
          ]
        }
        // {
        //   title: '商户平台',
        //   key: 'way',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.list1,
        //   listFormat: {
        //     label: 'name',
        //     value: 'id'
        //   },
        //   val: this.listQuery.siteId,
        //   reg: ['required'],
        //   search: true,
        //   clearable: true
        // },
      ]
    }
  },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = thirdGoodsOrderExport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    editDetailFn(params) {
      this.$confirm('', '提示', {
        confirmButtonText: '充值成功',
        cancelButtonText: '充值失败',
        distinguishCancelAndClose: true,
        center: true,
        callback: (action) => {
          if (action == 'cancel') {
            this.fiaFn(params.id)
          }
          if (action == 'confirm') {
            this.editSuccessFn(params.id)
          }
        }
      })
    },
    editSuccessFn(id) {
      this.$prompt('第三方订单', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'cancel') {
            done()
          } else {
            if (!instance.inputValue || instance.inputValue == '') {
              this.$message.error('请输入第三方订单号')
              return
            }
            done()
          }
        }
      }).then(({ value }) => {
        thirdGoodsOrderUpdateOrder({ thirdGoodsOrderId: id, orderStatus: 'finished', thirdOrderNo: value }).then(res => {
          if (res.code == 200) {
            this.$message.success('操作成功')
            this.$refs.equityGoodsWarehouse.getSubSuccess()
          }
        })
      })
    },
    fiaFn(id) {
      this.$confirm(`更改为充值失败后不可再对订单进行更改<br>注：可联系用户对应权益充值失败，支持重新换一个权益领取`, '提示', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        cancelButtonText: '取消',
        center: true
      })
        .then(() => {
          thirdGoodsOrderUpdateOrder({ thirdGoodsOrderId: id, orderStatus: 'fail' }).then(res => {
            if (res.code == 200) {
              this.$message.success('操作成功')
              this.$refs.equityGoodsWarehouse.getSubSuccess()
            }
          })
          console.log(123)
        }).catch(() => {
          console.log(456)
        })
    },
    addDetail(type, params = {}) {
      this.$confirmDialog({
        options: {
          className: 'addCommodity',
          title: `${type == 'add' ? '添加' : '编辑'}话费`,
          width: '800px'
          // customModal: true,
          // customModalIndex: 1000
        },
        component: () => import('@/yjgjViews/equityCommodity/addCommodity'),
        params: {
          ...params
        }
      }).then(res => {
        if (res.action === 'confirm') {
          this.$refs.equityGoodsWarehouse.getSubSuccess()
        }
      })
    },
    uploadClick() {
      this.$confirmDialog({
        options: {
          className: 'uploadDialog',
          title: `导入权益订单`,
          width: '350px'
        },
        component: () => import('@/qjjpViews/orders/components/uploadDialog')
      }).then(res => {
        if (res.action === 'confirm') {
          this.$confirmDialog({
            options: {
              className: 'previewTable',
              width: '1200px'
            },
            component: () => import('@/qjjpViews/orders/components/previewTable'),
            params: {
              row: res.params
            }
          }).then(rest => {
            if (rest.action === 'confirm') {
              this.$refs.equityGoodsWarehouse.getSubSuccess()
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>