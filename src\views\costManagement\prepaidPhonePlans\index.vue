<template>
  <div>
    <page :request="request" :list="list" table-title="套餐列表" />
    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="dialogOps">
      <vouchers :id="id" @close="close" @success="success" />
    </SDialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { prepaidPhonePlans_add, prepaidPhonePlans_upadte, prepaidPhonePlans_page } from '@/api/costManagement'
import moment from 'moment'
import SDialog from '@/components/restructure/dialog'
import vouchers from './components/vouchers'

export default {
  components: {
    page,
    SDialog,
    vouchers
  },
  data() {
    return {
      dialogFormVisible: false,
      dialogOps: {
        // title: '关联券',
        width: '650px'
      },
      listQuery: {
        // startDate: moment().subtract(6, 'd').format('YYYY-MM-DD'),
        // endDate: moment().format('YYYY-MM-DD'),
        plansStatus: 1
      },
      request: {
        getListUrl: data => {
          this.listQuery = { ...this.listQuery, ...data }
          return prepaidPhonePlans_page(this.listQuery)
        },
        insertHttp: data => {
          return prepaidPhonePlans_add(data)
        },
        updateHttp: data => {
          return prepaidPhonePlans_upadte(data)
        }
      },
      id: ''
    }
  },
  computed: {
    list() {
      return [
        {
          title: '套餐ID',
          key: 'id',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '话费面值',
          key: 'facePrice',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          list: [
            {
              label: '50',
              value: 50
            },
            {
              label: '100',
              value: 100
            },
            {
              label: '200',
              value: 200
            }
          ],
          reg: ['required'],
          tableHidden: true
        },
        {
          title: '话费面值',
          key: 'facePrice',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '充多多成本价',
          key: 'costPrice',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '粘鱼成本价',
          key: 'glueFish',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '陆优成本价',
          key: 'lyCost',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '净蓝成本价',
          key: 'jlCost',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '心链成本价',
          key: 'xlCost',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '润鼎成本价',
          key: 'rdCost',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '会员用户售价',
          key: 'sellingPrice',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '普通用户售价',
          key: 'notVipPrice',
          type: formItemType.input,
          reg: ['required']
        },

        {
          title: '展示位置',
          key: 'placement',
          list: [
            { value: 1, label: '话费活动' },
            { value: 2, label: '会员退款' }
          ],
          search: true,
          tableView: tableItemType.tableView.text,
          type: formItemType.select
        },
        {
          title: '排序',
          key: 'plansSort',
          type: formItemType.input,
          reg: ['required']
        },
        {
          key: 'createTime',
          title: '创建日期',
          render: (h, params) => {
            const data = params.data.row
            if (data.createTime) {
              return h('span', {}, moment(data.createTime).format('YYYY-MM-DD'))
            } else {
              return h('span', {}, '--')
            }
          },
          formHidden: true
        },
        // {
        //   key: 'createTime',
        //   title: '创建日期',
        //   type: formItemType.datePickerDaterangeGai,
        //   options: {
        //     format: 'YYYY-MM-DD',
        //     valueFormat: 'yyyy-MM-dd'
        //   },
        //   childKey: ['startDate', 'endDate'],
        //   search: true,
        //   tableHidden: true,
        //   val: [moment().subtract(6, 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        // },
        {
          title: '状态',
          key: 'plansStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          val: 1,
          list: [
            {
              label: '启用',
              value: 1
            },
            {
              label: '禁用',
              value: 0
            }
          ],
          // options: {
          //   valueType: 'Number',
          // },
          search: true
        },
        {
          type: tableItemType.active,
          headerContainer: true,
          width: '180px',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.detailsDialog,
              theme: 'warning'
            },
            {
              text: '关联券',
              key: 'edit',
              type: tableItemType.activeType.detailsDialog,
              theme: 'warning',
              hidden: (params) => {
                return params.placement === 2
              },
              click: ($index, item, params) => {
                this.id = params.id
                this.dialogFormVisible = true
              }
            }
          ]
        }
      ]
    }
  },
  mounted() {},
  methods: {
    success() {
      this.$store.dispatch('tableRefresh', this)
      this.dialogFormVisible = false
    },
    close() {
      this.dialogFormVisible = false
    }
  }
}
</script>

<style scoped></style>
