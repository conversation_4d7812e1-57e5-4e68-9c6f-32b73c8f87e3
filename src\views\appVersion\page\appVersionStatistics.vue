<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.status"
      >
        <page v-if="reFresh" :request="request" :list="list" table-title="统计信息" :table-pagination-state="false">
          <div slot="searchContainer" style="display: inline-block">
            <el-button
              plain
              type="warning"
              size="small"
              icon="el-icon-download"
              @click="handExport"
            >导出数据
            </el-button>
          </div>
        </page>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import {
  GET_COUNTAPPUSER_LISTBYCODE,
  GET_COUNTAPPUSER_LISTBYDATE,
  EXPORT_COUNTAPPUSER_EXPORTBYCODE,
  EXPORT_COUNTAPPUSER_EXPORTBYDATE,
  GET_CHANNEL_LIST
} from '@/api/appVersion'
import moment from 'moment'

export default {
  components: {
    page
  },
  props: {},
  data() {
    return {
      tabs: [
        { label: '按应用市场统计', status: '0' },
        { label: '按日统计', status: '1' }
      ],
      channelList: [],
      reFresh: true,
      listQuery: {
        startDate: moment().format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      activeName: '0',
      request: {
        getListUrl: data => {
          return this.getData(data)
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [Number(this.activeName) ? moment().subtract(7, 'days').format('YYYY-MM-DD') : moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '应用市场',
          key: 'appCode',
          type: formItemType.select,
          tableHidden: true,
          list: this.channelList,
          search: !Number(this.activeName),
          formHidden: true
        },
        {
          title: '市场名称',
          key: 'appName',
          tableHidden: !!Number(this.activeName)
        },
        {
          title: '日期',
          key: 'dateNo',
          tableHidden: !Number(this.activeName)
        },
        {
          title: 'APP登录用户数(总/新)',
          render: (h, params) => {
            return h(
              'span',
              `${params.data.row.appLoginTotal}/${params.data.row.appLoginNew}`
            )
          }
        },
        {
          title: 'APP普通用户登录(总/新)',
          render: (h, params) => {
            return h(
              'span',
              `${params.data.row.commonLoginTotal}/${params.data.row.commonLoginNew}`
            )
          }
        },
        {
          title: '购卡成功用户数',
          key: 'vipCardUser'
        },
        {
          title: '购卡金额',
          key: 'vipCardMoney'
        },
        {
          title: '下单用户数(总/新)',
          render: (h, params) => {
            return h(
              'span',
              `${params.data.row.orderUserTotal}/${params.data.row.orderUserNew}`
            )
          }
        },
        {
          title: '普通用户下单(总/新)',
          render: (h, params) => {
            return h(
              'span',
              `${params.data.row.orderUserCommonTotal}/${params.data.row.orderUserCommonNew}`
            )
          }
        },
        {
          title: '返利订单数',
          key: 'rebateOrderCount'
        },
        {
          title: '返利订单金额',
          key: 'rebateOrderMoney'
        },
        {
          title: '返利商品订单收益',
          key: 'rebateOrderCommission'
        },
        {
          title: '权益商品订单数',
          key: 'rightGoodsOrder'
        },
        {
          title: '权益商品金额',
          key: 'rightGoodsMoney'
        },
        {
          title: '权益商品收益',
          key: 'rightGoodsBenefit'
        },
        {
          title: '复购率',
          key: 'repeatOrderRate'
        },
        {
          title: '首单率',
          key: 'firstOrderRate'
        }
      ]
    }
  },
  watch: {},
  mounted() {
    this.getChannelList()
  },
  methods: {
    getData(data) {
      this.listQuery = { ...this.listQuery, ...data }
      delete this.listQuery.pageSize
      delete this.listQuery.pageNumber
      if (this.activeName === '0') {
        return Promise.all([GET_COUNTAPPUSER_LISTBYCODE(this.listQuery)]).then(
          res => {
            return Promise.resolve(res[0])
          }
        )
      } else {
        return Promise.all([GET_COUNTAPPUSER_LISTBYDATE(this.listQuery)]).then(
          res => {
            return Promise.resolve(res[0])
          }
        )
      }
    },
    getChannelList() {
      GET_CHANNEL_LIST().then(res => {
        const dataType = res.data
        dataType.forEach((el, i) => {
          const obj = {
            label: '',
            value: ''
          }
          obj.label = el.title
          obj.value = el.appCode
          this.channelList.push(obj)
        })
      })
    },
    handleClick(tab) {
      this.activeName = tab.name
      if (this.activeName === '0') {
        this.listQuery.appCode = ''
        this.listQuery.startDate = moment().format('YYYY-MM-DD')
        this.listQuery.endDate = moment().format('YYYY-MM-DD')
      } else {
        this.listQuery.startDate = moment().subtract(7, 'days').format('YYYY-MM-DD')
        this.listQuery.endDate = moment().format('YYYY-MM-DD')
        delete this.listQuery.appCode
      }
      this.reFresh = false
      this.$nextTick(() => {
        this.reFresh = true
      })
    },
    handExport() {
      if (!Number(this.activeName)) {
        const data = {
          ...this.listQuery
        }
        window.location.href = EXPORT_COUNTAPPUSER_EXPORTBYCODE({ ...data, token: this.$store.getters.authorization })
      } else {
        const data = {
          ...this.listQuery
        }
        window.location.href = EXPORT_COUNTAPPUSER_EXPORTBYDATE({ ...data, token: this.$store.getters.authorization })
      }
    }
  }
}
</script>

<style lang="" scoped>
</style>
