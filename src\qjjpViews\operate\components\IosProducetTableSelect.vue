<template>
  <el-table
    :data="tableList"
    style="width: 100%"
    max-height="300"
    border
    class="product-table"
    @selection-change="selectionChange"
    @select-all="tableSelectMethods.selectAll()"
  >
    <el-table-column
      type="selection"
      width="55"
    />
    <el-table-column
      prop="productName"
      :label="`${lable}名称`"
    />
    <el-table-column
      prop="paymentPrice"
      :label="`${lable}价格`"
    />
    <el-table-column
      v-if="type==2"
      prop="subscriptionPeriod"
      :label="`${lable}体验周期`"
    >
      <template slot-scope="scope">{{ scope.row.subscriptionPeriod|subscriptionPeriodsStr }}</template>
    </el-table-column>
  </el-table>
</template>
<script>
export default {
  name: 'IosProducetTableSelect',
  props: {
    value: {
      type: [Number, String],
      default: ''
    },
    tableList: {
      type: Array,
      default: () => []
    },
    type: {
      type: [Number, String],
      default: '1'
    },
    experienceAppleInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      checkList: [],
      tableSelectMethods: {

        selectAll: data => {
          this.checkList = []
          this.$children[0].clearSelection()
        }
      }
    }
  },
  computed: {
    lable() {
      return this.type == 2 ? 'iOS体验商品' : 'iOS单卖商品'
    }
  },
  watch: {
    tableList: {
      handler(val) {
        if (val && val.length > 0) {
          this.$nextTick(() => {
            this.checkList = this.tableList.filter(item => item.id == this.value)
            this?.$children?.[0].toggleRowSelection(this.checkList[0], true)
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    selectionChange(selection) {
      this.checkList = [...selection.slice(-1)] // 取最新一条
      if (selection && selection.length > 0) {
        this.$children[0].toggleRowSelection(this.checkList[0], true)
        selection.forEach((item, index) => {
          if (index < selection.length - 1) { this.$children[0].toggleRowSelection(item, false) }
        })
      }
      this.$emit('input', this.checkList?.[0]?.id ?? '')
      this.$emit('update:experienceAppleInfo', this.checkList?.[0] ?? {})
    }
  }
}
</script>
<style lang="scss" scoped>
.product-table{
  &::v-deep{
    thead{
  .el-checkbox{
    display: none;
  }
}
  }
}

</style>
