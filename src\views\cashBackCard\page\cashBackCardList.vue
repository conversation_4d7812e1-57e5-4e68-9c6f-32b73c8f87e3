<template>
  <div>
    <page :request="request" :list="list" table-title="话费券列表">
      <div slot="searchContainer" style="display: inline-block;">
        <el-button plain type="primary" size="small" icon="el-icon-circle-plus-outline" @click="handAdd">新增</el-button>
      </div>
    </page>
    <SDialog :dialog-form-visible.sync="dialogFormVisible" :data="dialogOps">
      <cash-back-card-detail :card-id="Id" :type="type" @success="success" @close="close" />
    </SDialog>
    <SDialog :dialog-form-visible.sync="showManual" :data="dialogManualOps">
      <section>
        请输入用户ID：
        <el-input v-model="sendUserId" style="width: 300px" />
        (多个ID用英文逗号隔开)
        <div style="text-align: center;padding: 20px 0 0;">
          <el-button @click="closeManual">取消</el-button>
          <el-button type="primary" @click="sureManual">确定</el-button>
        </div>
      </section>
    </SDialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import SDialog from '@/components/restructure/dialog'
import cashBackCardDetail from '@/views/cashBackCard/modules/cashBackCardDetail'
import { GET_PRESENT_COUPON, MANUAL_SEND } from '@/api/cashBackCard'

export default {
  components: {
    page,
    cashBackCardDetail,
    SDialog
  },
  data() {
    return {
      type: '',
      Id: '',
      favoritesId: '',
      sendUserId: '',
      dialogOps: {
        title: '',
        width: '500px'
      },
      dialogManualOps: {
        title: '手动发送',
        width: '600px'
      },
      dialogFormVisible: false,
      showManual: false,
      request: {
        getListUrl: GET_PRESENT_COUPON
      },
      typeList: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '类型',
          key: 'couponType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [
            {
              label: '购物返利',
              value: 0
            },
            {
              label: '返现卡',
              value: 1
            }
          ]
        },
        {
          title: '名称',
          key: 'couponName',
          type: formItemType.input,
          search: true
        },
        {
          title: '券面额',
          key: 'couponAmount',
          type: formItemType.input
        },
        {
          title: '消费金额',
          key: 'couponLimitAmount',
          type: formItemType.select
        },
        {
          title: '有效时间',
          key: 'couponLimitData',
          type: formItemType.input
        },
        {
          title: '创建时间',
          key: 'createTime',
          type: formItemType.input
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          list: [
            { label: '启用', value: 0 },
            { label: '停用', value: 1 }
          ],
          type: formItemType.select,
          tableHidden: true
        },
        {
          title: '状态',
          key: 'status',
          list: [
            { label: '启用', value: 0 },
            { label: '停用', value: 1 }
          ],
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          options: {
            valueType: 'Number'
          }
        },
        {
          type: tableItemType.active,
          width: 260,
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.Id = params.id
                this.type = 'edit'
                this.dialogFormVisible = true
              },
              theme: 'warning'
            },
            {
              text: '手动发送',
              type: tableItemType.activeType.event,
              theme: 'success',
              click: ($index, item, params) => {
                this.Id = params.id
                this.showManual = true
              }
            }
          ]
        }
      ]
    }
  },
  mounted() {

  },
  methods: {
    handAdd() {
      this.type = 'add'
      this.dialogFormVisible = true
    },
    close() {
      this.dialogFormVisible = false
    },
    closeManual() {
      this.showManual = false
    },
    sureManual() {
      this.showManual = false
      MANUAL_SEND({
        couponId: this.Id,
        userIds: this.sendUserId
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('发送成功')
          this.success()
        }
      })
    },
    success() {
      this.$store.dispatch('tableRefresh', this)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
