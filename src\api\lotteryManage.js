import {
  get,
  post
} from '@/libs/axios.package'

import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/**
 * 奖品列表
 * */
export const GET_LOTTERY_LIST = obj => {
  return get('/api/prize', obj)
}

/**
 * 添加奖品列表
 * */
export const ADD_LOTTERY_LIST = obj => {
  return post('/api/prize', obj)
}


/**
 * 编辑奖品列表
 * */
export const EDIT_LOTTERY_LIST = obj => {
  return post(`/api/prize/${obj.id}`, obj)
}

/**
 * 奖品详情
 * */

export const GET_LOTTERY_LIST_DETAIL = obj => {
  return get(`api/prize/${obj.id}`, obj)
}

/**
 * 抽奖列表页
 * */
export const GET_LOTTERY_PAGE_LIST = obj => {
  return get('api/draw/prize', obj)
}

/**
 * 查询抽奖页
 * */
export const GET_LOTTERY_PAGE_LIST_DETAIL = obj => {
  return get(`api/draw/prize/${obj.id}`)
}
/**
 * 添加抽奖页
 * */
export const ADD_LOTTERY_PAGE_LIST = obj => {
  return post('api/draw/prize', obj)
}

/**
 * 编辑抽奖页
 * */

export const EDIT_LOTTERY_PAGE_LIST = obj => {
  return post(`/api/draw/prize/${obj.id}`, obj)
}

/**
 * 大盘数据
 * */
export const LOTTERY_STATICS = obj => {
  return get('/channel/dispatch/count',obj)
}





/**
 * 数据详情
 * */
export const LOTTERY_STATICS_DETAIL = obj => {
  return get('/channel/dispatch/count/info',obj)
}

/**
 * 抽奖数据详情
 * */
export const LOTTERY_PAGE_STATICS_DETAIL = obj => {
  return get(`/channel/dispatch/count/${obj.drawId}/${obj.createDate}`,obj)
}

/**
 * 抽奖管理/抽奖统计
 * */
export const LOTTERY_PAGE_STATICS = obj => {
  return get('/channel/dispatch/count/prize',obj)
}


/**
 * 大盘数据导出
 * */
export const LOTTERY_STATICS_EXPORT = obj => CONSTANT.publicPath + '/channel/dispatch/count/export?' + qs.stringify(obj)

/**
 * 抽奖管理/抽奖统计导出
 * */
export const LOTTERY_PAGE_STATICS_EXPORT = obj => CONSTANT.publicPath + '/channel/dispatch/count/prize/export?' + qs.stringify(obj)

/**
 * 抽奖数据详情导出
 * */
export const LOTTERY_PAGE_STATICS_DETAIL_EXPORT = obj => CONSTANT.publicPath + `/channel/dispatch/count/${obj.drawId}/${obj.createDate}/export?` + qs.stringify(obj)
/**
 * 数据详情导出
 * */
export const LOTTERY_STATICS_DETAIL_EXPORT = obj => CONSTANT.publicPath + '/channel/dispatch/count/info/export?' + qs.stringify(obj)
