import { get, post, put } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

export const post_ad = obj => {
  return post('/ad', obj)
}

export const put_ad = obj => {
  return put('/ad', obj)
}

export const get_ad_page = obj => {
  return get('/ad/page', obj)
}
export const post_application = obj => {
  return post('/application', obj)
}

export const put_application = obj => {
  return put('/application', obj)
}

export const get_application_page = obj => {
  return get('/application/page', obj)
}

export const post_code_seat_manager = obj => {
  return post('/code/seat/manager', obj)
}

export const put_code_seat_manager = obj => {
  return put('/code/seat/manager', obj)
}

export const get_code_seat_manager_page = obj => {
  return get('/code/seat/manager/page', obj)
}

export const post_rule = obj => {
  return post('/rule', obj)
}

export const put_rule = obj => {
  return put('/rule', obj)
}

export const get_rule_page = obj => {
  return get('/rule/page', obj)
}

export const get_code_seat_manager_list = obj => {
  return get('/code/seat/manager/list', obj)
}

export const get_rule_list = obj => {
  return get('/rule/list', obj)
}

export const get_ad_type_list = obj => {
  return get('/ad/type/list', obj)
}

export const get_ad_list = obj => {
  return get('/ad/list', obj)
}

export const get_application_list = obj => {
  return get('/application/list', obj)
}

export const get_strategy_list = obj => {
  return get('/strategy/list', obj)
}

export const get_type_list = obj => {
  return get('/ad/type/list', obj)
}

export const get_count_motivational_scene_type = obj => {
  return get('/count/motivational/scene/type', obj)
}

export const get_count_motivational_scene = obj => {
  return get('/count/motivational/scene', obj)
}

export const export_count_motivational_scene = data => CONSTANT.publicPath + '/count/motivational/scene/export?' + qs.stringify(data)

// 插屏广告统计-列表
export const countAdvertisingChannelInsertionScreenAdCount = obj => {
  return get('/count/advertising/channel/insertionScreen/adCount', obj)
}

// 插屏广告统计-列表 导出
export const countAdvertisingChannelInsertionScreenAdExport = data => CONSTANT.publicPath + '/count/advertising/channel/insertionScreen/adExport?' + qs.stringify(data)

// dsp统计
export const countDspPageList = obj => {
  return get('/count/dsp/pageList', obj)
}

// dsp供应商
export const dspSupplierListByName = obj => {
  return get('/dspSupplier/listByName', obj)
}

// dsp-供应商添加
export const addDspSupplier = obj => {
  return post('/dspSupplier/save', obj)
}

// dsp-供应商修改
export const updateDspSupplier = obj => {
  return post('/dspSupplier/update', obj)
}

// dsp-供应商删除
export const deleteDspSupplier = obj => {
  return get('/dspSupplier/' + obj)
}

// dsp-获得供应商列表
export const getDspSupplierList = obj => {
  return get('/dspSupplier/page', obj)
}

// dsp-获得供应商所有
export const getDspSupplierAll = obj => {
  return get('/dspSupplier/list', obj)
}
