<template>
  <div>
    <page :request="request" :list="list">
      <div slot="searchContainer" style="display: inline-block; margin-bottom: 10px">
        <div class="box">
          <el-button plain type="warning" size="small" icon="el-icon-upload" style="margin-right: 10px"
            @click="handUpload">导出数据</el-button>
        </div>
      </div>
    </page>
  </div>
</template>
<script>
import { undertakeSelector, getPayScene, count_channel_application_list } from '@/qjjpApi/NewChannel'
import { getCountPayDayListe, getCountPayDayListExport, getCountPayChannelList, getCountPayChannelListExport, getCountPaySceneDetail } from '@/qjjpApi/statisticsOverview'
import page from '@/components/restructure/page/v8'
import { formItemType, tableItemType } from '@/config/sysConfig'
import moment from 'moment'
import seachRequestList from './seachRequestList'
import paymentDetail from './components/paymentDetail.vue'
import { checkChannelLook } from '@/qjjpApi/system'
import {
  get_admin_list
} from '@/api/system'
export default {
  name: 'paymentScenario',
  components: {
    page,
    paymentDetail
  },
  data() {
    return {
      haveQX: false,
      adminList: null,
      loadingDetail: false,
      undertakeList: [],
      paySceneList: [],
      siteIdsList: [],
      hdsxList: [
        {
          id: 1,
          name: '会员权益活动'
        },
        {
          id: -1,
          name: '其他'
        }
      ],
      listQuery: {
        fixedDate: false,
        startDate: moment().subtract(6, 'd').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        ...this.$route.query
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          await checkChannelLook().then(async res => {
            if (res.code === 200) {
              this.haveQX = res.data
              if (res.data) {
                await get_admin_list({ pageSize: 1000 }).then(res => {
                  if (res.code === 0) {
                    this.adminList = res.data
                  }
                })
              }

            }
          })
          await count_channel_application_list().then(res => {
            if (res.code === 200) {
              this.siteIdsList = res.data
            }
          })
          if (this.undertakeList.length == 0) {
            undertakeSelector().then(res => {
              if (res.code === 200) {
                this.undertakeList = res.data
              }
            })
          }
          if (this.paySceneList.length == 0) {
            getPayScene().then(res => {
              if (res.code === 200) {
                this.paySceneList = res.data
              }
            })
          }
          const fn = this.$route.params.type == 'channelCodeDetail' ? getCountPayChannelList : getCountPayDayListe
          const list = await fn(this.formatData(this.listQuery))
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  created() {
    if (this.$route.params.type == 'channelCodeDetail') {
      console.log(this.$route.params)
    }
  },
  computed: {
    list() {
      const seachtList = seachRequestList.call(this, {
        undertakeList: this.undertakeList,
        paySceneList: this.paySceneList
      })
      return [
        {
          title: '日期',
          key: 'eventDate',
          fixed: 'left',
          options: {
            format: 'YYYYMMDD',
            valueFormat: 'yyyyMMdd'
          },
          tableHidden: this.$route.params.type == 'channelCodeDetail'
        },
        {
          title: '渠道ID',
          key: 'channelCode',
          fixed: 'left',
          tableHidden: this.$route.params.type != 'channelCodeDetail',
          render: (h, params) => {
            if (params.data.row.eventDate == '汇总') {
              return h('span', '汇总')
            }
            return h('span', params.data.row.channelCode)
          }
        },
        {
          title: '优化师',
          key: 'adminName',
          tableHidden: !this.listQuery.adminId
        },
        ...seachtList,
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          multiple: true,
          reg: ['required'],
          clearable: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        {
          title: '激活数',
          key: 'deviceActiveUv',
          isCustom: true,
          defaultShowKey: true,
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '激活设备数，按照设备去重')
          }
        },
        {
          title: '授权数',
          key: 'authorizationUv',
          isCustom: true,
          defaultShowKey: true,
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '键盘授权成功设备数，按照设备去重')
          }
        },
        {
          title: '生成用户数',
          key: 'chatUv',
          isCustom: true,
          defaultShowKey: true,
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '生成会话的用户数，按照设备去重')
          }
        },
        {
          title: '使用用户数',
          key: 'chatUsedUv',
          isCustom: true,
          defaultShowKey: true,
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '生成会话并点击发送的用户数，按照设备去重')
          }
        },
        {
          title: '达标用户数',
          key: 'noTrialUv',
          isCustom: true,
          defaultShowKey: true,
          renderHeader: (h, { column }) => {
            return this.renderHeaders(h, column, '体验次数=0的用户数，按照设备去重')
          }
        },
        {
          isCustom: true,
          title: '用户行为数据',
          tableHidden: false,
          key: 'basics',
          children: [
            {
              isCustom: true,
              title: '场景访问次数',
              key: 'activityPagePv',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, `不同支付场景的场景访问埋点规则如下：\n
                1、站内承接场景：站内落地页首个页面的曝光\n
                2、首页主推场景：冷启动首页曝光\n
                3、首页次推场景：首页曝光\n
                4、定制人设场景：定制人设首个页面曝光\n
                5、打卡会员场景：打卡会员活动页曝光\n
                6、人设市场场景：人设市场列表页曝光\n
                7、会员中心场景：会员中心页曝光\n
                8、首页待支付banner场景：首页曝光\n
                9、待支付场景：待支付页曝光\n
                10、端内键盘-引导使用场景：引导使用页曝光\n
                11、端内键盘-拦截场景：端内拦截付费弹窗曝光\n
                12、端外键盘-引导使用场景：端外键盘曝光\n
                13、端外键盘-拦截场景：端外拦截付费弹窗曝光\n
                14、卸载拦截场景：卸载拦截弹窗曝光\n
                15、复登场景：复登弹窗曝光`)
              }
            },
            {
              isCustom: true,
              title: '场景访问人数',
              key: 'activityPageUv',
              render: (h, params) => {
                return this.showTipsHandler(h, params, 'activityPageUv', '场景访问明细', 1)
              },
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '场景访问人数，按照设备去重')
              }
            },
            {
              isCustom: true,
              title: '人均访问次数',
              key: 'activityPageRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '场景平均访问次数，计算公式：场景访问次数/场景访问人数')
              }
            },
            {
              isCustom: true,
              title: '支付页曝光次数',
              key: 'payPagePv',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付页/支付弹窗曝光次数')
              }
            },
            {
              isCustom: true,
              title: '支付页曝光人数',
              key: 'payPageUv',
              render: (h, params) => {
                return this.showTipsHandler(h, params, 'payPageUv', '支付页曝光明细', 2)
              },
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付页/支付弹窗曝光人数，按照设备去重')
              }
            },
            {
              isCustom: true,
              title: '人均触达次数',
              key: 'activityPayPageRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付页人均触达次数，计算公式：支付页曝光次数/支付页曝光人数')
              }
            },
            {
              isCustom: true,
              title: 'SKU点击人数',
              key: 'skuClickUv',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付页点击切换SKU的人数，按照设备去重')
              }
            },
            {
              isCustom: true,
              title: '点击支付次数',
              key: 'payClickPv',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付页点击支付按钮的次数')
              }
            },
            {
              isCustom: true,
              title: '点击支付人数',
              key: 'payClickUv',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付页点击支付按钮的人数')
              }
            },
            {
              isCustom: true,
              title: '拉起支付次数',
              key: 'startPayPv',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付页拉起支付的次数，即订单创建数')
              }
            },
            {
              isCustom: true,
              title: '拉起支付人数',
              key: 'startPayUv',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付页拉起支付的人数，即订单创建数按照设备去重')
              }
            },
            {
              isCustom: true,
              title: '人均拉起支付次数',
              key: 'startPayRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '人均创建订单数，计算公式：拉起支付次数/拉起支付人数')
              }
            },
            {
              isCustom: true,
              title: '支付成功订单数',
              key: 'orderPv',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付成功订单数')
              }
            },
            {
              isCustom: true,
              title: '支付成功人数',
              key: 'orderUv',
              render: (h, params) => {
                return this.showTipsHandler(h, params, 'orderUv', '支付成功数明细', 3)
              },
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付成功人数，支付成功订单按照设备去重')
              }
            },
            {
              isCustom: true,
              title: '付费用户人均触达次数',
              key: 'orderPvRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '付费用户的支付页人均触达次数，计算公式：付费用户的支付页曝光次数/支付成功人数')
              }
            },
            {
              isCustom: true,
              title: '支付成功金额',
              key: 'orderAmount',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付成功订单金额')
              }
            },
            {
              isCustom: true,
              title: 'ecpm',
              key: 'ecpm',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付页千次展示收益，计算公式：支付成功金额/支付页曝光次数*1000')
              }
            },
            {
              isCustom: true,
              title: '客单价',
              key: 'orderAmountRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付成功订单均价，计算公式：支付成功金额/支付成功数')
              }
            },
            {
              isCustom: true,
              title: '激活付费平均时长',
              key: 'activePayAvgDuration',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '激活付费平均时长，计算公式：（订单支付成功时间-设备激活时间）/支付成功数')
              }
            },
            {
              isCustom: true,
              title: '达标付费平均时长',
              key: 'trialPayAvgDuration',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '达标付费平均时长，计算公式：（订单支付成功时间-设备达标时间）/支付成功数，达标前付费不计入')
              }
            }
          ]
        },
        {
          isCustom: true,
          title: '转化漏斗-支付场景侧',
          tableHidden: false,
          key: 'basicsPaySance',
          children: [
            {
              isCustom: true,
              title: '激活访问率',
              key: 'deviceActiveRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '激活访问率，计算公式：场景访问人数/激活数')
              }
            },
            {
              isCustom: true,
              title: '激活曝光率',
              key: 'deviceShowRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '激活曝光率，计算公式：支付页曝光人数/激活数')
              }
            },
            {
              isCustom: true,
              title: '访问曝光率',
              key: 'payPageShowRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '访问曝光率，计算公式：支付页曝光人数/场景访问人数')
              }
            },
            {
              isCustom: true,
              title: '曝光点击率',
              key: 'payPageClickRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '曝光点击率，计算公式：点击支付人数/支付页曝光人数')
              }
            },
            {
              isCustom: true,
              title: '发起支付率',
              key: 'payClickRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '发起支付率，计算公式：拉起支付人数/点击支付人数')
              }
            },
            {
              isCustom: true,
              title: '支付成功率',
              key: 'paySuccessRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '支付成功率，计算公式：支付成功人数/拉起支付人数')
              }
            },
            {
              isCustom: true,
              title: '激活付费率',
              key: 'devicePayRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '激活付费率，计算公式：支付成功人数/激活数')
              }
            },
            {
              isCustom: true,
              title: '访问付费率',
              key: 'activePayRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '访问支付率，计算公式：支付成功人数/场景访问人数')
              }
            },
            {
              isCustom: true,
              title: '达标付费率',
              key: 'noTrialPayRate',
              renderHeader: (h, { column }) => {
                return this.renderHeaders(h, column, '达标付费率，计算公式：达标支付成功人数/达标用户数')
              }
            }
          ]
        },
        ...(this.$route.params.type == 'channelCodeDetail' ? [] : [{
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          tooltip: false,
          key: 'operation',
          fixed: 'right',
          width: '100px',
          activeType: [
            {
              text: '渠道详情',
              key: 'edit1',
              type: tableItemType.activeType.event,
              theme: 'warning',
              hidden: params => {
                return params.eventDate == '汇总'
                // return false
              },
              click: ($index, item, params) => {
                this.$router.push({
                  path: '/qjjp/statisticsOverview/paymentScenario/channelCodeDetail',
                  query: { ...this.formatData(this.listQuery), ...{ startDate: params.eventDate, endDate: params.eventDate, adminId: params.adminId, edit: true } }
                })
              }
            }
          ]
        }])

      ]
    }
  },
  methods: {
    fixedDateChange() {
      this.$store.dispatch('tableRefresh', this)
    },
    formatData(data) {
      const params = JSON.parse(JSON.stringify(data))
      if (params.deviceType == '-1') {
        params.deviceType = ''
      }
      return params
    },
    handUpload() {
      const data = {
        ...this.formatData(this.listQuery)
      }
      const fn = this.$route.params.type == 'channelCodeDetail' ? getCountPayChannelListExport : getCountPayDayListExport
      window.location.href = fn({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    renderHeaders(h, column, text, icon, styleObj = {}) {
      if (text && text != '') {
        return (
          <div>
            <span style={styleObj}>{column.label}</span>
            <el-tooltip content={text}>
              <i class='el-icon-question' style='color:#409eff;margin-left:5px;font-size: 16px;'></i>
            </el-tooltip>
            <span style={styleObj}>{icon}</span>
          </div>
        )
      } else {
        return (
          <div>
            <span style={styleObj}>{column.label}{icon}</span>
          </div>
        )
      }
    },
    showTipsHandler(h, params, key, title, type) {
      const pageData = { params, key, title, type }
      return (<paymentDetail pageData={pageData} listQuery={this.formatData(this.listQuery)}></paymentDetail>)
    }
  }
}
</script>
<style lang="scss" scoped>
.checkFixedDate {
  margin-left: 20px;
}
</style>
