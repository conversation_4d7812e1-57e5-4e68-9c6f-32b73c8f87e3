<template>
  <div>
    <page
      v-if="show"
      :request="request"
      :list="list"
      table-title="渠道广告管理"
    >
      <div slot="searchContainer" style="display: inline-block">
        <el-button
          plain
          type="primary"
          size="small"
          icon="el-icon-circle-plus"
          @click="handAdd"
        >新增</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { get_channel_page } from '@/api/appChannel'
import { get_type_list } from '@/api/advertisement'
export default {
  name: 'channelAdList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      listQuery: {},
      dialogVisible: false,
      dialogVisibleData: false,
      title: '新增',
      currName: '',
      taskType: '',
      show: true,
      id: '',
      params: {},
      advertisementTypeList: [],
      request: {
        getListUrl: get_channel_page
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'id',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '名称',
          key: 'name',
          type: formItemType.input,
          search: true
        },
        {
          title: '广告类型',
          key: 'adTypeName',
          type: formItemType.input
        },
        {
          title: '广告类型',
          key: 'adType',
          type: formItemType.select,
          tableHidden: true,
          search: true,
          list: this.advertisementTypeList,
          options: {
            placeholder: '请选择广告类型'
          }
        },
        {
          title: '渠道ID',
          key: 'channelId',
          type: formItemType.input,
          search: true,
          tableHidden: true
        },
        {
          title: '代码位',
          key: 'codeSeatManagerName',
          type: formItemType.input,
          render: (h, params) => {
            const data = JSON.parse(params.data.row.codeSeats)
            return h(
              'div',
              data.map((item, index) => {
                return h(
                  'div',
                  `广告商：${item.adName} -- 代码位:${item.codeSeatManagerName}`
                )
              })
            )
          }
        },
        {
          title: '展示规则',
          key: 'ruleName',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '关联渠道',
          key: 'channelIds',
          type: formItemType.input,
          render: (h, params) => {
            const data = params.data.row.channelIds
            return h('div', data.join(' ; '))
          }
        },
        {
          title: '状态',
          key: 'status',
          list: [
            { label: '启用', value: true },
            { label: '停用', value: false }
          ],
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          options: {
            valueType: 'Number'
          }
        },
        {
          type: tableItemType.active,
          width: '150px',
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: ($index, item, params) => {
                this.$router.push({
                  name: 'adChannelDetail',
                  query: {
                    id: params.id,
                    type: 'edit'
                  }
                })
              }
            }
          ]
        }
      ]
    }
  },
  watch: {},
  mounted() {
    get_type_list().then(res => {
      this.advertisementTypeList = res.data.map(item => {
        return {
          ...item,
          label: item.name,
          value: item.id
        }
      })
    })
  },
  methods: {
    handAdd() {
      this.title = '新增'
      this.taskType = 'add'
      this.dialogVisible = true
      this.$router.push({
        name: 'adChannelDetail',
        query: {
          id: '',
          type: 'add'
        }
      })
    },
    close() {
      this.show = false
      this.dialogVisible = false
      this.$nextTick(() => {
        this.show = true
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
