<template>
  <div>
    <page :request="request" :list="list" table-title="iOS商品库">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small"
          @click="handleAdd">新增商品</el-button>
      </div>

    </page>
    <el-drawer v-if="drawer" :visible.sync="drawer" direction="rtl" size="50%" :with-header="false"
      :wrapper-closable="false">
      <div class="close_button">
        <i class="el-icon-close" @click="drawer = false" />
      </div>
      <div class="drawer_package">
        <div class="drawer_title">
          <span>iOS商品库编辑/新增</span>
        </div>
        <div class="addForm_package">
          <el-form ref="addForm" :model="addForm" :rules="addRules" label-width="120px" label-position="left"
            class="demo-ruleForm">
            <div class="form_view">
              <div class="form_view_title">
                <div class="title_line" /><span>基础信息</span>
              </div>
              <el-form-item label="应用名称" prop="siteId" :rules="addRules.common">
                <el-select v-model="addForm.siteId" placeholder="请选择应用名称">
                  <el-option v-for="item in siteIdsList" :key="item.siteId" :label="item.name" :value="item.siteId" />
                </el-select>
              </el-form-item>
              <el-form-item label="商品类型" prop="productType" :rules="addRules.common">
                <el-select v-model="addForm.productType" :disabled="addForm.id > 0" placeholder="请选择商品类型">
                  <el-option v-for="item in AppleGoodsType" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <div>
                <el-form-item label="商品名称" prop="productName" :rules="addRules.common" style="display: inline-block;">
                  <el-input v-model="addForm.productName" placeholder="请输入商品名称" maxlength="30" />
                </el-form-item>
                <span :style="{ padding: '0 0 0 10px' }">{{ (addForm.productName && addForm.productName.length || 0)
                  }}/30</span>
              </div>
              <div>
                <el-form-item label="支付价" prop="paymentPrice" :rules="addRules.payPrice" style="display: inline-block;">
                  <div :style="{ width: '200px' }">
                    <el-input v-model="addForm.paymentPrice" placeholder="请输入支付价"
                      oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')"
                      @blur="addForm.paymentPrice = $event.target.value" />
                  </div>
                </el-form-item>
                <span :style="{ padding: '0 0 0 10px' }">(元)</span>
              </div>
              <template v-if="addForm.productType == 2">
                <div>
                  <el-form-item label="订阅周期" prop="subscriptionPeriod" :rules="addRules.common"
                    :style="{ display: 'inline-block' }">
                    <span slot="label" style="color: #606266; font-size: 14px; font-weight: 700">
                      订阅周期
                      <el-tooltip content="IOS订阅不存在订阅总期数，用户取消订阅则订阅终止，该订阅周期为每次扣款周期" placement="top-start"
                        :style="{ color: '#409eff' }">
                        <i class="el-icon-question" style="font-size: 14px" />
                      </el-tooltip>
                    </span>
                    <el-select v-model="addForm.subscriptionPeriod" placeholder="请选择订阅周期">
                      <el-option v-for="item in AppleCycle" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </div>
                <el-form-item label="首期是否促销" prop="discount" :rules="addRules.common">
                  <el-radio-group v-model="addForm.discount" @change="changeIsDiscount">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div v-if="addForm.discount">
                  <el-form-item label="促销价" prop="discountPrice" :rules="addRules.discount"
                    style="display: inline-block;">
                    <div :style="{ width: '200px' }">
                      <el-input v-model="addForm.discountPrice"
                        oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')" placeholder="请输入促销价"
                        @blur="addForm.discountPrice = $event.target.value" />
                    </div>
                  </el-form-item>
                  <span :style="{ padding: '0 0 0 10px' }">(元)</span>
                </div>
                <el-form-item label="是否免费试用" prop="trialStatus" :rules="addRules.common">
                  <el-radio-group v-model="addForm.trialStatus" @change="changeTrialStatus">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div v-if="addForm.trialStatus">
                  <el-form-item label="免费试用周期" prop="subscriptionTrialPeriod" :rules="addRules.common"
                    :style="{ display: 'inline-block' }">
                    <el-select v-model="addForm.subscriptionTrialPeriod" placeholder="请选择免费试用周期">
                      <el-option v-for="item in subscriptionTrialPeriodList" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>
                  </el-form-item>
                </div>
              </template>

            </div>
            <div class="form_view">
              <div class="form_view_title">
                <div class="title_line" /><span>商品状态</span>
              </div>
              <el-form-item label="状态" prop="status" label-width="90" :rules="addRules.common">
                <el-switch v-model="addForm.status" :active-value="1" :inactive-value="0" @change="statusChange" />
              </el-form-item>
            </div>
            <div :style="{ 'text-align': 'right', width: '100%' }" class="view_button">
              <el-button @click="drawer = false">取消</el-button>
              <el-button type="primary" @click="handMessageStyleListAdd('addForm')">确认</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-drawer>
    <section>
      <el-dialog title="
          ios商品启用确认
        " width="520px" :visible.sync="dialogFormVisible" :show-close="false" top="30vh" :close-on-click-modal="false"
        :close-on-press-escape="false">
        <div class="dialog_tips">启用前请确认该iOS商品是否在Apple store完成审核，若未上架该商品在iOS应用不可支付，请确认</div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="updateStatus(0)">取 消</el-button>
          <el-button type="primary" :loading="time > 0" :disabled="time > 0" @click="dialogFormVisible = false">{{ time
            > 0
            ? '确
            定'+'（'+ time +'s）':'确 定' }}</el-button>
        </div>
      </el-dialog>
    </section>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { appleProduct, appleProductAdd } from '@/qjjpApi/payManage'
import moment from 'moment'
import { AppleGoodsType, AppleCycle } from '@/qjjpViews/payManage/basicParams'
import { count_channel_application_list } from '@/qjjpApi/NewChannel'
const oneMonthToDays = 30
export default {
  name: 'qjjpproductForIos',
  components: {
    page
  },
  data () {
    var validateDiscount = (rule, value, callback) => {
      if (value === '' || value === null || value === undefined) {
        callback(new Error('此项不能为空'))
      } else if (Number(value) < 1) {
        callback(new Error('不能小于1'))
      } else if (Number(value) >= Number(this.addForm.paymentPrice)) {
        callback(new Error('不能大于支付价格'))
      } else {
        callback()
      }
    }
    var validatePayPrice = (rule, value, callback) => {
      if (value === '' || value === null || value === undefined) {
        callback(new Error('此项不能为空'))
      } else if (Number(value) < 1) {
        callback(new Error('不能小于1'))
      } else {
        callback()
      }
    }
    return {
      AppleGoodsType,
      AppleCycle,
      subscriptionTrialPeriodList: [
        {
          value: 'THREE_DAYS',
          label: '3天',
          dayValue: 3
        },
        {
          value: 'ONE_WEEK',
          label: '1周',
          dayValue: 7
        },
        {
          value: 'TWO_WEEK',
          label: '2周',
          dayValue: 14
        },
        {
          value: 'ONE_MONTH',
          label: '1个月',
          dayValue: oneMonthToDays
        },
        {
          value: 'TWO_MONTH',
          label: '2个月',
          dayValue: oneMonthToDays * 2
        },
        {
          value: 'THREE_MONTH',
          label: '3个月',
          dayValue: oneMonthToDays * 3
        },
        {
          value: 'SIX_MONTH',
          label: '6个月',
          dayValue: oneMonthToDays * 6
        },
        {
          value: 'ONE_YEAR',
          label: '1年',
          dayValue: oneMonthToDays * 12
        }
      ],
      notPeriodDefaultForm: { // 不是订阅类型,表单订阅相关字段置空
        subscriptionPeriod: '', // 订阅周期
        discount: 0, // 收取是否促销
        discountPrice: '' // 促销价
      },
      addForm: {
        id: '',
        productType: '',
        productName: '',
        paymentPrice: '',
        subscriptionPeriod: '', // 订阅周期
        discount: 0, // 收取是否促销
        trialStatus: 0,
        discountPrice: '', // 促销价
        subscriptionTrialPeriod: '',
        status: 0
      },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }],
        discount: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { validator: validateDiscount, trigger: 'blur' }],
        payPrice: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { validator: validatePayPrice, trigger: 'blur' }]
      },
      drawer: false,
      list2: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ],
      siteIdsList: [],
      siteIds: [],
      listQuery: {
        productName: this.$route.query.productName || ''
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          const list = await appleProduct(this.listQuery)
          await count_channel_application_list().then(res => {
            if (res.code === 200) {
              this.siteIdsList = res.data
            }
          })
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      },
      dialogFormVisible: false,
      time: 5
    }
  },
  computed: {
    list () {
      return [
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          clearable: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '商品名称',
          key: 'productName',
          type: formItemType.input,
          search: true,
          clearable: true,
          tableHidden: true,
          val: this.listQuery.productName || ''
        },
        {
          title: '商品ID',
          key: 'productId',
          type: formItemType.input,
          search: true,
          clearable: true,
          renderHeader: (...args) => this.renderHeaders(...args, ['该商品ID需在Apple商店中心创建成功并且审核通过才可使用'])
        },
        {
          title: '商品名称',
          key: 'productName'
        },
        {
          title: '商品类型',
          key: 'productType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: AppleGoodsType,
          search: true,
          clearable: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIdsList,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true,
          options: {
            on: () => {
              return {
                change: e => {
                  const a = this.siteIdsList.filter(item => item.siteId == e)
                  this.packageName = a[0].packageName
                }
              }
            }
          }
        },
        {
          title: '支付价格',
          key: 'paymentPrice'
        },
        {
          title: '订阅周期',
          key: 'subscriptionPeriod',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.AppleCycle
        },
        {
          title: '首期是否促销',
          key: 'discount',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [{ value: 0, label: '否' }, { value: 1, label: '是' }]
        },
        {
          title: '促销价',
          key: 'discountPrice'
        },
        {
          title: '免费试用周期',
          key: 'subscriptionTrialPeriod',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.subscriptionTrialPeriodList,
          listFormat: {
            label: 'label',
            value: 'value'
          }
        },
        {
          title: '更新人员',
          key: 'updateUser'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.addForm = JSON.parse(JSON.stringify(params))
                this.drawer = true
              }
            }
          ]
        }
      ]
    }
  },
  async mounted () {
  },
  created () { },
  methods: {
    renderHeaders (h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h('div', { slot: 'content' }, [textArr.map(text => h('div', null, text))]),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    },
    handMessageStyleListAdd (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const params = { ...this.addForm, ...(this.addForm.productType != 2 ? this.notPeriodDefaultForm : {}) }
          appleProductAdd(params).then(res => {
            if (res.code == 200) {
              this.drawer = false
              this.$message.success('操作成功')
              this.$store.dispatch('tableRefresh', this)
            }
          })
        }
      })
    },
    reloadAddform () {
      this.addForm = {
        id: '',
        productType: '',
        productName: '',
        paymentPrice: '',
        subscriptionPeriod: '', // 订阅周期
        discount: 0, // 收取是否促销
        discountPrice: '', // 促销价
        trialStatus: 0,
        subscriptionTrialPeriod: null,
        status: 0
      }
    },
    handleAdd () {
      this.reloadAddform()
      this.drawer = true
    },
    changeIsDiscount () {
      if (this.addForm.discount === 0) {
        this.addForm.discountPrice = null
      } else if (this.addForm.discount === 1) {
        this.addForm.trialStatus = 0
      }
    },
    changeTrialStatus () {
      if (this.addForm.trialStatus === 0) {
        this.addForm.subscriptionTrialPeriod = null
      } else if (this.addForm.trialStatus === 1) {
        this.addForm.discount = 0
      }
    },
    statusChange (val) {
      if (val === 1) {
        this.handlerConfirm()
      }
    },
    updateStatus (val) {
      this.dialogFormVisible = false
      this.addForm.status = val
    },
    handlerConfirm () {
      this.dialogFormVisible = true
      this.time = 5
      this.setTimer && clearInterval(this.setTimer)
      this.setTimer = setInterval(() => {
        if (this.time <= 0) {
          this.time = 0
          return
        }
        this.time--
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  overflow: scroll;
  // padding-bottom: 20px;
  padding: 0 30px 20px;
  position: relative;
  /* overflow-x: auto; */
}

::v-deep .el-drawer__header {
  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 30px;
    text-align: left;
    font-style: normal;
  }

}

::v-deep .el-drawer__body {}

.close_button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background-color: rgb(0, 0, 0, 1);
  text-align: center;
  cursor: pointer;

  i {
    color: white;
    line-height: 40px;
  }
}

.drawer_package {
  height: 100%;
  position: relative;

  .drawer_title {
    padding: 10px 20px 5px;
    vertical-align: middle;

    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }

  }
}

.addForm_package {
  background-color: rgb(189, 184, 184, 0.1);
  padding: 15px;
  height: 100%;

  .demo-ruleForm {
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    position: relative;

    .view_button {
      position: absolute;
      bottom: 0;
    }

    ::v-deep .el-form-item {
      padding-left: 40px;
    }

    ::v-deep .el-form-item__label {
      padding-right: 5px;
    }
  }
}

.view_button {
  background-color: #ffffff;
  padding: 20px;
  border-top: 1px dashed #000000;

  ::v-deep .el-button {
    margin: 0 10px;
  }
}

.form_view {
  // margin: 0 0px 20px;
  background-color: #ffffff;
  // border: 1px solid rgba(0,0,0,0.2);
  width: 100%;
  padding: 15px;
  border-radius: 5px;

  .form_view_title {
    margin-bottom: 20px;

    .title_line {
      width: 2px;
      height: 10px;
      background-color: #66b1ff;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      padding-left: 5px;
      vertical-align: middle;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }
}

.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
