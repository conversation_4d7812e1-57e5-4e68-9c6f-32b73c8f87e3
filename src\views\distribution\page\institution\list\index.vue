<template>
  <div>
    <Page :request="request" :list="list" table-title="机构列表">
      <div slot="searchContainer" style="display: inline-block">
        <!-- <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small"
          @click="handleAdd">添加</el-button> -->
      </div>
    </Page>

    <Drawer
      :visible.sync="showDialog"
      :title="!formData.id ? '添加' : '编辑'"
      @confirm="handleSubmit"
      @cancel="showDialog = false"
    >
      <el-form ref="form" :model="formData" :rules="formRules" label-position="top" class="demo-ruleForm">
        <!-- 基础信息 -->
        <FormSection title="基础信息">
          <el-form-item label="机构名称" prop="advertiserName">
            <el-input v-model="formData.advertiserName" placeholder="请输入机构名称" maxlength="30" style="width: 50%"></el-input>
          </el-form-item>
        </FormSection>

        <!-- 分发设置 -->
        <FormSection title="分发设置">
          <el-form-item label="分发方式" prop="triageType">
            <el-checkbox-group v-model="formData.triageType">
              <el-checkbox label="1">API</el-checkbox>
              <el-checkbox label="2">表单</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="撞库api" prop="stuffingApiType">
            <template slot="label">
              <span>撞库api</span>
              <el-tooltip content="已接入撞库api的广告主支持分发状态校验" placement="top">
                <i class="el-icon-question" style="color:#409eff;margin-left:5px;font-size: 16px;cursor:pointer;"></i>
              </el-tooltip>
            </template>
            <el-radio-group v-model="formData.stuffingApiType" size="small">
              <el-radio-button :label="1">已接入</el-radio-button>
              <el-radio-button :label="0">未接入</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </FormSection>

        <!-- 状态 -->
        <FormSection title="状态">
          <el-form-item label="状态" prop="status">
            <el-radio v-model="formData.status" :label="1">启用</el-radio>
            <el-radio v-model="formData.status" :label="0">禁用</el-radio>
          </el-form-item>
        </FormSection>
      </el-form>
    </Drawer>
  </div>
</template>

<script>
import Page from '@/components/restructure/page/index'
import {formItemType, tableItemType} from '@/config/sysConfig'
import {getAdvertiserList, addAdvertiser, updateAdvertiser} from '@/api/distribution'
import moment from 'moment'
import Drawer from '@/components/Drawer'
import FormSection from '@/components/Drawer/FormSection'

export default {
  components: {
    Page,
    Drawer,
    FormSection
  },
  data() {
    return {
      showDialog: false,
      formData: {
        advertiserName: '',
        triageType: [],
        stuffingApiType: '',
        status: 1
      },
      formRules: {
        advertiserName: [
          {required: true, message: '请输入机构名称', trigger: 'blur'},
          {max: 30, message: '机构名称不能超过30个字符', trigger: 'blur'}
        ],
        triageType: [
          {required: true, message: '请选择分发方式', trigger: 'change'},
          {type: 'array', min: 1, message: '请至少选择一种分发方式', trigger: 'change'}
        ],
        stuffingApiType: [
          {required: true, message: '请选择撞库api', trigger: 'change'}
        ],
        status: [
          {required: true, message: '请选择状态', trigger: 'change'}
        ]
      },

      // 机构名称
      institutionNameList: [],

      listQuery: {},
      request: {
        getListUrl: async data => {
          this.listQuery = {...this.listQuery, ...data}
          const list = await getAdvertiserList(this.listQuery)
          const {records = [], total = 0} = list.data
          return {
            data: {
              total: total,
              rows: records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '机构名称',
          searchKey: 'advertiserName',
          type: formItemType.input,
          search: true,
          clearable: true,
          tableHidden: true,
          titleHidden: true,
          options: {
            placeholder: '请输入机构名称'
          }
        },
        {
          title: '分发方式',
          searchKey: 'deliveryLinkType',
          type: formItemType.select,
          list: [
            {
              label: 'API',
              value: 1
            },
            {
              label: '表单',
              value: 2
            }
          ],
          search: true,
          clearable: true,
          tableHidden: true,
          titleHidden: true
        },
        {
          title: '撞库API',
          searchKey: 'stuffingApiType',
          type: formItemType.select,
          list: [
            {
              label: '已接入',
              value: 1
            },
            {
              label: '未接入',
              value: 0
            }
          ],
          search: true,
          clearable: true,
          tableHidden: true,
          titleHidden: true
        },
        {
          title: '状态',
          searchKey: 'status',
          type: formItemType.select,
          list: [
            {
              label: '启用',
              value: 1
            },
            {
              label: '禁用',
              value: 0
            }
          ],
          search: true,
          clearable: true,
          tableHidden: true,
          titleHidden: true
        },
        {
          title: '序号',
          key: 'id'
        },
        {
          title: '机构名称',
          key: 'advertiserName'
        },
        {
          title: '分发方式',
          key: 'triageType',
          render: (h, params) => {
            const types = this.handleTriageType(params.data.row.triageType)
            // 映射显示文本
            const typeMap = {
              '1': 'API',
              '2': '表单'
            }
            return h('span', types.map(t => typeMap[t] || '').filter(Boolean).join('、') || '--')
          }
        },
        {
          title: '撞库API',
          key: 'stuffingApiType',
          render: (h, params) => {
            const api = params.data.row.stuffingApiType
            return h('span', api === 1 ? '已接入' : '未接入')
          }
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return h('span', params.data.row.status === 1 ? '启用' : '禁用')
          }
        },
        {
          title: '更新人',
          key: 'updateByName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) return h('span', '--')
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '操作',
          key: 'operation',
          type: tableItemType.active,
          headerContainer: false,
          width: 120,
          activeType: [
            {
              text: '编辑',
              key: 'details',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                this.showDialog = true
                const triageType = this.handleTriageType(params.triageType)
                
                this.formData = {
                  ...params,
                  triageType
                }
                // 等待 DOM 更新后清除表单验证状态
                this.$nextTick(() => {
                  if (this.$refs.form) {
                    this.$refs.form.clearValidate()
                  }
                })
              }
            }
          ]
        }
      ]
    }
  },
  created() {
    // 获取机构名称列表
    this.getInstitutionNameList()
  },
  methods: {
    // 处理分发方式数据转换
    handleTriageType(triageType) {
      let types = []
      if (Array.isArray(triageType)) {
        types = triageType
      } else if (typeof triageType === 'string') {
        try {
          types = JSON.parse(triageType)
        } catch {
          types = triageType.replace(/[\[\]\s']/g, '').split(',').filter(Boolean)
        }
      }
      return types.map(String)
    },
    async getInstitutionNameList() {
      try {
        //  替换为实际的机构列表接口
        const res = { code: 200, data: [] }
        if (res.code === 200) {
          this.institutionNameList = res.data
        }
      } catch (error) {
        console.error('获取机构列表失败:', error)
      }
    },
    handleAdd() {
      this.showDialog = true
      this.formData = {
        advertiserName: '',
        triageType: [],
        stuffingApiType: '',
        status: 1
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
      })
    },
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            // 过滤掉不需要提交的字段
            const { 
              updateBy, 
              updateByName, 
              updateTime, 
              ...rest 
            } = this.formData

            const submitData = {
              ...rest,
              triageType: Array.isArray(rest.triageType) ? rest.triageType : []
            }

            console.log('submitData', submitData)
            const res = this.formData.id 
              ? await updateAdvertiser(submitData)
              : await addAdvertiser(submitData)

            if (res.code === 200) {
              this.showDialog = false
              this.$message.success(this.formData.id ? '编辑成功' : '新增成功')
              this.$store.dispatch('tableRefresh', this)
            }
          } catch (error) {
            this.$message.error(error.message || '操作失败')
          }
        }
      })
    },
    renderHeader(h, params, tips) {
      const {column} = params
      return (
        <div>
          <span>{column.label}</span>
          <el-tooltip content={tips}>
            <i class="el-icon-question" style="color:#409eff;margin-left:5px;font-size: 16px;"></i>
          </el-tooltip>
        </div>
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.distribution-institution {
  padding: 20px;
  
  ::v-deep .activeButton {
    .el-button {
      margin-right: 5px;
      padding: 7px 6px;
    }
  }
}
</style>
