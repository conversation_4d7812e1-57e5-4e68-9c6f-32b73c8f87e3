<template>
  <div>
    <page :request="request" :list="list" table-title="dsp统计" />
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import {
  count_channel_application_list
} from '@/api/NewChannel.js'
import {
  countDspPageList,
  dspSupplierListByName
} from '@/api/advertisement'
import moment from 'moment'

export default {
  components: {
    page
  },
  data() {
    return {
      dialogFormVisible: true,
      dialogOps: {
        title: 'dsp统计'
      },
      listQuery: {
        startDate: moment().subtract(6, 'd').format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD'),
        siteId: '',
        supplierId: ''
      },
      dspNameList: [],
      siteIds: [],
      request: {
        getListUrl: async(data) => {
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                res.data.unshift({
                  siteName: '综合',
                  siteId: ''
                })
                this.siteIds = res.data
              }
            })
          }
          this.listQuery = { ...this.listQuery, ...data }
          return countDspPageList(this.listQuery)
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          key: 'date',
          title: '日期',
          type: formItemType.datePickerDaterangeGai,
          options: {
            format: 'YYYYMMDD',
            valueFormat: 'yyyyMMdd'
          },
          childKey: ['startDate', 'endDate'],
          val: [this.listQuery.startDate, this.listQuery.endDate],
          search: true
        },

        {
          title: '供应商',
          key: 'supplierName',
          type: formItemType.select,
          searchKey: 'supplierId',
          tableView: tableItemType.tableView.text,
          list: this.dspNameList,
          tableHidden: true,
          search: true
        },
        {
          title: '供应商',
          key: 'supplierName'
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: false,
          tableHidden: true
        },
        {
          title: '请求uv',
          key: 'requestUv'
        },
        {
          title: '请求pv',
          key: 'requestPv'
        }

      ]
    }
  },
  created() {
    dspSupplierListByName().then(res => {
      res.data.forEach(item => {
        item.label = item.supplierName
        item.value = item.id
      })
      this.dspNameList = res.data
    })
  },
  mounted() {
  },
  methods: {
  }
}
</script>

<style scoped lang="scss">
</style>
