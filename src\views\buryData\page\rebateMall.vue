<template>
  <page :request="request" :list="list" table-title="返利商城统计">
    <div slot="searchContainer" style="display: inline-block">
      <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { EXPORT_REBATE_MALL, GET_REBATE_MALL } from '@/api/buryData'
import moment from 'moment'
export default {
  components: {
    page
  },
  data() {
    return {
      request: {
        getListUrl: data => {
          return this.getData(data)
        }
      },
      listQuery: {
        startTime: moment()
          .subtract(7, 'days')
          .format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterange<PERSON><PERSON>,
          childKey: ['startTime', 'endTime'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [
            moment()
              .subtract(7, 'days')
              .format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '平台',
          key: 'os',
          type: formItemType.select,
          list: [
            {
              label: 'IOS',
              value: 'ios'
            },
            {
              label: 'ANDROID',
              value: 'android'
            }
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '日期',
          key: 'date'
        },
        {
          title: '返利商城访问PV/UV',
          key: 'mallVisitPvUv'
        },
        {
          title: '搜索按钮PV/UV',
          key: 'searchPvUv'
        },
        {
          title: '顶部bannerPV/UV',
          key: 'topBannerPvUv'
        },
        {
          title: '返利商城橱窗PV/UV',
          key: 'mallShopWindowPvUv'
        },
        {
          title: '中间bannerPV/UV',
          key: 'middleBannerPvUv'
        },
        {
          title: '返利商城推荐商品运营位PV/UV',
          key: 'operationPVUv'
        },
        {
          title: '返利商城大牌返利PV/UV',
          key: 'brandPvUv'
        },
        {
          title: '返利商城抖音爆款PV/UV',
          key: 'hotPvUv'
        },
        {
          title: '返利商城第三方专题PV/UV',
          key: 'topicPvUv'
        },
        {
          title: '返利商城推荐商品PV/UV',
          key: 'commendPvUv'
        },
        {
          title: '返利订单数',
          key: 'orderNum'
        },
        {
          title: '下单人数',
          key: 'orderUserNum'
        }
      ]
    }
  },
  mounted() {

  },
  methods: {
    getData(data) {
      this.listQuery = { ...this.listQuery, ...data }
      return Promise.all([GET_REBATE_MALL(this.listQuery)]).then(
        res => {
          return Promise.resolve(res[0])
        }
      )
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_REBATE_MALL({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
