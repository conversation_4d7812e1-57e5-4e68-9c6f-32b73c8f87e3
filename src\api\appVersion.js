/*
 * @Author: 陈小豆
 * @Date: 2023-07-21 14:56:17
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-04-30 10:59:45
 */
import CONSTANT from '@/config/constant.conf'
import {
  get,
  post, put
} from '@/libs/axios.package'
import qs from 'qs'

/**
 * APP渠道市场
 */
export const GET_APP_CHANNEL_LIST = obj => {
  return get('/app/channel/pagingQuery', obj)
}

export const POST_APP_CHANNEL = obj => {
  return post('/app/channel/save', obj)
}

/**
 * APP版本管理
 */
// ANDROID
export const GET_APP_PAGING_QUERY = obj => {
  return get('/app/android/pagingQuery', obj)
}

export const ADD_ANDROID_SAVE = obj => {
  return post('/app/android/save', obj)
}

export const GET_ANDROID_DETAILS_BY_ID = id => {
  return get(`/app/android/details/${id}`, null, false)
}

// IOS
export const GET_APP_IOS_LIST = obj => {
  return get('/app/ios', obj)
}

export const ADD_IOS_SAVE = obj => {
  return post('/app/ios', obj)
}
export const PUT_IOS_SAVE = obj => {
  return put(`/app/ios/${obj.id}`, obj)
}
export const GET_IOS_DETAILS_BY_ID = id => {
  return get(`/app/ios/${id}`, null, false)
}

/**
 * APP渠道市场
 */
export const GET_CHANNEL_LIST = obj => {
  return get('/app/channel/list', obj)
}

/**
 * APP渠道市场统计
 */
export const GET_COUNTAPPUSER_LISTBYCODE = obj => {
  return get('/countappuser/listByCode', obj)
}

export const GET_COUNTAPPUSER_LISTBYDATE = obj => {
  return get('/countappuser/listByDate', obj)
}

export const EXPORT_COUNTAPPUSER_EXPORTBYCODE = data => CONSTANT.publicPath + '/countappuser/exportByCode?' + qs.stringify(data)

export const EXPORT_COUNTAPPUSER_EXPORTBYDATE = data => CONSTANT.publicPath + '/countappuser/exportByDate?' + qs.stringify(data)

export const GET_UPLOAD_CALLBACK = obj => {
  return get('/upload/mergefile', obj)
}

export const UPDATE_CHANNEL_DOWNLOAD = obj => {
  return post('/app/android/updateChannelDownload', obj)
}

export const update_branch = obj => {
  return post('/app/channel/update/branch', obj)
}

// 获取城市列表
export function getCityList() {
  return fetch('https://tapi.haoxincd.cn/major/city/allCity', {
    method: 'GET'
  })
}

/**
 * AB测试页面
 */
export const GET_AB_PAGE = obj => {
  return get('/app/ab/page', obj)
}

export const GET_AB_APP_NAMES = () => {
  return get('/app/ab/name')
}

// 获取应用市场列表
export const GET_AB_PLATFORM = () => {
  return get('/app/ab/platform')
}

// 新增 AB 测试
export const ADD_AB = obj => {
  return post('/app/ab', obj)
}

// 编辑 AB 测试
export const UPDATE_AB = obj => {
  return put(`/app/ab`, obj)
}
