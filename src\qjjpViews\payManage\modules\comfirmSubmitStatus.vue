<template>
  <div class="c-box">
    <el-dialog :top="'40vh'" :visible="show" :show-close="false" width="520px">
      <div class="c-contianer">
        <div class="el-message-box__header">
          <div class="el-message-box__title">
            <span>商户号启用确认</span>
          </div>
          <button type="button"  @click="$emit('update:show',false)" aria-label="Close" class="el-message-box__headerbtn">
            <i class="el-message-box__close el-icon-close" />
          </button>
        </div>
        <div class="el-message-box__content"><div class="el-message-box__container"><div class="el-message-box__status el-icon-warning" /><div class="el-message-box__message"><p>该支付场景已存在启用商户，启用新的商户将自动禁用已启用商户，请确认是否启用</p></div></div><div class="el-message-box__input" style="display: none;"><div class="el-input"><input type="text" autocomplete="off" placeholder="" class="el-input__inner"></div><div class="el-message-box__errormsg" style="visibility: hidden;" /></div></div>
        <div class="el-message-box__btns" @click="$emit('update:show',false)"><el-button size="small"><span>
          取消
        </span></el-button><el-button :loading="times>0" size="small" type="primary" @click="times<=0&&$emit('confirm')"><span>
          确定<template v-if="times>0">{{ times }}s</template>
        </span></el-button></div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'comfirmSubmitStatus',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      times: 5
    }
  },
  created() {
    this.setTimes && clearInterval(this.setTimes)
    this.times = 5
    this.setTimes = setInterval(() => {
      if (this.times <= 0) {
        clearInterval(this.setTimes)
        return
      }
      this.times--
    }, 1000)
  }
}
</script>
<style scoped>
.c-box ::v-deep .el-dialog__header,{
  padding: 0;
}
.c-box ::v-deep .el-dialog__body{
  padding:10px 0;
}
.c-contianer{
width: 100%;
}
</style>

