const videoMembership = [
  {
    path: '/videoMembership/prepaidPhonePlans',
    name: 'vPrepaidPhonePlans',
    meta: {
      title: '充值套餐'
    },
    component: () => import('@/views/videoMembership/prepaidPhonePlans/index')
  },
  {
    path: '/videoMembership/membershipCardManagement',
    name: 'membershipCardManagement',
    meta: {
      title: '会员卡管理'
    },
    component: () => import('@/views/videoMembership/membershipCardManagement/index')
  },
  {
    path: '/videoMembership/prepaidPhoneRecords',
    name: 'vPprepaidPhoneRecords',
    meta: {
      title: '充值记录'
    },
    component: () => import('@/views/videoMembership/prepaidPhoneRecords/index')
  },
  {
    path: '/videoMembership/getTheRecord',
    name: 'vGetTheRecord',
    meta: {
      title: '领取记录'
    },
    component: () => import('@/views/videoMembership/getTheRecord/index')
  },
  {
    path: '/videoMembership/platformEarnings',
    name: 'vPlatformEarnings',
    meta: {
      title: '平台收益'
    },
    component: () => import('@/views/videoMembership/platformEarnings/index')
  },
  {
    path: '/videoMembership/topUpBuriedPoint',
    name: 'vTopUpBuriedPoint',
    meta: {
      title: '充值埋点'
    },
    component: () => import('@/views/videoMembership/topUpBuriedPoint/index')
  },
  {
    path: '/videoMembership/openStatistics',
    name: 'vOpenStatistics',
    meta: {
      title: '开通统计'
    },
    component: () => import('@/views/videoMembership/openStatistics/index')
  },
  {
    path: '/videoMembership/activationBuriedPoint',
    name: 'vActivationBuriedPoint',
    meta: {
      title: '激活埋点'
    },
    component: () => import('@/views/videoMembership/activationBuriedPoint/index')
  },
  {
    path: '/videoMembership/setPageStatistics',
    name: 'setPageStatistics',
    meta: {
      title: '集合页统计'
    },
    component: () => import('@/views/videoMembership/setPageStatistics/index')
  },
  {
    path: '/videoMembership/setPageStatistics/detail',
    name: 'setPageStatisticsDetail',
    meta: {
      title: '集合页统计详情',
      activeMenu: '/videoMembership/setPageStatistics',
      parentTitle: '集合页统计'
    },
    component: () => import('@/views/videoMembership/setPageStatistics/detail')
  }
]
export default videoMembership
