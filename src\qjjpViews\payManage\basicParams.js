import { appleProduct } from '@/qjjpApi/payManage'
import {
  getmembershipTemplate
} from '@/qjjpApi/payManage'

export const AppleGoodsType = [
  {
    value: 1,
    label: '消耗型'
  },
  {
    value: 2,
    label: '订阅型'
  }
]
const oneMonthToDays = 30
export const AppleCycle = [
  {
    value: 'ONE_WEEK',
    label: '1周',
    dayValue: 7
  },
  {
    value: 'ONE_MONTH',
    label: '1个月',
    dayValue: oneMonthToDays
  },
  {
    value: 'TWO_MONTH',
    label: '2个月',
    dayValue: oneMonthToDays * 2
  },
  {
    value: 'THREE_MONTH',
    label: '3个月',
    dayValue: oneMonthToDays * 3
  },
  {
    value: 'SIX_MONTH',
    label: '6个月',
    dayValue: oneMonthToDays * 6
  },
  {
    value: 'ONE_YEAR',
    label: '1年',
    dayValue: oneMonthToDays * 12
  }
]
export const getIosProduct = function(siteId='') {
  return Promise.all([appleProduct({ status: 1, pageNumber: 1, pageSize: 1000, productType: 1,siteId:siteId }), appleProduct({ status: 1, pageNumber: 1, pageSize: 1000, productType: 2,siteId:siteId })]).then(res => {
    const normalProduct = res?.[0]?.data?.records ?? []
    const cycleProduct = res?.[1]?.data?.records ?? []
    return {
      normalProduct,
      cycleProduct,
      allProduct: [...normalProduct, ...cycleProduct]
    }
  })
}

export const getPayTemplate = function() {
  const getPayTemplate = function() {
    return getmembershipTemplate({ status: 1,
      pageNumber: 1,
      pageSize: 999 }).then(res => {
      return res?.data?.records ?? []
    })
  }
  const payTemplateForOs = function({ list, os }) {
    if (list && list.length) {
      return list.filter(item => String(item.os) === String(os))
    }
  }
  return {
    allPayTemplate: getPayTemplate,
    payTemplateForOs
  }
}
