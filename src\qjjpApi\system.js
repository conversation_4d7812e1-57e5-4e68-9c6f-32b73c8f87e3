/*
 * @Author: 陈小豆
 * @Date: 2024-04-25 21:08:56
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-08-22 17:58:21
 */
import { getJjjp, postJjjp } from '@/libs/axios.package'

// 消息风格列表
export const systemParam = obj => {
  return getJjjp(`/cms/systemParam/list`, obj)
}
// 编辑或新增商品分类接口
export const getSystemParamById = obj => {
  return getJjjp('/cms/systemParam/byId', obj)
}

export const updateById = obj => {
  return postJjjp('/cms/systemParam/updateById', obj)
}

// 根据多个key查询系统配置值,返回结构为{"key1":"value1","key2":"value2"}
export const systemParamGetByCodes = obj => {
  return postJjjp('/cms/systemParam/getByCodes', obj)
}

export const checkChannelLook = obj => {
  return getJjjp('/cms/admin/checkChannelLook', obj)
}