<template>
  <div class="uploadDialog">
    <uploadFile
      type="data"
      drag
      :width="250"
      :accept-array="['xlsx','xls','csv']"
      :send-url="`${$CONSTANT.qjjpPath}/cms/thirdGoodsOrder/importExcelPreview`"
      :height="180"
      :multiple="false"
      :limit="1"
      @getData="getData"
    >
      <div class="addDiv">
        <i class="el-icon-plus" />
        <span class="txt">将文件拖到此处，或 <span>点击上传</span></span>
      </div>
    </uploadFile>
  </div>
</template>
<script>
export default {
  components: {
    uploadFile: () => import('@/components/yc-upload/handleUploadImage')
  },
  methods: {
    getData(e) {
      this.$emit('action', 'confirm', e)
    }
  }
}
</script>
<style lang="scss" scoped>
.uploadDialog{
  display: flex;
  justify-content: center;
  align-items: center;
}
.addDiv {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .el-icon-plus{
    font-size: 16px;
    color: #409EFF;
  }
  .txt {
    width: 100%;
    height: 20px;
    font-size: 12px;
    color: #999999;
    text-align: center;
    line-height: 1.5;
    transform: scale(0.8);
    span{
      color: #409EFF;
    }
  }
}
</style>
