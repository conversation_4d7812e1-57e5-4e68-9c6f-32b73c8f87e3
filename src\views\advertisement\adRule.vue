<template>
  <div>
    <page
      v-if="show"
      :request="request"
      :list="list"
      table-title="广告展示规则"
    >
      <div slot="searchContainer" style="display: inline-block">
        <el-button
          plain
          type="primary"
          size="small"
          icon="el-icon-circle-plus"
          @click="handAdd"
        >新增</el-button>
      </div>
    </page>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="40%">
      <addRule
        v-if="dialogVisible"
        :id="id"
        :type="listQuery.activityType"
        :params="params"
        :task-type="taskType"
        @close="close"
      />
    </el-dialog>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import addRule from '@/views/advertisement/components/addRule'

import { get_rule_page, get_type_list } from '@/api/advertisement'
import { tableItemType, formItemType } from '@/config/sysConfig'
export default {
  name: 'AdCodeSeat',
  components: {
    page,
    addRule
  },
  props: {},
  data() {
    return {
      listQuery: {},
      dialogVisible: false,
      dialogVisibleData: false,
      title: '新增',
      currName: '',
      taskType: '',
      show: true,
      id: '',
      params: {},
      advertisementTypeList: [],
      request: {
        getListUrl: get_rule_page
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'id',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '名称',
          key: 'name',
          type: formItemType.input,
          search: true
        },
        {
          title: '广告类型',
          key: 'adTypeName',
          type: formItemType.input
        },
        {
          title: '展示策略',
          key: 'strategyName',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '比例',
          key: 'rule',
          type: formItemType.select,
          tableHidden: true
        },
        {
          title: '广告类型',
          key: 'adType',
          type: formItemType.select,
          tableHidden: true,
          search: true,
          list: this.advertisementTypeList,
          options: {
            placeholder: '请选择广告类型'
          }
        },
        {
          title: '状态',
          key: 'status',
          list: [
            { label: '启用', value: true },
            { label: '停用', value: false }
          ],
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          options: {
            valueType: 'Number'
          }
        },
        {
          type: tableItemType.active,
          width: '150px',
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: ($index, item, params) => {
                this.title = '编辑'
                this.id = params.id
                this.params = params
                this.taskType = 'edit'
                this.dialogVisible = true
              }
            }
          ]
        }
      ]
    }
  },
  watch: {},
  mounted() {
    get_type_list().then(res => {
      this.advertisementTypeList = res.data.map(item => {
        return {
          ...item,
          label: item.name,
          value: item.id
        }
      })
    })
  },
  methods: {
    handAdd() {
      this.title = '新增'
      this.taskType = 'add'
      this.dialogVisible = true
    },
    close() {
      this.show = false
      this.dialogVisible = false
      this.$nextTick(() => {
        this.show = true
      })
    }
  }
}
</script>

<style lang="" scoped></style>
