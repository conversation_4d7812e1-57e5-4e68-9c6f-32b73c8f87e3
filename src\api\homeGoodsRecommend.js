import {
  get,
  post
} from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/**
 *
 * @param obj
 * @returns {Promise | Promise<unknown>}
 * @constructor
 */
export const GET_DISPOSE_FIND_LIST = obj => {
  return get('/goods/dispose/findList', obj)
}
export const GET_DISPOSE_FIND_DETAIL = obj => {
  return get(`/goods/dispose/details/${obj.id}`, null)
}
export const DISPOSE_SAVE = obj => {
  return post(`/goods/dispose/save`, obj)
}

export const GET_HOMEPAGE_CATEGORY_LIST = obj => {
  return get('/homepage/right/category/list', obj)
}
export const ADD_HOMEPAGE_CATEGORY = obj => {
  return post('/homepage/right/category/add', obj)
}
export const UPDATE_HOMEPAGE_CATEGORY = obj => {
  return post('/homepage/right/category/update', obj)
}

export const GET_RIGHTS_GOODS_LIST = obj => {
  return get('/homepage/rightsgoods/list', obj)
}

export const GET_RIGHTS_GOODS_BY_ID = obj => {
  return get(`/homepage/rightsgoods/${obj.id}`, null)
}

export const ADD_RIGHTS_GOODS = obj => {
  return post('/homepage/rightsgoods/add', obj)
}
export const EDIT_RIGHTS_GOODS = obj => {
  return post('/homepage/rightsgoods/update', obj)
}

export const EXPORT_MOBILE_ORDER = data => CONSTANT.publicPath + '/activity/mobile/goods/order/export?' + qs.stringify(data)
