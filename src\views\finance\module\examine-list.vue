<template>
  <div>
    <el-row class="search-row" type="flex" style="margin-bottom: 30px;">
      <el-col class="search-col" style="display: flex;flex-wrap: wrap">
        <span>
          <label class="name">用户账号：</label>
          <el-input
            v-model="parameterObj.phoneNo"
            class="search-maxInput"
            clearable
            placeholder="请输入用户账号"
          />
          <label class="name">选择时间：</label>
          <el-date-picker
            v-model="parameterObj.timeArr"
            :picker-options="basics.pickerOptions()"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 260px;margin-left: 10px;"
          />
          <el-button type="primary" class="search-btn" icon="el-icon-search" @click="getData()">查询</el-button>
          <el-button
            type="primary"
            class="search-btn"
            icon="el-icon-search"
            @click="openApply(1)"
          >打款</el-button>
          <el-button
            type="primary"
            class="search-btn"
            icon="el-icon-search"
            @click="openApply(2)"
          >批量打款</el-button>
        </span>
      </el-col>
    </el-row>
    <section style="margin: 30px 0;">
      <div class="tab-head">
        <span class="title">数据列表</span>
        <el-button
          type="warning"
          size="small"
          icon="el-icon-download"
          plain
          @click="handUpload()"
        >导出数据</el-button>
      </div>
      <el-table
        ref="multipleTable"
        v-loading="DataLoading"
        :data="tableData"
        border
        style="width: 100%;margin:0 0 30px;"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="applyNo" label="订单号" />
        <el-table-column prop="userId" label="用户id" />
        <el-table-column prop="mobileNo" label="用户账号" />
        <el-table-column prop="withdrawAccountNo" label="提现账号" />
        <el-table-column prop="withdrawRealName" label="提现人姓名" />
        <el-table-column prop="withdrawAmount" label="提现金额" />
        <el-table-column prop="withdrawFee" label="提现手续费" />
        <el-table-column prop="withdrawRealy" label="到账金额" />
        <el-table-column prop="applyTime" label="提交时间">
          <template slot-scope="scope">
            <span>{{ moment(scope.row.applyTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template slot-scope="scope">
            <el-button type="warning" size="mini" plain @click="openApply(3,scope.row)">打款</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 70, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </section>

    <el-dialog title="提现申请" :visible.sync="dialogVisible" width="520px">
      <div style="font-size:23px;display:flex;justify-content:center">
        <div style="font-size:23px;">订单数：{{ orderApproval.orderNum }}</div>
        <div style="font-size:23px;margin-left:20px">打款金额：{{ orderApproval.sumAmount }}</div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelForm1">取 消</el-button>
        <el-button
          type="primary"
          :loading="btn_disabled"
          :disabled="btn_disabled"
          @click="handleApply"
        >确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  get_financeOperate_list,
  post_finance_approval,
  post_finance_approval_batch,
  get_finance_approval_batch,
  post_finance_approval_all,
  get_finance_approval_all,
  EXPORT_Withdraw_Finance
} from '@/api/finance'
import moment from 'moment'
import qs from 'qs'
import CONSTANT from '@/config/constant.conf'
export default {
  props: {},
  data() {
    return {
      btn_disabled: false,
      moment: moment,
      dialogVisible: false,
      DataLoading: false,
      tableData: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      parameterObj: {
        phoneNo: '',
        type: 1,
        timeArr: []
      },
      editId: '',
      typeApproval: '',
      selectApproval: [],
      selectApprovalStr: '',
      orderApproval: {
        orderNum: 0,
        sumAmount: 0
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    /**
     * 取消弹窗
     */
    cancelForm1() {
      this.dialogVisible = false
    },
    /**
     * 申请
     */
    handleApply() {
      this.btn_disabled = true
      if (this.typeApproval == 1) {
        post_finance_approval_batch({
          withdrawApplyIds: this.selectApprovalStr
        }).then(res => {
          if (res.code === 0) {
            this.handleApplySuccess()
          }
          this.btn_disabled = false
        })
      } else if (this.typeApproval == 2) {
        post_finance_approval_all({
          phoneNo: this.parameterObj.phoneNo,
          startDate: this.parameterObj.timeArr
            ? this.$utils.getTimer(this.parameterObj.timeArr[0])
            : '',
          endDate: this.parameterObj.timeArr
            ? this.$utils.getTimer(this.parameterObj.timeArr[1])
            : ''
        }).then(res => {
          if (res.code === 0) {
            this.handleApplySuccess()
          }
          this.btn_disabled = false
        })
      } else if (this.typeApproval == 3) {
        post_finance_approval(this.editId).then(res => {
          if (res.code === 0) {
            this.handleApplySuccess()
          }
          this.btn_disabled = false
        })
      }
    },
    handleApplySuccess() {
      this.$message({
        type: 'success',
        message: '操作成功'
      })
      this.dialogVisible = false
      // this.$emit("send", "1");
      this.getData()
    },
    /**
     * 弹框
     */
    openApply(type, item) {
      this.typeApproval = type
      if (type == 3) {
        this.editId = item.id
        this.orderApproval = {
          orderNum: 1,
          sumAmount: item.withdrawRealy
        }
        this.dialogVisible = true
      } else if (type == 2) {
        if (
          this.parameterObj.timeArr.length == 2 ||
          this.parameterObj.phoneNo
        ) {
          get_finance_approval_all({
            phoneNo: this.parameterObj.phoneNo,
            startDate: this.parameterObj.timeArr
              ? this.$utils.getTimer(this.parameterObj.timeArr[0])
              : '',
            endDate: this.parameterObj.timeArr
              ? this.$utils.getTimer(this.parameterObj.timeArr[1])
              : ''
          }).then(res => {
            if (res.code == 200) {
              this.orderApproval = { ...res.data }
              this.dialogVisible = true
            }
          })
        } else {
          this.$message({
            type: 'error',
            message: '不允许对所有数据进行审批!'
          })
        }
      } else if (type == 1) {
        if (this.selectApproval.length == 0) {
          this.$message({
            type: 'error',
            message: '请选择需要打款的数据'
          })
          return
        }
        const arr = []
        this.selectApproval.forEach(item => {
          arr.push(item.id)
        })
        this.selectApprovalStr = arr.join(',')
        get_finance_approval_batch({
          withdrawApplyIds: this.selectApprovalStr
        }).then(res => {
          if (res.code == 200) {
            this.orderApproval = { ...res.data }
            this.dialogVisible = true
          }
        })
      }
    },
    /**
     * 获取列表
     */
    getData() {
      this.DataLoading = true
      get_financeOperate_list({
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
        type: 1,
        phoneNo: this.parameterObj.phoneNo,
        startDate: this.parameterObj.timeArr
          ? this.$utils.getTimer(this.parameterObj.timeArr[0])
          : '',
        endDate: this.parameterObj.timeArr
          ? this.$utils.getTimer(this.parameterObj.timeArr[1])
          : ''
      }).then(res => {
        this.DataLoading = false
        if (res.code === 0) {
          this.tableData = res.data
          this.total = res.totalCount
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },
    /** 导出 */
    handUpload() {
      const obj = {
        status: this.parameterObj.status,
        phoneNo: this.parameterObj.phoneNo,
        token: this.$utils.getToken(),
        startDate: this.parameterObj.timeArr
          ? this.$utils.getTimer(this.parameterObj.timeArr[0])
          : '',
        endDate: this.parameterObj.timeArr
          ? this.$utils.getTimer(this.parameterObj.timeArr[1])
          : '',
        pageNumber: this.currentPage,
        pageSize: this.pageSize
      }
      window.location.href = EXPORT_Withdraw_Finance(obj)
    },
    handleSelectionChange(val) {
      this.selectApproval = val
    }
  }
}
</script>

<style scoped>
</style>
