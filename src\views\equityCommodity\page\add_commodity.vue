<template>
  <!--  新增权益商品 -->
  <div>
    <ycTitle style="margin-top: 20px;" />
    <!-- 【 H5商品 】 -->
    <section v-if="ruleForm.type == 1" v-loading="DataLoading">
      <el-form
        ref="ruleForm"
        style="margin:50px auto;width: 600px;"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="商品名称：" prop="title">
          <el-input v-model="ruleForm.title" style="width: 300px;" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品描述：">
          <el-input v-model="ruleForm.subTitle" style="width: 300px;" placeholder="颜色使用# &包含" />
        </el-form-item>
        <el-form-item label="色号：" prop="subTitleColor">
          <span class="colorBg-wrap">
            <el-input v-model="ruleForm.subTitleColor" style="width: 300px;" placeholder="请输入" />
            <i :style="{'background':ruleForm.subTitleColor}" class="colorBg" />
          </span>
        </el-form-item>
        <el-form-item label="商品图片：" prop="coverPic">
          <ycUpload
            ele-name="coverPic"
            :pic-url="ruleForm.coverPic"
            @uploadSuccess="(url) => {ruleForm.coverPic = url}"
          />
        </el-form-item>
        <el-form-item label="商品详情图片：" prop="detailPic">
          <ycUpload
            ele-name="detailPic"
            :pic-url="ruleForm.detailPic"
            @uploadSuccess="(url) => {ruleForm.detailPic = url}"
          />
        </el-form-item>
        <el-form-item label="角标图片：" prop="labelPic">
          <ycUpload
            ele-name="labelPic"
            :pic-url="ruleForm.labelPic"
            :show-delete="true"
            @remove="removeUrl"
            @uploadSuccess="(url) => {ruleForm.labelPic = url}"
          />
        </el-form-item>
        <el-form-item label="所属分类：" prop="cid">
          <el-select v-model="ruleForm.cid" placeholder="请选择所属分类">
            <el-option
              v-for="(item,index) in category_list"
              :key="index"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转类型：" prop="linkType">
          <el-radio-group v-model="ruleForm.linkType">
            <el-radio :label="1">内链</el-radio>
            <el-radio :label="2">外链</el-radio>
            <el-radio :label="3">动态链接</el-radio>
            <el-radio :label="4">淘宝</el-radio>
            <el-radio :label="5">京东</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="跳转链接：" prop="linkUrl">
          <el-input
            v-if="ruleForm.linkType != 1 && ruleForm.linkType != 3"
            v-model="ruleForm.linkUrl"
            style="width: 300px;"
            placeholder="请输入跳转链接"
          />
          <!-- 内链跳转  -->
          <el-select
            v-if="ruleForm.linkType == 1"
            v-model="ruleForm.linkUrl"
            clearable
            placeholder="请选择跳转页面"
          >
            <el-option
              v-for="(item,index) in innerlinks"
              :key="index"
              :label="item.title"
              :value="item.code"
            />
          </el-select>
          <!-- 动态链接 -->
          <el-select
            v-if="ruleForm.linkType == 3"
            v-model="ruleForm.linkUrl"
            clearable
            placeholder="请选择跳转链接"
          >
            <el-option
              v-for="(item, index) in dynamicLinkList"
              :key="index"
              :label="item.msg"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <!-- 淘宝Id-->
        <el-form-item v-if="ruleForm.linkType == 4" label="活动Id">
          <el-input v-model="ruleForm.activityId" clearable placeholder="请输入活动Id" />
        </el-form-item>
        <el-form-item label="显示设备：" prop="platform">
          <el-checkbox-group v-model="ruleForm.platform">
            <el-checkbox label="0">android</el-checkbox>
            <el-checkbox label="1">ios</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="国外可见">
          <el-radio-group v-model="ruleForm.abroadVisible">
            <el-radio :label="1">可见</el-radio>
            <el-radio :label="0">不可见</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态：" prop="status">
          <el-radio-group v-model="ruleForm.status">
            <el-radio :label="0">上架</el-radio>
            <el-radio :label="1">下架</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序：" prop="sort">
          <el-input v-model="ruleForm.sort" style="width: 300px;" placeholder="请输入排序值" />
        </el-form-item>
      </el-form>
    </section>

    <!-- 【 api商品 】 -->
    <section v-if="ruleForm.type == 0" v-loading="DataLoading">
      <h1 class="title">基本信息</h1>
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
        style="padding-top: 20px;"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="商品名称：" prop="title">
              <el-input v-model="ruleForm.title" style="width: 260px;" placeholder="请输入商品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="描述：" prop="subTitle">
              <el-input v-model="ruleForm.subTitle" style="width: 260px;" placeholder="颜色使用# &包含" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="色号：" prop="subTitleColor">
              <span class="colorBg-wrap">
                <el-input v-model="ruleForm.subTitleColor" style="width: 100px;" placeholder="请输入" />
                <i :style="{'background':ruleForm.subTitleColor}" class="colorBg" />
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属分类：" prop="cid">
              <el-select v-model="ruleForm.cid" placeholder="请选择所属分类">
                <el-option
                  v-for="(item,index) in category_list"
                  :key="index"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态：" prop="status">
              <el-radio-group v-model="ruleForm.status">
                <el-radio :label="0">上架</el-radio>
                <el-radio :label="1">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="国外可见">
              <el-radio-group v-model="ruleForm.abroadVisible">
                <el-radio :label="1">可见</el-radio>
                <el-radio :label="0">不可见</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序：" prop="sort">
              <el-input v-model="ruleForm.sort" style="width: 260px;" placeholder="请输入排序值" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="分类商品图片：" prop="coverPic">
              <ycUpload
                ele-name="coverPic"
                :pic-url="ruleForm.coverPic"
                @uploadSuccess="(url) => {ruleForm.coverPic = url}"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="角标图片：" prop="labelPic">
              <ycUpload
                ele-name="labelPic"
                :pic-url="ruleForm.labelPic"
                :show-delete="true"
                @remove="removeUrl"
                @uploadSuccess="(url) => {ruleForm.labelPic = url}"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="详情图片：" prop="detailPic">
              <ycUpload
                ele-name="detailPic"
                :pic-url="ruleForm.detailPic"
                @uploadSuccess="(url) => {ruleForm.detailPic = url}"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="订单类型：" prop="coverPic">
              <el-select v-model="ruleForm.orderTypeId" placeholder="请选择所属分类">
                <el-option
                  v-for="(item,index) in orderTypeList"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="详情模板：" prop="labelPic">
              <el-radio-group v-model="ruleForm.template">
                <el-radio :label="1">模板一</el-radio>
                <el-button type="text" style="margin-right:10px" @click="previewer(1)">预览</el-button>
                <el-radio :label="2">模板二</el-radio>
                <el-button type="text" @click="previewer(2)">预览</el-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="温馨提示：" prop="tips">
              <!--              <el-input v-model="ruleForm.tips" style="width: 300px;"></el-input>-->
              <ycTinymce ref="content" text-id="content" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <h1 class="title">商品SKU</h1>
      <div v-for="(data,dataIndex) in skusForm" :key="dataIndex" class="skuWrap">
        <el-form
          ref="skusForm"
          :model="data"
          :rules="skusRules"
          label-width="120px"
          class="demo-ruleForm"
        >
          <!--  已选择商品类型列表 -->
          <el-row>
            <el-col :span="24">
              <el-form-item v-if="ruleForm.template === 1" label="购买类型：" prop="vipTypeValue">
                <el-input v-model="data.vipTypeValue" style="width: 300px;" placeholder="请输入购买类型名称" />
                <span style="margin-left: 20px;">
                  <el-button
                    type="danger"
                    plain
                    :disabled="skusForm.length === 1"
                    icon="el-icon-minus"
                    circle
                    @click="putSkuGoods(0,skusForm,dataIndex,'vipVal')"
                  />
                  <el-button
                    v-if="dataIndex+1 === skusForm.length"
                    type="primary"
                    plain
                    icon="el-icon-plus"
                    circle
                    @click="putSkuGoods(1,skusForm,dataIndex,'vipVal')"
                  />
                </span>
              </el-form-item>
            </el-col>
          </el-row>
          <section v-for="(item,itemIndex) in data.detailed" :key="itemIndex">
            <!--  已选择商品（面值）列表 -->
            <el-form ref="detailedRules" :model="item" :rules="detailedRules" label-width="120px">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="选择商品：" prop="thirdGoodsId">
                    <el-button
                      type="success"
                      plain
                      @click="handGoods(item,itemIndex,data.detailed)"
                    >
                      {{ item.thirdGoodsId
                        ? '重新选择' : '请选择' }}
                    </el-button>
                    <!-- 已选择商品显示商品id、商品名称 -->
                    <span
                      v-if="item.thirdGoodsId"
                    >{{ item.thirdGoodsNo }} -------- {{ item.thirdGoodsName }} -- {{ item.virtualType | formateGoodsType }}</span>
                    <span style="margin-left: 20px;">
                      <el-button
                        type="danger"
                        plain
                        :disabled="data.detailed.length === 1"
                        icon="el-icon-minus"
                        circle
                        @click="putSkuGoods(0,data.detailed,itemIndex,'goodsVal')"
                      />
                      <el-button
                        v-if="itemIndex+1 === data.detailed.length"
                        type="primary"
                        plain
                        icon="el-icon-plus"
                        circle
                        @click="putSkuGoods(1,data.detailed,itemIndex,'goodsVal')"
                      />
                    </span>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 已选择商品 ，显示商品价格 -->
              <el-row v-if="item.thirdGoodsId">
                <el-row>
                  <el-col :span="5">
                    <el-form-item label="显示名称：" prop="vipFaceValue">
                      <el-input v-model="item.vipFaceValue" placeholder="请输入" style="width: 180px;" />
                    </el-form-item>
                  </el-col>
                  <el-col v-if="ruleForm.template === 2" :span="5">
                    <el-form-item label="显示图片：" prop="goodsPicture">
                      <ycUpload
                        :ele-name="`goodsPicture-${itemIndex}`"
                        :pic-url="item.goodsPicture"
                        @uploadSuccess="(url) => {
                          item.goodsPicture = url}"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="官方价：" prop="officialPrice">
                      <el-input
                        v-model="item.officialPrice"
                        style="width: 80px;"
                        :disabled="item.thirdGoodsTableId !== 1020"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="3">
                    <el-form-item label="第三方售价：" prop="thirdGoodsPrice">
                      <el-input
                        v-model="item.thirdGoodsPrice"
                        style="width: 80px;"
                        :disabled="true"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="3">
                    <el-form-item label="销售价：" prop="price">
                      <el-input v-model="item.price" placeholder="请输入" style="width: 90px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="3">
                    <el-form-item label="库存：" prop="inventory">
                      <el-input v-model="item.inventory" style="width: 90px;" :disabled="true" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="状态：" prop="status">
                      <el-radio-group v-model="item.status">
                        <el-radio :label="0">上架</el-radio>
                        <el-radio :label="1">下架</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="充值账号类型：" prop="rechargeAccountType">
                      <el-checkbox-group v-model="item.rechargeAccountType">
                        <el-checkbox :label="`phone`">手机号</el-checkbox>
                        <el-checkbox :label="`qq`">QQ账号</el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- <el-col :span="6">
                  <el-form-item label="充值账号类型：" prop="virtualType">
                    <el-radio-group v-model="item.virtualType">
                      <el-radio label="1">直冲</el-radio>
                      <el-radio label="2">卡密</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>-->
              </el-row>
            </el-form>
          </section>
        </el-form>
      </div>
    </section>

    <div style="text-align: center;margin: 40px 0;">
      <el-button plain icon="el-icon-arrow-left" @click="onCallback()">返回</el-button>
      <el-button
        style="width:100px;"
        type="primary"
        :disabled="btn_disabled"
        @click="handUpdated('ruleForm')"
      >提交</el-button>
    </div>

    <!--  选择商品库  -->
    <section>
      <el-dialog
        title="选择商品库"
        width="1000px"
        :visible.sync="dialogFormVisible"
        :before-close="onHide"
        :close-on-click-modal="false"
      >
        <commodityBank :get-goods="true" @getGoodsItem="getGoodsItem" />
      </el-dialog>
    </section>
    <!-- 预览 -->
    <section>
      <el-dialog :visible.sync="dialogVisible" width="60%" title="预览" :before-close="onPreviewHide">
        <div style="display:flex;justify-content:center;align-items:center">
          <img
            v-if="dialogVisibleType == 1"
            class="showDom"
            src="@/assets/img/modelO.png"
            alt="logo"
          >
          <img
            v-if="dialogVisibleType == 2"
            class="showDom"
            src="@/assets/img/modelT.png"
            alt="logo"
          >
        </div>
      </el-dialog>
    </section>
  </div>
</template>

<script>
import {
  goods_virtual_add,
  goods_virtual_category_list,
  goods_virtual_getGoodsVirtualInfo,
  goods_virtual_order_type_all_get
} from '@/api/equityCommodity'
import { get_innerlinkcode } from '@/api/home'
import { GET_PORTAL_LINK_SOURCE } from '@/api/operate'

export default {
  components: {
    ycTitle: () => import('@/components/yc-title/title'),
    ycUpload: () => import('@/components/yc-upload/upload'), // 图片上传
    ycTinymce: () => import('@/components/yc-tinymce/newTinymce'), // 富文本编辑
    commodityBank: () => import('./commodityBank') // 权益商品库列表
  },
  filters: {
    formateGoodsType(val) {
      let str = ''
      switch (Number(val)) {
        case -1:
          str = '直冲'
          break
        case 0:
          str = '二维码'
          break
        case 1:
          str = '条形码'
          break
        case 2:
          str = '条形码和二维码'
          break
        case 3:
          str = '卡券URL地址'
          break
        case 4:
          str = '只包含密码'
          break
        case 5:
          str = '卡号和密码'
          break
      }
      return str
    }
  },
  data() {
    const validateColor = (rule, value, callback) => {
      const testReg = /^#([a-fA-F\d]{6}|[a-fA-F\d]{3})$/
      if (value && !testReg.test(value)) {
        return callback(new Error('请输入16进制格式'))
      } else {
        callback()
      }
    }
    return {
      itemId: '',
      category_list: [],
      orderTypeList: [],
      btn_disabled: false,
      dialogVisible: false,
      dialogVisibleType: 1,
      ruleForm: {
        type: '',
        abroadVisible: '',
        title: '',
        coverPic: '',
        detailPic: '',
        labelPic: '',
        cid: '',
        linkType: '',
        linkUrl: '',
        platform: [],
        status: '',
        sort: '',
        subTitleColor: '',
        orderTypeId: '',
        template: 1,
        // api
        subTitle: '',
        tips: ''
      },
      rules: {
        title: [
          { required: true, message: '商品名称不能为空', trigger: 'blur' }
        ],
        coverPic: [
          { required: true, message: '商品图片不能为空', trigger: 'blur' }
        ],
        detailPic: [
          { required: true, message: '商品详情图片不能为空', trigger: 'blur' }
        ],
        cid: [{ required: true, message: '所属分类不能为空', trigger: 'blur' }],
        subTitleColor: [{ validator: validateColor, trigger: 'blur' }],
        // api
        subTitle: [
          { required: true, message: '描述不能为空', trigger: 'blur' }
        ],
        status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
        tips: [{ required: true, message: '温馨提示不能为空', trigger: 'blur' }]
      },
      skusForm: [
        {
          vipTypeValue: '',
          index: 1,
          detailed: [
            {
              thirdGoodsTableId: '',
              thirdGoodsId: '',
              vipFaceValue: '',
              goodsPicture: '',
              price: '',
              thirdGoodsPrice: '',
              officialPrice: '',
              inventory: '',
              thirdGoodsName: '',
              thirdGoodsNo: '',
              status: '',
              rechargeAccountType: [],
              virtualType: ''
            }
          ]
        }
      ],
      skusRules: {
        vipTypeValue: [
          { required: true, message: '类型名称不能为空', trigger: 'change' }
        ]
      },
      detailedRules: {
        vipFaceValue: [
          { required: true, message: '名称不能为空', trigger: 'change' }
        ],
        goodsPicture: [
          { required: true, message: '图片不能为空', trigger: 'change' }
        ],
        thirdGoodsId: [
          { required: true, message: '请选择商品', trigger: 'change' }
        ],
        price: [{ required: true, message: '价格不能为空', trigger: 'change' }],
        officialPrice: [
          { required: true, message: '价格不能为空', trigger: 'change' }
        ],
        status: [
          { required: true, message: '状态不能为空', trigger: 'change' }
        ],
        rechargeAccountType: [
          { required: true, message: '充值账号类型不能为空', trigger: 'change' }
        ],
        virtualType: [
          { required: true, message: '权益商品类型不能为空', trigger: 'change' }
        ]
      },
      innerlinks: [],
      dynamicLinkList: [],
      dialogFormVisible: false,
      skusCurrent: {},
      DataLoading: false,
      timer: 0
    }
  },
  beforeRouteEnter(to, from, next) {
    to.meta.title = to.query.itemId ? '编辑权益商品' : '新增权益商品'
    next()
  },
  created() {
    this.ruleForm.type = this.$route.query.type // 0api商品,1H5商品
    this.itemId = this.$route.query.itemId // 商品id，新增为空
    this.init()
  },
  destroyed() {
    clearInterval(this.timer)
  },
  methods: {
    onPreviewHide() {
      this.dialogVisible = false
    },
    previewer(val) {
      this.dialogVisibleType = val
      this.dialogVisible = true
    },
    getPortalLink() {
      GET_PORTAL_LINK_SOURCE().then(res => {
        if (res.code === 0) {
          this.dynamicLinkList = res.data
        }
      })
    },
    init() {
      // 获取分类数据
      this.getPortalLink()
      goods_virtual_category_list().then(res => {
        if (res.code === 0) {
          this.category_list = res.data
        }
      })
      goods_virtual_order_type_all_get().then(res => {
        if (res.code === 0) {
          this.orderTypeList = res.data
        }
      })
      // 获取内链跳转数据
      get_innerlinkcode().then(res => {
        if (res.code === 0) {
          this.innerlinks = res.data
        }
      })
      if (this.itemId) {
        // 获取商品详情
        this.DataLoading = true
        goods_virtual_getGoodsVirtualInfo({
          id: this.itemId
        }).then(res => {
          this.DataLoading = false
          if (res.code === 0) {
            const data = res.data.goodsVirtual
            const sku = res.data.sku
            this.ruleForm.type = data.type
            this.ruleForm.abroadVisible = data.abroadVisible
            this.ruleForm.title = data.title
            this.ruleForm.coverPic = data.coverPic
            this.ruleForm.detailPic = data.detailPic
            this.ruleForm.labelPic = data.labelPic
            this.ruleForm.cid = data.cid
            this.ruleForm.linkType = data.linkType
            this.ruleForm.linkUrl = data.linkUrl
            this.ruleForm.platform = data.platform
              ? data.platform.split(',')
              : []
            this.ruleForm.status = data.status
            this.ruleForm.activityId = data.activityId
            this.ruleForm.sort = data.sort
            this.ruleForm.subTitle = data.subTitle
            this.ruleForm.subTitleColor = data.subTitleColor
            this.ruleForm.template = data.template
            this.ruleForm.orderTypeId = data.orderTypeId
            if (this.ruleForm.type == 0) {
              // api商品
              this.ruleForm.tips = data.tips
              if (data.template == 1) {
                this.skusForm = sku.map(item => {
                  item.detailed = item.detailed.map(chileItem => {
                    if (!chileItem.goodsPicture) {
                      chileItem.goodsPicture = ''
                    }
                    return chileItem
                  })
                  return item
                })
              } else {
                this.skusForm = [
                  {
                    vipTypeValue: '',
                    // vipType: 1,
                    detailed: sku
                  }
                ]
              }
              // console.log(this.ruleForm.tips)
              window.tinyMCE.activeEditor.setContent(this.ruleForm.tips)
              // console.log(this.ruleForm)
              // if (this.$refs.content) {
              //   this.$refs.content.value = this.ruleForm.tips; //温馨提示
              // } else {
              //   this.timer = setInterval(() => {
              //     if (this.$refs.content) {
              //       this.$refs.setContent(this.ruleForm.tips);

              //       this.$refs.content.value = this.ruleForm.tips; //温馨提示
              //       clearInterval(this.timer);
              //     }
              //   }, 10);
              // }
            }
          }
        })
      }
    },
    putSkuGoods(type, list, index, valType) {
      // todo: 增加商品
      if (type == 1) {
        // 增加
        const obj = {
          thirdGoodsTableId: '',
          thirdGoodsId: '',
          vipFaceValue: '',
          goodsPicture: '',
          price: '',
          thirdGoodsPrice: '',
          officialPrice: '',
          inventory: '',
          thirdGoodsName: '',
          thirdGoodsNo: '',
          status: '',
          rechargeAccountType: [],
          virtualType: ''
        }
        if (valType === 'vipVal') {
          // todo: 增加类型(sku)
          const skuObj = {
            vipTypeValue: '',
            index: list.length + 1,
            detailed: [obj]
          }
          list.push(skuObj)
        } else if (valType === 'goodsVal') {
          // todo: 增加面值
          list.push(obj)
        }
      } else {
        // 减 sku ,至少保留一个
        if (list.length > 1) list.splice(index, 1)
      }
    },
    getGoodsItem(item) {
      // 选择商品库回调
      this.skusCurrent.thirdGoodsId = item.id // 商品库的ID
      this.skusCurrent.thirdGoodsTableId = item.supplierId // 供货商ID
      this.skusCurrent.thirdGoodsPrice = item.price // 第三方售价
      this.skusCurrent.officialPrice = item.officialPrice // 官方价
      this.skusCurrent.inventory = item.inventory // 库存
      this.skusCurrent.thirdGoodsName = item.name // 商品名称
      this.skusCurrent.thirdGoodsNo = item.goodsNo // 商品编号
      this.skusCurrent.virtualType = item.goodsType // 商品编号
      this.onHide()
    },
    handGoods(data, index, list) {
      // 打开商品库
      this.dialogFormVisible = true
      this.skusCurrent = data
    },
    onHide() {
      // 关闭商品库
      this.dialogFormVisible = false
    },
    handUpdated(formName) {
      const thar = this
      let flag = true
      console.log(thar.ruleForm, thar.skusForm)
      if (this.ruleForm.type == 0) {
        // todo: api商品，验证sku数组
        // thar.ruleForm.tips = this.$refs.content.value; //获取温馨提示内容
        thar.ruleForm.tips = window.tinyMCE.activeEditor.getContent() // 获取温馨提示内容

        this.$refs['skusForm'].forEach(item => {
          // 验证类型
          item.validate(valid => {
            if (!valid) {
              flag = false
            }
          })
        })
        this.$refs['detailedRules'].forEach(item => {
          // 验证商品
          item.validate(valid => {
            if (!valid) {
              flag = false
            }
          })
        })
      }
      console.log(this.$refs)
      this.$refs['ruleForm'].validate(valid => {
        // 验证基本信息
        if (!valid) {
          flag = false
        }
      })
      if (flag) {
        // todo 字段验证正确
        this.btn_disabled = true
        const parameter = thar.ruleForm
        if (thar.itemId) parameter.id = thar.itemId // 编辑
        if (this.ruleForm.type == 0) {
          // api商品
          parameter.sku = thar.skusForm // api商品加入sku字段
        }
        parameter.platform =
          thar.ruleForm.platform.length > 0
            ? thar.ruleForm.platform.join(',')
            : '' // 设备
        goods_virtual_add(parameter).then(res => {
          this.btn_disabled = false
          if (res.code === 0) {
            this.$message({
              message: thar.itemId ? '编辑成功' : '新增成功',
              type: 'success'
            })
            thar.delForm()
            thar.onCallback()
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    delForm() {
      // 清空新增时所填内容
      this.$refs.ruleForm.clearValidate() // 移除校验结果
      this.ruleForm = this.$utils.removeObj(this.ruleForm)
      this.itemId = ''
    },
    onCallback() {
      // 返回
      this.$router.push({
        path: '/equityCommodity/commodityList'
      })
    },
    removeUrl(url) {
      this.$confirm('是否删除该图片, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.ruleForm.labelPic = ''
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style scoped lang="scss">
.title {
  display: inline-block;
  background: rgba(216, 216, 216, 0.71);
  width: 150px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  margin: 40px 0 10px;
}

.demo-ruleForm {
  border-bottom: solid 1px #ccc;
  padding-top: 30px;
}

.skuWrap:last-child .demo-ruleForm {
  border-bottom: none;
}

.colorBg-wrap {
  display: inline-flex;
  align-items: center;

  .colorBg {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-left: 20px;
  }
}
.showDom {
  width: 300px;
}
</style>

