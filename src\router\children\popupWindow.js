/*
* 用户管理子路由
* */

const popupWindow = [
  {
    path: '/popupWindow/popupWindowList',
    name: 'popupWindowNewList',
    meta: {
      title: '弹窗列表'
    },
    component: () => import('@/views/popupWindow/page/popupWindowList')
  },
  {
    path: '/popupWindow/popupWindowDetail',
    name: 'newPopupWindowDetail',
    meta: {
      title: '弹窗详情',
      activeMenu: '/popupWindow/popupWindowList'
    },
    component: () => import('@/views/popupWindow/page/popupWindowDetail')
  },
  {
    path: '/popupWindow/popupPathConfig',
    name: 'popupPathConfig',
    meta: {
      title: '路径配置'
    },
    component: () => import('@/views/popupWindow/page/popupPathConfig')
  },
  {
    path: '/popupWindow/popupWindowConfig',
    name: 'popupWindowConfig',
    meta: {
      title: '页面配置',
      activeMenu: '/popupWindow/popupPathConfig'
    },
    component: () => import('@/views/popupWindow/page/popupWindowConfig')
  },
  {
    path: '/popupWindow/popupPathConfigDetail',
    name: 'popupPathConfigDetail',
    meta: {
      title: '路径配置详情',
      activeMenu: '/popupWindow/popupPathConfig'
    },
    component: () => import('@/views/popupWindow/page/popupPathConfigDetail')
  }
]

export default popupWindow
