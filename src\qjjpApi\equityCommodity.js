/*
 * @Author: 陈小豆
 * @Date: 2024-11-13 19:43:02
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-11-15 18:01:55
 */
import CONSTANT from '@/config/constant.conf'
import { getJjjp, postJjjp } from '@/libs/axios.package'
import qs from 'qs'

// 同步蓝色兄弟商品库
export const syncThirdGoods = obj => {
  return getJjjp(`/cms/thirdGoods/syncThirdGoods`, obj)
}
// 获取权益列表
export const thirdGoodsPage = obj => {
  return getJjjp(`/cms/thirdGoods/page`, obj)
}
// 新增 话费套餐
export const thirdGoodsCreate = obj => {
  return postJjjp(`/cms/thirdGoods/create`, obj)
}
// 更新 话费套餐

export const thirdGoodsUpdate = obj => {
  return postJjjp(`/cms/thirdGoods/update`, obj)
}

// 集合权益配置列表
export const thirdGoodsEquityPage = obj => {
  return getJjjp(`/cms/thirdGoodsEquity/page`, obj)
}
// 集合权益配置添加
export const thirdGoodsEquityCreate = obj => {
  return postJjjp(`/cms/thirdGoodsEquity/create`, obj)
}
// 集合权益配置编辑
export const thirdGoodsEquityUpdate = obj => {
  return postJjjp(`/cms/thirdGoodsEquity/update`, obj)
}

// 获取供应商
export const supplierList = obj => {
  return postJjjp(`/cms/thirdGoodsEquity/supplierList`, obj)
}

// 获取供应商
export const thirdGoodsEquityCheck = obj => {
  return postJjjp(`/cms/thirdGoodsEquity/check`, obj)
}

// 权益订单列表
export const thirdGoodsOrderPageList = obj => {
  return getJjjp(`/cms/thirdGoodsOrder/pageList`, obj)
}

// 通过redis的key导入数据
export const thirdGoodsOrderImportExcel = obj => {
  return postJjjp(`/cms/thirdGoodsOrder/importExcel`, obj)
}
// 权益订单编辑
export const thirdGoodsOrderUpdateOrder = obj => {
  return postJjjp(`/cms/thirdGoodsOrder/updateOrder`, obj)
}

export const thirdGoodsOrderExport = data => CONSTANT.qjjpPath + '/cms/thirdGoodsOrder/export?' + qs.stringify(data)
