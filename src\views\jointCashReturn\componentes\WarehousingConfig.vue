<template>
  <div>
    <div>已选择商品价格区间：￥{{ minMaxVal.minPrice }}-￥{{ minMaxVal.maxPrice }}</div>
    <el-button v-if="list.length === 0" class="plus-button" icon="el-icon-plus" @click="handleList('add')" />
    <div class="warehousing-config">
      <div v-for="(item, index) in list" :key="index" class="item">
        <span class="number">{{ index + 1 }}、</span>
        <div class="label1">
          *商品售价：三方售价满足
          <input v-model="item.minPrice" type="text" @change="validatePrice(index, 'minPrice', item.minPrice)" />到
          <input v-model="item.maxPrice" type="text" @change="validatePrice(index, 'maxPrice', item.maxPrice)" />
        </div>
        <div class="label2">
          原价执行：<input
            v-model="item.priceRatio"
            type="text"
            @change="validateNumber(index, 'priceRatio', item.priceRatio)"
          />%
        </div>
        <div class="label3">
          商品打标：
          <el-select
            v-model="item.productMarking"
            multiple
            filterable
            allow-create
            placeholder="请输入"
            @change="handleGoodsLabel(item.productMarking, index)"
          >
            <el-option v-for="item in optionsForLabel" :key="item.id" :label="item.labelName" :value="item.id" />
          </el-select>
        </div>
        <div class="label4">
          运费模板：
          <el-select v-model="item.addressTemplateId" placeholder="请选择">
            <el-option v-for="item in optionsShipping" :key="item.id" :label="item.templateName" :value="item.id" />
          </el-select>
        </div>
        <div v-if="index >= 0" class="add-remove">
          <el-button v-if="index < 9 && index === list.length - 1" icon="el-icon-plus" @click="handleList('add')" />
          <el-button icon="el-icon-minus" @click="handleList('remove', index)" />
        </div>
      </div>
    </div>
    <div class="tips">默认配置：配置项未覆盖部分，走默认配置，原价执行x120%，运费模板：1688-默认</div>
    <div class="desc">
      配置说明：<br />
      三方销售价：三方销售价为商品在三方平台的价格，例如1688的价格<br />
      平台销售价：平台销售价为用户在我们平台内的销售价<br />
      输入数值不可低于100%，最多可配置10条规则<br />
      规则间三方销售价的价格区间不可重复<br />
      示例：三方销售价满足 100 到 200 平台销售价执行为120%，如果商品在三方平台内的销售价为100，则我们平台内的销售价为120
    </div>
    <div class="group-btns">
      <el-button @click="$emit('input', false)">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="submit">执行保存</el-button>
    </div>
  </div>
</template>
<script>
import {crowedAddressTemplateGetData, getCrowdLabelList, CrowdLabelAdd} from '@/api/jointCashReturn'
import {validNumbers} from '@/utils/validate'
import throttle from 'lodash/throttle'
export default {
  name: 'WarehousingConfig',
  props: {
    value: {
      typeof: Boolean,
      default: false
    },
    priceArray: {
      typeof: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      searchLoading: false,
      list: [],
      optionsForLabel: [], // 商品打标列表
      optionsShipping: [],
      checkPrice: {
        minPrice: '',
        maxPrice: ''
      }
    }
  },
  computed: {
    minMaxVal() {
      return {minPrice: this.priceArray?.[0] ?? 0, maxPrice: this.priceArray?.[1] ?? 0}
    }
  },
  created() {
    crowedAddressTemplateGetData().then(res => {
      if (res.code === 0) {
        this.optionsShipping = res?.data ?? []
      }
    })
    this.getLabelList()
  },
  methods: {
    getLabelList() {
      return getCrowdLabelList().then(res => {
        if (res.code === 0) {
          this.optionsForLabel = res?.data ?? []
        }
        return res
      })
    },
    submit() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
      }, 200)
      console.log(this.list, 'this.list')
      if (this.list && this.list.length > 0) {
        const keyArrays = Object.keys(this.list[0])
        for (let i = 0; i < this.list.length; i++) {
          for (let j = 0; j < keyArrays.length; j++) {
            if (
              !this.list[i][keyArrays[j]] ||
              (Array.isArray(this.list[i][keyArrays[j]]) && !this.list[i][keyArrays[j]][0])
            ) {
              this.$message.error('请完善所有项！')
              return
            }
          }
        }
      }
      this.$emit('confirm', this.list)
    },
    validatePrice(index, type, val) {
      const minMaxVal = {
        minPrice: 0,
        maxPrice: 9999999999999999
      }
      const validate = validNumbers(2, minMaxVal.minPrice, minMaxVal.maxPrice, val)
      // 校验输入类型
      if (validate) {
        this.$message.warning(validate)
        this.list[index][type] = ''
        return
      }

      if (!this.checkPrice.minPrice || this.list.length === 1) this.checkPrice.minPrice = minMaxVal.minPrice
      if (!this.checkPrice.maxPrice || this.list.length === 1) this.checkPrice.maxPrice = minMaxVal.maxPrice
      if (this.list.length > 1) {
        const array = this.list.slice(0, index)
        const minPrice = array.reduce((pre, cur) => {
          const maxVal = Math.max(Number(cur.maxPrice), Number(cur.minPrice))
          if (pre < maxVal || pre === 0) {
            pre = maxVal
          }
          return pre
        }, 0)
        if (index === this.list.length - 1) {
          // 如果当前输入是最后一项 最大输入是原最大价
          this.checkPrice.maxPrice = minMaxVal.maxPrice
        } else if (index < this.list.length - 1) {
          // 如果当前输入不是最后一项  minPrice 是前面几项的最大值 maxPrice是后面几项的最小值
          const nexArray = this.list.slice(index + 1, this.list.length)
          const maxPrice = nexArray.reduce((pre, cur) => {
            let minVal =
              Number(cur.maxPrice) && Number(cur.minPrice)
                ? Math.min(Number(cur.maxPrice), Number(cur.minPrice))
                : cur.maxPrice || cur.minPrice
            !minVal ? (minVal = pre) : ''
            if (pre > minVal || pre === 0) {
              pre = minVal
            }
            return pre
          }, 0)
          !maxPrice ? (this.checkPrice.maxPrice = minMaxVal.maxPrice) : (this.checkPrice.maxPrice = Number(maxPrice)) // 若后面几项都未填值取原高低价兜底
        }
        // minPrice 是前面几项的最大值
        !minPrice ? (this.checkPrice.minPrice = minMaxVal.minPrice) : (this.checkPrice.minPrice = Number(minPrice)) // 若前面几项都未填值原最低价兜底
      }
      const checkPrice = {...this.minMaxVal, ...this.checkPrice}
      if (val < checkPrice.minPrice) {
        this.$message.warning(`输入值不能低于${checkPrice.minPrice}`)
        this.list[index][type] = ''
        return
      }
      if (val > checkPrice.maxPrice) {
        this.$message.warning(`输入值不能高于${checkPrice.maxPrice}`)
        this.list[index][type] = ''
        return
      }
      if (index !== 0 && type === 'minPrice' && val == checkPrice.maxPrice) {
        this.$message.warning(`输入值不能等于${checkPrice.maxPrice}`)
        this.list[index][type] = ''
        return
      }
      if (this.list[index].maxPrice && Number(this.list[index].maxPrice) < Number(this.list[index].minPrice)) {
        // 当选择商品的价格有区间时，左右输入值不能相同
        this.$message.warning(`左边值不能高于右边值`)
        this.list[index][type] = ''
        return
      }
    },
    validateNumber(index, type, val) {
      const validate = validNumbers(2, 100, 99999, val)
      // 校验输入类型
      if (validate) {
        this.$message.warning(validate)
        this.list[index][type] = ''
        return
      }
    },
    handleList(type = 'add', index) {
      if (this.list.length < 10 && type === 'add') {
        this.list.push({
          minPrice: '',
          maxPrice: '',
          priceRatio: '',
          productMarking: '',
          addressTemplateId: this.list?.[this.list.length - 1]?.addressTemplateId ?? ''
        })
      }
      if (type === 'remove' && this.list.length > 0) {
        this.list.splice(index, 1)
        // this.list.pop()
      }
    },
    async handleGoodsLabel(val, index) {
      let haveLabel = null
      if (val && val.length) {
        const labelName = val[val.length - 1]
        if (this.optionsForLabel && this.optionsForLabel.length) {
          haveLabel = this.optionsForLabel.find(item => {
            return item.id === labelName || item.labelName === labelName
          })
        }
        if (!haveLabel) {
          await CrowdLabelAdd({labelName})
          await this.getLabelList()
          const newVal = this.optionsForLabel.find(item => item.labelName === labelName)?.id ?? null
          if (!newVal) {
            // 无匹配到id删除
            this.list[index].productMarking.pop()
          } else {
            this.$set(this.list[index].productMarking, val.length - 1, newVal)
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped >
.warehousing-config{
  max-height: 50vh;
  overflow-y: auto;
  .item{
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    line-height: 40px;
    margin-bottom: 10px;
    .number{
      display: inline-block;
      width: 26px;
      text-align:right;
    }
    >div{
      margin-right: 10px;
      white-space: nowrap;
    }
    .label1{
      input{
        border-bottom: 1px solid #DCDFE6;
      }
    }
    .label2{
      input{
        display: inline-block;
         height: 40px;
        border-radius: 4px;
        border:1px solid #DCDFE6;
      }
    }
    input{
      display: inline-block;
      width:80px
    }
    .el-select{
      width: 120px;
    }
  }
  .add-remove{
    margin-right: 0;
    .el-button{
      width: 30px;
      height: 30px;
      text-align: center;
      padding:0 0;
    }
  }

}
.tips {
  font-size: 14px;
  color: #ff0000;
  margin: 20px 0;
}
.desc {
  font-size: 14px;
  color: #1a1a1a;
  margin: 20px 0;
  border: 1px solid #DCDFE6;
  line-height: 1.8;
  padding: 5px 10px;
}
.group-btns{
    margin-top: 10px;
    display: flex;
    justify-content: center;
  }
.plus-button{
    margin-top: 10px;
  }
</style>

