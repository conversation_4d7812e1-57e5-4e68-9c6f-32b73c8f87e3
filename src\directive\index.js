import Vue from 'vue'

Vue.directive('error', {
  inserted(e) {
    if (!e.src) {
      e.src = require('../assets/img/imgerror.jpg')
    }
    e.onerror = function() {
      e.src = require('../assets/img/imgerror.jpg')
    }
  }
})

/*
 * 防止重复点击
 * */
Vue.directive('preventReClick', {
  inserted(el, binding) {
    el.addEventListener('click', () => {
      if (!el.disabled) {
        el.disabled = true
        setTimeout(() => {
          el.disabled = false
        }, binding.value || 2000)
      }
    })
  }
})
