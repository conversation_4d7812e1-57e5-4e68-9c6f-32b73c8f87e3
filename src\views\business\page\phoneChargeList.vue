<template>
  <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
    <el-tab-pane
      v-for="(item, index) in tabs"
      :key="index"
      :label="item.title"
      :name="item.id"
    >
      <page v-if="item.id === activeName" :request="request" :list="list" table-title="充值商品配置" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { ADD_MOBILE_GOODS, EDIT_MOBILE_GOODS, GET_MOBILE_GOODS_PAGE } from '@/api/business'

export default {
  components: {
    page
  },
  data() {
    return {
      tabs: [
        {
          id: '1',
          title: '中国移动'
        },
        {
          id: '2',
          title: '中国联通'
        },
        {
          id: '3',
          title: '中国电信'
        }
      ],
      activeName: '',
      listQuery: {},
      request: {
        getListUrl: data => {
          const obj = {
            ...data,
            catType: this.activeName
          }
          return GET_MOBILE_GOODS_PAGE(obj)
        },
        insertHttp: data => {
          const obj = {
            ...data,
            catType: this.activeName
          }
          return ADD_MOBILE_GOODS(obj)
        },
        updateHttp: data => {
          const obj = {
            ...data,
            catType: this.activeName
          }
          return EDIT_MOBILE_GOODS(obj)
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: 'ID',
          key: 'id',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '话费面值',
          key: 'originalPrice',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '商品显示名称',
          key: 'name',
          type: formItemType.input,
          reg: ['required'],
          search: true
        },
        {
          title: '第三方售价',
          key: 'thirdPrice',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '普通用户售价',
          key: 'userPrice',
          reg: ['required'],
          type: formItemType.input
        },
        {
          title: '爵士会员售价',
          key: 'vipPrice',
          reg: ['required'],
          type: formItemType.input
        },
        {
          title: '商品形式',
          key: 'goodsForm',
          reg: ['required'],
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          options: {
            valueType: 'Number'
          },
          list: [
            { label: '直充', value: 1 },
            { label: '卡密', value: 2 }
          ]
        },
        {
          title: '产品类型',
          key: 'productType',
          type: formItemType.radio,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          options: {
            valueType: 'Number'
          },
          list: [
            { label: '快充', value: 1 },
            { label: '慢充', value: 2 }
          ]
        },
        {
          title: '排序',
          key: 'sort',
          type: formItemType.input,
          reg: ['required']
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          reg: ['required'],
          list: [
            {
              label: '启用',
              value: 0
            },
            {
              label: '禁用',
              value: 1
            }
          ],
          search: true
        },
        {
          type: tableItemType.active,
          width: 100,
          headerContainer: true,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.detailsDialog,
              theme: 'warning'
            }
          ]
        }
      ]
    }
  },
  mounted() {
    const localActive = sessionStorage.getItem('phoneChargeListTabActive')
    if (localActive) {
      this.activeName = localActive
    }
    this.activeName = this.tabs[0].id
  },
  methods: {
    handAdd() {

    },
    handleClick(tab) {
      sessionStorage.setItem('phoneChargeListTabActive', tab.name)
    }
  }
}
</script>

<style scoped>
</style>
