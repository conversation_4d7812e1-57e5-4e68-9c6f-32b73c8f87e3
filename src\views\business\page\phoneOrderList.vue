<template>
  <page :request="request" :list="list" table-title="话费订单">
    <div slot="searchContainer" style="display: inline-block">
      <el-button
        plain
        type="warning"
        size="small"
        icon="el-icon-download"
        @click="handExport"
      >导出数据</el-button>
    </div>
  </page>
</template>
<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { EXPORT_MOBILE_ORDER, GET_ORDER_LIST } from '@/api/business'

export default {
  components: {
    page
  },
  data() {
    return {
      listQuery: {},
      request: {
        getListUrl: data => {
          return this.getData(data)
        }
      }
    }
  },
  computed: {
    list() {
      const that = this
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startTime', 'endTime'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          search: true,
          tableHidden: true
        },
        {
          title: '订单编号',
          key: 'orderNo',
          type: formItemType.input
        },
        {
          title: '用户id',
          key: 'userId',
          render: (h, params) => {
            const id = params.data.row.userId
            return h('span', {
              style: {
                cursor: 'pointer',
                color: '#57BEE6'
              },
              on: {
                click() {
                  that.$router.push({
                    name: 'userList_detail',
                    query: {
                      id: id
                    }
                  })
                }
              }
            }, id)
          }
        },
        {
          title: '用户账号',
          key: 'nickName',
          type: formItemType.input
        },
        {
          title: '充值面值',
          key: 'goodsFaceName',
          type: formItemType.input
        },
        {
          title: '运营商',
          key: 'catType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [
            {
              label: '中国移动',
              value: 1
            },
            {
              label: '中国联通',
              value: 2
            },
            {
              label: '中国电信',
              value: 3
            }
          ],
          search: true
        },
        {
          title: '满减券减免(元)',
          key: 'couponsAmount',
          type: formItemType.input
        },
        {
          title: '实付金额',
          key: 'goodsPrice',
          type: formItemType.input
        },
        {
          title: '订单状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [
            {
              label: '待付款',
              value: 'WAITPAY'
            },
            {
              label: '待发货',
              value: 'WAITSEND'
            },
            {
              label: '已完成',
              value: 'FINISHED'
            },
            {
              label: '已取消',
              value: 'CANCELED'
            }
          ],
          search: true
        }
      ]
    }
  },
  methods: {
    getData(data) {
      this.listQuery = { ...this.listQuery, ...data }
      return Promise.all([GET_ORDER_LIST(this.listQuery)]).then(
        res => {
          return Promise.resolve(res[0])
        }
      )
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = EXPORT_MOBILE_ORDER({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>
