/*
 * @Description:
 * @Autor: 蒋雪
 * @Date: 2024-05-11 16:51:14
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-11-14 17:31:44
 */
import CONSTANT from '@/config/constant.conf'
import { getJjjp, postJjjp } from '@/libs/axios.package'
import qs from 'qs'

// 分页查询会员订单
export const membershipOrder = obj => {
  return getJjjp(`/cms/membershipOrder/page`, obj)
}
// 编辑或新增商品分类接口
export const crowdProductCategoryEdit = obj => {
  return postJjjp('/crowd-product-category/edit', obj)
}

export const membershipRefundOrder = obj => {
  return getJjjp(`/cms/membershipRefundOrder/page`, obj)
}

export const doRefund = obj => {
  return postJjjp('/cms/membershipRefundOrder/doRefund', obj)
}

export const aliComplaintPage = obj => {
  return getJjjp(`/cms/complaint/merchant/page`, obj)
}

export const refundOrderexport = data => CONSTANT.qjjpPath + '/cms/membershipRefundOrder/page/export?' + qs.stringify(data)

export const membershipOrderexport = data => CONSTANT.qjjpPath + '/cms/membershipOrder/cycle/export?' + qs.stringify(data)
export const memberOrderexport = data => CONSTANT.qjjpPath + '/cms/membershipOrder/page/export?' + qs.stringify(data)

export const aliComplaintPageexport = data => CONSTANT.qjjpPath + '/cms/complaint/merchant/page/export?' + qs.stringify(data)

export const membershipOrdercycle = obj => {
  return getJjjp(`/cms/membershipOrder/cycle`, obj)
}

export const periodProductTypes = obj => {
  return getJjjp(`/cms/membershipOrder/cycle/periodProductTypes`, obj)
}

export const remainRefundOrder = obj => {
  return postJjjp(`/cms/membershipRefundOrder/remainRefund`, obj)
}

// 恢复用户会员身份
export const recoveryMembership = obj => {
  return postJjjp(`/cms/membershipRefundOrder/recoveryMembership`, obj)
}
