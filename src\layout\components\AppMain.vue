<template>
  <section id="appMain" class="app-main" :class="{'bg-color': key.includes('home')}">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
import Watermark from '@/libs/waterMark'
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    },
    userInfo() {
      return this.$store.getters.userInfo
    },
    lastLoginTime() {
      return this.$store.getters.lastLoginTime
    }
  },
  watch: {
    // userInfo(newV, oldV) {
    //   if (JSON.stringify(newV) !== JSON.stringify(oldV)) {
    //     this.setWaterMark()
    //   }
    // }
  },
  mounted() {
    // this.setWaterMark()
  },
  methods: {
    setWaterMark() {
      const { userName, ip } = this.userInfo
      const time = this.lastLoginTime
      const desc = '版权所有: 成都优卡信息科技有限公司'
      this.$nextTick(() => {
        Watermark.set(`${userName} ${ip} ${time},${desc}`, 'appMain')
      })
    }
  }
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 15px;
}
.fixed-header+.app-main {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
.bg-color {
  background: #F7F8FA;
}
</style>
