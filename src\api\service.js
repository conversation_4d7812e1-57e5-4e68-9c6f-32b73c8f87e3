import { del, get, post } from '@/libs/axios.package'
import CONSTANT from '@/config/constant.conf'
import qs from 'qs'

/**
 * 新增客服标题内容
 */
export const addServiceContent = obj => {
  return post('/serviceConfig/save', obj)
}
/**
 * 客服标题内容详情
 */
export const serviceConfigDetail = obj => {
  return get('/serviceConfig/' + obj.id)
}

/**
 * 获取客服标题内容
 */
export const getServiceList = obj => {
  return get('/serviceConfig/page', obj)
}

/**
 * 展示状态
 */
export const updateStatus = obj => {
  return get('/serviceConfig/updateShowStatus/' + obj.id, obj)
}

/**
 * 修改标题内容
 */
export const updateServiceContent = obj => {
  return post('serviceConfig/update', obj)
}

/**
 * 在线客服欢迎语设置，更新
 */
export const updateServiceWelcome = obj => {
  return post('/serviceWelcome/update', obj)
}

/**
 * 在线客服欢迎语列表
 */
export const getServiceWelcome = obj => {
  return get('serviceWelcome/page', obj)
}

/**
 * 获取一级列表
 */
export const getParentBySiteIds = obj => {
  return get('serviceConfig/getParentBySiteIds', obj)
}
