<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      status-icon
      :rules="rules"
      label-width="80px"
      class="ruleForm"
    >
      <el-form-item label="参数名称" prop="paramName">
        <el-input
          v-model="ruleForm.paramName"
          type="text"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="参数说明" prop="paramDesc">
        <el-input
          v-model="ruleForm.paramDesc"
          type="textarea"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="ruleForm.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="sysmteType == '0'" label="参数配置" prop="paramValue">
        <el-input
          v-model="ruleForm.paramValue"
          type="text"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item v-if="sysmteType == '1'" label="参数配置" prop="paramValue">
        <el-checkbox-group v-model="ruleForm.paramValue">
          <el-checkbox label="2">支付宝支付</el-checkbox>
          <el-checkbox label="1">微信支付</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item style="margin-top: 60px;">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="sure('ruleForm')">确认</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { getSystemParamById } from '@/qjjpApi/system'
export default {
  props: ['sysmteType', 'type', 'sysmteID'],
  data() {
    return {
      ruleForm: {
        paramName: '',
        paramDesc: '',
        status: 1,
        paramValue: []
      },
      rules: {
        paramName: [
          { required: true, message: '此项不能为空', trigger: 'blur' }
        ],
        paramDesc: [
          { required: true, message: '此项不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '此项不能为空', trigger: 'blur' }
        ],
        paramValue: [
          {
            type: this.sysmteType == '1' ? 'array' : 'string',
            required: true,
            message: '此项不能为空',
            trigger: 'change'
          }
        ]
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getSystemParamById({ paramId: this.sysmteID }).then(res => {
        if (res.code == 200) {
          const sysData = res.data
          this.ruleForm = sysData
          if (this.sysmteType == '1') {
            this.ruleForm.paramValue = sysData.paramValue.split(',')
          }
        }
      })
    },
    close() {
      this.$emit('close')
    },
    sure() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          if (this.sysmteType == '1') {
            this.ruleForm.paramValue = this.ruleForm.paramValue.join(',')
          }
          this.$emit('sumbit', this.ruleForm)
        } else {
          return false
        }
      })
    }
  }
}
</script>

