<template>
  <div>
    <el-row type="flex" class="row-bg" justify="end" style="margin: 0 0 20px;">
      <el-button type="primary" plain icon="el-icon-circle-plus-outline" @click="linkDetails('add')">添加类型</el-button>
      <el-button type="success" plain icon="el-icon-refresh" @click="refresh()">刷新</el-button>
    </el-row>
    <div class="tab-head">
      <span class="title">数据列表</span>
    </div>
    <el-table
      v-loading="DataLoading"
      :data="tableData"
      border
      style="width: 100%;margin-bottom: 30px;"
    >
      <el-table-column label="序列" type="index" />
      <el-table-column prop="username" label="编号" />
      <el-table-column prop="created_at" label="类型名称" />
      <el-table-column prop="created_at" label="属性数量" />
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button type="warning" size="mini" plain @click="linkDetails('edit')">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 70, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!--新增/编辑 类型-->
    <section>
      <el-dialog
        :title="isType === 'add' ? '新增类型' :'编辑类型'"
        width="520px"
        :visible.sync="dialogFormVisible"
        :show-close="false"
      >
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm">
          <el-form-item label="类型名称：" prop="name" class="form-item">
            <el-input v-model="ruleForm.name" placeholder="请输入类型名称" style="width:320px;" />
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="userUpdated('0','ruleForm')">取 消</el-button>
          <el-button type="primary" :loading="btn_disabled" :disabled="btn_disabled" @click="userUpdated('1','ruleForm')">确 定</el-button>
        </div>
      </el-dialog>
    </section>
  </div>
</template>

<script>
export default {
  data() {
    return {
      parameterObj: {
        phone: ''
      },
      DataLoading: false,
      tableData: [1],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      isType: '',
      dialogFormVisible: false,
      ruleForm: {
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '类型名称不能为空', trigger: 'blur' }
        ]
      },
      loadingFlag: false,
      btn_disabled: false
    }
  },
  mounted() {
  },
  methods: {
    getData() {
      // this.DataLoading = true;
      // get_accountData({
      //   username:this.parameterObj.username,
      //   role_id	:this.parameterObj.role_id,
      //   page: this.currentPage,
      //   per_page: this.pageSize
      // }).then(res =>{
      //   this.loadingFlag = false;
      //   this.DataLoading = false;
      //   if(res.code == 200){
      //     this.tableData = res.data;
      //     this.total = res.meta.total;
      //   }else{
      //     this.$message.error(res.message);
      //   }
      // })
    },
    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getData()
    },
    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getData()
    },
    linkDetails(type) {
      this.isType = type
      this.dialogFormVisible = true
    },
    // 【操作】 新增 | 修改
    userUpdated(isShow, formName) {
      const thar = this
      if (isShow === '0') { // 取消
        thar.dialogFormVisible = false
      } else {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.btn_disabled = true
            if (thar.isType === 'add') { // 新增

            } else {

            }
          }
        })
      }
    },
    refresh() {
      this.getData()
    }
  }
}
</script>

<style scoped>

</style>
