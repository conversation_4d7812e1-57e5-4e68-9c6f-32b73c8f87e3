
<template>
  <div>
    <page ref="openingList" :request="request" :list="list" table-title="开场白管理">
      <div slot="searchContainer" style="display: inline-block">
        <!-- <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button> -->
        <el-button
          plain
          icon="el-icon-circle-plus-outline"
          type="primary"
          size="small"
          @click="handleAdd"
        >新增开场白</el-button>
        <el-button
          plain
          icon="el-icon-setting"
          type="primary"
          size="small"
          @click="$emit('classifyManage','opening')"
        >管理分类</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { messageStyleList, messageStyleListexport, modelRepositoryplatform, categoryList } from '@/qjjpApi/operate'
import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      list2: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ],
      siteIds: [],
      styleClassifyList: [],
      listQuery: {
        category: 2
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          await modelRepositoryplatform().then(res => {
            if (res.code === 200) {
              if (res.data && res.data.length) {
                this.siteIds = res.data
              }
            }
          })
          await this.getCategoryList()

          const list = await messageStyleList(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '模型平台',
          key: 'platform',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'code'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '模型名称',
          key: 'modelName',
          type: formItemType.input,
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '性别预设',
          key: 'sex',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list1,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '分类名称',
          key: 'styleCategoryIds',
          // type: formItemType.selectMultiple,
          // multiple: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.styleClassifyList,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '开场白名称',
          key: 'styleName',
          type: formItemType.input,
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '序号',
          key: 'id'
        },
        {
          title: '性别预设',
          key: 'sex',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list1,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          clearable: false
        },
        {
          title: '分类名称',
          key: 'styleClassify',
          render: (h, params) => {
            const data = params.data.row
            const styleCategories = data.styleCategories
            let text = '-'
            if (styleCategories && styleCategories.length) {
              text = styleCategories.map(n => {
                const item = this.styleClassifyList.find(t => t.value == n.categoryId)
                if (item) {
                  return item.label
                }
                return null
              }).filter(n => n).join(',')
            }
            return h('span', text)
          }
        },
        {
          title: '开场白名称',
          key: 'styleName'
        },
        {
          title: '问题',
          key: 'question'
        },
        {
          title: '引导词',
          key: 'guideContent'
        },
        {
          title: '模型平台',
          key: 'platform',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'code'
          },
          val: this.listQuery.siteId,
          reg: ['required']
        },
        {
          title: '模型名称',
          key: 'modelName',
          type: formItemType.input
        },
        {
          title: '模型参数',
          key: 'modelCs',
          tooltip: false,
          render: (h, params) => {
            return this.renders(h, params)
          }
        },
        {
          title: '更新时间',
          key: 'updateTime'
        },
        {
          title: '更新人员',
          key: 'adminName'
        },
        {
          title: '排序',
          key: 'sort'
        },
        {
          title: '启用状态',
          key: 'status',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: this.listQuery.siteId,
          reg: ['required'],
          clearable: false
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit1',
              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                localStorage.setItem(`openingListDetail`, JSON.stringify(params))
                this.$router.push({
                  name: 'qjjpOpeningDetail',
                  query: {
                    id: params.id
                  }
                })
              }
            }
          ]
        }
      ]
    }
  },
  async mounted() {
    const list1 = await messageStyleList()
    // const { records, total } = list1.data
    // let dataList = []
    // console.info(records, 'records')
    // if (records && records.length) {
    //   dataList = [total, ...records]
    // }
    // const result = {
    //   data: dataList
    // }
    // console.info(result, 'result')
  },
  created() {},
  methods: {
    getCategoryList() {
      return new Promise(async(r) => {
        const { code: tyleClassifyCode, data: styleClassifyListData } = await categoryList({ type: 2 })
        if (tyleClassifyCode == 200) {
          if (styleClassifyListData && styleClassifyListData.length) {
            this.styleClassifyList = styleClassifyListData.map(item => {
              return {
                value: item.id,
                label: item.name
              }
            })
          }
        }
        r(true)
      })
    },
    aa(params) {
      localStorage.setItem(`openingListDetail`, JSON.stringify(params))
      this.$router.push({
        name: 'qjjpOpeningDetail',
        query: {
          id: params.id
        }
      })
    },
    initFN() {
      this.$refs.openingList.getSubSuccess()
    },
    renders(h, params, popArr, key) {
      const data = params.data.row
      return (
        <div>
          <el-popover trigger='hover' placement='top'>
            <div onClick={() => {
              this.aa(data)
            }} class='styleManagePop'>
              <div class='popTitle'>模型参数详情</div>
              <div>
                <div class='toolView'>
                  <el-tooltip
                    content='用于控制输出tokens的多样性，TopP值越大输出的tokens类型越丰富，取值范围0~1，未填写时默认值为0.7'
                    placement='top-start'
                    class='styleManagePopTooltip'
                  >
                    <i class='el-icon-question' style='font-size: 14px' />
                  </el-tooltip>
                  <div class='popView'>temperature:{data.temperature ? data.temperature : '-'}</div>
                </div>
                <div class='toolView'>
                  <el-tooltip
                    content='用于控制输出tokens的多样性，TopP值越大输出的tokens类型越丰富，取值范围0~1，未填写时默认值为0.9'
                    placement='top-start'
                    class='styleManagePopTooltip'
                  >
                    <i class='el-icon-question' style='font-size: 14px' />
                  </el-tooltip>
                  <div class='popView'>{'top_p:' + (data.topP ? data.topP : '-')}</div>
                </div>
              </div>
              <div>
                <div class='toolView'>
                  <el-tooltip
                    content='选择预测值最大的k个token进行采样，取值范围0-1000，0表示不生效，未填写时默认值为0'
                    placement='top-start'
                    class='styleManagePopTooltip'
                  >
                    <i class='el-icon-question' style='font-size: 14px' />
                  </el-tooltip>
                  <div class='popView'>{'topK:' + (data.topK ? data.topK : '-')}</div>
                </div>
                <div class='toolView'>
                  <el-tooltip
                    content='模型是否采样，未填写时将使用模型平台默认值'
                    placement='top-start'
                    class='styleManagePopTooltip'
                  >
                    <i class='el-icon-question' style='font-size: 14px' />
                  </el-tooltip>
                  <div class='popView'>{'doSample:' + (data.doSample == 'true' ? '是' : data.doSample == 'false' ? '否' : '-')}</div>
                </div>
              </div>
              <div>
                <div class='toolView'>
                  <el-tooltip
                    content='存在惩罚，如果为正，值越大，模型谈论到新话题的概率越大，取值范围为 [-2.0, 2.0] ，未填写时将使用模型平台默认值'
                    placement='top-start'
                    class='styleManagePopTooltip'
                  >
                    <i class='el-icon-question' style='font-size: 14px' />
                  </el-tooltip>
                  <div class='popView'>{'presencePenalty:' + (data.presencePenalty ? data.presencePenalty : '-')}</div>
                </div>
                <div class='toolView'>
                  <el-tooltip
                    content='频率惩罚，如果为正，值越大，模型逐字重复同一行的概率越小，取值范围为 [-2.0, 2.0] ，未填写时将使用模型平台默认值'
                    placement='top-start'
                    class='styleManagePopTooltip'
                  >
                    <i class='el-icon-question' style='font-size: 14px' />
                  </el-tooltip>
                  <div class='popView'>{'frequencyPenalty:' + (data.frequencyPenalty ? data.frequencyPenalty : '-')}</div>
                </div>
              </div>
            </div>
            <span slot='reference' class='temperatures'>{'temperature:' + (data.temperature ? data.temperature : '-') + '...'}</span>
          </el-popover>
        </div>
      )
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = messageStyleListexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    handleAdd() {
      this.$router.push({
        name: 'qjjpOpeningDetail',
        query: {
          type: 'add'
        }

      })
    }
  }
}
</script>

<style lang="scss" scoped>

.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}

</style>

<style lang="scss">
.temperatures{
    color:#409eff;
    cursor: pointer;
  }
.styleManagePop{
  width: 340px;
  .styleManagePopTooltip{
    color:#409eff;
  }
  .popTitle{
    font-weight: 600;
  }
  .toolView{
    width: 170px;
    display: inline-block;
  }
  .popView{
    color: #409eff;
    margin: 0 20px 0 3px;
    padding: 5px 0;
    display: inline-block;
    cursor: pointer;
  }
}

</style>
