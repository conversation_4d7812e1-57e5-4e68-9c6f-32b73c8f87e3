<template>
  <div>
    <drawerForm
      v-if="show"
      :dialog-footer-state="dialogFooterState"
      :label-width="labelWidth"
      :click-type="clickType"
      :before-update="beforeUpdate"
      :title="title"
      :drawer-visible.sync="drawerVisible"
      :form-item-list="formItemList"
      :data="data"
      :urls="urls"
      @getSubSuccess="$emit('getSubSuccess',true)"
    />
  </div>
</template>

<script>
import drawerForm from './index'
import dialogFormMixins from '../dialog/mixins/index'
export default {
  name: 'DrawerInit',
  components: {
    drawerForm
  },
  mixins: [dialogFormMixins],
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      drawerVisible: this.show
    }
  },
  watch: {
    drawerVisible(to) {
      if (!to) {
        setTimeout(() => {
          this.$emit('update:show', to)
        }, 500)
      }
    },
    show(to) {
      setTimeout(() => {
        this.drawerVisible = to
      })
    }
  }
}
</script>

<style scoped>

</style>
