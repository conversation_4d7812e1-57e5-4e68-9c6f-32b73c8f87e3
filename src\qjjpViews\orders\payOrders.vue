<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 10:43:33
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-11-13 19:34:28
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="支付订单">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
    <section>
      <el-dialog title="
          退款确认
        " width="520px" :visible.sync="dialogFormVisible" :show-close="false">
        <div class="dialog_tips">请确认是否发起退款申请，发起退款后该笔订单将全额退款给用户!</div>
        <el-form ref="ruleForm" :model="refundForm" :rules="rules" label-width="120px" class="demo-ruleForm">
          <el-form-item label="投诉来源：" prop="refundSourceFrom" class="form-item" :rules="rules.common">
            <el-select v-model="refundForm.refundSourceFrom" placeholder="请选择投诉来源：">
              <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="投诉原因：" prop="refundReason" class="form-item" :rules="rules.common">
            <el-input v-model="refundForm.refundReason" autocomplete="off" type="textarea" placeholder="请填写退款原因"
              maxlength="200" />
          </el-form-item>
          <template v-if="currentParams.totalReduceAmount">
            <div class="titletip">{{ currentParams.thirdOrderMsg }}元</div>
            <div class="titletip">订单金额：{{ currentParams.orderPrice }}元</div>
            <div class="titletip">使用权益：{{ currentParams.totalReduceAmount }}元</div>
            <div class="titletip red">退款金额：{{
              Number((currentParams.orderPrice - currentParams.totalReduceAmount).toFixed(4)) }}元<span
                v-if="Number((currentParams.orderPrice - currentParams.totalReduceAmount).toFixed(4)) < 0">(实际金额
                0元)</span>
            </div>
          </template>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" :loading="btn_disabled" :disabled="time != 0"
            @click="userUpdated('1', 'ruleForm')">{{
              time != 0 ? '确 定' + '（' + time + 's）' : '确 定' }}</el-button>
        </div>
      </el-dialog>
    </section>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { user_list, getOrderSourceFrom } from '@/qjjpApi/user'
import { count_channel_application_list, mediaAll } from '@/qjjpApi/NewChannel'

import { membershipOrder, doRefund, memberOrderexport } from '@/qjjpApi/orders'

import moment from 'moment'
import { osList } from '@/qjjpViews/appVersion/basicParams'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      btn_disabled: false,
      refundForm: {
        id: '',
        refundSourceFrom: '',
        refundReason: ''
      },
      time: 5,
      currentParams: {},
      ruleForm: {},
      rules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      dialogFormVisible: false,
      optionsList: [
        {
          label: '商户号',
          value: 1
        },
        {
          label: '媒体',
          value: 2
        },
        {
          label: '工商',
          value: 3
        }
      ],
      list1: [
        {
          id: 1,
          name: '奇迹键盘'
        }
      ],
      list2: [
        {
          id: 'waitPay',
          name: '待支付'
        },
        {
          id: 'paid',
          name: '已支付'
        },
        {
          id: 'cancel',
          name: '已取消'
        },
        {
          id: 'refund',
          name: '已退款'
        },
        {
          id: 'expired',
          name: '已到期'
        }
      ],
      list3: [
        {
          id: 1,
          name: '微信支付'
        },
        {
          id: 2,
          name: '支付宝支付'
        },
        {
          id: 3,
          name: '支付宝自动续费'
        },
        {
          id: 0,
          name: 'ios'
        }
      ],
      list4: [
        {
          id: 1,
          name: '会员订单'
        },
        {
          id: 2,
          name: '续费订单'
        },
        {
          id: 3,
          name: '打卡订单'
        },
        {
          id: 4,
          name: '体验会员'
        },
        {
          id: 5,
          name: '单卖人设'
        },
        {
          id: 8,
          name: '权益卡类'
        }
      ],
      qyTypeList: [
        {
          id: 1,
          name: '支付并签约'
        },
        {
          id: 2,
          name: '签约后扣款'
        }
      ],
      list5: [
        {
          id: 1,
          name: '第1期'
        },
        {
          id: 2,
          name: '第2期'
        },
        {
          id: 3,
          name: '第3期'
        },
        {
          id: 4,
          name: '第4期'
        },
        {
          id: 5,
          name: '第5期'
        },
        {
          id: 6,
          name: '第6期'
        },
        {
          id: 7,
          name: '第7期'
        },
        {
          id: 8,
          name: '第8期'
        },
        {
          id: 9,
          name: '第9期'
        },
        {
          id: 10,
          name: '第10期'
        },
        {
          id: 11,
          name: '第11期'
        },
        {
          id: 12,
          name: '第12期'
        }
      ],
      list10: [
        {
          id: '1',
          name: '支付并签约'
        },
        {
          id: '2',
          name: '签约后扣款'
        }
      ],
      list9: [],
      listActivity: [
        {
          id: 1,
          name: '常规会员'
        },
        {
          id: 2,
          name: '打卡会员'
        },
        {
          id: 3,
          name: '买一赠一'
        },
        {
          id: 4,
          name: '普通权益会员'
        },
        {
          id: 5,
          name: '打卡权益会员'
        },
        {
          id: 6,
          name: '权益卡非会员'
        }
      ],
      mediaList: [],
      siteIds: [],
      listQuery: {
        startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          await mediaAll().then(res => {
            if (res.code === 200) {
              this.mediaList = res.data
            }
          })
          if (!this.siteIds.length) {
            await count_channel_application_list().then(res => {
              if (res.code === 200) {
                if (res.data && res.data.length) {
                  this.siteIds = res.data
                }
              }
            })
          }
          if (!this.list9.length) {
            await getOrderSourceFrom().then(res => {
              if (res.code === 200) {
                if (res.data && res.data.length) {
                  this.list9 = res.data
                }
              }
            })
          }
          const list = await membershipOrder(this.listQuery)
          const { records, total } = list.data
          console.info(list, 'list')
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list8() {
      return this.listQuery.payType == 3 ? [
        {
          id: 1,
          name: '第1期'
        },
        {
          id: 2,
          name: '第2期'
        },
        {
          id: 3,
          name: '第3期'
        },
        {
          id: 4,
          name: '第4期'
        },
        {
          id: 5,
          name: '第5期'
        },
        {
          id: 6,
          name: '第6期'
        },
        {
          id: 7,
          name: '第7期'
        },
        {
          id: 8,
          name: '第8期'
        },
        {
          id: 9,
          name: '第9期'
        },
        {
          id: 10,
          name: '第10期'
        },
        {
          id: 11,
          name: '第11期'
        },
        {
          id: 12,
          name: '第12期'
        }
      ] : []
    },
    list() {
      return [
        {
          key: 'dateSearch',
          title: '日期',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          search: true,
          val: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
          tableHidden: true,
          pickerDay: 30
        },
        {
          title: '订单编号',
          key: 'orderNo',
          type: formItemType.input,
          search: true,
          searchKey: 'orderNo',
          clearable: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          reg: ['required'],
          search: true,
          clearable: true
        },
        {
          title: '订单编号',
          key: 'orderNo'
        },
        {
          title: '用户账号',
          key: 'mobileNo'
        },
        {
          title: '设备id',
          key: 'deviceCode'
        },
        {
          title: '设备来源',
          key: 'os',
          list: osList,
          type: formItemType.select,
          tableView: tableItemType.tableView.text
        },
        {
          title: '用户账号',
          key: 'useAccount',
          type: formItemType.input,
          search: true,
          tableHidden: true
        },
        {
          title: '用户Id',
          key: 'userCode',
          search: true,
          type: formItemType.input
        },
        {
          title: '渠道id',
          key: 'channelCodes',
          search: true,
          type: formItemType.input,
          tableHidden: true
        },
        {
          title: '投放媒体',
          key: 'apiCode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.mediaList,
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '渠道id',
          key: 'channelCode'
        },
        {
          title: '第三方订单号',
          key: 'outTradeNo'
        },
        {
          title: '订单金额',
          key: 'orderPrice'
        },
        {
          title: '订单支付成功金额',
          key: 'payPrice'
        },
        {
          title: '订单状态',
          key: 'orderStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '支付方式',
          key: 'payType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          options: {
            on: () => {
              return {
                change: e => {
                  this.$set(this.listQuery, 'payType', e)
                  if (this.listQuery.payType != 3) {
                    this.$set(this.listQuery, 'numberPeriod', '')
                  }
                  console.info(this.listQuery.numberPeriod)
                }
              }
            }
          },
          reg: ['required'],
          search: true,
          clearable: true
        },
        {
          title: '订单类型',
          key: 'orderType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true
        },
        {
          title: '支付位置',
          key: 'orderSourceFrom',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list9,
          listFormat: {
            label: 'desc',
            value: 'code'
          },
          reg: ['required']
        },
        {
          title: '活动属性',
          key: 'activityAttributes',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.listActivity,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required']
        },

        {
          title: '会员身份',
          key: 'memberIdentity'
        },
        {
          title: '打卡天数',
          key: 'checkedDay',
          renderHeader: (...args) => this.renderHeader(...args, ['用户连续打卡天数/需要连续打卡天数'])
        },
        {
          title: '扣款期数',
          key: 'numberPeriod',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list8,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: this.list8.length > 0,
          clearable: true,
          tableHidden: true
        },
        {
          title: '支付位置',
          key: 'orderSourceFrom',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list9,
          listFormat: {
            label: 'desc',
            value: 'code'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '签约类型',
          key: 'cycleAgreementType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list10,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '签约类型',
          key: 'cycleAgreementTypeName'
        },
        {
          title: '活动属性',
          key: 'activityAttributes',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.listActivity,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '设备id',
          key: 'deviceCode',
          type: formItemType.input,
          tableHidden: true,
          search: true
        },
        {
          title: '设备来源',
          key: 'os',
          type: formItemType.select,
          list: osList,
          tableHidden: true,
          search: true
        },
        {
          title: '扣款期数',
          key: 'numberPeriod',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list5,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '收款商户号',
          key: 'merchant'
        },

        {
          title: '订单状态',
          key: 'orderStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          clearable: false
        },
        {
          title: '创建时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '支付时间',
          key: 'payTime',
          render: (h, params) => {
            if (!params.data.row.payTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.payTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '订单有效期',
          key: 'expiryTime',
          render: (h, params) => {
            if (!params.data.row.expiryTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.expiryTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '退款',
              key: 'edit1',
              hidden(params) {
                // return params.orderStatus != 'paid' || params.os == 1
                return params.orderStatus != 'paid' || params.os == 1
              },
              // type: tableItemType.activeType.detailsDialog
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                // params.parentMediaAccount
                this.currentParams = { ...params }
                this.refundForm = {
                  id: '',
                  refundSourceFrom: '',
                  refundReason: ''
                }
                this.refundForm.id = this.currentParams.id
                this.dialogFormVisible = true
              }
            }
          ]
        }
      ]
    }
  },
  watch: {
    dialogFormVisible: {
      handler(val) {
        if (val) {
          this.time = 5
          this.setTime = setInterval(() => {
            this.time -= 1
            if (this.time == 0) {
              clearInterval(this.setTime)
            }
          }, 1000)
        } else {
          clearInterval(this.setTime)
        }
      }
    }
  },
  created() { },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = memberOrderexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    userUpdated(type, formName) {
      this.$refs[formName].validate(valid => {
        if (valid && !this.btn_disabled) {
          this.btn_disabled = true
          doRefund(this.refundForm).then(res => {
            this.btn_disabled = false
            if (res.code == 200) {
              this.$message.success('退款成功！')
              this.$store.dispatch('tableRefresh', this)
              this.dialogFormVisible = false
            }
          })
        }
      })
    },
    renderHeader(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h(
            'div',
            {
              slot: 'content'
            },
            [textArr.map(item => h('div', null, item))]
          ),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog_tips {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
  line-height: 30px;
  text-align: left;
  font-style: normal;
  padding-bottom: 20px;
}

.excel-upload {
  text-align: center;

  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}

.fail_list {
  margin-top: 10px;

  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}

.copy-btn {
  cursor: pointer;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
