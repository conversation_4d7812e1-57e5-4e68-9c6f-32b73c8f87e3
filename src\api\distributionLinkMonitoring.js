import CONSTANT from '@/config/constant.conf'
import { del, get, post, put } from '@/libs/axios.package'
import qs from 'qs'

// 分发链路 - 列表
export const getList = obj => get('/count/triage/page', obj)

// 分发链路 - 列表子项列表
export const getDetailList = obj => get('/count/triage/detail', obj)

// 分发链路 - 列表导出
export const listExport = data => CONSTANT.publicPath + '/count/triage/export?' + qs.stringify(data)


// 分发链路 - 渠道列表
export const getChannelCodeList = obj => get('/count/triage/channel/page', obj)

// 分发链路 - 渠道列 表子项列表
export const getChannelCodeDetailList = obj => get('/count/triage/channel/detail', obj)

// 分发链路 - 渠道列表导出
export const channelCodeListExport = data => CONSTANT.publicPath + '/count/triage/channel/export?' + qs.stringify(data)
