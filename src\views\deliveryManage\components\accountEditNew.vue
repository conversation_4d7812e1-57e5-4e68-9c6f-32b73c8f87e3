<template>
  <div>
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm">
      <el-form-item label="媒体" prop="mediaPlatform">
        <el-select v-model="ruleForm.mediaPlatform" placeholder="请选择媒体类型">
          <el-option v-for="(item, i) in mediaList" :key="i" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="投放应用" prop="siteId" :rules="rules.common">
        <el-select v-model="ruleForm.siteId" placeholder="请先选择投放应用">
          <el-option v-for="(item, i) in appList" :key="i" :label="item.siteName" :value="item.siteId" />
        </el-select>
      </el-form-item>
      <el-form-item label="账户id" prop="accountId" :rules="rules.common">
        <el-input v-model="ruleForm.accountId" maxlength="30" placeholder="账户id" />
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName" :rules="rules.common">
        <el-input v-model="ruleForm.accountName" maxlength="30" placeholder="账户名称" />
      </el-form-item>
      <!-- <el-form-item label="账号" prop="mediaAccount">
        <el-input v-model="ruleForm.mediaAccount" maxlength="30" placeholder="请输入账号" />
      </el-form-item> -->
      <!-- <el-form-item label="密码" prop="mediaPassword">
        <el-input v-model="ruleForm.mediaPassword" placeholder="请输入账户密码" />
      </el-form-item> -->
      <el-form-item label="appId" prop="appId" :rules="rules.common">
        <el-select v-model="ruleForm.optionId" :placeholder="`${ruleForm.mediaPlatform==''|| !ruleForm.mediaPlatform ?'请先选择媒体类型':ruleForm.siteId==''||!ruleForm.siteId?'请先选择投放应用':'请选择appId'}`">
          <el-option v-for="(item, i) in optionList" :key="i" :label="item.appId" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="secretId" prop="secretId" :rules="rules.common">
        <el-select v-model="ruleForm.optionId" :placeholder="`${ruleForm.mediaPlatform==''|| !ruleForm.mediaPlatform ?'请先选择媒体类型':ruleForm.siteId==''||!ruleForm.siteId?'请先选择投放应用':'请选择secretID'}`">
          <el-option v-for="(item, i) in optionList" :key="i" :label="item.secretId" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="代理名称" prop="proxyConfigId">
        <el-select v-model="ruleForm.proxyConfigId" filterable placeholder="请选择">
          <el-option
            v-for="(item, i) in proxyList"
            :key="i"
            :label="item.proxyName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="代理返点">
        <el-input readonly disabled :value="discountData" placeholder="代理返点" />
      </el-form-item>
      <el-form-item label="是否启用" prop="status">
        <el-radio-group v-model="ruleForm.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">确认</el-button>
        <el-button @click="$emit('cancle')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { addOrUpdateNew, optionListNew } from '@/api/deliveryManage'
import { count_channel_application_list } from '@/api/NewChannel'

export default {
  name: 'AccountEdit',
  props: {
    currentParams: {
      type: Object,
      default: () => ({})
    },
    proxyList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogFormVisible: false,
      mediaList: [
        { label: '字节', value: 1 },
        { label: '快手', value: 2 },
        { label: '百度', value: 3 },
        { label: '腾讯', value: 4 }
      ],
      ruleForm: {
        mediaPlatform: '',
        siteId: '',
        accountId: '',
        optionId: '',
        appId: '',
        secretId: '',
        proxyConfigId: '',
        status: '',
        accountName: ''
      },
      appList: [],
      optionList: [],
      rules: {
        mediaPlatform: [{ required: true, message: '请选择', trigger: 'change' }],
        proxyConfigId: [{ required: true, message: '请选择', trigger: 'change' }],
        status: [{ required: true, message: '请选择', trigger: 'change' }],
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }],
        accountName: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    }
  },
  computed: {
    discountData() {
      const item = this.proxyList.find(n => n.id == this.ruleForm.proxyConfigId)
      if (item) {
        return item.discount
      } else {
        return 0
      }
    },
    optionParams() {
      return {
        mediaPlatform: this.ruleForm.mediaPlatform || '',
        siteId: this.ruleForm.siteId || ''
      }
    }
  },
  watch: {
    currentParams(val) {
      this.initFn(val)
    },
    optionParams: {
      handler(value) {
        if (value && value.mediaPlatform && value.mediaPlatform != '' && value.siteId && value.siteId != '') {
          this.getOptionList(value.mediaPlatform, value.siteId, (this.ruleForm.appId || ''), (this.ruleForm.secretId || ''))
        }
      },
      deep: true,
      immediate: true
    },
    'ruleForm.optionId': {
      handler(value) {
        const isTrue = value && value != '' && value != undefined
        if (isTrue) {
          const listItem = this.optionList.find(n => n.id == value)
          if (listItem) {
            this.ruleForm.appId = listItem.appId
            this.ruleForm.secretId = listItem.secretId
          }
        } else {
          this.ruleForm.appId = ' '
          this.ruleForm.secretId = ''
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.initFn(this.currentParams)
    count_channel_application_list().then((response) => {
      this.appList = response.data
    })
  },
  methods: {
    initFn(value) {
      Object.assign(this.ruleForm, value)
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const params = { ...this.ruleForm }
          delete params.optionId
          params['currentPage'] = window.location.href
          addOrUpdateNew(params).then(res => {
            if (res.code === 0) {
              this.$message.success(this.ruleForm.id ? '更新成功' : '新增成功,即将授权跳转')
              if (!this.ruleForm.id || this.ruleForm.id == '' && res.data) {
                setTimeout(() => {
                  window.open(res.data)
                }, 1000)
              }
              this.$emit('success')
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    getOptionList(mediaPlatform, siteId, appId, secretId) {
      this.ruleForm.optionId = ''
      optionListNew({ platform: mediaPlatform, siteId: siteId }).then(res => {
        console.log(res)
        this.optionList = res.data || []
        if (appId != '' && secretId != '') {
          const listItem = this.optionList.find(n => n.appId == appId && n.secretId == secretId)
          if (listItem) {
            this.ruleForm.optionId = listItem.id
          }
        } else {
          if (this.optionList.length > 0) {
            this.ruleForm.optionId = this.optionList[0].id
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.flex_item {
  display: flex;

  .reduce {
    width: 200px;
  }

  .f_time {
    width: 300px;
  }
}

.cascader {
  ::v-deep .el-cascader__tags {
    font-size: 0;
  }
}
::v-deep .el-select ,.el-input{
  width: 85% !important;
}
</style>
