<template>
  <div>
    <ycTitle style="margin: 20px 0;" />
    <div style="margin:50px auto;">
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="300px"
        class="demo-ruleForm"
      >
        <el-form-item label="选择分类：" prop="categories">
          <el-cascader
            v-model="ruleForm.categories"
            placeholder="请选择商品分类"
            :options="casCadeData"
            :props="optionProps"
            clearable
          />
        </el-form-item>
        <el-form-item label="商品库选择：" prop="thirdGoodsId">
          <el-button
            v-model="ruleForm.thirdGoodsId"
            plain
            size="small"
            type="success"
            :disabled="type=='edit'"
            @click="chooseGood"
          >请选择</el-button>
          <span
            v-if="choosedObj.zkFinalPriceWap && choosedObj.zkFinalPriceWap != 0"
          >来源:
            <span v-if="this.type === 'edit'">{{ details.source == 0 ? '淘宝': '京东' }}</span>
            <span v-if="this.type === 'add'">{{ productSource == 0 ? '淘宝': '京东' }}</span>
            -- 原价:{{ choosedObj.zkFinalPriceWap }} -- 优惠券金额:{{ choosedObj.couponValue }}</span>
        </el-form-item>
        <el-form-item label="到手价：">
          <el-input
            v-model="arrivalPrice"
            disabled
            style="width:200px;"
            clearable
            placeholder="到手价"
            autocomplete="off"
          />&emsp;元
          <span>（本版本由于返利比例设置无效，故到手价不以此输入框显示值为准）</span>
        </el-form-item>

        <el-form-item label="排序：" prop="sort">
          <el-input
            v-model="ruleForm.sort"
            style="width:200px;"
            clearable
            placeholder="请输入排序"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="是否上架：" prop="status">
          <el-radio v-model="ruleForm.status" label="0">上架</el-radio>
          <el-radio v-model="ruleForm.status" label="1">下架</el-radio>
        </el-form-item>
        <el-form-item label="商品推荐：" prop="location">
          <el-checkbox-group v-model="ruleForm.location">
            <el-checkbox
              v-for="(item, index) in locationArray"
              :key="index"
              :label="item.id"
              name="location"
            >{{ item.title }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="可见用户：" prop="userAuthority">
          <el-checkbox-group v-model="ruleForm.userAuthority">
            <el-checkbox
              v-for="(item, index) in userAuthorityArray"
              :key="index"
              :label="item.value"
              name="location"
            >{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item>
          <el-button plain icon="el-icon-arrow-left" @click="onCallback()">返回</el-button>
          <el-button style="width:100px;" type="primary" @click="submit('ruleForm')">提交</el-button>
        </el-form-item>
      </el-form>
    </div>

    <section v-if="dialogVisible">
      <el-dialog :visible.sync="dialogVisible" width="65%" :before-close="handleClose">
        <el-form :inline="true">
          <el-form-item label="商品来源">
            <el-select v-model="productSource" placeholder="请选择商品来源" @change="chooseGoodSource">
              <el-option
                v-for="item in sourceArray"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >{{ item.name }}</el-option>
            </el-select>
          </el-form-item>
          <!--选择商品库 -->
          <el-form-item label="商品库">
            <el-select v-model="packageId">
              <el-option
                v-for="(item, index) in goodsList"
                :key="index"
                :label="item.title"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <!--填写商品名称-->
          <el-form-item label="商品名称：">
            <el-input v-model="searchTitle" placeholder="请输入商品名称" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleClick">查询</el-button>
          </el-form-item>
        </el-form>

        <el-table height="500" :data="tableData" border style="width: 100%;margin: 30px 0;">
          <el-table-column :prop="'itemId'" label="产品编号" />
          <el-table-column label="预览图">
            <template slot-scope="scope">
              <viewer
                v-if="scope.row.coverPic"
                class="img-wrap"
                style="margin: auto;"
              >
                <img
                  :src="scope.row.coverPic"
                  style="max-width: 50px;max-height: 50px;"
                >
              </viewer>
            </template>
          </el-table-column>
          <el-table-column prop="title" label="商品名称" width="400px" />
          <el-table-column prop="originalPrice" label="原价" />
          <el-table-column prop="couponPrice" label="优惠券" />
          <el-table-column prop="commission" label="佣金 " />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button :type="!scope.row.checked ? 'primary' : 'danger'" size="mini" :disabled="scope.row.checked" plain @click="handleChoosedObj(scope.row)">选取</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 70, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-dialog>
    </section>
  </div>
</template>

<script>
import ycTitle from '@/components/yc-title/title'
import {
  getPromotionStoreroomApi,
  setPromotionGoodApi,
  getEditCommonListDetailApi,
  editEditCommonListDetailApi,
  getGoodsLocationApi,
  getGoodsTableArrayApi,
  getGoodsTbGoodsItem,
  getGoodsPackageApi,
  getRatioApi
} from '@/api/goods'
import constant from '@/config/constant.conf'
import Store from '@/store'
export default {
  components: {
    ycTitle
  },
  data() {
    const validateCategories = (rule, value, callback) => {
      if (!value.length) {
        return callback(new Error('请选择商品分类'))
      } else {
        callback()
      }
    }
    return {
      searchTitle: '', // 查询商品名称
      goodsList: [], // 查询商品库列表
      productSource: 0, // 商品来源
      commodityBank: '', // 商品库
      sourceArray: [{ name: '淘宝', id: 0 }, { name: '京东', id: 1 }],
      locationArray: [], // 商品显示位置
      dialogVisible: false,
      editId: '', // 编辑页 查询商品详情ID
      tableData: [], // 查询商品数据
      total: 0,
      pageSize: 10,
      price: '', // 编辑页 到手价
      currentPage: 1,
      choosedObj: {
        sourceId: '', // 商品ID
        commission: '', // 佣金
        couponValue: '', // 优惠券金额
        zkFinalPriceWap: '', // 原价
        source: ''
      },
      chooseOptions: [], // 选取下拉框内容
      favoritesId: '', // 拉去选择的id
      favoritesTitle: '', // 拉去选择的名称
      optionProps: {
        // 选择框配置
        value: 'id',
        label: 'name'
      },
      type: '', // 确认是编辑还是新增
      casCadeData: [], // 推广商品分类
      userAuthorityArray: [
        {
          name: '游客',
          value: 1
        }, {
          name: '普通未参加0元购用户',
          value: 2
        }, {
          name: '普通参加0元购用户',
          value: 3
        }, {
          name: '爵士未参加0元购用户',
          value: 4
        }, {
          name: '爵士已参加0元购用户',
          value: 5
        }
      ],
      ruleForm: {
        categories: [], // 商品类目
        status: '0', // 是否上架
        isHomeShow: '0', // 首页是否展示
        sort: '', // 排序
        thirdGoodsId: '', // 选择按钮
        sourceId: '',
        userAuthority: [],
        location: []
      },
      rules: {
        categories: [
          { required: true, message: '请选择商品分类', trigger: 'blur' },
          { validator: validateCategories, trigger: 'blur' }
        ],
        sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
        percent: [
          { required: true, message: '请输入返利比例', trigger: 'blur' }
        ],
        thirdGoodsId: [{ required: true, message: '请选择', trigger: 'blur' }]
      },
      details: '',
      packageId: '',
      percent: 0
    }
  },
  beforeRouteEnter(to, from, next) {
    to.meta.title = to.query.type === 'add' ? '添加推广商品' : '编辑推广商品'
    next()
  },
  computed: {
    // 计算到手价
    arrivalPrice() {
      if (this.type == 'edit') {
        return this.price
      } else {
        if (
          this.choosedObj.zkFinalPriceWap &&
          this.choosedObj.commission &&
          this.percent
        ) {
          return (
            this.choosedObj.zkFinalPriceWap -
            this.choosedObj.couponValue -
            this.choosedObj.commission * (this.percent / 100)
          ).toFixed(2)
        } else {
          return ''
        }
      }
    }
  },
  mounted() {
    this.type = this.$route.query.type
    this.getRatio()
    this.getPromotionCommoditiesData()
    this.getGoodsLocation() // 获取商品位置
    if (this.type == 'edit') {
      this.editId = this.$route.query.id
      this.ruleForm.thirdGoodsId = 1// 避免点击'请选择'按钮  验证不通过
      this.getDetail()
    } else {
      this.getCommodityBank()
      this.getGoodstableArray()
      this.ruleForm.userAuthority = [1, 2, 3, 4, 5]
    }
  },
  methods: {
    // 获取商品详情（编辑页）
    getRatio() {
      getRatioApi().then(res => {
        this.percent = res.data
      })
    },
    getDetail() {
      getEditCommonListDetailApi(this.editId).then(res => {
        if (res.code == 200) {
          this.details = res.data
          const newcategories = []
          res.data.category.forEach(item => {
            if (item != 0) {
              newcategories.push(item)
            }
          })
          this.ruleForm.categories = newcategories
          this.ruleForm.sort = res.data.sort
          this.ruleForm.userAuthority = res.data.userAuthority
          this.ruleForm.status = String(res.data.status)
          this.ruleForm.location = res.data.locationIds == null ? [] : res.data.locationIds
          this.price = res.data.price
          this.choosedObj.zkFinalPriceWap = res.data.sourcePrice
          this.choosedObj.couponValue = res.data.couponPrice
          this.choosedObj.sourceId = res.data.sourceId
          this.choosedObj.source = res.data.source
        }
      })
    },

    handleCurrentChange(page) {
      // 页码改变
      this.currentPage = page
      this.getGoodstableArray()
    },

    handleSizeChange(page) {
      // 每页条数改变时
      this.currentPage = 1
      this.pageSize = page
      this.getGoodstableArray()
    },
    // 新增商品库
    setPromotionGood(params) {
      setPromotionGoodApi(params)
        .then(res => {
          if (res.code === 0) {
            this.$message({
              message: '添加成功！',
              type: 'success'
            })
            this.$router.push('/commodity/commonList')
          } else {
            this.$message.error(res.message)
          }
        })
        .catch(res => {
          console.log(res)
        })
    },
    // 修改商品库
    editEditCommonListDetail(params) {
      editEditCommonListDetailApi(params).then(res => {
        if (res.code === 0) {
          this.$message({
            message: '修改成功！',
            type: 'success'
          })
          this.$router.push('/commodity/commonList')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击新增商品
    submit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const { categories, sort, status, sourceId } = this.ruleForm
          if (this.type == 'edit') {
            const params = {
              id: this.editId,
              categories,
              sort: Number(sort),
              status: Number(status),
              sourceId: this.choosedObj.sourceId,
              locationIds: this.ruleForm.location,
              source: this.choosedObj.source,
              userAuthority: this.ruleForm.userAuthority
            }
            console.log(params)
            this.editEditCommonListDetail(params)
          } else {
            const params = {
              categories,
              sort: Number(sort),
              status: Number(status),
              sourceId: this.choosedObj.sourceId,
              locationIds: this.ruleForm.location,
              source: this.choosedObj.source,
              userAuthority: this.ruleForm.userAuthority
            }
            console.log(params)
            this.setPromotionGood(params)
          }
        } else {
          return false
        }
      })
    },
    // 选取新增商品
    handleChoosedObj(value) {
      console.log(value)
      this.ruleForm.thirdGoodsId = 1
      this.choosedObj = {
        sourceId: value.itemId,
        zkFinalPriceWap: value.originalPrice,
        couponValue: value.couponPrice,
        commission: value.commission,
        source: value.source
      }
      this.dialogVisible = false
    },
    // 点击选择（弹出模态框）
    chooseGood() {
      this.dialogVisible = true
    },
    // 获取推广商品分类
    getPromotionCommoditiesData() {
      getPromotionStoreroomApi().then(res => {
        if (res.code == '0') {
          const allObj = [
            {
              children: null,
              id: 0,
              logoUrl: '',
              name: '全部',
              sort: 0,
              status: 0
            }
          ]
          this.casCadeData = res.data
        }
      })
    },
    // 关闭模态框
    handleClose() {
      this.dialogVisible = false
    },

    // 获取商品位置
    getGoodsLocation() {
      getGoodsLocationApi().then(res => {
        if (res.code === 0) {
          this.locationArray = res.data
        }
      })
    },
    // 获取商品库列表
    getCommodityBank() {
      const param = {
        source: this.productSource
      }
      getGoodsPackageApi(param).then(res => {
        this.goodsList = res.data
      })
    },
    // 点击切换商品来源
    chooseGoodSource(sourceType) {
      this.productSource = sourceType
      this.tableData = []
      this.goodsList = []
      this.searchTitle = ''
      this.total = 0
      this.packageId = ''
      // 调用获取渠道的商品库
      this.getCommodityBank()
      this.getGoodstableArray()
    },
    // 获取商品列表
    getGoodstableArray() {
      let param
      param = {
        goodsTitle: this.searchTitle,
        source: this.productSource,
        packageId: this.packageId,
        checked: true,
        pageNumber: this.currentPage,
        pageSize: this.pageSize
      }

      getGoodsTableArrayApi(param).then(res => {
        if (res.code === 0) {
          this.tableData = res.data
          this.total = res.totalCount
        } else {
          this.tableData = []
          this.total = 0
        }
      })
    },
    // 点击后查询商品
    handleClick() {
      this.currentPage = 1
      this.getGoodstableArray()
    },

    onCallback() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
</style>
