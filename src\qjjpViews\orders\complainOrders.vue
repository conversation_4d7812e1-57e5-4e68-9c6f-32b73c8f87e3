<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 10:44:40
 * @LastEditors: 蒋雪 <EMAIL>
 * @LastEditTime: 2024-07-18 16:36:45
-->
<template>
  <div>
    <page :request="request" :list="list" table-title="投诉订单">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
    <section>
      <el-dialog
        title="
          退款确认
        "
        width="520px"
        :visible.sync="dialogFormVisible"
        :show-close="false"
      >
        <div class="dialog_tips">请确认是否发起退款申请，发起退款后该笔订单将全额退款给用户!</div>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="120px"
          class="demo-ruleForm"
        >
          <el-form-item label="投诉来源：" prop="name" class="form-item">
            <el-select v-model="value" placeholder="请选择投诉来源：">
              <el-option
                v-for="item in optionsList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="投诉原因：" prop="sort" class="form-item">
            <el-input
              v-model="value"
              autocomplete="off"
              type="textarea"
              placeholder="请填写退款原因"
              maxlength="200"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible=false">取 消</el-button>
          <el-button
            type="primary"
            :loading="btn_disabled"
            :disabled="time!=0"
            @click="userUpdated('1', 'ruleForm')"
          >{{ time!=0?'确 定'+'（'+ time +'s）':'确 定' }}</el-button>
        </div>
      </el-dialog>
    </section>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { user_list } from '@/qjjpApi/user'
import { count_channel_application_list, mediaAll } from '@/qjjpApi/NewChannel'
import { aliComplaintPage, aliComplaintPageexport } from '@/qjjpApi/orders'

import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
import { osList } from '@/qjjpViews/appVersion/basicParams'
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      time: 5,
      currentParams: {},
      ruleForm: {},
      rules: [],
      dialogFormVisible: false,
      optionsList: [
        {
          label: '商户号',
          value: 1
        },
        {
          label: '媒体',
          value: 2
        },
        {
          label: '工商',
          value: 3
        }
      ],
      list1: [
        {
          id: 1,
          name: '奇迹键盘'
        }
      ],
      list2: [
        {
          id: 'waitPay',
          name: '待支付'
        },
        {
          id: 'paid',
          name: '已支付'
        },
        {
          id: 'cancel',
          name: '已取消'
        },
        {
          id: 'refund',
          name: '已退款'
        },
        {
          id: 'expired',
          name: '已到期'
        }
      ],
      list3: [
        {
          id: 1,
          name: '微信支付'
        },
        {
          id: 2,
          name: '支付宝支付'
        },
        {
          id: 3,
          name: '支付宝自动续费'
        }
      ],
      list4: [
        {
          id: 1,
          name: '会员订单'
        },
        {
          id: 2,
          name: '续费订单'
        },
        {
          id: 3,
          name: '打卡订单'
        },
        {
          id: 4,
          name: '体验会员'
        },
        {
          id: 5,
          name: '单卖人设'
        }
      ],
      list5: [
        {
          id: 1,
          name: '第1期'
        },
        {
          id: 2,
          name: '第2期'
        },
        {
          id: 3,
          name: '第3期'
        },
        {
          id: 4,
          name: '第4期'
        },
        {
          id: 5,
          name: '第5期'
        },
        {
          id: 6,
          name: '第6期'
        },
        {
          id: 7,
          name: '第7期'
        },
        {
          id: 8,
          name: '第8期'
        },
        {
          id: 9,
          name: '第9期'
        },
        {
          id: 10,
          name: '第10期'
        },
        {
          id: 11,
          name: '第11期'
        },
        {
          id: 12,
          name: '第12期'
        }
      ],
      list6: [
        {
          id: 1,
          name: '会员'
        }
      ],
      list7: [
        {
          id: 1,
          name: '待处理'
        },
        {
          id: 2,
          name: '处理中'
        },
        {
          id: 3,
          name: '处理完成'
        },
        {
          id: 4,
          name: '处理超时'
        }
      ],
      // list7: [
      //   {
      //     id: 'MERCHANT_PROCESSING',
      //     name: '商家处理中'
      //   },
      //   {
      //     id: 'MERCHANT_FEEDBACKED',
      //     name: '商家已反馈'
      //   },
      //   {
      //     id: 'FINISHED',
      //     name: '投诉已完结'
      //   },
      //   {
      //     id: 'CANCELLED',
      //     name: '投诉已撤销'
      //   },
      //   {
      //     id: 'PLATFORM_PROCESSING',
      //     name: '平台处理中'
      //   },
      //   {
      //     id: 'PLATFORM_FINISH',
      //     name: '平台处理完结'
      //   },
      //   {
      //     id: 'CLOSED',
      //     name: '系统关闭'
      //   }
      // ],
      siteIds: [],
      mediaList: [],
      listQuery: {
        startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD')
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then(res => {
              if (res.code === 200) {
                if (res.data && res.data.length) {
                  this.siteIds = res.data
                }
              }
            })
          }
          await mediaAll().then(res => {
            if (res.code === 200) {
              this.mediaList = res.data
            }
          })
          const list = await aliComplaintPage(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list8() {
      return this.listQuery.payType == 3 ? [
        {
          id: 1,
          name: '第1期'
        },
        {
          id: 2,
          name: '第2期'
        },
        {
          id: 3,
          name: '第3期'
        },
        {
          id: 4,
          name: '第4期'
        },
        {
          id: 5,
          name: '第5期'
        },
        {
          id: 6,
          name: '第6期'
        },
        {
          id: 7,
          name: '第7期'
        },
        {
          id: 8,
          name: '第8期'
        },
        {
          id: 9,
          name: '第9期'
        },
        {
          id: 10,
          name: '第10期'
        },
        {
          id: 11,
          name: '第11期'
        },
        {
          id: 12,
          name: '第12期'
        }
      ] : []
    },
    list() {
      return [
        {
          title: '设备id',
          key: 'deviceCode',
          type: formItemType.input,
          tableHidden: true,
          search: true
        },
        {
          title: '设备来源',
          key: 'os',
          type: formItemType.select,
          list: osList,
          tableHidden: true,
          search: true
        },
        {
          key: 'dateSearch',
          title: '日期',
          type: formItemType.rangeDatePicker,
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          childKey: ['startDate', 'endDate'],
          formHidden: true,
          search: true,
          val: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
          tableHidden: true,
          pickerDay: 30
        },
        {
          title: '订单编号',
          key: 'orderNo	',
          type: formItemType.input,
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId'
          },
          val: null,
          reg: ['required'],
          search: true,
          clearable: true
        },
        {
          title: '订单编号',
          key: 'membershipOrderNo'
        },
        {
          title: '用户账号',
          key: 'mobileNo'
        },
        {
          title: '设备id',
          key: 'deviceCode'
        },
        {
          title: '设备来源',
          key: 'os',
          list: osList,
          type: formItemType.select,
          tableView: tableItemType.tableView.text
        },
        {
          title: '用户Id',
          key: 'userCode',
          search: true,
          type: formItemType.input
        },
        {
          title: '投放媒体',
          key: 'apiCode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.mediaList,
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          },
          reg: ['required'],
          clearable: true
        },
        {
          title: '渠道id',
          key: 'channelCode'
        },
        {
          title: '商户投诉订单号',
          key: 'complainEventId'
        },
        {
          title: '订单金额',
          key: 'orderPrice'
        },
        {
          title: '订单状态',
          key: 'orderStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: null,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '支付方式',
          key: 'payType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list3,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          options: {
            on: () => {
              return {
                change: e => {
                  this.$set(this.listQuery, 'payType', e)
                  if (this.listQuery.payType != 3) {
                    this.$set(this.listQuery, 'numberPeriod', '')
                  }
                  console.info(this.listQuery.numberPeriod)
                }
              }
            }
          },
          val: null,
          reg: ['required'],
          search: true,
          clearable: true
        },
        {
          title: '订单类型',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: null,
          reg: ['required'],
          clearable: true
        },
        {
          title: '扣款期数',
          key: 'numberPeriod',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list5,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: null,
          reg: ['required'],
          clearable: true
        },
        {
          title: '投诉商户号',
          key: 'merchant'
        },
        {
          title: '用户投诉次数',
          key: 'complaintNum'
        },
        {
          title: '用户投诉内容',
          key: 'descDetail'
        },
        {
          title: '商户投诉状态',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list7,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: null,
          reg: ['required'],
          clearable: true
        },
        {
          title: '订单状态',
          key: 'orderStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list2,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: null,
          reg: ['required'],
          clearable: true
        },
        // {
        //   title: '购买内容',
        //   key: 'siteId',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.list6,
        //   listFormat: {
        //     label: 'name',
        //     value: 'id'
        //   },
        //   val: null,
        //   reg: ['required'],
        //   search: true,
        //   clearable: true,
        //   tableHidden: true
        // },
        {
          title: '商户投诉状态',
          key: 'complaintStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list7,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: null,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '订单类型',
          key: 'orderType',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list4,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: null,
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: '扣款期数',
          key: 'numberPeriod',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list8,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          val: null,
          reg: ['required'],
          search: this.list8.length > 0,
          clearable: true,
          tableHidden: true
        },

        {
          title: '创建时间',
          key: 'orderCreateTime',
          render: (h, params) => {
            if (!params.data.row.orderCreateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.orderCreateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '支付时间',
          key: 'paySuccessTime',
          render: (h, params) => {
            if (!params.data.row.paySuccessTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.paySuccessTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        // {
        //   title: '购买内容',
        //   key: 'siteId',
        //   type: formItemType.select,
        //   tableView: tableItemType.tableView.text,
        //   list: this.list6,
        //   listFormat: {
        //     label: 'name',
        //     value: 'id'
        //   },
        //   val: null,
        //   reg: ['required'],
        //   clearable: true
        // },
        {
          title: '到期时间',
          key: 'expiryTime',
          render: (h, params) => {
            if (!params.data.row.expiryTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.expiryTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        }
      ]
    }
  },
  watch: {
    dialogFormVisible: {
      handler(val) {
        if (val) {
          this.time = 5
          this.setTime = setInterval(() => {
            this.time -= 1
            if (this.time == 0) {
              clearInterval(this.setTime)
            }
          }, 1000)
        } else {
          clearInterval(this.setTime)
        }
      }
    }
  },
  created() {},
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = aliComplaintPageexport({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog_tips{
    font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 18px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
padding-bottom: 20px;
}
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
