// import CONSTANT from '@/config/constant.conf'
import { getJjjp, postJjjp } from '@/libs/axios.package'
// import qs from 'qs'

// 联调设备名称
export const white_list_device = params => {
  return getJjjp(`/white/list/device`, params, null)
}
/*
 * 清除设备信息
 * */
export const white_list_clear = obj => {
  return getJjjp('/white/list/clear', obj)
}
// 站内用户初始化
export const white_verification_code = obj => {
  return getJjjp('/white/list/verification/code', obj)
}
// 站外用户初始化
export const white_verification_code_h5 = obj => {
  return getJjjp('/white/list/verification/H5/code', obj)
}

export const resetUserChat = obj => {
  return postJjjp('/cms/message-reply-bank/resetUserChat', obj)
}

// export const userlistexport = data => CONSTANT.qjjpPath + '/cms/user/list/export?' + qs.stringify(data)
