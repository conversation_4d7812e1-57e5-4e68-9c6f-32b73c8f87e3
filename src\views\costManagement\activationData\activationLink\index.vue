<template>
  <div>
    <!-- <page :list="list" :request="request" table-title="激活链路" /> -->

    <el-row class="search-row" type="flex" style="margin-bottom: 30px;display: flex;align-items: center;">
      <span>时间：</span>
      <div class="block" style="margin:0px 20px 0 6px">
        <el-date-picker
          v-model="value"
          :picker-options="basics.pickerOptions()"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDate"
        />
      </div>
      <!-- <label class="name">应用名称：</label> -->
      <span>应用名称：</span>
      <el-select
        v-model="listQuery.siteId"
        placeholder="综合"
        clearable
        style="margin-right: 10px"
      >
        <el-option
          v-for="(type, index) in siteIds"
          :key="index"
          :label="type.siteName"
          :value="type.siteId"
        />
      </el-select>
      <span>渠道ID：</span>
      <el-input v-model="listQuery.channelCode" style="width: 150px;" type="text" placeholder="渠道ID" />
      <span>
        <el-button
          type="primary"
          class="search-btn"
          icon="el-icon-search"
          @click="search()"
        >
          查询
        </el-button>
      </span>
    </el-row>
    <el-table
      :data="listData"
      style="width: 100%"
    >
      <el-table-column
        prop="date"
        label="时间"
        width="100"
        fixed="left"
      />
      <el-table-column
        prop="commonVisitS"
        label="非会员访问（总/新）"
        width="100"
      />
      <el-table-column label="普通用户">
        <el-table-column
          prop="activateCountS"
          label="激活成功数（总/新）"
          width="100"
        />
        <el-table-column
          prop="vipCardPayPopupShowS"
          label="引导购卡弹窗曝光（总/新）"
          width="100"
        />
        <el-table-column
          prop="vipCardPayPopupClickS"
          label="引导购卡弹窗点击（总/新）"
          width="100"
        />
        <el-table-column
          prop="activateUpUvS"
          label="立即升级点击（总/新）"
          width="100"
        />
        <el-table-column
          prop="payS"
          label="支付成功数/金额"
          width="100"
        />
        <el-table-column
          prop="returnAmountS"
          label="退款数/金额"
          width="100"
        />
      </el-table-column>
      <el-table-column label="会员用户">
        <el-table-column
          prop="vipVisitS"
          label="会员访问（总/新）"
          width="100"
        />
        <el-table-column
          prop="drawPopupS"
          label="话费券领取弹窗曝光（总/新）"
          width="110"
        />
        <el-table-column
          prop="drawClickS"
          label="话费券领取弹窗点击（总/新）"
          width="110"
        />
        <el-table-column
          prop="closeInterceptShowS"
          label="关闭拦截弹窗曝光（总/新）"
          width="100"
        />
        <el-table-column
          prop="closeInterceptClickS"
          label="关闭拦截弹窗点击（总/新）"
          width="100"
        />
        <el-table-column
          prop="closePopupInterceptClosePvUv"
          label="关闭拦截弹窗关闭（pv/uv）"
          width="100"
        />
        <el-table-column
          prop="drawSuccessPopupShowS"
          label="领取成功弹窗曝光（总/新）"
          width="100"
        />
        <el-table-column
          prop="drawSuccessPopupClickS"
          label="领取成功弹窗点击（总/新）"
          width="100"
        />
        <el-table-column
          prop="activateButtonClickS"
          label="活动页激活点击（总/新）"
          width="100"
        />
        <el-table-column
          prop="activateVipCountS"
          label="激活成功数（总/新）"
          width="100"
        />
      </el-table-column>
      <el-table-column
        prop="advertisingShowUvS"
        label="观看视频用户数（总/新）"
        width="100"
      />
      <el-table-column
        prop="advertisingShowPvS"
        label="观看视频次数（总/新）"
        width="100"
      />
      <el-table-column
        prop="advertisingWatchRateS"
        label="完播率（总/新）"
        width="100"
      />
      <el-table-column
        prop="vipCardPayPopupShowRatioS"
        label="引导支付转化（总/新）??引导购卡弹窗点击/引导购卡弹窗曝光"
        width="100"
        :render-header="renderHeader"
      />
      <el-table-column
        prop="cvrRateS"
        label="访问cvr??支付数/非会员访问新"
        width="100"
        :render-header="renderHeader"
      />
      <el-table-column
        prop="returnRatioS"
        label="退款率??退款数/支付数"
        width="100"
        :render-header="renderHeader"
      />
      <el-table-column
        prop="drawRatioS"
        label="话费券领取弹窗转化（总/新）??话费券领取弹窗点击/话费券领取弹窗曝光"
        width="100"
        :render-header="renderHeader"
      />
      <el-table-column
        prop="closeInterceptRatioS"
        label="关闭拦截弹窗转化（总/新）??关闭拦截弹窗点击/关闭拦截弹窗曝光"
        width="100"
        :render-header="renderHeader"
      />
      <el-table-column
        prop="drawSuccessPopupRatioS"
        label="领取成功弹窗转化（总/新）??领取成功弹窗点击/领取成功弹窗曝光"
        width="100"
        :render-header="renderHeader"
      />
      <el-table-column
        prop="vipConversionRatioS"
        label="激活转化（总/新）??激活成功数/会员访问数"
        width="100"
        :render-header="renderHeader"
      />
    </el-table>
    <div style="margin:30px auto;display: flex;justify-content: flex-end;">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 70, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

  </div>
</template>

<script>
import { activationStatistics } from '@/api/costManagement'
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import moment from 'moment'
import { count_channel_application_list } from '@/api/NewChannel'
export default {
  name: 'costManagementActivationLink',
  components: {
    page
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      value: '',
      listQuery: {
        startDate: moment().subtract(7, 'd').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        siteId: '',
        channelCode: '',
        pageNumber: 1,
        pageSize: 10
      },
      listData: [],
      siteIds: []
    }
  },
  computed: {
    time() {
      return [moment().subtract(7, 'd').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
    }
  },
  async created() {
    if (!this.siteIds.length) {
      await count_channel_application_list().then((res) => {
        if (res.code === 0) {
          this.siteIds = res.data
          this.getList()
        }
      })
    }
    this.value = this.time
  },
  methods: {
    getList() {
      activationStatistics(this.listQuery).then(res => {
        this.listData = res.data
        this.total = res.totalCount
      })
    },
    selectDate(val) {
      this.listQuery.startDate = val[0]
      this.listQuery.endDate = val[1]
    },
    handleCurrentChange(val) {
      this.listQuery.pageNumber = val
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    search() {
      this.getList()
    },
    renderHeader(h, { column }) {
      const arr = column.label.split('??')
      return h('div', [
        h('span', arr[0]),
        h(
          'el-tooltip',
          {
            props: {
              content: arr[1]
            }
          },
          [
            h('i', {
              class: 'el-icon-question',
              style: 'color:#409eff;margin-left:5px;font-size: 16px;'
            })
          ]
        )
      ])
    }
  }
}
</script>

<style></style>
