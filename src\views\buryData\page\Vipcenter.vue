<template>
  <page :request="request" :list="list" table-title="会员中心统计">
    <div slot="searchContainer" style="display: inline-block">
      <el-button
        plain
        type="warning"
        size="small"
        icon="el-icon-download"
        @click="handExport"
      >导出数据</el-button>
    </div>
  </page>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { userCenterPage, userCenterPageExport } from '@/api/buryData'
import moment from 'moment'
import { count_channel_application_list } from '@/api/NewChannel'

export default {
  components: {
    page
  },
  props: {},
  data() {
    const that = this
    return {
      listQuery: {
        startDate: moment().subtract(7, 'days').format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD'),
        siteId: ''
      },
      request: {
        getListUrl: async(data) => {
          this.listQuery = { ...this.listQuery, ...data }
          if (!this.siteIds.length) {
            await count_channel_application_list().then((res) => {
              if (res.code === 0) {
                this.siteIds = res.data
                // this.listQuery.siteId = res.data[0].siteId
              }
            })
          }
          return Promise.all([userCenterPage(this.listQuery)]).then(
            (res) => {
              return Promise.resolve(res[0])
            }
          )
        }
      },
      siteIds: []
    }
  },
  computed: {
    list() {
      return [
        {
          title: '选择时间',
          key: 'createTime',
          type: formItemType.datePickerDaterangeGai,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyyMMdd'
          },
          val: [
            moment().subtract(7, 'days').format('YYYYMMDD'),
            moment().format('YYYYMMDD')
          ],
          search: true,
          formHidden: true,
          tableHidden: true
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'siteName',
            value: 'siteId'
          },
          val: '',
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true,
          options: {
            placeholder: '综合'
          }
        },
        {
          title: '日期',
          key: 'date',
          type: formItemType.input,
          formHidden: true
        },
        {
          title: '会员中心访问PV/UV',
          key: 'pageVisitS'
        },
        {
          title: '支付点击（pv/uv）',
          key: 'payClickS'
        },
        {
          title: '支付返回拦截曝光（pv/uv）',
          key: 'payReturnPvUv'
        },
        {
          title: '支付返回拦截点击（pv/uv）',
          key: 'payReturnClickPvUv'
        },
        {
          title: '必中-支付成功',
          key: 'mustlotterySuccess'
        },
        {
          title: '话费-支付成功',
          key: 'phoneSuccess'
        },
        {
          title: '支付成功总',
          key: 'paySuccess'
        },
        {
          title: '退款新',
          key: 'refundNew',
          renderHeader: (...args) => this.renderHeader(...args, ['当日购卡当日退款数'])
        },
        {
          title: '退款总',
          key: 'refundTotal',
          renderHeader: (...args) => this.renderHeader(...args, ['当日所有退款数'])
        },
        {
          title: 'cvr',
          key: 'cvr',
          renderHeader: (...args) => this.renderHeader(...args, ['支付成功总/会员中心访问uv'])
        },
        {
          title: '退款率新',
          key: 'refundNewRatio',
          renderHeader: (...args) => this.renderHeader(...args, ['退款新/支付成功总'])
        },
        {
          title: '退款率总',
          key: 'refundRatio',
          renderHeader: (...args) => this.renderHeader(...args, ['退款总/支付成功总'])
        },
        {
          title: '支付返回拦截转化',
          key: 'payReturnRatio',
          renderHeader: (...args) => this.renderHeader(...args, ['支付返回拦截点击uv/支付返回拦截曝光uv'])
        }
      ]
    }
  },
  methods: {
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = userCenterPageExport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    renderHeader(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h(
            'div',
            {
              slot: 'content'
            },
            [textArr.map(item => h('div', null, item))]
          ),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    }
  }
}
</script>

<style lang="scss" scoped></style>
