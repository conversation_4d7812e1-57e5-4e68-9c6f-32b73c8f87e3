export default [
  {
    path: '/distribution/policy',
    name: 'policy',
    meta: {
      title: '分发策略'
    },
    component: () => import('@/views/distribution/page/policy/index.vue')
  },
  {
    path: '/distribution/strategy',
    name: 'strategy',
    meta: {
      title: '策略关联'
    },
    component: () => import('@/views/distribution/page/strategy/index.vue')
  },
  {
    path: '/distribution/strategyCollect',
    name: 'strategyCollect',
    meta: {
      title: '策略集'
    },
    component: () => import('@/views/distribution/page/strategyCollect/index.vue')
  },
  {
    path: '/distribution/node',
    name: 'node',
    meta: {
      title: '节点管理'
    },
    component: () => import('@/views/distribution/page/node/index.vue')
  },
  {
    path: '/distribution/institution',
    name: 'distributionInstitution',
    meta: {
      title: '机构管理'
    },
  },
  {
    path: '/distribution/institution/list',
    name: 'institutionList',
    meta: {
      title: '机构列表'
    },
    component: () => import('@/views/distribution/page/institution/list/index.vue')
  },
  {
    path: '/distribution/institution/info',
    name: 'institutionAudit',
    meta: {
      title: '信息分类管理'
    },
    component: () => import('@/views/distribution/page/institution/info/index.vue')
  },
  {
    path: '/distribution/record',
    name: 'distributionRecord',
    meta: {
      title: '分发记录'
    },
    component: () => import('@/views/distribution/page/record/index.vue')
  }
]
