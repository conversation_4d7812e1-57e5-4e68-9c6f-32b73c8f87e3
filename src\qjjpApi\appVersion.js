/*
 * @Author: 陈小豆
 * @Date: 2024-04-28 13:57:55
 * @LastEditors: 蒋雪 <EMAIL>
 * @LastEditTime: 2024-07-22 18:53:25
 */
import CONSTANT from '@/config/constant.conf'
import {
  getJjjp,
  postJjjp, put
} from '@/libs/axios.package'
import qs from 'qs'

/**
 * APP渠道市场
 */
export const GET_APP_CHANNEL_LIST = obj => {
  return getJjjp('/cms/app/channel/pagingQuery', obj)
}

export const POST_APP_CHANNEL = obj => {
  return postJjjp('/cms/app/channel/save', obj)
}

/**
 * APP版本管理
 */
// ANDROID
export const GET_APP_PAGING_QUERY = obj => {
  return getJjjp('/cms/app/android/pagingQuery', obj)
}

export const ADD_ANDROID_SAVE = obj => {
  return postJjjp('/cms/app/android/save', obj)
}

export const GET_ANDROID_DETAILS_BY_ID = id => {
  return getJjjp(`/cms/app/android/details/${id}`, null, false)
}

// IOS
export const GET_APP_IOS_LIST = obj => {
  return getJjjp('/cms/app/android/pagingQuery', obj)
}

export const ADD_IOS_SAVE = obj => {
  return postJjjp('/cms/app/android/save', obj)
}
export const PUT_IOS_SAVE = obj => {
  return put(`/cms/app/android/save`, obj)
}
export const GET_IOS_DETAILS_BY_ID = id => {
  return getJjjp(`/cms/app/android/details/${id}`, null, false)
}

/**
 * APP渠道市场
 */
export const GET_CHANNEL_LIST = obj => {
  return getJjjp('/cms/app/channel/list', obj)
}

/**
 * APP渠道市场统计
 */
export const GET_COUNTAPPUSER_LISTBYCODE = obj => {
  return getJjjp('/countappuser/listByCode', obj)
}

export const GET_COUNTAPPUSER_LISTBYDATE = obj => {
  return getJjjp('/countappuser/listByDate', obj)
}

export const EXPORT_COUNTAPPUSER_EXPORTBYCODE = data => CONSTANT.publicPath + '/countappuser/exportByCode?' + qs.stringify(data)

export const EXPORT_COUNTAPPUSER_EXPORTBYDATE = data => CONSTANT.publicPath + '/countappuser/exportByDate?' + qs.stringify(data)

export const GET_UPLOAD_CALLBACK = obj => {
  return getJjjp('/upload/mergefile', obj)
}

export const UPDATE_CHANNEL_DOWNLOAD = obj => {
  return postJjjp('/cms/app/android/updateChannelDownload', obj)
}
export const apk2 = obj => {
  return postJjjp('/cms/upload/apk2', obj)
}

export const update_branch = obj => {
  return postJjjp('/cms/app/channel/update/branch', obj)
}
