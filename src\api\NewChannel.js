import CONSTANT from '@/config/constant.conf'
import { get, post, put } from '@/libs/axios.package'
import qs from 'qs'

/**
 * 新增渠道类型
 */
export const ADD_CHANNEL_TYPE = obj => {
  return post('/v1/channelType/saveChannelType', obj)
}

/**
 * 编辑渠道类型
 */
export const PUT_CHANNEL_TYPE = obj => {
  return post('/v1/channelType/updateChannelType', obj)
}

/**
 * 删除渠道类型
 */
export const DEL_CHANNEL_TYPE = id => {
  return post(`/v1/channelType/deleteChannelType`, {
    id: id
  })
}

/**
 * 渠道类型详情
 */
export const GET_CHANNEL_TYPE_BY_ID = obj => {
  return post('/v1/channelType/selectChannelTypeById', obj)
}

/**
 * 渠道类型列表
 */
export const GET_CHANNEL_TYPE_LIST = obj => {
  return post(`/v1/channelType/selectChannelTypeList?pageNumber=${obj.pageNumber}&pageSize=${obj.pageSize}`)
}

/**
 * 渠道类型列表(不分页)
 */
export const GET_CHANNEL_TYPE_LIST_NO_PAGE = obj => {
  return get('/v1/channelType/selectAllChannelTypeList', obj)
}

/**
 * 渠道统计
 */
export const GET_CHANNEL_STATICS = obj => {
  return get(`/v1/channel/count`, obj, false)
}

// 渠道站内统计
export const GET_CHANNEL_STATICS_BY_CHANNEL = obj => {
  return get('/v1/channel/count/order', obj, false)
}

// 渠道站内导出
export const EXPORT_CHANNEL_STATICS_BY_CHANNEL = data =>
  CONSTANT.publicPath + '/v1/channel/count/order/export?' + qs.stringify(data)

// 导出所有渠道一天的统计
export const EXPORT_CHANNEL_STATICS_ONE_DAY = data =>
  CONSTANT.publicPath + '/v1/channel/count/order/export/all?' + qs.stringify(data)

/**
 * 渠道统计列表导出
 */
export const EXPORT_CHANNEL_STATICS = data => CONSTANT.publicPath + '/v1/channel/count/export?' + qs.stringify(data)

/**
 * 渠道统计详情列表
 */
export const GET_CHANNEL_STATICS_DETAIL = obj => {
  return get(`/v1/channel/count/${obj.channelCode}`, obj, false)
}
/**
 * 渠道统计详情列表导出
 */
export const EXPORT_CHANNEL_STATICS_DETAIL = data =>
  CONSTANT.publicPath + `/v1/channel/count/export/${data.channelCode}?` + qs.stringify(data)

/**
 * 落地页流程列表（不分页）
 * */
export const GET_CHANNEL_PROCESS_LIST_NO_PAGE = obj => {
  return get('/v1/channelProcess/getList', obj, null)
}

/**
 * 渠道会员卡新增
 * */
export const ADD_LANDING_PAGE = obj => {
  return post('/v1/channel_landing_page/insert', obj, null)
}

/**
 * 渠道会员卡新增B面
 * */
export const ADD_LANDING_PAGE_B = obj => {
  return post('/v1/channel_landing_page_b/insert', obj, null)
}
/**
 * 渠道会员卡查询列表
 * */
export const GET_LANDING_PAGE_LIST = obj => {
  return get('/v1/channel_landing_page/get_page_list', obj, null)
}

/**
 * 渠道会员卡查询列表B面
 * */
export const GET_LANDING_PAGE_LIST_B = obj => {
  return get('/v1/channel_landing_page_b/get_page_list', obj, null)
}

/**
 * 落地页查询列表（不分页）
 * */
export const GET_LANDING_PAGE_LIST_NO_PAGE = obj => {
  return get('/v1/channel_landing_page/list', obj, null)
}

/**
 * 落地页查询列表（不分页）
 * */
export const GET_LANDING_PAGE_LIST_NO_PAGE_B = obj => {
  return get('/v1/channel_landing_page_b/list', obj, null)
}

/**
 * 编辑渠道P1落地页
 * */
export const PUT_LANDING_PAGE_EDIT = obj => {
  return post('/v1/channel_landing_page/update', obj, null)
}

/**
 * 编辑渠道P1落地页B面
 * */
export const PUT_LANDING_PAGE_EDIT_B = obj => {
  return post('/v1/channel_landing_page_b/update', obj, null)
}

/**
 *礼券订单
 * */
export const GET_GIFTS = obj => {
  return get('/vip/order/gift', obj)
}

/**
 *礼券-禁用
 * */
export const GET_GIFTS_DISABLED = id => {
  return get(`/vip/order/gift/disabled/${id}`)
}

/**
 *礼券-充值
 * */
export const GET_GIFTS_RECHARGE = id => {
  return get(`/vip/order/gift/recharge/${id}`)
}

/**
 *礼券-导出
 * */

export const GET_GIFTS_EXPOER = data => CONSTANT.publicPath + `/vip/order/gift/export?` + qs.stringify(data)

/**
 *领取礼包
 * */
export const GET_GIFTS_LIST = obj => {
  return get('/vip/package/order/list', obj)
}

/**
 *礼包 - 禁用
 * */
export const GET_GIFTS_ORDER_DIS = id => {
  return get(`/vip/package/order/disabled/${id}`)
}

export const GET_GIFT_LIST = obj => {
  return get('/channel/package/all', obj)
}

/**
 *礼包 - 导出
 * */
export const GET_GIFTS_ORDERS_EXPOSRT = data => CONSTANT.publicPath + `/vip/package/order/export?` + qs.stringify(data)

// 渠道异常列表
export const GET_ABNORMAL_CHANNEL = data => {
  return get('/channel/black/record/selectList', data)
}

// 渠道异常详情
export const GET_ABNORMAL_CHANNEL_DETAIL = data => {
  return get('/channel/black/record/selectDetail', data)
}
// 渠道异常列表导出
export const GET_ABNORMAL_EXPOSRT = data =>
  CONSTANT.publicPath + `/channel/black/record/listExport?` + qs.stringify(data)

// 渠道异常详情导出
export const GET_ABNORMAL_DETAIL_EXPOSRT = data =>
  CONSTANT.publicPath + `/channel/black/record/detailExport?` + qs.stringify(data)

// IP统计列表
export const GET_IPSTATIC = data => {
  return get('/channel/page/black/record/selectList', data)
}
// 登录明细列表
export const GET_IPSTATIC_LOGIN = data => {
  return get('/channel/page/black/record/selectLoginList', data)
}
// 访问明细列表
export const GET_IPSTATIC_VISIT = data => {
  return get('/channel/page/black/record/selectVisitList', data)
}
// 渠道异常详情导出
export const GET_IPSTATIC_EXPOSRT = data =>
  CONSTANT.publicPath + `/channel/page/black/record/listExport?` + qs.stringify(data)

// 登录明细列表导出
export const GET_IPSTATIC_LOGIN_EXPOSRT = data =>
  CONSTANT.publicPath + `/channel/page/black/record/loginListExport?` + qs.stringify(data)
// 访问明细列表导出
export const GET_IPSTATIC_VISIT_EXPOSRT = data =>
  CONSTANT.publicPath + `/channel/page/black/record/visitListExport?` + qs.stringify(data)

// 获取弹窗列表
export const GET_POP_UPS_LIST = data => {
  return get('/popupWindowManagement/select/page', data)
}

// 获取弹窗位置选择列表（所有信息列表）
export const GET_POP_UPS_POSITION = data => {
  return get('/popupWindowManagement/popup/window/position/list', data)
}

// 获取弹窗业务逻辑选择列表
export const GET_POP_UPS_LOGIC = data => {
  return get('/popupWindowManagement/service/logic/list', data)
}

// 添加编辑弹窗
export const UPDATE_POP_UPS = data => {
  return post('/popupWindowManagement/insertOrUpdate', data)
}

// 查询所有弹窗信息（用于用户选择）
export const GET_POP_UPS_LIST_SIMPLE = data => {
  return get('/popupWindowManagement/select/list', data)
}

// 获取渠道P3页列表
export const GET_PAY_PAGE_LIST = data => {
  return get('/channel/pay/page/list', data)
}

// 获取渠道P3页列表 B面
export const GET_PAY_PAGE_LIST_B = data => {
  return get('/channel/pay/page/b/list', data)
}

// 获取简易的渠道P3页列表
export const GET_PAY_PAGE_CONDITION = data => {
  return get('/channel/pay/page/id/list', data)
}

// 获取简易的渠道P3页列表 B面
export const GET_PAY_PAGE_CONDITION_B = data => {
  return get('/channel/pay/page/b/id/list', data)
}

// 新增渠道P3页
export const ADD_PAY_PAGE_LIST = data => {
  return post('/channel/pay/page/add', data)
}
// 新增渠道P3页 B面
export const ADD_PAY_PAGE_LIST_B = data => {
  return post('/channel/pay/page/b/add', data)
}
// 编辑渠道P3页
export const EDIT_PAY_PAGE_LIST = data => {
  return post('/channel/pay/page/edit', data)
}
// 编辑渠道P3页 B面
export const EDIT_PAY_PAGE_LIST_B = data => {
  return post('/channel/pay/page/b/edit', data)
}
// 渠道p3页统计查询
export const GET_PAY_PAGE_STATIC = data => {
  return get('/bury/count/p3/payment/page/selectStatis', data)
}

// 渠道p3页统计详情查询
export const GET_PAY_PAGE_STATIC_DETAIL = data => {
  return get('/bury/count/p3/payment/page/selectDetailStatis', data)
}

// 查询支付详情接口
export const SEARCH_PAY_PAGE_DETAIL = data => {
  return get(`/channel/pay/page/${data.id}`)
}

// 查询支付详情接口 B面
export const SEARCH_PAY_PAGE_DETAIL_B = data => {
  return get(`/channel/pay/page/b/${data.id}`)
}

// 添加广告
export const ADD_ADVER_LIST = data => {
  return post('/channel/ad/add', data)
}

// 编辑广告
export const EDIT_ADVER_LIST = data => {
  return post('/channel/ad/edit', data)
}

// 检测渠道
export const CHECK_ADVER_CHANNEL = data => {
  return post('/channel/ad/add/check', data)
}

// 渠道列表
export const GET_ADVER_CHANNEL_LIST = data => {
  return get('/v1/channel/code/list', data)
}

// 广告列表
export const GET_ADVER_LIST = data => {
  return get('/channel/ad/list', data)
}

// 广告详情
export const GET_ADVER_DETAIL = data => {
  return get(`/channel/ad/${data.id}`, data)
}

// 广告删除
export const DELETE_ADVER_LIST = data => {
  return post(`/channel/ad/del/${data.id}`, data)
}

// 广告统计 按渠道统计
export const ADVER_TOTAL_BY_CHANNEL = data => {
  return get('/channel/ad/count/channel/list', data)
}
// 广告统计导出 按渠道统计
export const EXPORT_ADVER_TOTAL_BY_CHANNEL = data =>
  CONSTANT.publicPath + `/channel/ad/count/channel/list/export?` + qs.stringify(data)

// 广告统计 按渠道统计详情
export const ADVER_TOTAL_BY_CHANNEL_DETAIL = data => {
  return get('/channel/ad/count/channel/info/list', data)
}
// 广告统计导出 按渠道统计详情
export const EXPORT_ADVER_TOTAL_BY_CHANNEL_DETAIL = data =>
  CONSTANT.publicPath + `/channel/ad/count/channel/info/list/export?` + qs.stringify(data)

// 广告统计 按广告
export const ADVER_TOTAL_BY_ADVER = data => {
  return get('/channel/ad/count/ad/list', data)
}

// 广告统计导出 按广告
export const EXPORT_ADVER_TOTAL_BY_ADVER = data =>
  CONSTANT.publicPath + `/channel/ad/count/ad/list/export?` + qs.stringify(data)

// 广告统计详情 按广告
export const ADVER_TOTAL_BY_ADVER_DETAIL = data => {
  return get('/channel/ad/count/ad/info/list', data)
}

// 广告统计导出详情 按广告
export const EXPORT_ADVER_TOTAL_BY_ADVER_DETAIL = data =>
  CONSTANT.publicPath + `/channel/ad/count/ad/info/list/export?` + qs.stringify(data)

// 根据id获取广告名字
export const GET_ADVER_NAME_BY_ID = data => {
  return post(`/channel/ad/count/ad/name/list`, data)
}

// 广告p1数据统计
export const GET_ADVER_TOTAL_BY_P1 = data => {
  return get('/channel/ad/count/p1', data)
}
// p1数据按广告
export const GET_ADVER_TOTAL_BY_P1ADVER = data => {
  return get('/channel/ad/count/p1/ad', data)
}

// p1数据  按渠道
export const GET_ADVER_TOTAL_BY_P1CAHNNEL = data => {
  return get('/channel/ad/count/p1/channel', data)
}

// 重复购卡数据
export const GET_ADVER_TOTAL_BY_P1REBUY = data => {
  return get('/channel/ad/count/rebuy', data)
}

// 重复购卡数据按广告
export const GET_ADVER_TOTAL_REBUY_BYADVER = data => {
  return get('/channel/ad/count/rebuy/ad', data)
}

// 重复购卡数据按渠道
export const GET_ADVER_TOTAL_REBUY_BYCHANNEL = data => {
  return get('/channel/ad/count/rebuy/channel', data)
}

// 查看广告详情
export const GET_ADVER_TOTAL_DETAIL_BYADVER = data => {
  return get('/channel/ad/count/info/ad', data)
}

// 查看渠道详情
export const GET_ADVER_TOTAL_DETAIL_BYCHANNEL = data => {
  return get('/channel/ad/count/info/channel', data)
}

// 获取渠道所属平台
export const GET_OUTER_CHANNEL_TYPE = data => {
  return get('/v1/channel/platform', data)
}
// 获取API列表
export const GET_API_CHANNEL_TYPE = data => {
  return get('/v1/channel/platform/api', data)
}

// 异常监控列表
export const GET_ABNORMAL_CHANNEL_LIST = data => {
  return get('/channel/exception/list', data)
}

// 渠道明细

export const GET_CHANNEL_DETAIL = data => {
  return get('/channel/exception/list/info', data)
}

// ip明细异常渠道
export const GET_ABNORMAL_IP_LIST = data => {
  return get('/channel/exception/ip/list', data)
}

// ip明细 异常

export const GET_ABNORMAL_IP_DETAIL = data => {
  return get('/channel/exception/ip/list/info', data)
}

// 投放商品类型列表
export const GET_CHANNEL_CATEGORY_LIST = data => {
  return get('/channelcategory/list', data)
}

// 禁用投放商品类型
export const CHANNEL_CATEGORY_SWITCH = data => {
  return put('/channelcategory/' + data.id)
}

// 禁用投放商品类型
export const POST_CHANNEL_CATEGORY = data => {
  return post('/channelcategory', data)
}

// 投放商品类型下拉选择
export const GET_CHANNEL_CATEGORY_SELECTOR = data => {
  return get('/channelcategory/selector', data)
}
// 投放商品类型下拉选择-分组
export const channelcategoryGroupSelector = data => {
  return get('/channelcategory/groupSelector', data)
}

/**
  渠道相关统计数据 2021.3
*/
// 查询所有的渠道类型
export const GET_CHANNEL_DATA_TYPE = data => {
  return get('/count/channel/earnings/type/name', data)
}

// 查询所有的流程信息
export const GET_CHANNEL_DATA_PROCESS = data => {
  return get('/count/channel/earnings/process', data)
}

// 渠道收益数据
export const GET_CHANNEL_DATA_EARNING = data => {
  return get('/count/channel/earnings/page', data)
}

// 渠道收益数据导出
export const EXPORT_CHANNEL_DATA_EARNING = data =>
  CONSTANT.publicPath + `/count/channel/earnings/page/export?` + qs.stringify(data)

// 渠道收益历史数据
export const GET_CHANNEL_DATA_EARNING_HISTORY = data => {
  return get('/count/channel/earnings/page/history', data)
}

// 渠道收益历史数据导出
export const EXPORT_CHANNEL_DATA_EARNING_HISTORY = data =>
  CONSTANT.publicPath + `/count/channel/earnings/page/history/export?` + qs.stringify(data)

// 渠道收益历史数据
export const GET_CHANNEL_DATA_EARNING_AD = data => {
  return get('/count/channel/earnings/ad/earnings', data)
}

// 渠道收益接口（新）
// export const GET_CHANNEL_PROCESS_LIST = data => {
//   return get('/count/channel/launch/statistics/process',data);
// }
// export const GET_CHANNEL_TYPE = data => {
//   return get('/count/channel/launch/statistics/type/name',data)
// }
// 获取渠道收益列表
export const GET_CHANNEL_EARNINGS_LIST = data => {
  return get('/count/channel/launch/statistics/page/history', data)
}
// 导出渠道收益列表
export const EXPORT_CHANNEL_EARNINGS_LIST = data =>
  CONSTANT.publicPath + `/count/channel/launch/statistics/page/history/export?` + qs.stringify(data)

// 历史数据列表
export const GET_CHANNEL_EARNINGS_HISTORY = data => {
  return get('/count/channel/launch/statistics/page/history/channelCode', data)
}
// 导出历史数据列表
export const EXPORT_CHANNEL_EARNINGS_HISTORY = data => {
  return get('/count/channel/launch/statistics/page/history/channelCode/export', data)
}
// 埋点数据统计
export const GET_CHANNEL_EARNINGS_BURIEDPOINT = data => {
  return get('/count/new/channel/page/history', data)
}
// 埋点数据统计导出
export const EXPORT_CHANNEL_EARNINGS_BURIEDPOINT = data => {
  return get('/count/new/channel/page/history/export', data)
}
// 设备初始化
export const GET_EQUIPMENT_INIT = data => {
  return get('/channel/jointDebug/reset/clearDevice', data)
}
// 回传初始化
export const GET_COMES_INIT = data => {
  return get('/channel/return/reset/getVerificationCode', data)
}

// 渠道AB面关联
export const GET_REDIRECT_LIST = data => {
  return get('/channel/redirect/list', data)
}
// 渠道AB面关联
export const GET_REDIRECT_DETAIL = data => {
  return get(`/channel/redirect/${data.channelCode}`, data)
}
// 渠道AB面关联
export const PUT_REDIRECT = data => {
  return put('/channel/redirect', data)
}

//   app收益数据
export const GET_APP_EARNINGS_DATA = data => {
  return get('/count/channel/instation/page', data)
}
export const EXPORT_APP_EARNINGS_DATA = data =>
  CONSTANT.publicPath + `/count/channel/instation/page/export?` + qs.stringify(data)

export const GET_APP_HISTORY_DATA = data => {
  return get('/count/channel/instation/page/history', data)
}
export const EXPORT_APP_HISTORY_DATA = data =>
  CONSTANT.publicPath + `/count/channel/instation/page/history/export?` + qs.stringify(data)

// 历史数据
export const GET_APP_ADVER_DATA = data => {
  return get('/count/channel/instation/ad/data', data)
}

// 留存数据
export const GET_APP_RETAINED_DATA = data => {
  return get('/count/channel/retained/list', data)
}
// 留存数据导出
export const EXPORT_APP_RETAINED_DATA = data =>
  CONSTANT.publicPath + `/count/channel/retained/list/export?` + qs.stringify(data)
/**
 * 渠道广告topon
 */

export const get_channel_docking_page = obj => {
  return get('/ad/channel/docking/page', obj)
}
export const post_ad_docking_channel = obj => {
  return post('/ad/channel/docking', obj)
}

export const put_ad_docking_channel = obj => {
  return put('/ad/channel/docking', obj)
}

export const get_adChannel_docking_ById = id => {
  return get(`/ad/channel/docking/${id}`)
}
// 查询渠道投放统计
export const channel_launch_statistics_list = obj => {
  return get(`/count/channel/launch/statistics/list`, obj)
}
// 查询渠道投放统计导出
export const channel_launch_statistics_list_export = data =>
  CONSTANT.publicPath + `/count/channel/launch/statistics/list/export?` + qs.stringify(data)
// 查询渠道投放统计-历史数据
export const channel_launch_statistics_pageListHistory = obj => {
  return get(`/count/channel/launch/statistics/pageListHistory`, obj)
}
// 查询渠道投放统计-历史数据导出
export const channel_launch_statistics_pageListHistory_export = data =>
  CONSTANT.publicPath + `/count/channel/launch/statistics/listExport/export?` + qs.stringify(data)

// 广告统计(广告)

export const GET_COUNT_ADVERTISE_AD = params => {
  return get('/count/advertise/list', params, null)
}

// 广告统计(广告) 导出

export const GET_COUNT_ADVERTISE_AD_EXPORT = params =>
  CONSTANT.publicPath + '/count/advertise/list/export?' + qs.stringify(params)

// 广告详情统计(渠道) 导出

export const GET_COUNT_ADVERTISE_AD_DETAIL_EXPORT = params =>
  CONSTANT.publicPath + '/count/advertise/detailList/export?' + qs.stringify(params)

// 广告详情统计(广告)

export const GET_COUNT_ADVERTISE_AD_DETAIL = params => {
  return get('/count/advertise/detailList', params, null)
}
// 广告详情统计(渠道) 导出

export const GET_COUNT_ADVERTISE_AD_BY_CHANNEL_DETAIL_EXPORT = params =>
  CONSTANT.publicPath + '/count/advertise/detailListByChannel/export?' + qs.stringify(params)

// 广告详情统计(渠道)

export const GET_COUNT_ADVERTISE_AD_BY_CHANNEL_DETAIL = params => {
  return get('/count/advertise/detailListByChannel', params, null)
}

// 广告统计(渠道)

export const GET_COUNT_ADVERTISE_AD_BY_CHANNEL = params => {
  return get('/count/advertise/listByChannel', params, null)
}

// 广告统计(渠道) 导出

export const GET_COUNT_ADVERTISE_AD_BY_CHANNEL_EXPORT = params =>
  CONSTANT.publicPath + '/count/advertise/listByChannel/export?' + qs.stringify(params)

// 获取渠道绑定广告

export const GET_COUNT_ADVERTISE_AD_BIND_BY_CHANNEL = params => {
  return get('/count/advertise/getAdvertiseList', params, null)
}

export const GET_CHANNEL_LIST_ALL = params => {
  return get(`/channelNew/channelList`, params, null)
}

export const GET_CHANNEL_LIST_ALL_NEW = params => {
  return get(`/channelAd/vipOrderMedia/pageList`, params, null)
}

export const GET_AREA_TREE_ALL = params => {
  return get(`/area/tree/all`, params, null)
}

export const channelAd = params => {
  return post(`/channelAd`, params, null)
}

export const check_adver_channel = params => {
  return post(`/channelAd/add/check`, params, null)
}

// 批量详情
export const check_adver_details = params => {
  return get(`/channelAd/getDetailByIds?ids=${params.ids}`, null, null)
}

// 批量校验
export const check_adver_channels = params => {
  return post(`/channelAd/add/checkBatch`, params, null)
}

export const search_adver_list = params => {
  return get(`/channelAd/${params.id}`, null, null)
}

export const channelAdEdit = params => {
  return post(`/channelAd/edit`, params, null)
}

// 批量修改
export const channelAdEdits = params => {
  return post(`/channelAd/editBatch`, params, null)
}

export const undertakePageList = params => {
  return get(`/undertakePage/list`, null, null)
}

export const channelAdGroupType = params => {
  return get(`/channelAdGroup/type/${params.type}`, null, null)
}

export const channelAdGroupDelete = params => {
  return post(`/channelAdGroup/${params.id}`, params, null)
}

export const channelAdGroupArea = params => {
  return post(`/channelAdGroup/area`, params, null)
}

export const channelAdGroupAreaId = params => {
  return get(`/channelAdGroup/area/${params.id}`, null, null)
}

export const channelAdGroupCode = params => {
  return get(`/channelAdGroup/code/${params.id}`, null, null)
}

export const channelAdGroupCodes = params => {
  return post(`/channelAdGroup/code`, params, null)
}
// 广告统计大盘导出

export const GET_COUNT_ADVERTISE_DAPAN_EXPORT = params =>
  CONSTANT.publicPath + '/count/advertise/listDaPan/export?' + qs.stringify(params)

// 广告统计大盘

export const GET_COUNT_ADVERTISE_DAPAN = params => {
  return get('/count/advertise/listDaPan', params, null)
}
// 投放拦截优化-分页
export const channelClosePagination = params => {
  return get(`/channelClose/pagination`, params, null)
}

export const channeAdlList = params => {
  return get(`/channelAd/list`, params, null)
}

export const channelAdDelete = params => {
  return post(`/channelAd/delete`, params, null)
}

// 投放拦截优化-新增/修改
export const channelClose = params => {
  return post(`/channelClose`, params, null)
}
// 投放拦截优化-修改回显
export const channelCloseDetail = params => {
  return get(`/channelClose/` + params.id)
}
// 投放拦截优化-检测渠道code
export const channelCloseCheck = params => {
  return post(`/channelClose/check`, params, null)
}
// 渠道收益（总、新）

export const count_channel_earnings_page = obj => {
  return get(`/count/channel/earnings/page`, obj)
}
// 渠道收益（总、新）导出
export const count_channel_earnings_page_export = data =>
  CONSTANT.publicPath + `/count/channel/earnings/page/export?` + qs.stringify(data)

// 获取所有的渠道域名
export const GET_CHANNEL_DOMAIN_LIST = params => {
  return get(`/v1/channel/domain`, params, null)
}

// 渠道购卡统计查询
export const count_channel_pay_list = obj => {
  return get(`/count/channel/pay/list`, obj)
}

// 渠道购卡统计详情查询
export const count_channel_pay_detail_list = obj => {
  return get(`/count/channel/pay/detail`, obj)
}
// 渠道购卡统计时段详情查询
export const count_channel_pay_time_detail_list = obj => {
  return get(`/count/channel/pay/hour/detail/list`, obj)
}
// 导出
export const count_channel_pay_list_export = data =>
  CONSTANT.publicPath + `/count/channel/pay/list/export?` + qs.stringify(data)

// 导出
export const count_channel_pay_list_listHfivePayExport = data =>
  CONSTANT.publicPath + `/count/channel/pay/list/listHfivePayExport?` + qs.stringify(data)

// 详情导出
export const count_channel_pay_detail_list_export = data =>
  CONSTANT.publicPath + `/count/channel/pay/detail/export?` + qs.stringify(data)
// 渠道购卡统计时段详情导出
export const count_channel_pay_time_detail_list_export = data =>
  CONSTANT.publicPath + `/count/channel/pay/hour/detail/list/export?` + qs.stringify(data)

//  媒体下沉
// 渠道购卡统计媒体下沉列表查询
export const count_channel_pay_media_list = obj => {
  return get(`/count/channel/pay/media/list`, obj)
}
// 详情
export const count_channel_pay_media_daily_list = obj => {
  return get(`/count/channel/pay/media/daily/list`, obj)
}
// 导出
export const count_channel_pay_media_list_export = data =>
  CONSTANT.publicPath + `/count/channel/pay/media/export?` + qs.stringify(data)

// 详情导出
export const count_channel_pay_media_daily_export = data =>
  CONSTANT.publicPath + `/count/channel/pay/media/daily/export?` + qs.stringify(data)

// 广告下沉

// 渠道购卡广告下沉统计查询
export const count_channel_pay_ad_list = obj => {
  return get(`/count/channel/pay/media/ad/list`, obj)
}

// 详情查询
export const count_channel_pay_ad_daily_list = obj => {
  return get(`/count/channel/pay/media/ad/daily/list`, obj)
}
// 导出
export const count_channel_pay_ad_list_export = data =>
  CONSTANT.publicPath + `/count/channel/pay/media/ad/export?` + qs.stringify(data)

// 导出
export const count_channel_pay_ad_daily_export = data =>
  CONSTANT.publicPath + `/count/channel/pay/media/ad/daily/export?` + qs.stringify(data)

// 渠道查看投诉支付统计
export const count_channel_merchant_list = params => {
  return get(`/count/channel/merchant/list`, params, null)
}
// 应用名称列表
export const count_channel_application_list = params => {
  return get(`/application/info`, params, null)
}

export const refund_date = params => {
  return get(`/count/viporder/refund/date`, params, null)
}
export const channelStatisticsDetail = params => {
  return get(`/count/channel/launch/statistics/detail`, params, null)
}

// 已处理退款订单列表导出
export const refund_export = data => CONSTANT.publicPath + '/count/viporder/refund/date/export?' + qs.stringify(data)
// 渠道投放统计回传明细导出
export const channelStatisticsDetailExport = data =>
  CONSTANT.publicPath + '/count/channel/launch/statistics/detail/export?' + qs.stringify(data)

// 落地页周期扣款订单列表
export const cycleDeductionsOrdersList = params => {
  return get(`/order/goodsorder/agreemet`, params)
}

// 落地页周期扣款订单列表导出
export const cycleDeductionsOrdersExport = data => {
  return CONSTANT.publicPath + '/order/goodsorder/agreemet/export?' + qs.stringify(data)
}

// 落地页周期扣款订单退款
export const cycleDeductionsOrdersRefund = params => {
  return post(`/order/goodsorder/agreemet/refund`, params)
}

// 落地页周期扣款统计
export const cycleDeductionsOrdersStatistics = params => {
  return get(`/count/launch/list`, params)
}

// 落地页周期扣款统计导出
export const cycleDeductionsOrdersStatisticsExp = data => {
  return CONSTANT.publicPath + '/count/launch/list/export?' + qs.stringify(data)
}

// 综合数据查询
export const countChannelMonitorSynthesizeList = params => {
  return get(`/countChannelMonitor/synthesizePage`, params)
}

// 综合数据导出
export const countChannelMonitorSynthesizeExp = data => {
  return CONSTANT.publicPath + '/countChannelMonitor/synthesizeExport?' + qs.stringify(data)
}

// 渠道表查询
export const countChannelMonitorChannelList = params => {
  return get(`/countChannelMonitor/channelPage`, params)
}

// 渠道表导出
export const countChannelMonitorChannelExp = data => {
  return CONSTANT.publicPath + '/countChannelMonitor/channelExport?' + qs.stringify(data)
}

// 测试发送短信
export const testSmsSend = params => {
  return post(`/channel/sms/send`, params)
}
// 落地页类型
export const channelLandingPageType = params => {
  return get(`/v1/channel_landing_page/type`, params)
}

// 关联三方商户号下拉选
export const channelThirdPayconf = params => {
  return get(`/v1/channel/third/payconf`, params)
}

// 上传json
export const uploadJson = params => {
  return post(`/upload/json`, params)
}

// 动效列表-新增
export const lottieAdd = params => {
  return post(`/channelGif/add`, params)
}

// 动效列表-修改
export const lottieUpdate = params => {
  return post(`/channelGif/update`, params)
}

// 动效列表-详情
export const lottieDetail = id => {
  return get(`/channelGif/${id}`)
}

// 动效列表-分页
export const lottieList = params => {
  return get(`/channelGif/page`, params)
}

// 动效列表-所有
export const lottieListAll = params => {
  return get(`/channelGif/listAll`, params)
}

// 落地页-新增弹窗
export const landingWindowsAdd = params => {
  return post(`/landingWindows/save`, params)
}

// 落地页-修改弹窗
export const landingWindowsUpdate = params => {
  return post(`/landingWindows/update`, params)
}

// 落地页-获取弹窗列表
export const landingWindowsList = params => {
  return get(`/landingWindows/list`, params)
}

// 落地页类型页面下拉选择
export const channelpageNumberType = params => {
  return get(`/v1/channel_landing_page/listByType`, params)
}

// 客诉数据阶梯表
export const viporderRetained = params => {
  return get(`/viporder/retained`, params)
}

// 客诉数据阶梯表导出
export const viporderRetainedExport = data => {
  return CONSTANT.publicPath + '/viporder/retained/export?' + qs.stringify(data)
}

// 渠道监控-媒体下沉统计
export const countChannelMonitorChannelPageMedia = params => {
  return get(`/countChannelMonitor/channelPage/media`, params)
}

// beta表总/新时间趋势查询/导出
export const countTimeTrend = params => {
  return get(`/count/time/trend`, params)
}

// 渠道表导出
export const countTimeTrendExp = data => {
  return CONSTANT.publicPath + '/count/time/trend/export?' + qs.stringify(data)
}

// 渠道表导出
export const countChannelMonitorChannelPageMediaExport = data => {
  return CONSTANT.publicPath + '/countChannelMonitor/channelPage/media/export?' + qs.stringify(data)
}
// 媒体&广告位数据监控渠道数据-导出
export const countChannelMonitorChannelPageMediaListExport = data => {
  return CONSTANT.publicPath + '/countChannelMonitor/channelPage/media/list/export?' + qs.stringify(data)
}

// 渠道监控-媒体下沉统计-归因率
export const countChannelMonitorChannelPageMediaAttribution = params => {
  return get(`/countChannelMonitor/channelPage/media/attribution`, params)
}
// 站外购卡-互动统计
export const getInteractive = params => {
  return get(`/count/channel/pay/interactive/list`, params)
}
// 获取大盘今日和七日环比数据
export const getCompareList = params => {
  return get(`/count/monitor/compare/list`, params)
}
// 获取大盘列表和圆环数据
export const getChannelAlarmMediaList = params => {
  return get(`/count/complaintMonitor/market/channelAlarmMedia/list`, params)
}

// 获取大盘圆环数据
export const getRingDataList = params => {
  return get(`/count/complaintMonitor/market/ringData`, params)
}

// 渠道投诉率/退款 告警-兼容媒体/广告纬度
export const complaintMonitorMarketChannelAlarmMediaList = params => {
  return get(`/count/complaintMonitor/market/channelAlarmMedia/list`, params)
}

// 渠道投诉率/退款 告警-兼容媒体/广告纬度导出
export const complaintMonitorMarketChannelAlarmMediaExport = data => {
  return CONSTANT.publicPath + '/count/complaintMonitor/market/channelAlarmMedia/export?' + qs.stringify(data)
}

// 渠道投诉率/退款 告警-兼容媒体/广告纬度
export const getRetainedTable = params => {
  return get(`/viporder/retained/table`, params)
}
export const viporderRetainedTable = params => {
  return get(`/viporder/retained/table`, params)
}

// 站内退款统计数据
export const getInstationRefund = params => {
  return get(`/count/instationRefund/page`, params)
}
// vivo链接列表
export const channelVivoUrl = params => {
  return get(`/v1/channel/vivoUrl`, params)
}
// 享点链接列表
export const channelXiangdianUrl = params => {
  return get(`/v1/channel/xiangdian`, params)
}

// 站内退款统计数据导出
export const instationRefundExport = data => {
  return CONSTANT.publicPath + '/count/instationRefund/export?' + qs.stringify(data)
}

// 短信供应商
export const getSupplier = params => {
  return get(`/channel/sms/getSupplier`, params)
}

// 审核设备初始化检查
export const deviceInitCheck = params => {
  return get(`/channel/jointDebug/reset/getDeviceByParam`, params)
}

// 审核设备清楚
export const reviewDeviceClear = params => {
  return get(`/channel/jointDebug/reset/deleteChannelCodeByParam`, params)
}

// 退款策略列表
export const refundStrategyList = params => {
  return get(`/refundStrategy/v2/list`, params)
}

// 新增退款策略
export const refundStrategyAdd = data => {
  return post(`/refundStrategy/v2/add`, data)
}

// 编辑退款策略
export const refundStrategyEdit = data => {
  return post(`/refundStrategy/v2/edit`, data)
}

// 退款策略详情
export const refundStrategyDetail = params => {
  return get(`/refundStrategy/v2/detail/` + params)
}

// 删除退款策略
export const refundStrategyRemove = params => {
  return get(`/refundStrategy/v2/remove/` + params)
}

// 退款策略下拉
export const refundStrategySelect = params => {
  return get(`/refundStrategy/v2/selector`, params)
}

// 退款策略关联列表
export const refundRelatePagelist = params => {
  return get(`/refund/strategy/relate/pagelist`, params)
}

// 添加退款策略关联
export const refundRelateAdd = params => {
  return post(`/refund/strategy/relate/batch/add`, params)
}
// 添加退款策略关联检查
export const refundRelateCheckForAdd = params => {
  return post(`/refund/strategy/relate/precheck`, params)
}

// 添加退款策略关联
export const refundRelateDelete = params => {
  return get(`/refund/strategy/relate/delete/`, params)
}

// 添加退款策略关联
export const getRefundRelateStrategy = params => {
  return get(`/refund/strategy/relate/query/strategy`, params)
}

// 推啊媒体链接
export const channeltTaUrl = params => {
  return get(`/v1/channel/taUrl`, params)
}

// 获取投放网址
export const getChanneltLaunchSites = params => {
  return get(`/v1/channel/getThridLaunchDomain`, params)
}

// 渠道拦截关联查询配置
export const channelAdSelector = params => {
  return get(`/channelAd/selector`, params)
}

// 推啊媒体链接
export const channelAdItemLink = params => {
  return get(`/channel/aditem/link`, params)
}

// 拦截关联管理-分页查询
export const interceptionAssociationManagement = params => {
  return get(`/channel/aditem/list`, params)
}

// 新增关联时校验
export const channelAdItemCheck = params => {
  return post(`/channel/aditem/check`, params)
}

// 新增关联时校验
export const channelAdItemDelete = params => {
  return get(`/channel/aditem/delete`, params)
}

// 新增关联时校验
export const channelAdItemSave = params => {
  return post(`/channel/aditem/add`, params)
}

// 广告拦截-渠道P4页管理-分页查询
export const channelPageFourPage = params => {
  return get(`/channelPageFour/page`, params)
}

// 广告拦截-渠道P4页管理-新增
export const channelPageFourSave = params => {
  return post(`/channelPageFour/save`, params)
}

// 广告拦截-渠道P4页管理-分页查询
export const channelPageFourUpdate = params => {
  return post(`/channelPageFour/update`, params)
}

// 广告拦截-渠道P4页管理-分页查询
export const channelPageFourDelete = params => {
  return get(`/channelPageFour/${params}`)
}

// 广告拦截-渠道P4-获取媒体id
export const channelPageFourGetVipOrderMediaId = params => {
  return get(`/channelAd/getVipOrderMediaId`, params)
}

// 广告拦截-渠道P4-获取广告位id
export const channelPageFourGetVipOrderMediaAdId = params => {
  return get(`/channelAd/getVipOrderMediaAdId`, params)
}

// 编辑或删除渠道时校验
export const channelAdCheckAdById = params => {
  return get(`/channelAd/checkAd/${params}`)
}

// 支付策略分页列表
export const payLinkStrategy = params => {
  return get(`/payLinkStrategy/pageList`, params)
}

// 支付策略新增&修改
export const payLinkStrategySave = params => {
  return post(`/payLinkStrategy/save`, params)
}

// 删除支付策略
export const payLinkStrategyDel = id => {
  return get(`/payLinkStrategy/del/${id}`)
}

// 审核屏蔽新增
export const auditShieldAdd = params => {
  return post(`/audit/shield/add`, params)
}

// 审核屏蔽修改
export const auditShieldEdit = params => {
  return post(`/audit/shield/edit`, params)
}

// 审核屏蔽分页查询
export const auditShieldList = params => {
  return get(`/audit/shield/list`, params)
}

// 审核屏蔽删除
export const auditShieldDel = params => {
  return get(`/audit/shield/del`, params)
}

// 审核屏蔽单个查询
export const auditShieldGetById = params => {
  return get(`/audit/shield/getById`, params)
}

// 根据应用获取该应用的所有渠道
export const auditShieldChannelGetBySiteId = params => {
  return get(`/v1/channel/getBySiteId`, params)
}

// 站内退款统计数据导出
export const auditShieldListExport = data => {
  return CONSTANT.publicPath + '/audit/shield/list/export?' + qs.stringify(data)
}
// 渠道cvr综合看板top10媒体查询
export const cvrTop = data => {
  return get('/count/channel-pay/cvr/top', data)
}
// 渠道cvr综合看板top10媒体查询
export const cvrTrend = data => {
  return get('/count/channel-pay/cvr/trend', data)
}
// 渠道付费链路统计落地页样式下拉选
export const landingPageList = data => {
  return get('/count/channel/pay/landingPage/list', data)
}
// 付费链路cvr数据汇总
export const paySummary = data => {
  return get('/count/channel/pay/summary', data)
}
// 渠道总CVR 大盘数据
export const cvrBroadMarketData = data => {
  return get('/count/channel-pay/cvr/total', data)
}
// 获取sku名称列表
export const getSkuList = data => {
  return get('/channelcategory/all', data)
}

// 九宫格后台分页查询
export const countSudokuPageList = params => {
  return get(`/countSudoku/pageList`, params)
}

// 九宫格后台分页导出
export const countSudokuPageListExport = data => {
  return CONSTANT.publicPath + '/countSudoku/pageList/export?' + qs.stringify(data)
}
// 红包提现统计
export const countUserRedPage = params => {
  return get(`/count/user/red/page`, params)
}
// 红包提现统计导出
export const countUserRedPageExport = data => {
  return CONSTANT.publicPath + '/count/user/red/page/export?' + qs.stringify(data)
}
// 红包提现记录-分页查询
export const redPackWithdrawDetailPage = params => {
  return get(`/redPackWithdrawDetail/page`, params)
}
// 红包提现记录导出
export const redPackWithdrawDetailPageExport = data => {
  return CONSTANT.publicPath + '/redPackWithdrawDetail/page/export?' + qs.stringify(data)
}
// 红包提现审核
export const redPackWithdrawDetailAudit = params => {
  return get(`/redPackWithdrawDetail/audit`, params)
}
// 授权记录分页查询
export const getAgreementRecordPage = params => {
  return get(`/cycleVipOrderRelationship/getAgreementRecordPage`, params)
}
// 授权记录分页导出
export const getAgreementRecordPageExport = data => {
  return CONSTANT.publicPath + '/cycleVipOrderRelationship/getAgreementRecordPageExport?' + qs.stringify(data)
}
// 周期扣款订单分页查询
export const getCycleVipOrderPage = params => {
  return get(`/cycleVipOrderRelationship/getCycleVipOrderPage`, params)
}
// 周期扣款订单分页导出
export const getCycleVipOrderPageExport = data => {
  return CONSTANT.publicPath + '/cycleVipOrderRelationship/getCycleVipOrderPageExport?' + qs.stringify(data)
}
// 查询红包提现设置的接口
export const redPackWithdrawConfigList = params => {
  return get(`/redPackWithdrawConfig/list`, params)
}
// 红包提现-领取记录查询
export const redPackWithdrawDetailPageType = params => {
  return get(`/redPackWithdrawDetail/page/type`, params)
}

// 红包提现-领取记录
export const redPackWithdrawDetailPageTypeExport = data => {
  return CONSTANT.publicPath + '/redPackWithdrawDetail/page/type/export?' + qs.stringify(data)
}
// 查询红包提现设置
export const redPackWithdrawConfigUpdate = params => {
  return post(`/redPackWithdrawConfig/saveOrUpdate`, params)
}
// 红包提现-周期扣款按天查询
export const countCircleDateList = params => {
  return get(`/count/circle/dateList`, params)
}
// 红包提现-周期扣款按渠道查询
export const countCircleChannelList = params => {
  return get(`/count/circle/channelList`, params)
}
// 红包提现-周期扣款按天渠道查询
export const countCircleDateChannelList = params => {
  return get(`/count/circle/dateChannelList`, params)
}
// 红包提现-周期扣款按天查询
export const countCircleDateListExport = data => {
  return CONSTANT.publicPath + '/count/circle/dateList/export?' + qs.stringify(data)
}
// 周期扣款按渠道查询
export const countCircleChannelListExport = data => {
  return CONSTANT.publicPath + '/count/circle/channelList/export?' + qs.stringify(data)
}
// 周期扣款按天渠道查询
export const countCircleDateChannelListExport = data => {
  return CONSTANT.publicPath + '/count/circle/dateChannelList/export?' + qs.stringify(data)
}
//  分页查询红包提现设置
export const redPackWithdrawConfigListV2 = params => {
  return get(`/redPackWithdrawConfig/list/v2`, params)
}

//  删除
export const redPackWithdrawConfigDelete = id => {
  return get(`/redPackWithdrawConfig/delete/${id}`)
}

//  售后数据综合表接口
export const countSaleAfterDataList = params => {
  return get(`/count/sale-after/data/list`, params)
}
// 售后数据综合表接口导出
export const countSaleAfterDataListExport = data => {
  return CONSTANT.publicPath + '/count/sale-after/data/list/export?' + qs.stringify(data)
}
//  售后数据渠道表接口
export const countSaleAfterDataChannelList = params => {
  return get(`/count/sale-after/data/channel/list`, params)
}
// 售后数据渠道表接口导出
export const countSaleAfterDataChannelListExport = data => {
  return CONSTANT.publicPath + '/count/sale-after/data/channel/list/export?' + qs.stringify(data)
}

//  分页查询供应商
export const getSupplierList = params => {
  return get(`/crowd/supplier/all`, params)
}

export const getChangeStatusRoleList = params => {
  return get(`/crowdGoodsOrderRefund/getChangeStatusRoleList`, params)
}
// 获取所有的站外落地页域名
export const channelDomains = params => {
  return get(`/channel/domains`, params)
}
