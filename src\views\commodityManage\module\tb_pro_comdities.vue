<template>
  <div>
    <el-row type="flex" class="row-bg" justify="end" style="margin: 0 0 20px;">
      <el-button
        type="primary"
        plain
        icon="el-icon-circle-plus-outline"
        @click="linkDetails('addParent')"
        >添加</el-button
      >
      <el-button type="success" plain icon="el-icon-refresh" @click="refresh()"
        >刷新</el-button
      >
    </el-row>
    <el-table
      :data="tableData"
      style="width: 100%;margin-bottom: 20px;"
      row-key="id"
      border
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column prop="name" label="分类名称" width="180" />
      <el-table-column prop="sort" label="排序" width="180" />
      <el-table-column prop="logoUrl" label="图片">
        <template slot-scope="scope">
          <viewer
            v-if="scope.row.logoUrl"
            class="img-wrap"
            style="margin: auto;"
          >
            <img
              :src="scope.row.logoUrl"
              style="max-width: 50px;max-height: 50px;"
            />
          </viewer>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="是否显示">
        <template slot-scope="scope">
          <span v-if="scope.row.status == '0'" style="color:#1ABC9C">是</span>
          <span v-if="scope.row.status == '1'" style="color:#FF7F50">否</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="warning"
            plain
            @click="linkDetails('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="primary"
            plain
            @click="linkDetails('add', scope.row)"
            >添加</el-button
          >
          <el-button
            size="mini"
            type="danger"
            plain
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <section>
      <el-dialog
        :title="
          isType === 'addParent' || isType === 'add' ? '新增类型' : '编辑类型'
        "
        width="520px"
        :visible.sync="dialogFormVisible"
        :show-close="false"
      >
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="120px"
          class="demo-ruleForm"
        >
          <el-form-item label="类型名称：" prop="name" class="form-item">
            <el-input
              v-model="ruleForm.name"
              placeholder="请输入类型名称"
              style="width:320px;"
            />
          </el-form-item>
          <el-form-item label="排序：" prop="sort" class="form-item">
            <el-input
              v-model="ruleForm.sort"
              placeholder="请输入排序"
              style="width:320px;"
            />
          </el-form-item>
          <el-form-item label="分类图标：" prop="logoUrl">
            <div v-show="!ruleForm.logoUrl">
              <el-upload
                ref="upload"
                style="width:300px"
                accept="image/png, image/jpg, image/jpeg, image/gif"
                :headers="headers"
                :action="uploadUrl"
                :before-upload="beforeUpload"
                :on-success="
                  (response, file) => uploadSuccess(response, file, 'logo')
                "
                :on-error="uploadError"
                :multiple="false"
                :limit="1"
                list-type="picture"
                :file-list="fileList"
              >
                <el-button
                  id="uploadEle"
                  icon="el-icon-upload"
                  size="small"
                  plain
                  type="warning"
                  >点击上传</el-button
                >
                <div slot="tip" class="el-upload__tip">
                  (二级必传)只能上传jpg/png格式文件，文件不能超过1M
                </div>
              </el-upload>
            </div>
            <div v-if="ruleForm.logoUrl">
              <viewer style="display: inline-flex; ">
                <div class="img-wrap">
                  <img
                    :src="ruleForm.logoUrl"
                    style="max-width: 100px;max-height: 100px;"
                  />
                </div>
              </viewer>
              <div slot="tip" class="el-upload__tip">
                (二级必传)只能上传jpg/png格式文件，文件不能超过1M
              </div>
              <p>
                <el-button
                  type="warning"
                  plain
                  size="small"
                  @click="CikUpload('logo')"
                  >重新选取</el-button
                >
              </p>
            </div>
          </el-form-item>
          <el-form-item label="对应淘宝分类" prop="categoryItemId">
            <el-cascader
              v-model="ruleForm.categoryItemId"
              placeholder="请选择,默认一级"
              :show-all-levels="false"
              :options="categoryList"
              :props="{ checkStrictly: true }"
              clearable
              @change="handleChooseParentIds"
            />
          </el-form-item>
          <el-form-item label="是否上架：" prop="status">
            <el-radio v-model="ruleForm.status" label="DISPLAY">上架</el-radio>
            <el-radio v-model="ruleForm.status" label="HIDE">下架</el-radio>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="userUpdated('0', 'ruleForm')">取 消</el-button>
          <el-button
            type="primary"
            :loading="btn_disabled"
            :disabled="btn_disabled"
            @click="userUpdated('1', 'ruleForm')"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </section>
  </div>
</template>

<script>
import {
  getPromotionCommoditiesDataApi, // 获取推广商品分类
  setPromotionCommoditiesDataApi, // 新增推广商品分类
  editPromotionCommoditiesDataApi, // 编辑推广商品分类
  deletPromotionCommoditiesDataApi, // 删除推广商品分类
  getGoodsCategoryThreepartyListByMallId
} from '@/api/goods'
import constant from '@/config/constant.conf'
import Store from '@/store'
export default {
  props: {
    mallId: {
      type: String
    }
  },
  data() {
    // 排序类型限制 只能输入正整数
    const validateSort = (rule, value, callback) => {
      if (isNaN(value)) {
        callback(new Error('请输入数字'))
      } else if (!/(^[1-9]\d*$)/.test(value)) {
        callback(new Error('请输入正整数'))
      } else {
        callback()
      }
    }
    return {
      fileList: [], // 上传文件列表
      tableData: [], // 表格数据
      dialogFormVisible: false, // 弹窗展示开关
      uploadUrl: '', // 文件上传url
      isType: '', // 判断是编辑还是新增
      headers: {}, // 文件请求头，携带token
      btn_disabled: false, // 按钮是否能点击
      ruleForm: {
        name: '',
        sort: '', // 排序
        status: 'DISPLAY', // 是否上架
        logoUrl: '', // 上传文件url
        parentId: '', // 编辑id
        mallId: this.$props.mallId,
        categoryItemId: ''
      },
      rules: {
        name: [
          { required: true, message: '类型名称不能为空', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '排序不能为空', trigger: 'blur' },
          {
            validator: validateSort,
            trigger: 'blur'
          }
        ],
        logoUrl: [
          { required: true, message: '上传图片不能为空', trigger: 'blur' }
        ]
      },
      categoryList: []
    }
  },
  watch: {
    mallId(to) {
      this.getPromotionCommoditiesData()
      this.getThreeGoodsList()
    }
  },
  created() {
    // 上传地址
    this.uploadUrl = constant.publicPath + '/upload/image'
    // 请求头
    this.headers = { Authorization: `${Store.getters.authorization}` }
    this.getPromotionCommoditiesData()
    this.getThreeGoodsList()
  },
  methods: {
    // 获取推广商品分类
    getPromotionCommoditiesData() {
      getPromotionCommoditiesDataApi().then(res => {
        if (res.code == '0') {
          this.tableData = res.data
        }
      })
    },
    // 点击添加推广商品
    linkDetails(type, value) {
      this.isType = type
      this.dialogFormVisible = true
      if (this.isType == 'edit') {
        //   点击编辑
        let { name, sort, status, logoUrl, id } = value
        status == 0 ? (status = 'DISPLAY') : (status = 'HIDE')
        this.ruleForm = {
          name,
          sort,
          status,
          logoUrl,
          parentId: id,
          mallId: this.$props.mallId
        }
        if (value.threePartyCategory) {
          this.ruleForm.categoryItemId = value.threePartyCategory.id
        }
      } else {
        //   点击添加
        this.ruleForm = {
          name: '',
          sort: '',
          status: 'DISPLAY',
          logoUrl: null,
          parentId: '',
          mallId: this.$props.mallId,
          categoryItemId: ''
        }
        if (value) {
          this.ruleForm.parentId = value.id
        } else {
          this.ruleForm.parentId = null
        }
      }
    },
    beforeUpload(file) {
      // 上传之前
      if (file.size > 4 * 1024 * 1024) {
        this.$message.error('上传图片大小不能超过4M!')
      }
      return file.size
    },
    // 图片上传成功
    uploadSuccess: function(res, file, type) {
      const thar = this
      if (res.code === 0) {
        thar.ruleForm.logoUrl = res.data
      }
    },
    uploadError: function(res, file) {
      // 图片上传失败
      this.$message.error('图片上传失败')
    },
    // 【操作】 新增 | 修改
    userUpdated(isShow, formName) {
      if (isShow === '0') {
        // 取消
        this.dialogFormVisible = false
        this.fileList = [] // 清空图片缓存
      } else {
        this.$refs[formName].validate(valid => {
          if (valid) {
            this.btn_disabled = true
            if (this.isType === 'addParent' || this.isType === 'add') {
              this.ruleForm.status == 'DISPLAY'
                ? (this.ruleForm.status = 0)
                : (this.ruleForm.status = 1)
              this.setPromotionCommoditiesData(this.ruleForm)
            } else {
              /** 编辑 */
              let {
                name,
                logoUrl,
                sort,
                status,
                parentId,
                categoryItemId
              } = this.ruleForm
              status == 'DISPLAY' ? (status = 0) : (status = 1)
              const param = {
                name,
                logoUrl,
                sort,
                status,
                parentId: '',
                id: parentId,
                mallId: this.$props.mallId,
                categoryItemId
              }
              this.editPromotionCommoditiesData(param)
            }
          }
        })
      }
    },
    // 新增推广分类
    setPromotionCommoditiesData(params) {
      const categoryItemId = params.categoryItemId
        ? params.categoryItemId.slice(-1)
        : ''
      params.categoryItemId = categoryItemId ? categoryItemId.join(',') : ''
      setPromotionCommoditiesDataApi(params).then(res => {
        this.btn_disabled = false
        if (res.code === 0) {
          this.$message.success('新增成功！')
          this.getPromotionCommoditiesData()
          this.dialogFormVisible = false
          this.fileList = [] // 清空图片缓存
        }
      })
    },
    // 编辑推广分类
    editPromotionCommoditiesData(params) {
      if (typeof params.categoryItemId == 'object') {
        const categoryItemId = params.categoryItemId
          ? params.categoryItemId.slice(-1)
          : ''
        params.categoryItemId = categoryItemId ? categoryItemId.join(',') : ''
      }
      editPromotionCommoditiesDataApi(params).then(res => {
        this.btn_disabled = false
        if (res.code === 0) {
          this.$message.success('编辑成功！')
          this.getPromotionCommoditiesData()
          this.dialogFormVisible = false
          this.fileList = [] // 清空图片缓存
        }
      })
    },
    // 删除推广商品分类
    handleDelete(value) {
      const { id } = value
      this.$confirm('有子类类目将一起删除，是否确认删除？', '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletPromotionCommoditiesDataApi(id).then(res => {
            this.btn_disabled = false
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getPromotionCommoditiesData()
            }
          })
        })
        .catch(() => {
          // 取消
        })
    },
    // 重新选取图片
    CikUpload(type) {
      if (type === 'logo') {
        document.getElementById('uploadEle').click()
        this.$refs.upload.clearFiles()
      }

      if (type === 'detail') {
        document.getElementById('uploadEle2').click()
        this.$refs.upload2.clearFiles()
      }
    },
    // 点击刷新页面
    refresh() {
      this.getPromotionCommoditiesData()
    },
    getThreeGoodsList() {
      this.categoryList = []
      getGoodsCategoryThreepartyListByMallId(this.$props.mallId).then(res => {
        if (res.code == 200) {
          res.data.map(item => {
            item.label = item.name
            item.value = item.categoryId
            if (item.children && item.children.length > 0) {
              item.children.map(child => {
                child.label = child.name
                child.value = child.categoryId
                delete child.children
                return child
              })
            } else {
              delete item.children
            }
            return item
          })
          this.categoryList = res.data
        }
      })
    },
    handleChooseParentIds(e) {
      console.log(e)
    }
  }
}
</script>

<style scoped></style>
