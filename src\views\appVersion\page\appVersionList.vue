<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.status"
      >
        <page
          v-if="item.show"
          :request="item.request"
          :list="list"
          table-title="APP版本列表"
          :row-style="rowStyle"
        >
          <div slot="titleContainer" class="title-container-box">
            <el-button
              v-show="activeName === '0'"
              plain
              type="success"
              size="small"
              @click="syncAndroid"
              >同步Android版本</el-button
            >
            <el-button
              plain
              icon="el-icon-circle-plus-outline"
              type="primary"
              size="small"
              @click="handAdd"
              >新增</el-button
            >
          </div>
        </page>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  GET_APP_PAGING_QUERY,
  GET_APP_IOS_LIST,
  GET_CHANNEL_LIST,
  UPDATE_CHANNEL_DOWNLOAD,
} from '@/api/appVersion'
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { count_channel_application_list } from '@/api/NewChannel'

export default {
  components: {
    page,
  },
  data() {
    return {
      tabs: [
        {
          label: 'android',
          status: '0',
          request: {
            getListUrl: async (data) => {
              if (!this.siteIds.length) {
                await count_channel_application_list().then((response) => {
                  this.siteIds = response.data
                })
              }
              const list = await GET_APP_PAGING_QUERY({ ...data, os: 2 })
              const { records, total } = list.data
              const result = {
                data: {
                  total: total,
                  rows: records
                }
              }
              return result
            },
          },
          show: true,
        },
        {
          label: 'ios',
          status: '1',
          request: {
            getListUrl: async (data) => {
              if (!this.siteIds.length) {
                await count_channel_application_list().then((response) => {
                  this.siteIds = response.data
                })
              }

              const list = await GET_APP_PAGING_QUERY({ ...data, os: 1 })
              const { records, total } = list.data
              const result = {
                data: {
                  total: total,
                  rows: records
                }
              }
              return result
            },
          },
          show: false,
        },
      ],
      activeName: '0',
      id: '',
      siteIds: [],
    }
  },
  computed: {
    list() {
      const that = this
      return [
        {
          title: '版本号',
          key: 'version',
          type: formItemType.input,
        },
        {
          title: '应用名称',
          key: 'siteId',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.siteIds,
          listFormat: {
            label: 'name',
            value: 'siteId',
          },
          // val:'d6c4a4bbd1f748f89e879c00d60edd8e',
          reg: ['required'],
          search: true,
        },
        {
          title: '渠道/应用市场',
          key: 'relationCode',
          type: formItemType.select,
          requestList: GET_CHANNEL_LIST,
          listFormat: {
            label: 'title',
            value: 'appCode',
          },
          tableView: tableItemType.tableView.requestText,
        },
        {
          title: '版本描述',
          key: 'description',
          type: formItemType.input,
        },
        {
          title: '是否更新',
          key: 'setForce',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
        {
          title: '强制更新',
          key: 'setUpdate',
          tableView: tableItemType.tableView.text,
          type: formItemType.input,
          list: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
        {
          title: '应用类型',
          key: 'os',
          tableView: tableItemType.tableView.text,
          type: formItemType.input,
          list: [
            {
              label: 'iOS',
              value: 1,
            },
            {
              label: 'Android',
              value: 2,
            },
          ],
        },
        {
          title: '状态',
          key: 'status',
          tableView: tableItemType.tableView.text,
          type: formItemType.input,
          list: [
            {
              label: '禁用',
              value: 1,
            },
            {
              label: '启用',
              value: 0,
            },
          ],
        },
        {
          title: '上传时间',
          type: formItemType.datePickerDaterangeGai,
          key: 'createTime',
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd',
          },
        },
        {
          title: '地址',
          key: 'linkUrl',
          type: formItemType.input,
          tableView: tableItemType.tableView.jump,
        },
        {
          type: tableItemType.active,
          width: 180,
          headerContainer: false,
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              theme: 'warning',
              click: ($index, item, params) => {
                this.$router.push({
                  name: 'appVersionDetail',
                  query: {
                    os: this.activeName,
                    type: 'edit',
                    id: params.id,
                  },
                })
              },
            },
            {
              text: '下载',
              type: tableItemType.activeType.event,
              theme: 'primary',
              click: ($index, item, params) => {
                location.href = params.linkUrl
              },
              hidden(params) {
                if (that.activeName == 0) {
                  return false
                } else {
                  return true
                }
              },
            },
          ],
        },
      ]
    },
  },
  mounted() {
    const os = this.$route.query.os
    if (os) {
      this.handleClick({ name: os })
    }
  },
  methods: {
    syncAndroid() {
      UPDATE_CHANNEL_DOWNLOAD().then((res) => {
        if (res.code === 0) {
          this.$message.success('同步成功！')
        } else {
          this.$message.error('同步失败！')
        }
      })
    },
    handAdd() {
      if (this.activeName == 0) {
        this.$router.push({
          name: 'appVersionDetail',
          query: {
            os: this.activeName,
            type: 'add',
          },
        })
      } else {
        this.$router.push({
          name: 'appVersionDetail',
          query: {
            os: this.activeName,
            type: 'add',
          },
        })
      }
    },
    rowStyle({ row, column, rowIndex, columnIndex }) {
      if (
        row.status == 1 &&
        (columnIndex == 0 ||
          columnIndex == 1 ||
          columnIndex == 2 ||
          columnIndex == 3 ||
          columnIndex == 4 ||
          columnIndex == 5 ||
          columnIndex == 6 ||
          columnIndex == 7)
      ) {
        return 'backG'
      }
    },
    handleClick(tab) {
      this.activeName = tab.name
      this.tabs.map((val, index) => {
        if (index == tab.name) {
          val.show = true
        } else {
          val.show = false
        }
      })
    },
  },
}
</script>

<style lang="" scoped></style>
