/*
 * @Author: ZSY
 * @Date: 2020-08-26 09:30:22
 * @LastEditors: ZSY
 * @LastEditTime: 2020-08-26 09:31:25
 * @FilePath: \undefinedg:\work\sale-boss-frontend\src\api\buryData.js
 */
import CONSTANT from '@/config/constant.conf'
import {
  get
} from '@/libs/axios.package'
import qs from 'qs'

// 会员中心统计
export const GET_Bury_Vipcenter = obj => {
  return get(`/bury/vip/center/selectStatis`, obj)
}

export const EXPORT_Bury_Vipcenter = data => CONSTANT.publicPath + '/bury/vip/center/export?' + qs.stringify(data)

// 底导页面统计
export const GET_Bury_BottomGuide = obj => {
  return get(`/bury/bottom/guide/selectStatis`, obj)
}

export const EXPORT_Bury_BottomGuide = data => CONSTANT.publicPath + '/bury/bottom/guide/export?' + qs.stringify(data)

// 商品详情统计
export const GET_Bury_ProductDetail = obj => {
  return get(`/bury/product/detail/selectStatis`, obj)
}

export const EXPORT_Bury_ProductDetail = data => CONSTANT.publicPath + '/bury/product/detail/export?' + qs.stringify(data)

export const GET_RIGHTS_GOODS = obj => {
  return get(`/homepage/rightsgoods/count/new`, obj)
}

export const EXPORT_RIGHTS_GOODS = data => CONSTANT.publicPath + '/homepage/rightsgoods/count/export/new?' + qs.stringify(data)
/** 加油订单 */

export const get_bury_count_oil_selectStatis = obj => get(`/bury/count/oil/selectStatis`, obj)

export const export_bury_count_oil_selectStatis = data => CONSTANT.publicPath + '/bury/count/oil/selectStatis/port?' + qs.stringify(data)

export const GET_RIGHTS_GOODS_DETAILS = obj => {
  return get(`/homepage/rightsgoods/count/details/new`, obj)
}

export const EXPORT_RIGHTS_GOODS_DETAILS = data => CONSTANT.publicPath + '/homepage/rightsgoods/count/details/export/new?' + qs.stringify(data)

export const GET_REBATE_MALL = obj => {
  return get(`/count/rebate/mall/page`, obj)
}

export const EXPORT_REBATE_MALL = data => CONSTANT.publicPath + '/count/rebate/mall/export?' + qs.stringify(data)

/**   商品详情统计   */

export const get_count_goods_share_page = obj => {
  return get(`count/goods/share/page`, obj)
}

export const get_count_goods_share_page_exports = data => CONSTANT.publicPath + '/count/goods/share/export?' + qs.stringify(data)

export const GET_count_authorizations_page = obj => {
  return get(`/count/authorizations/page`, obj)
}

export const export_count_authorizations_page = data => CONSTANT.publicPath + '/count/authorizations/export?' + qs.stringify(data)

export const GET_RETURN_DEVICE = obj => {
  return get(`/count/return/device`, obj)
}

export const EXPORT_COUNT_RETURN_DEVICE = data => CONSTANT.publicPath + '/count/return/device/export?' + qs.stringify(data)

export const GET_COUNT_RETURN_USER = obj => {
  return get(`/count/return/user`, obj)
}

export const EXPORT_COUNT_RETURN_USER = data => CONSTANT.publicPath + '/count/return/user/export?' + qs.stringify(data)
/**   会员中心统计   */
export const userCenterPage = obj => {
  return get(`/count/user/center/page`, obj)
}
/**   会员中心统计导出   */
export const userCenterPageExport = data => CONSTANT.publicPath + '/count/user/center/page/export?' + qs.stringify(data)

/**   续费页统计   */
export const countVipRenewPage = obj => {
  return get(`/count/vipRenew/page`, obj)
}
/**   续费页统计导出   */
export const countVipRenewPageExport = data => CONSTANT.publicPath + '/count/vipRenew/page/export?' + qs.stringify(data)

