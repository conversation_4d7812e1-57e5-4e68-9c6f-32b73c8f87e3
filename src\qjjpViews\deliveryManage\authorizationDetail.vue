<!--
 * @Author: 陈小豆
 * @Date: 2024-04-25 14:33:57
 * @LastEditors: 陈小豆
 * @LastEditTime: 2024-06-27 17:42:43
-->
<template>
  <div>
    <page
      :request="request"
      :list="list"
      table-title="广告账户列表"
    >
      <div slot="searchContainer" style="display: inline-block" />
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import { tableItemType, formItemType } from '@/config/sysConfig'
import { undertakeList, addOrUpdate, undertakeListexport, modelRepositorypage, modelRepositoryplatform, modelRepositoryexit } from '@/qjjpApi/operate'
import { count_channel_application_list, mediaAll } from '@/qjjpApi/NewChannel'
import { advertiserpage } from '@/qjjpApi/deliveryManage'

import moment from 'moment'
const currentDate = moment().format('YYYY-MM-DD')
export default {
  name: 'qjjpUserList',
  components: {
    page
  },
  props: {},
  data() {
    return {
      id: '',
      addForm: {
        id: '',
        platform: '',
        name: '',
        endPointId: '',
        accessKey: '',
        secretKey: '',
        status: 1
      },
      mediaList: [],
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      },
      drawer: false,
      list4: [
        {
          id: 1,
          name: '会员用户'
        },
        {
          id: 0,
          name: '非会员用户'
        }
      ],
      list1: [
        {
          id: 1,
          name: '男生'
        },
        {
          id: 0,
          name: '女生'
        }
      ],
      list2: [
        {
          id: 1,
          name: '启用'
        },
        {
          id: 0,
          name: '禁用'
        }
      ],
      list3: [
        {
          id: 1,
          name: '落地页'
        }
      ],
      list6: [
        {
          id: 0,
          name: '未授权'
        },
        {
          id: 1,
          name: '已授权'
        },
        {
          id: 2,
          name: '占用'
        }
      ],
      siteIds: [],
      listQuery: {
        mediaDeliveryAccountId: ''
      },
      request: {
        getListUrl: async data => {
          this.listQuery = { ...this.listQuery, ...data }
          //   if (!this.siteIds.length) {
          //     await modelRepositoryplatform().then(res => {
          //       if (res.code === 200) {
          //         if (res.data && res.data.length) {
          //           this.siteIds = res.data
          //         }
          //       }
          //     })
          //   }

          const list = await advertiserpage(this.listQuery)
          const { records, total } = list.data
          let dataList = []
          if (records && records.length) {
            dataList = {
              total: total,
              rows: records
            }
          }
          const result = {
            data: dataList
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '广告主账号ID',
          type: formItemType.input,
          search: true,
          clearable: true,
          searchKey: 'adAccount',
          val:this.listQuery.adAccount,
          tableHidden: true
        },
        {
          title: '授权状态',
          key: 'authorizationStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.list6,
          listFormat: {
            label: 'name',
            value: 'id'
          },
          reg: ['required'],
          search: true,
          clearable: true,
          tableHidden: true
        },
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '媒体平台',
          key: 'mediaPlatformStr'
        },
        {
          title: '授权账号',
          key: 'deliveryAccount'
        },
        {
          title: '广告主账户ID',
          key: 'adAccount'
        },
        {
          title: '授权状态',
          key: 'authorizationStatusStr'
        },
        {
          title: '更新时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        }
      ]
    }
  },
  async mounted() {
      this.listQuery.mediaDeliveryAccountId = this.$route.query.id
    
    await mediaAll().then(res => {
      if (res.code === 200) {
        this.mediaList = res.data
      }
    })
    // const list1 = await messageStyleList()
    // const { records, total } = list1.data
    // let dataList = []
    // console.info(records, 'records')
    // if (records && records.length) {
    //   dataList = [total, ...records]
    // }
    // const result = {
    //   data: dataList
    // }
    // console.info(result, 'result')
  },
  created() {},
  methods: {
    renderHeaders(h, { column }, textArr) {
      return h('div', [
        h('span', column.label),
        h('el-tooltip', null, [
          h('div', { slot: 'content' }, [textArr.map(text => h('div', null, text))]),
          h('i', {
            class: 'el-icon-question',
            style: 'color:#409eff;margin-left:5px;font-size: 16px;'
          })
        ])
      ])
    },
    handMessageStyleListAdd(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          modelRepositoryexit({ ...this.addForm }).then(res => {
            if (res.code == 200) {
              this.drawer = false
              this.$message.success('操作成功')
              this.$store.dispatch('tableRefresh', this)
            }
          })
        }
      })
    },
    handExport() {
      const data = {
        ...this.listQuery
      }
      window.location.href = undertakeListexport({
        ...data,
        token: this.$store.getters.authorization
      })
    },
    reloadAddform() {
      this.addForm = {
        id: '',
        platform: '',
        name: '',
        endPointId: '',
        accessKey: '',
        secretKey: '',
        status: 1
      }
    },
    handleAdd() {
      this.reloadAddform()
      this.drawer = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
    overflow: scroll;
    // padding-bottom: 20px;
    padding: 0 30px 20px;
    position: relative;
    /* overflow-x: auto; */
}
::v-deep .el-drawer__header{
  span{
    font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
  }

}
::v-deep .el-drawer__body{

}
.close_button{
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    background-color: rgb(0, 0, 0,1);
    text-align: center;
    cursor: pointer;
i{
  color: white;
  line-height: 40px;
}
  }
.drawer_package{
  height: 100%;
  position: relative;

  .drawer_title{
    padding: 10px 20px 5px;
    vertical-align: middle;
    span{
      font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
    }

}
}

.addForm_package{
  background-color: rgb(189, 184, 184,0.1);
  padding: 15px;
  height: 100%;
  .demo-ruleForm{
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    position: relative;
    .view_button{
      position: absolute;
      bottom: 0;
    }
  }
}
.view_button{
          background-color: #ffffff;
          padding: 20px;
          border-top: 1px dashed #000000;
          ::v-deep .el-button{
            margin: 0 10px;
          }
        }
.form_view{
        // margin: 0 0px 20px;
        background-color: #ffffff;
        // border: 1px solid rgba(0,0,0,0.2);
        width: 100%;
        padding: 15px;
        border-radius: 5px;

        .form_view_title{
            margin-bottom: 20px;
            .title_line{
              width: 2px;
              height: 10px;
              background-color:#66b1ff ;
              display: inline-block;
              vertical-align: middle;
            }
            span{
              padding-left: 5px;
              vertical-align: middle;
font-family: PingFangSC, PingFang SC;
font-weight: 600;
font-size: 20px;
color: #333333;
line-height: 30px;
text-align: left;
font-style: normal;
            }
        }
    }
.excel-upload {
  text-align: center;
  &::v-deep.el-upload-dragger {
    width: 225px;
  }
}
.fail_list {
  margin-top: 10px;
  .tip {
    width: 100%;
    height: 20px;
    background: #e4e7ed;
    text-align: center;
    line-height: 20px;
    margin: 10px 0;
  }
}
.copy-btn {
  cursor: pointer;
}
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
