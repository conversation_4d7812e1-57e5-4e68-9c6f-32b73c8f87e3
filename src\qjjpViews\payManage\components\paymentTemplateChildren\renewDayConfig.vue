<template>
  <el-form :model="itemForm" :rules="addRules" label-width="130px" class="demo-ruleForm">
    <div :style="{ display: 'inline-block', width: '390px' }">
      <el-form-item label="续费类型" prop="renewType" :rules="addRules.common" :style="{ display: 'inline-block' }">
        <el-select v-model="itemForm.renewType" placeholder="请选择续费类型">
          <el-option :key="1" label="支付并签约" :value="1">支付并签约</el-option>
          <el-option :key="2" label="签约后扣款" :value="2" :disabled="true">签约后扣款</el-option>
        </el-select>
      </el-form-item>
    </div>
    <div v-if="itemForm.renewType == 1" :style="{ display: 'inline-block', width: '390px' }">
      <el-form-item label="签约支付金额" prop="renewPayPrice" :rules="addRules.common" :style="{ display: 'inline-block' }">
        <div :style="{ width: '200px' }">
          <el-input v-model="itemForm.renewPayPrice" placeholder="请输入签约支付金额，单位：元" />
        </div>
      </el-form-item>
    </div>
    <div :style="{ display: 'inline-block', width: '390px' }">
      <el-form-item label="首次扣款时间类型" prop="renewFirstDeductDayType" :rules="addRules.common"
        :style="{ display: 'inline-block' }">
        <div :style="{ width: '200px' }">
          <el-select v-model="itemForm.renewFirstDeductDayType" placeholder="首次扣款时间类型" @change="deductDayTypeChange">
            <el-option :key="1" label="天" :value="1">天</el-option>
            <el-option :key="2" label="小时" :value="2">小时</el-option>
          </el-select>
        </div>
      </el-form-item>
    </div>
    <div :style="{ display: 'inline-block', width: '390px' }">
      <el-form-item label="首次扣款时间" prop="renewFirstDeductDay" :rules="addRules.validRenewFirstDay"
        :style="{ display: 'inline-block' }">
        <div class="sufixed-span">
          <el-input v-model="itemForm.renewFirstDeductDay" placeholder="请输入首次扣款时间" @change="renewFirstDayInput" />
        </div>
      </el-form-item>
      <span>{{ itemForm.renewFirstDeductDayType === 2 ? '小时' : '天' }}</span>
    </div>
  </el-form>

</template>
<script>
const renewParams = {
  renewType: '',
  renewPayPrice: '',
  renewFirstDeductDayType: '',
  renewFirstDeductDay: ''
}
export default {
  name: 'renewDayConfig',
  props: {
    item: {
      type: Object,
      default: () => { return renewParams }
    },
    disLxkf: {
      type: Boolean,
      default: false
    }
  },
  data() {
    var validRenewFirstDay = (rule, value, callback) => {
      if (!value) {
        callback(new Error('必填'))
      } else if (Number(value) < 1) {
        callback(new Error('不能小于1'))
      } else if (!(/^[1-9]\d*$/.test(value))) {
        callback(new Error('类型必须为整数'))
      } else {
        callback()
      }
    }
    const { renewType, renewPayPrice, renewFirstDeductDayType, renewFirstDeductDay } = this.item || {}
    return {
      itemForm: { renewType, renewPayPrice, renewFirstDeductDayType, renewFirstDeductDay },
      addRules: {
        common: [{ required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }],
        validRenewFirstDay: [{ validator: validRenewFirstDay, trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'blur' }, { required: true, message: '此项不能为空', trigger: 'change' }]
      }
    }
  },
  watch: {
    itemForm: {
      handler(val) {
        this.$emit('getRenewConfig', val)
      },
      deep: true
    }
  },
  methods: {
    deductDayTypeChange() {
      this.itemForm.renewFirstDeductDay = null
    },
    resetForm() {
      this.itemForm = { ...renewParams }
    },
    renewFirstDayInput(value) {
      if (value < 1) {
        this.itemForm.renewFirstDeductDay = 1
        return
      }
      if (this.itemForm.renewFirstDeductDayType === 2 && (value < 1 || value > 24)) {
        this.$message.error('扣款时间必须大于1且小于24')
        this.itemForm.renewFirstDeductDay = ''
        return
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
