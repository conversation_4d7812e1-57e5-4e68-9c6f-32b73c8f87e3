<template>
  <div>
    <el-form
      :inline="true"
      :model="searchForm"
      class="demo-form-inline"
      style="margin-top:23px;"
    >
      <el-form-item label="分类名称：">
        <el-input v-model="searchForm.name" placeholder="请输入分类名称" />
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="searchForm.status" placeholder="请选择状态">
          <el-option label="全部" value />
          <el-option label="上架" value="0" />
          <el-option label="下架" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getGoodList"
          >查询</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-circle-plus-outline"
          @click="linkDetails('add')"
          >添加</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      style="width: 100%;margin-bottom: 20px;"
      row-key="id"
      border
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column prop="name" label="分类名称" width="180" />
      <el-table-column prop="logoUrl" label="图片">
        <template slot-scope="scope">
          <viewer
            v-if="scope.row.logoUrl"
            class="img-wrap"
            style="margin: auto;"
          >
            <img
              :src="scope.row.logoUrl"
              style="max-width: 50px;max-height: 50px;"
            />
          </viewer>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="对应分类" width="180">
        <template slot-scope="scope">
          <div>
            {{
              scope.row.threePartyCategory && scope.row.threePartyCategory.name
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column prop="updateTime" label="更新时间" width="180" />
      <el-table-column prop="sort" label="排序" width="180" />
      <el-table-column prop="status" label="是否显示">
        <template slot-scope="scope">
          <span v-if="scope.row.status == '0'" style="color:#1ABC9C">上架</span>
          <span v-if="scope.row.status == '1'" style="color:#FF7F50">下架</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="warning"
            plain
            @click="linkDetails('edit', scope.row)"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="danger"
            plain
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <section>
      <el-dialog
        :title="isType === 'add' ? '新增类型' : '编辑类型'"
        width="520px"
        :visible.sync="dialogFormVisible"
        :show-close="false"
      >
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="150px"
          class="demo-ruleForm"
        >
          <!-- <el-form-item label="平台选择" v-if="isType == 'add'">
            <el-select v-model="ruleForm.mallId" placeholder="请选择平台选择">
              <el-option
                v-for="(item,index) in platformList"
                :key="index"
                :label="item.title"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="趣淘生活分类：">
            <el-select
              v-model="ruleForm.parentId"
              placeholder="请选择趣淘生活分类"
            >
              <el-option
                v-for="(item, index) in selectDataList"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="趣淘生活分类名称：" prop="name" class="form-item">
            <el-input
              v-model="ruleForm.name"
              placeholder="分类自定义名称"
              style="width:320px;"
            />
          </el-form-item>
          <el-form-item label="分类图标：" prop="logoUrl">
            <div v-show="!ruleForm.logoUrl">
              <el-upload
                ref="upload"
                style="width:300px"
                accept="image/png, image/jpg, image/jpeg, image/gif"
                :headers="headers"
                :action="uploadUrl"
                :before-upload="beforeUpload"
                :on-success="
                  (response, file) => uploadSuccess(response, file, 'logo')
                "
                :on-error="uploadError"
                :multiple="false"
                :limit="1"
                list-type="picture"
                :file-list="fileList"
              >
                <el-button
                  id="uploadEle"
                  icon="el-icon-upload"
                  size="small"
                  plain
                  type="warning"
                  >点击上传</el-button
                >
                <div slot="tip" class="el-upload__tip">
                  (二级必传)只能上传jpg/png格式文件，文件不能超过1M
                </div>
              </el-upload>
            </div>
            <div v-if="ruleForm.logoUrl">
              <viewer style="display: inline-flex; ">
                <div class="img-wrap">
                  <img
                    :src="ruleForm.logoUrl"
                    style="max-width: 100px;max-height: 100px;"
                  />
                </div>
              </viewer>
              <div slot="tip" class="el-upload__tip">
                (二级必传)只能上传jpg/png格式文件，文件不能超过1M
              </div>
              <p>
                <el-button
                  type="warning"
                  plain
                  size="small"
                  @click="CikUpload('logo')"
                  >重新选取</el-button
                >
              </p>
            </div>
          </el-form-item>
          <el-form-item label="平台分类">
            <el-cascader
              v-model="ruleForm.categoryItemId"
              placeholder="请选择,默认一级"
              :options="categoryList"
              :props="{ checkStrictly: true, emitPath: false }"
              clearable
              @change="handleChooseParentIds"
            />
          </el-form-item>
          <el-form-item label="排序：" prop="sort" class="form-item">
            <el-input
              v-model="ruleForm.sort"
              placeholder="请输入排序"
              style="width:320px;"
            />
          </el-form-item>
          <el-form-item label="是否上架：" prop="status">
            <el-radio v-model="ruleForm.status" label="0">上架</el-radio>
            <el-radio v-model="ruleForm.status" label="1">下架</el-radio>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="userUpdated('0', 'ruleForm')">取 消</el-button>
          <el-button
            type="primary"
            :loading="btn_disabled"
            :disabled="btn_disabled"
            @click="userUpdated('1', 'ruleForm')"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </section>
  </div>
</template>
<script>
import {
  getGoodsCategoryListByMallId,
  setPromotionCommoditiesDataApi,
  editPromotionCommoditiesDataApi,
  deletPromotionCommoditiesDataApi,
  getGoodsCategoryThreepartyListByMallId
} from '@/api/goods'
import constant from '@/config/constant.conf'
import Store from '@/store'
export default {
  name: '',
  props: {
    mallId: {
      type: String,
      default: '1002'
    },
    platformList: {
      type: Array
    }
  },
  data() {
    const validateSort = (rule, value, callback) => {
      if (isNaN(value)) {
        callback(new Error('请输入数字'))
      } else if (!/(^[1-9]\d*$)/.test(value)) {
        callback(new Error('请输入正整数'))
      } else {
        callback()
      }
    }
    return {
      searchForm: {
        mallId: this.$props.mallId,
        name: '',
        status: ''
      },
      fileList: [], // 上传文件列表
      tableData: [], // 表格数据
      dialogFormVisible: false, // 弹窗展示开关
      uploadUrl: '', // 文件上传url
      isType: '', // 判断是编辑还是新增
      headers: {}, // 文件请求头，携带token
      btn_disabled: false, // 按钮是否能点击
      categoryList: [],
      selectDataList: [],
      ruleForm: {
        name: '',
        sort: '', // 排序
        status: 'DISPLAY', // 是否上架
        logoUrl: '', // 上传文件url
        parentId: '', // 编辑id
        categoryItemId: '',
        mallId: this.$props.mallId
      },
      rules: {
        name: [
          { required: true, message: '类型名称不能为空', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '排序不能为空', trigger: 'blur' },
          {
            validator: validateSort,
            trigger: 'blur'
          }
        ],
        logoUrl: [
          { required: true, message: '上传图片不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    mallId(to) {
      this.searchForm.mallId = to
      this.getGoodList()
      this.getThreeGoodsList()
    }
  },
  created() {
    this.uploadUrl = constant.publicPath + '/upload/image'
    this.headers = { Authorization: `${Store.getters.authorization}` }
  },
  mounted() {
    this.getGoodList()
    this.getThreeGoodsList()
  },
  methods: {
    getGoodList() {
      this.selectDataList = []
      this.selectDataList.push({
        name: '一级分类',
        id: ''
      })
      this.tableData = []
      getGoodsCategoryListByMallId(this.searchForm).then(res => {
        if (res.code == 200) {
          res.data.forEach(item => {
            this.selectDataList.push(item)
          })
          res.data = res.data.map(item => {
            item.parentId = ''
            if (item.children) {
              item.children.map(child => {
                child.parentId = item.id
                return child
              })
            }
            return item
          })
          this.tableData = res.data
        }
      })
    },
    getThreeGoodsList() {
      this.categoryList = []
      getGoodsCategoryThreepartyListByMallId(this.$props.mallId).then(res => {
        if (res.code == 200) {
          if (res.data) {
            res.data.map(item => {
              item.label = item.name
              item.value = item.categoryId
              if (item.children && item.children.length > 0) {
                item.children.map(child => {
                  child.label = child.name
                  child.value = child.categoryId
                  delete child.children
                  return child
                })
              } else {
                delete item.children
              }
              return item
            })
            this.categoryList = res.data
          }
        }
      })
    },
    linkDetails(type, value) {
      this.isType = type
      this.dialogFormVisible = true
      if (this.isType == 'edit') {
        //   点击编辑
        const { name, sort, status, logoUrl, id, parentId } = value
        this.ruleForm = {
          name,
          sort,
          status: String(status),
          logoUrl,
          categoryItemId: value.threePartyCategory.id,
          mallId: this.$props.mallId,
          parentId: parentId,
          id
        }
      } else {
        //   点击添加
        this.ruleForm = {
          name: '',
          sort: '',
          status: '0',
          logoUrl: null,
          parentId: '',
          categoryItemId: '',
          mallId: ''
        }
      }
    },
    handleDelete(value) {
      const { id } = value
      this.$confirm('有子类类目将一起删除，是否确认删除？', '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletPromotionCommoditiesDataApi(id).then(res => {
            this.btn_disabled = false
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getGoodList()
            }
          })
        })
        .catch(() => {
          // 取消
        })
    },
    beforeUpload(file) {
      // 上传之前
      if (file.size > 4 * 1024 * 1024) {
        this.$message.error('上传图片大小不能超过4M!')
      }
      return file.size
    },
    // 图片上传成功
    uploadSuccess: function(res, file, type) {
      const thar = this
      if (res.code === 0) {
        thar.ruleForm.logoUrl = res.data
      }
    },
    uploadError: function(res, file) {
      // 图片上传失败
      this.$message.error('图片上传失败')
    },
    // 【操作】 新增 | 修改
    userUpdated(isShow, formName) {
      if (isShow === '0') {
        // 取消
        this.dialogFormVisible = false
        this.fileList = [] // 清空图片缓存
      } else {
        this.$refs[formName].validate(valid => {
          if (valid) {
            this.btn_disabled = true
            this.ruleForm.mallId = this.$props.mallId
            if (this.isType === 'add') {
              this.setPromotionCommoditiesData(this.ruleForm)
            } else {
              this.editPromotionCommoditiesData(this.ruleForm)
            }
          }
        })
      }
    },
    // 新增推广分类
    setPromotionCommoditiesData(params) {
      params.categoryItemId = Number(params.categoryItemId)
      setPromotionCommoditiesDataApi(params).then(res => {
        this.btn_disabled = false
        if (res.code === 0) {
          this.$message.success('新增成功！')
          this.getGoodList()
          this.dialogFormVisible = false
          this.fileList = [] // 清空图片缓存
        }
      })
    },
    // 编辑推广分类
    editPromotionCommoditiesData(param) {
      editPromotionCommoditiesDataApi(param).then(res => {
        this.btn_disabled = false
        if (res.code === 0) {
          this.$message.success('编辑成功！')
          this.getGoodList()
          this.dialogFormVisible = false
          this.fileList = [] // 清空图片缓存
        }
      })
    },
    // 重新选取图片
    CikUpload(type) {
      if (type === 'logo') {
        document.getElementById('uploadEle').click()
        this.$refs.upload.clearFiles()
      }

      if (type === 'detail') {
        document.getElementById('uploadEle2').click()
        this.$refs.upload2.clearFiles()
      }
    },
    handleChooseParentIds(val) {}
  }
}
</script>
<style lang="scss" scoped></style>
